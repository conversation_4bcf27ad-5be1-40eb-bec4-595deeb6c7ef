<?xml version="1.0" encoding="UTF-8"?>
<!-- $Id: findbugs-exclude.xml 208237 2011-02-10 05:49:09Z oaleksandrova
	$ -->
<FindBugsFilter>
    <Match>
        <Class name="~.*Test" />
    </Match>
    <Match>
		<!-- exclude google collections -->
        <Or>
            <Package name="com.google.common.base" />
            <Package name="com.google.common.collect" />
        </Or>
    </Match>
    <Match>
		<!-- Классы сгенерированные ANTLR -->
        <Package name="ru.naumen.bcp.server.parser.antlr4" />
    </Match>
    <Match>
        <Or>
            <Bug pattern="EI_EXPOSE_REP" />
            <Bug pattern="EI_EXPOSE_REP2" />
            <!-- В Java 7 нет этой проблемы -->
            <Bug pattern="DMI_ENTRY_SETS_MAY_REUSE_ENTRY_OBJECTS" />
        </Or>
    </Match>
    <Match>
        <Bug pattern="EQ_DOESNT_OVERRIDE_EQUALS" />
        <Or>
            <Class name="~.*_SnapshotObject" />
            <Class name="ru.naumen.core.server.metastorage.impl.StorageValue" />
            <Class name="ru.naumen.metainfo.server.spi.elements.sec.ProfileImpl" />
            <Package name="ru.naumen.metainfo.shared" />
            <Package name="ru.naumen.metainfo.shared.metaclass" />
            <Package name="ru.naumen.metainfo.shared.relations" />
            <Package name="ru.naumen.metainfo.shared.ui" />
        </Or>
    </Match>
    <Match>
        <Bug pattern="BC_BAD_CAST_TO_CONCRETE_COLLECTION" />
        <Or>
            <Class name="~.*_SnapshotObject" />
        </Or>
    </Match>
    <Match>
        <Bug pattern="UUF_UNUSED_FIELD" />
        <Or>
            <Class name="ru.naumen.core.shared.dispatch.SerializationPolicyTypes" />
            <Class name="ru.naumen.core.shared.dispatch.SerializationPolicyTypes$DeserializeTypes" />
            <Class name="ru.naumen.core.shared.dispatch.SerializationPolicyTypes$SerializeTypes" />

            <Class name="ru.naumen.mailreader.shared.dispatch.SerializationPolicyTypes" />
            <Class name="ru.naumen.mailreader.shared.dispatch.SerializationPolicyTypes$DeserializeTypes" />
            <Class name="ru.naumen.mailreader.shared.dispatch.SerializationPolicyTypes$SerializeTypes" />

            <Class name="ru.naumen.mailsender.shared.dispatch.SerializationPolicyTypes" />
            <Class name="ru.naumen.mailsender.shared.dispatch.SerializationPolicyTypes$DeserializeTypes" />
            <Class name="ru.naumen.mailsender.shared.dispatch.SerializationPolicyTypes$SerializeTypes" />

            <Class name="ru.naumen.monitoring.shared.dispatch.SerializationPolicyTypes" />
            <Class name="ru.naumen.monitoring.shared.dispatch.SerializationPolicyTypes$DeserializeTypes" />
            <Class name="ru.naumen.monitoring.shared.dispatch.SerializationPolicyTypes$SerializeTypes" />

            <Class name="ru.naumen.advimport.shared.dispatch.SerializationPolicyTypes" />
            <Class name="ru.naumen.advimport.shared.dispatch.SerializationPolicyTypes$DeserializeTypes" />
            <Class name="ru.naumen.advimport.shared.dispatch.SerializationPolicyTypes$SerializeTypes" />

            <Class name="ru.naumen.reports.shared.dispatch.SerializationPolicyTypes" />
            <Class name="ru.naumen.reports.shared.dispatch.SerializationPolicyTypes$DeserializeTypes" />
            <Class name="ru.naumen.reports.shared.dispatch.SerializationPolicyTypes$SerializeTypes" />

            <Class name="ru.naumen.workload.shared.dispatch.SerializationPolicyTypes" />
            <Class name="ru.naumen.workload.shared.dispatch.SerializationPolicyTypes$DeserializeTypes" />
            <Class name="ru.naumen.workload.shared.dispatch.SerializationPolicyTypes$SerializeTypes" />
        </Or>
    </Match>
    <Match>
        <Bug pattern="EQ_UNUSUAL" />
        <Class name="ru.naumen.metainfo.shared.ElementBase" />
    </Match>
    <Match>
        <Bug pattern="OBL_UNSATISFIED_OBLIGATION" />
        <Or>
            <Class name="ru.naumen.utils.L10n.L10nUtil" />
        </Or>
    </Match>
    <Match>
        <Bug pattern="ES_COMPARING_PARAMETER_STRING_WITH_EQ" />
        <Or>
            <Class name="ru.naumen.core.shared.utils.ObjectUtils" />
            <Class name="ru.naumen.advimport.server.engine.LoggerAdapter" />
        </Or>
    </Match>
    <Match>
        <Bug pattern="CN_IDIOM" />
        <Or>
            <Class name="ru.naumen.metainfo.shared.ClassFqn" />
            <Class name="ru.naumen.metainfo.server.spi.elements.DependenceImpl" />
            <Class name="ru.naumen.core.shared.timer.definition.TimerDefinition" />
            <Class name="ru.naumen.core.server.timer.AbstractTimer" />
        </Or>
    </Match>
    <Match>
        <Bug pattern="CN_IDIOM_NO_SUPER_CALL" />
        <Or>
            <Class name="ru.naumen.core.server.timer.Timer" />
            <Class name="ru.naumen.core.server.timer.BackTimer" />
            <Class name="ru.naumen.metainfo.shared.ClassFqn" />
            <Class name="ru.naumen.metainfo.shared.elements.ResponsibilityTransferItem" />
        </Or>
    </Match>
    <Match>
        <Bug pattern="BC_UNCONFIRMED_CAST" />
        <Or>
            <Class
                name="ru.naumen.metainfo.server.spi.serialization.MetaClassImplDeserializationMappers$AbstractMetaClassMapper" />
        </Or>
    </Match>
    <Match>
        <Bug pattern="UNKNOWN" />
        <Or>
            <Class name="ru.naumen.core.shared.utils.CommonUtils" />
        </Or>
    </Match>
    <Match>
        <Bug pattern="IL_INFINITE_RECURSIVE_LOOP" />
        <Or>
            <Class name="ru.naumen.core.server.configuration.binding.ConfigValueBinder" />
        </Or>
    </Match>
    <Match>
        <Bug pattern="NP_NULL_ON_SOME_PATH_FROM_RETURN_VALUE" />
        <Or>
            <Class name="ru.naumen.core.server.naming.PeriodicalIDGenerator" />
            <Class name="ru.naumen.core.server.naming.RandomIDGenerator" />
            <Class name="ru.naumen.core.server.dispatch.DispatchStatistic$SyncronizationImpl" />
        </Or>
    </Match>
    <Match>
        <Bug pattern="UR_UNINIT_READ" />
        <Or>
            <Class name="ru.naumen.core.client.widgets.SimplePager" />
        </Or>
    </Match>
    <Match>
        <Bug pattern="EQ_COMPARETO_USE_OBJECT_EQUALS" />
        <Or>
            <Class name="ru.naumen.core.client.menu.MenuItem" />
        </Or>
    </Match>
    <Match>
        <Bug pattern="EQ_DOESNT_OVERRIDE_EQUALS" />
        <Or>
            <Class name="ru.naumen.metainfo.server.spi.elements.sec.GroupImpl" />
            <Class name="ru.naumen.metainfo.server.spi.elements.sec.RoleImpl" />
            <Class name="ru.naumen.metainfo.server.spi.elements.sec.SecDomainImpl" />

        </Or>
    </Match>
    <Match>
        <Bug pattern="ST_WRITE_TO_STATIC_FROM_INSTANCE_METHOD" />
        <Or>
            <Class name="ru.naumen.core.server.SpringContext" />
            <Class name="ru.naumen.core.client.JSUncaughtExceptionHandler" />
            <Class name="ru.naumen.core.client.MainContentDisplayOldImpl" />
            <Class name="ru.naumen.core.client.common.AsyncValueProxy" />
            <Class name="ru.naumen.core.client.common.impl.DialogsImpl" />
            <Class name="ru.naumen.core.client.forms.FormDisplayImpl" />
            <Class name="ru.naumen.core.client.mvp.AbstractAsyncCallback" />
            <Class name="ru.naumen.core.client.mvp.BasicPresenter" />
            <Class name="ru.naumen.core.client.mvp.LoggingEventBus" />
            <Class name="ru.naumen.core.client.widgets.PropertyFormDisplayBase" />
            <Class name="ru.naumen.core.client.widgets.FootedTextBox" />
            <Class name="ru.naumen.core.client.widgets.RichTextToolbar" />
            <Class name="ru.naumen.core.client.widgets.SimpleFileUpload" />
            <Class name="ru.naumen.core.client.widgets.columns.CssClassTextSafeHtmlRenderer" />
            <Class name="ru.naumen.core.client.widgets.navigation.HideShowNavigationPanel" />
            <Class name="ru.naumen.core.client.widgets.select.AbstractSelectListWidget" />
            <Class name="ru.naumen.core.client.widgets.select.SelectSearchInfoPanelWidget" />
            <Class name="ru.naumen.core.client.widgets.columns.LinkToPlaceColumn" />
            <Class name="ru.naumen.metainfoadmin.client.wf.statesetting.columns.PopupStateSettingButtonColumnBase" />
            <Class name="ru.naumen.core.client.forms.OkCancelPresenter" />
            <Class name="ru.naumen.core.client.widgets.tree.PopupValueCellTree" />
            <Class name="ru.naumen.dynaform.client.OperatorDisplayImpl" />
            <Class name="ru.naumen.dynaform.client.content.EditablePropertyListContentPresenter" />
            <Class name="ru.naumen.mailreader.client.TableDisplayImpl" />
            <Class name="ru.naumen.metainfoadmin.client.UIPresenter" />
            <Class name="ru.naumen.metainfoadmin.client.attributes.AttributesPresenter" />
            <Class name="ru.naumen.metainfoadmin.client.catalog.columns.CatalogItemCellRenderer" />
            <Class name="ru.naumen.metainfoadmin.client.catalog.forms.CatalogItemFormDisplayImpl" />
            <Class name="ru.naumen.metainfoadmin.client.common.content.LayoutContentPresenter" />
            <Class name="ru.naumen.metainfoadmin.client.common.content.TabContentPresenter" />
            <Class name="ru.naumen.metainfoadmin.client.common.content.tabbar.columns.EditCaptionTextCell" />
            <Class name="ru.naumen.metainfoadmin.client.dynadmin.content.WindowContentPresenter" />
            <Class name="ru.naumen.metainfoadmin.client.group.AttributeGroupDisplayImpl" />
            <Class name="ru.naumen.metainfoadmin.client.newentry.content.FormContentDisplayImpl" />
            <Class name="ru.naumen.metainfoadmin.client.scheduler.SchedulerTasksDisplayImpl" />
            <Class name="ru.naumen.metainfoadmin.client.scheduler.TriggersDisplayImpl" />
            <Class name="ru.naumen.metainfoadmin.client.sec.SecItemsDisplayImpl" />
            <Class name="ru.naumen.metainfoadmin.client.wf.statesetting.columns.PopupStateSettingButtonColumnBase" />
            <Class name="ru.naumen.core.server.flex.FlexHelper" />
        </Or>
    </Match>
    <Match>
        <Bug pattern="SF_SWITCH_NO_DEFAULT" />
        <Or>
            <Class name="ru.naumen.common.shared.utils.Color" />
            <Class name="ru.naumen.core.client.widgets.ThreeStateCheckBox" />
        </Or>
    </Match>
    <Match>
        <Bug pattern="URF_UNREAD_PUBLIC_OR_PROTECTED_FIELD" />
        <Or>
            <Class name="ru.naumen.core.client.MainDisplayImpl" />
            <Class name="ru.naumen.core.client.content.factory.InjectorFactory" />
            <Class name="ru.naumen.core.client.forms.OkCancelPresenter" />
            <Class name="ru.naumen.core.server.flex.attr.AbstractMultiColumnTypeStrategy" />
            <Class name="ru.naumen.core.shared.criteria.Order" />
            <Class name="ru.naumen.dynaform.client.content.objectlist.listpresentation.ListPresenterContextLogicImpl" />
            <Class name="ru.naumen.dynaform.client.content.objectlist.listpresentation.ListPresenterContextLogicImpl" />
            <Class name="ru.naumen.metainfoadmin.client.catalog.servicetime.ServiceTimeDisplay" />
            <Class name="ru.naumen.metainfoadmin.client.forms.AbstractHasAttributesElementFormPresenter" />
        </Or>
    </Match>
    <Match>
        <Bug pattern="SA_LOCAL_SELF_COMPARISON" />
        <Or>
            <Class name="ru.naumen.core.client.validation.OnGetValueThrowValidator" />
        </Or>
    </Match>
    <Match>
        <Bug pattern="NP_BOOLEAN_RETURN_NULL" />
        <Or>
            <Class name="ru.naumen.advimport.server.engine.converters.BooleanConverterBean" />
            <Class name="ru.naumen.core.client.widgets.BooleanRadioButtonGroup" />
            <Class name="ru.naumen.core.client.widgets.tree.ValueCellTreeCheckBoxHasCell" />
            <Class name="ru.naumen.sec.server.autorize.AuthorizationServiceImpl" />
            <Class name="ru.naumen.core.server.bo.bop.OldValueScriptObject" />
            <Class name="ru.naumen.core.server.eventaction.EventActionOldSubjectDtObject" />
            <Class name="ru.naumen.core.server.script.spi.ScriptDtObject" />
            <Class name="ru.naumen.core.shared.dto.AbstractDtObject" />
            <Class name="ru.naumen.core.shared.dto.AnyDtObject" />
            <Class name="ru.naumen.metainfoadmin.client.sec.AccessMatrixDisplayImpl" />
            <Class name="ru.naumen.metainfoadmin.client.sec.MarkersProfilesDisplayImpl" />
            <Class name="ru.naumen.metainfoadmin.client.wf.responsibility.DefaultResponsibilityContext" />
        </Or>
    </Match>
    <!-- Ошибки, не дающие сериализовывать обеъекты. Мешают использованию кластера -->
    <Match>
        <Bug pattern="SE_BAD_FIELD" />
        <Or>
            <Class name="ru.naumen.sec.server.autorize.SimpleAuthorizationContext" />
            <Class name="ru.naumen.core.server.metastorage.impl.userinfo.UserAdvlistSettings" />
            <Class name="ru.naumen.core.shared.timer.definition.ScriptTimerCondition" />
            <Class name="ru.naumen.core.server.flex.spi.ClassLoaderServiceImpl" />
            <Class name="ru.naumen.core.server.upload.spi.DBFileItem" />
            <Class name="ru.naumen.metainfo.server.spi.elements.AttributeGroupDeclarationImpl" />
            <Class name="ru.naumen.metainfo.server.spi.elements.CatalogImpl" />
            <Class name="ru.naumen.metainfo.server.spi.elements.JBCNodeBaseElement" />
            <Class name="ru.naumen.metainfo.server.spi.elements.MetaClassImpl" />
            <Class name="ru.naumen.metainfo.server.spi.elements.sec.RoleImpl" />
            <Class name="ru.naumen.metainfo.server.spi.elements.wf.ActionImpl" />
            <Class name="ru.naumen.metainfo.server.spi.elements.wf.ConditionImpl" />
            <Class name="ru.naumen.metainfo.server.spi.elements.wf.WorkflowImpl" />
            <Class name="ru.naumen.reports.server.ReportResourcesServlet" />
        </Or>
    </Match>
    <!-- Ошибки, не дающие сериализовывать обеъекты. Мешают использованию кластера -->
    <Match>
        <Bug pattern="SE_BAD_FIELD_INNER_CLASS" />
        <Or>
            <Class name="ru.naumen.core.server.flex.spi.ReloadableSessionFactoryBean$ReloadableSessionFactory" />
        </Or>
    </Match>
    <Match>
        <Bug pattern="SE_BAD_FIELD_STORE" />
        <Or>
            <Class name="ru.naumen.core.server.flex.spi.ClassLoaderServiceImpl" />
            <Class name="ru.naumen.core.server.flex.spi.ClassLoaderServiceImpl$1" />
        </Or>
    </Match>
    <Match>
        <Bug pattern="MS_SHOULD_BE_FINAL" />
        <Class name="ru.naumen.core.server.Version" />
    </Match>
    <Match>
        <Bug pattern="URF_UNREAD_FIELD" />
        <Or>
            <Class name="ru.naumen.migration.server.metainfoscripts.V4_0_0_6_2" />
            <Class name="ru.naumen.migration.server.metainfoscripts.V4_0_0_6_3" />
        </Or>
    </Match>
    <Match>
        <Bug pattern="DP_CREATE_CLASSLOADER_INSIDE_DO_PRIVILEGED" />
        <Or>
            <Class name="ru.naumen.core.server.flex.spi.ClassLoaderServiceImpl" />
        </Or>
    </Match>
    <Match>
        <Bug pattern="EQ_COMPARETO_USE_OBJECT_EQUALS" />
        <Or>
            <Class name="ru.naumen.metainfo.shared.dispatch2.DirectLinkAttributeDescription" />
        </Or>
    </Match>
    <Match>
        <Bug pattern="MF_CLASS_MASKS_FIELD" />
        <Or>
            <Class
                name="ru.naumen.dynaform.client.content.objectlist.listpresentation.extended.advlist.filter.or.presentation.ListFilterOrPresentationInfoDateFromToImpl" />
        </Or>
    </Match>
    <Match>
        <Bug pattern="MS_MUTABLE_ARRAY" />
        <Or>
            <Class name="ru.naumen.core.shared.Constants.Association" />
        </Or>
    </Match>
    <Match>
        <Bug pattern="NM_SAME_SIMPLE_NAME_AS_SUPERCLASS" />
        <Or>
            <Class name="ru.naumen.core.server.dispatch.BatchActionHandler" />
            <Class name="ru.naumen.core.server.scheduler.CronTrigger" />
            <Class name="ru.naumen.core.server.scheduler.PeriodicTrigger" />
            <Class name="ru.naumen.sec.server.session.SessionAuthenticationException" />
        </Or>
    </Match>
    <Match>
        <Bug pattern="LI_LAZY_INIT_UPDATE_STATIC" />
        <Or>
            <Class name="ru.naumen.core.client.common.impl.DialogsImpl" />
        </Or>
    </Match>
    <Match>
        <Bug pattern="SE_COMPARATOR_SHOULD_BE_SERIALIZABLE" />
        <Or>
            <Class name="ru.naumen.metainfoadmin.client.sec.AccessMatrixBasePresenter$MarkersTreeComparator" />
        </Or>
    </Match>
    <Match>
        <Bug pattern="RCN_REDUNDANT_NULLCHECK_OF_NULL_VALUE" />
        <Or>
            <Class name="ru.naumen.core.server.hibernate.DDLTool" />
            <Class name="ru.naumen.core.server.hibernate.MSDDLDialect" />
            <Class name="ru.naumen.core.server.hibernate.OracleDDLDialect" />
            <Class name="ru.naumen.core.server.hibernate.PostgresqlDDLDialect" />
        </Or>
    </Match>
    <!-- Необходимо исправить замечания, чтобы работала кластеризация -->
    <Match>
        <Bug pattern="SE_NO_SUITABLE_CONSTRUCTOR" />
        <Or>
            <Class name="ru.naumen.metainfo.server.spi.elements.MetaClassImpl" />
        </Or>
    </Match>
    <!-- Возможно, необоходимо исправить замечания, чтобы работала кластеризация -->
    <Match>
        <Bug pattern="SE_TRANSIENT_FIELD_NOT_RESTORED" />
        <Or>
            <Class name="ru.naumen.core.server.upload.NestedFileItem" />
            <Class name="ru.naumen.core.shared.EntityReference" />
        </Or>
    </Match>
    <!-- Возможно, необоходимо исправить замечания, чтобы работала кластеризация -->
    <Match>
        <Class name="ru.naumen.metainfo.server.spi.MetainfoExportServlet" />
    </Match>
    <Match>
        <Bug pattern="UWF_UNWRITTEN_PUBLIC_OR_PROTECTED_FIELD" />
        <Or>
            <Class name="ru.naumen.metainfoadmin.client.sec.AbstractTablePresenter" />
        </Or>
    </Match>
    <Match>
        <Bug pattern="DMI_COLLECTION_OF_URLS" />
        <Or>
            <Class name="ru.naumen.core.server.flex.spi.ClassLoaderServiceImpl$2" />
        </Or>
    </Match>
    <Match>
        <Bug pattern="RCN_REDUNDANT_NULLCHECK_OF_NONNULL_VALUE" />
        <Or>
            <Class name="ru.naumen.sec.server.autorize.AuthorizationServiceImpl" />
        </Or>
    </Match>
    <!-- Классы, скопированные из других библиотек -->
    <Match>
        <Or>
            <Class name="net.customware.gwt.dispatch.shared.BatchAction$OnException" />
            <Class name="ru.naumen.core.server.hibernate.uuid.UUIDIdentifiableByteBuddyProxyFactory" />
        </Or>
    </Match>
    <!-- Отдельные классы -->
    <Match>
        <Class name="com.google.gwt.user.client.ui.NTree" />
        <Bug pattern="NP_LOAD_OF_KNOWN_NULL_VALUE" />
    </Match>
    <Match>
        <Class name="ru.naumen.core.server.flex.spi.FlexSessionFactoryBuilder" />
    </Match>
    <Match>
    	<Or>
	    	<Class name="ru.naumen.metainfoadmin.client.wf.DeleteStateCommand" />
	        <Class name="ru.naumen.metainfoadmin.client.wf.MoveStateCommand" />
    	</Or>
        <Bug pattern="BC_UNCONFIRMED_CAST" />
    </Match>
    <!-- копипаст из движка отчетов, для возможности переопределения логики -->
    <Match>
        <Or>
            <Class name="ru.naumen.reports.engine.classic.core.modules.output.table.html.HtmlPrinter"/>
        </Or>
    </Match>
</FindBugsFilter>
