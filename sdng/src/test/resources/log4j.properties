#$Id$
log4j.rootLogger=WARN, FILE

log4j.appender.stdout=org.apache.log4j.ConsoleAppender
log4j.appender.stdout.layout=org.apache.log4j.PatternLayout
log4j.appender.stdout.layout.ConversionPattern=%r [%t] (%d{dd MMM yyyy HH:mm:ss,SSS Z}) %-5p %c{2} - %m%n


# Варианты настройки логгера

# Простой аппендер
#log4j.appender.FILE=org.apache.log4j.FileAppender

# Меняет файл по превышению максимального размера
#log4j.appender.FILE=org.apache.log4j.RollingFileAppender
#log4j.appender.FILE.MaxFileSize=20MB
#log4j.appender.FILE.MaxBackupIndex=0

# Меняет файл периодически по времени (зависит от datePattern)
log4j.appender.FILE=org.apache.log4j.DailyRollingFileAppender
log4j.appender.FILE.datePattern='.'yyyy-MM-dd

# Общие для всех аппендеров настройки
log4j.appender.FILE.file=target/tests.log
log4j.appender.FILE.append=true
log4j.appender.FILE.encoding=utf-8
log4j.appender.FILE.layout=org.apache.log4j.PatternLayout
log4j.appender.FILE.layout.ConversionPattern=%r [%t] (%d{dd MMM yyyy HH:mm:ss,SSS Z}) %-5p %c{2} - %m%n


# Default log level for Naumen classes
log4j.category.ru.naumen=INFO
log4j.category.ru.naumen.core.server.hquery=INFO

# JSP compiler logging
log4j.category.org.apache.jasper.compiler=ALL

# Spring
log4j.category.org.springframework=WARN
log4j.category.org.springframework.security.web.csrf=INFO

# Flyway
log4j.category.org.flywaydb=INFO

# Hibernate debugging & low-level activity
#log4j.category.org.hibernate.engine.QueryParameters=DEBUG
#log4j.category.org.hibernate.engine.query.HQLQueryPlan=DEBUG
#log4j.category.org.hibernate.event.def.DefaultLoadEventListener=DEBUG
#log4j.category.org.hibernate.event.def.AbstractFlushingEventListener=DEBUG
#log4j.category.org.hibernate.cache.ReadWriteCache=DEBUG

log4j.category.org.hibernate=WARN
# Log all SQL DML statements as they are executed
#log4j.category.org.hibernate.SQL=INFO
# Log all JDBC parameters
#log4j.category.org.hibernate.type=INFO
# Log all SQL DDL statements as they are executed
#log4j.category.org.hibernate.tool.hbm2ddl=INFO
# Log the state of all entities (max 20 entities) associated with the session at flush time
#log4j.category.org.hibernate.pretty=INFO
# Log all second-level cache activity ALL for debug
#log4j.category.org.hibernate.cache=INFO
# Log transaction related activity
#log4j.category.org.hibernate.transaction=INFO
# Log all JDBC resource acquisition
#log4j.category.org.hibernate.jdbc=INFO
# Log HQL and SQL ASTs during query parsing
#log4j.category.org.hibernate.hql.internal.ast.AST=INFO
# Log all JAAS authorization requests
#log4j.category.org.hibernate.secure=INFO
