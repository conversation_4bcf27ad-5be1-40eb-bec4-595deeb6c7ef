<?xml version="1.0" encoding="UTF-8"?>
<!--$Id$-->
<beans
        xmlns="http://www.springframework.org/schema/beans"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns:tx="http://www.springframework.org/schema/tx"
        xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
	http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.0.xsd">

	<bean
			class="ru.naumen.core.server.SpringPropertyPlaceholderConfigurer" name="propertyConfigurer">
		<property name="ignoreResourceNotFound" value="true"/>
		<property name="locations">
			<list>
				<value>classpath:/async-default.properties</value>
				<value>classpath:/auth-default.properties</value>
				<value>classpath:/dbaccess-default.properties</value>
				<value>classpath:/integration-default.properties</value>
				<value>classpath:/cache-default.properties</value>
				<value>classpath:/cluster-default.properties</value>
				<value>classpath:/jms-default.properties</value>
				<value>classpath:/log-default.properties</value>
				<value>classpath:/mail-default.properties</value>
				<value>classpath:/mobile-default.properties</value>
				<value>classpath:/search-default.properties</value>
				<value>classpath:/settings-default.properties</value>
				<value>classpath:/smia-default.properties</value>
				<value>classpath:/ndap-default.properties</value>
				<value>classpath:/omnichannel-default.properties</value>
				<value>classpath:/report-default.properties</value>
				<value>classpath:/planned-versions-default.properties</value>
				<value>classpath:/websocket-default.properties</value>
				<value>classpath:/common-test.properties</value>
				<value>classpath:/dbaccess.properties</value>
				<value>classpath:/ui2-default.properties</value>
				<value>file://${dbaccess}</value>
			</list>
		</property>
	</bean>

	<bean id="uRestMappings" class="org.springframework.beans.factory.config.PropertiesFactoryBean">
		<property name="ignoreResourceNotFound" value="true"/>
		<property name="locations">
			<list>
				<value>classpath:/urestmappings-default.properties</value>
				<value>classpath:/urestmappings.properties</value>
			</list>
		</property>
	</bean>

	<!-- Общие настройки подключения к БД для выполнения тестов -->
	<bean
			id="basicDataSource"
			class="org.apache.commons.dbcp2.managed.BasicManagedDataSource"
			destroy-method="close">
		<property
				name="driverClassName"
				value="${db.driver}"/>
		<property
				name="url"
				value="${db.url}"/>
		<property
				name="username"
				value="${db.user}"/>
		<property
				name="password"
				value="${db.password}"/>
		<property
				name="connectionProperties"
				value="trustServerCertificate=${db.mssql.trustServerCertificate}"/>
		<property name="transactionManager" ref="jtaTxManager"/>
		<property name="defaultAutoCommit" value="true"/>
	</bean>
	<bean id="dataSource" class="ru.naumen.core.server.jta.managed.local.TransactionalDataSourceImpl">
		<constructor-arg ref="basicDataSource"/>
	</bean>


	<tx:annotation-driven
			transaction-manager="txManager"/>

	<bean class="ru.naumen.core.server.TransactionalConfiguration"/>
	<bean id="context" class="ru.naumen.core.server.SpringContext"/>
	<bean id="springContext" class="ru.naumen.core.server.SpringContext"/>
	<bean id="ddlConfiguration" class="ru.naumen.core.server.hibernate.DDLConfiguration"/>
	<bean class="ru.naumen.core.server.hibernate.ConnectionPermissionCheckerStub"/>
	<bean id="forbiddenIndexRegistry" class="ru.naumen.core.server.flex.spi.ForbiddenIndexRegistry"/>
</beans>