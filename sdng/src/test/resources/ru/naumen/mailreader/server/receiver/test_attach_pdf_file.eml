X-Account-Key: account1
X-UIDL: 000001f653db3ec8
X-Mozilla-Status: 0001
X-Mozilla-Status2: ********
X-Mozilla-Keys:                                                                                 
Return-Path: <<EMAIL>>
X-Original-To: <EMAIL>
Delivered-To: <EMAIL>
Received: from [***************] (unknown [***************])
	(using TLSv1.2 with cipher ECDHE-RSA-AES128-GCM-SHA256 (128/128 bits))
	(No client certificate requested)
	(Authenticated sender: <EMAIL>)
	by mx2.naumen.ru (Postfix) with ESMTPSA id 09C6E68507FC
	for <<EMAIL>>; Fri,  3 Oct 2014 15:07:07 +0600 (YEKT)
Message-ID: <<EMAIL>>
Date: Fri, 03 Oct 2014 15:07:07 +0600
From: =?UTF-8?B?0KPQu9GM0Y/QvdC+0LIg0JDRgNGC0LXQvA==?=
 <<EMAIL>>
User-Agent: Mozilla/5.0 (X11; Linux x86_64; rv:31.0) Gecko/******** Thunderbird/31.1.2
MIME-Version: 1.0
To: =?UTF-8?B?0KPQu9GM0Y/QvdC+0LIg0JDRgNGC0LXQvA==?= <<EMAIL>>
Subject: MailApiTest
Content-Type: multipart/mixed;
 boundary="------------090407000102060502080200"

This is a multi-part message in MIME format.
--------------090407000102060502080200
Content-Type: text/plain; charset=utf-8; format=flowed
Content-Transfer-Encoding: 7bit

This letter contains pdf file in attachments.

--------------090407000102060502080200
Content-Type: application/pdf;
 name="sample.pdf"
Content-Transfer-Encoding: base64
Content-Disposition: attachment;
 filename="sample.pdf"


--------------090407000102060502080200--

