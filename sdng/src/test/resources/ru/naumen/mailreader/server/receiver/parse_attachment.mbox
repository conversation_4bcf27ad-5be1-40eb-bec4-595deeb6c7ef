From n<PERSON><PERSON><PERSON>@naumen.ru Tue Sep  1 16:12:45 2009
From: <PERSON><PERSON> <n<PERSON><PERSON><PERSON>@naumen.ru>
X-KMail-Transport: Sendmail
X-KMail-Fcc: sent-mail
To: n<PERSON><PERSON><PERSON>@sd-dev2.naumen.ru
Subject: =?utf-8?b?0J/QuNGB0YzQvNC+INGB?= =?utf-8?b?INCy0LvQvtC20LXQvdC40LXQvA==?=
Date: Tue, 1 Sep 2009 16:12:45 +0600
User-Agent: KMail/1.11.4 (Linux/2.6.30-1-amd64; KDE/4.2.4; x86_64; ; )
X-KMail-QuotePrefix: >
MIME-Version: 1.0
Content-Type: Multipart/Mixed;
  boundary="Boundary-00=_dOPnKWa+JYDeLqF"
Message-Id: <<EMAIL>>
Status: RO
X-Status: RST
X-KMail-EncryptionState:  
<PERSON>-<PERSON><PERSON><PERSON>-SignatureState:  
X-KMail-MDN-Sent:  

--Boundary-00=_dOPnKWa+JYDeLqF
Content-Type: text/plain;
  charset="utf-8"
Content-Transfer-Encoding: base64
Content-Disposition: inline

0KLQtdC70L4g0L/QuNGB0YzQvNCwCg==

--Boundary-00=_dOPnKWa+JYDeLqF
Content-Type: text/plain;
  charset="UTF-8";
  name*=UTF-8''%D0%B2%D0%BB%D0%BE%D0%B6%D0%B5%D0%BD%D0%B8%D1%8F%2Etxt
Content-Transfer-Encoding: base64
Content-Disposition: attachment;
	filename*=utf-8''%D0%B2%D0%BB%D0%BE%D0%B6%D0%B5%D0%BD%D0%B8%D1%8F%2Etxt

0KLQtdC60YHRgiDQstC70L7QttC10L3QuNGP

--Boundary-00=_dOPnKWa+JYDeLqF--
