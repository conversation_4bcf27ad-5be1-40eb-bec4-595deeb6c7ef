X-Account-Key: account3
X-UIDL: 000003c24d777c91
X-Mozilla-Status: 0001
X-Mozilla-Status2: ********
X-Mozilla-Keys:                                                                                 
Return-Path: <oaleksand<PERSON><EMAIL>>
Delivered-To: o<PERSON><PERSON><PERSON><PERSON><PERSON>@sd-dev2.naumen.ru
Received: from [***************] (unknown [***************])
	by sd-dev2.office0.naumen.ru (Postfix) with ESMTPA id 590D648263
	for <<EMAIL>>; Mon, 25 May 2015 15:45:36 +0600 (YEKT)
Message-ID: <<EMAIL>>
Date: Mon, 25 May 2015 14:45:35 +0500
From: =?UTF-8?B?0J7Qu9GM0LPQsCDQkNC70LXQutGB0LDQvdC00YDQvtCy0LA=?=
 <oaleksand<PERSON><EMAIL>>
User-Agent: Mozilla/5.0 (X11; Linux x86_64; rv:31.0) Gecko/******** Thunderbird/31.6.0
MIME-Version: 1.0
To: "<EMAIL>" <<EMAIL>>
Subject: testMsg
Content-Type: multipart/mixed;
 boundary="------------080900090205050208030404"

This is a multi-part message in MIME format.
--------------080900090205050208030404
Content-Type: text/plain; charset=utf-8; format=flowed
Content-Transfer-Encoding: 7bit



--------------080900090205050208030404
Content-Type: text/xml;
 name="license-example.xml"
Content-Transfer-Encoding: 8bit
Content-Disposition: inline;
 filename="license-example.xml"

<?xml version="1.0" encoding="UTF-8"?>
<ns2:naumen-license xmlns:ns2="http://naumen.ru/license">
	<client>Стенд для тестирования</client>
	<superuser code="superuser" count="1">Супер пользователь</superuser>
	<named code="named" count="3">Именная</named>
	<concurrent code="concurrent" count="10">Конкурентная</concurrent>
	<parameter name="rolesForUnlicensedUsers">
		currentUser, AgreementRecipient, slmServiceRecipient, ServiceCallClient, fileAuthor, commentAuthor, ouMember, serviceCallEmployeeOfClientOU
	</parameter>
</ns2:naumen-license>
--------------080900090205050208030404--

