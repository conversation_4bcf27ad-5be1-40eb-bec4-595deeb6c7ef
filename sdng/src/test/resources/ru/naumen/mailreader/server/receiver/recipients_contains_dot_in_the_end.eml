Received: from example.local (***********) by
 example.local (***********) with Microsoft SMTP Server
 (version=TLS1_2, cipher=TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256) id 15.1.1713.5
 via Mailbox Transport; Mon, 30 Sep 2024 03:30:12 +0500
Received: from [***********] (***********) by example.local
 (***********) with Microsoft SMTP Server (version=TLS1_2,
 cipher=TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256) id 15.1.1713.5; Mon, 30 Sep
 2024 03:30:11 +0500
Message-ID: <<EMAIL>>
Date: Mon, 30 Sep 2024 01:30:57 +0300
User-Agent: Mozilla Thunderbird
Content-Language: en-US
To: <PERSON> <<EMAIL>.>
From: <<EMAIL>>
Subject: test
Content-Type: text/plain; charset="UTF-8"; format=flowed
Content-Transfer-Encoding: 7bit
Return-Path: <EMAIL>
X-MS-Exchange-Organization-Network-Message-Id: a879b4f3-1e6a-4e07-04fd-08dce0d6481b
X-MS-Exchange-Organization-AuthSource: example.local
X-MS-Exchange-Organization-AuthAs: Internal
X-MS-Exchange-Organization-AuthMechanism: 07
X-Originating-IP: [***********]
X-ClientProxiedBy: example.local (***********) To
 example.local (***********)
X-MS-Exchange-Organization-AVStamp-Enterprise: 1.0
X-MS-Exchange-Organization-Recipient-P2-Type: Bcc
X-MS-Exchange-Transport-EndToEndLatency: 00:00:00.7021407
X-MS-Exchange-Processed-By-BccFoldering: 15.01.1713.009
MIME-Version: 1.0