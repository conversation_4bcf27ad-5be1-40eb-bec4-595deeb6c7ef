<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<metainfoContainer>
	<head>
		<date>2019-08-28T11:09:01.415+05:00</date>
		<exportMode>partial</exportMode>
		<version>4.10.0.20-SNAPSHOT</version>
	</head>
	<tags/>
	<system-metaclass>
		<fqn>
			<id>employee</id>
		</fqn>
		<parent>
			<id>abstractBO</id>
		</parent>
		<properties/>
		<tags/>
		<responsibilityTransferTableEnabled>true</responsibilityTransferTableEnabled>
		<attributes>
			<attribute>
				<code>newTestAttr1</code>
				<computable>false</computable>
				<editable>true</editable>
				<editableInLists>false</editableInLists>
				<required>false</required>
				<requiredInInterface>false</requiredInInterface>
				<unique>false</unique>
				<determinable>false</determinable>
				<title lang="ru">newTestAttr1</title>
				<description lang="ru"/>
				<type>
					<code>string</code>
					<property code="code">string</property>
					<property code="inputMask"/>
					<property code="inputMaskMode"/>
					<property code="string">255</property>
				</type>
				<filteredByScript>false</filteredByScript>
				<computableOnForm>false</computableOnForm>
				<viewPresentation>
					<code>stringView</code>
				</viewPresentation>
				<editPresentation>
					<code>stringEdit</code>
				</editPresentation>
				<useGenerationRule>false</useGenerationRule>
				<accessor>flexAccessor</accessor>
				<defaultByScript>false</defaultByScript>
				<systemEditable>true</systemEditable>
				<searchSetting>
					<simpleSearchableForLicensed>false</simpleSearchableForLicensed>
					<simpleSearchableForNotLicensed>false</simpleSearchableForNotLicensed>
					<extendedSearchableForNotLicensed>false</extendedSearchableForNotLicensed>
					<extendedSearchableForLicensed>false</extendedSearchableForLicensed>
					<searchBoost>1.0</searchBoost>
					<code>newTestAttr1</code>
					<declaredMetaClass>employee</declaredMetaClass>
					<attrCode>newTestAttr1</attrCode>
				</searchSetting>
				<exportNDAP>false</exportNDAP>
				<hiddenWhenEmpty>false</hiddenWhenEmpty>
				<hiddenWhenNoPossibleValues>false</hiddenWhenNoPossibleValues>
			</attribute>
		</attributes>
		<attribute-overrides/>
		<permittedTypesInfo/>
		<attribute-groups/>
		<workflow inherit="true"/>
		<sortingCriteria>
			<type>BY_ATTR_WEIGHT</type>
		</sortingCriteria>
		<maxSearchResults>3</maxSearchResults>
		<searchOrder>3</searchOrder>
	</system-metaclass>
	<system-metaclass>
		<fqn>
			<id>root</id>
		</fqn>
		<parent>
			<id>abstractBO</id>
		</parent>
		<properties/>
		<tags/>
		<responsibilityTransferTableEnabled>true</responsibilityTransferTableEnabled>
		<attributes>
			<attribute>
				<code>newTestAttr1</code>
				<computable>false</computable>
				<editable>true</editable>
				<editableInLists>false</editableInLists>
				<required>false</required>
				<requiredInInterface>false</requiredInInterface>
				<unique>false</unique>
				<determinable>false</determinable>
				<title lang="ru">newTestAttr1</title>
				<description lang="ru"/>
				<type>
					<code>integer</code>
					<property code="code">integer</property>
					<property code="hasGroupSeparator">false</property>
				</type>
				<filteredByScript>false</filteredByScript>
				<computableOnForm>false</computableOnForm>
				<viewPresentation>
					<code>integerView</code>
				</viewPresentation>
				<editPresentation>
					<code>integerEdit</code>
				</editPresentation>
				<useGenerationRule>false</useGenerationRule>
				<accessor>flexAccessor</accessor>
				<defaultByScript>false</defaultByScript>
				<systemEditable>true</systemEditable>
				<searchSetting>
					<simpleSearchableForLicensed>false</simpleSearchableForLicensed>
					<simpleSearchableForNotLicensed>false</simpleSearchableForNotLicensed>
					<extendedSearchableForNotLicensed>false</extendedSearchableForNotLicensed>
					<extendedSearchableForLicensed>false</extendedSearchableForLicensed>
					<searchBoost>1.0</searchBoost>
					<code>newTestAttr1</code>
					<declaredMetaClass>root</declaredMetaClass>
					<attrCode>newTestAttr1</attrCode>
				</searchSetting>
				<exportNDAP>false</exportNDAP>
				<hiddenWhenEmpty>false</hiddenWhenEmpty>
				<hiddenWhenNoPossibleValues>false</hiddenWhenNoPossibleValues>
			</attribute>
		</attributes>
		<attribute-overrides/>
		<permittedTypesInfo/>
		<attribute-groups>
			<group>
				<code>64ca1639-a255-4577-994b-7793ef0059d1</code>
				<title lang="ru">Общая информация</title>
				<attribute>title</attribute>
				<order>title</order>
			</group>
			<override>
				<code>system</code>
				<title lang="ru">Системные атрибуты</title>
				<attribute>UUID</attribute>
				<order>title</order>
				<order>UUID</order>
			</override>
		</attribute-groups>
		<workflow inherit="true"/>
		<sortingCriteria>
			<type>BY_ATTR_WEIGHT</type>
		</sortingCriteria>
		<maxSearchResults>3</maxSearchResults>
		<searchOrder>7</searchOrder>
	</system-metaclass>
	<mail-processor-rules/>
	<style-templates/>
	<list-templates/>
	<user-events/>
	<event-actions/>
	<embedded-applications>
		<application>
			<base64FileContent>UEsDBBQAAAAIALVVqE47w7TZgQMAAGwIAAAKAAAAaW5kZXguaHRtbL1WXW/bNhR9nn8FpxVwgkZU4sRpmsUGGi+uk8ZrGztt2mEoaOlKok2RCkk5dtf99/HDju2gQTeg3YNF3uNLnsN7jwid5Lpg7dpJDiQxg4olLXUNISXjVpBrXarjKIpFAnh8W4Gc41gUkZ+G+/gA7+GCcjxWgVlDuYZMUj1vBSonjeZh2BncCHkz/RgPJ4TODj9MxWGvLOOPL0GPPvRfvh902endUS/tXIiW3SGWQikhaUZ5KyBc8HkhKhW0TyIvzCj8OQzROY9ZlQAijKGzhGohUckqs0ihzmCAlJ4zwCgM27XaCaN8giSwVuBglQPoAOUS0rXzJXyscMxElaSMSHCHJGMyixgdqSiVgjASgmOKGvg5bkaxWuKfPI4NErT/R75ykiWu+AtiXyGk5yW0Ag0zHY3JlHg0eNDPf8W/wT9+lH78eH86xjeoT6UU0jbGt+R7VMgasnD7Rk28j3fXgPWSfMMrFwOUUiNgoesx5RdL4d+vwt/S/6CmP5C4MEA0K5j9fY36683abFalAKeCa3IHShSe0iwBokBF0yY+wnvOv6YFrjMPLor9o4OwuStOr3tnxbR8+3SPvT+YN5+9GXaLvENuyt3+ZTM53O3v9Z9VvcZTfntNp5/PYZTz5PXFq8YR4dM0ePTqqG029s1vXQSzUkht229q8t97K8kdzqjOSTxxR4VXQpQF4Sqyd2mjTNKoIEqDjBKq9D2IRxVPGDwo8JK5bW6/J1tpxWNNBd/aRn8ZwEL1hE5/2Xgj69vYx97OWz7TXL+pJAUcIy0r2Kn9hBCYMstj9AR319Lx2e/Ds6tPp1c7i2VaCDYi8rTSWnB1jP6ojwRL6juoTjVhNLYzoxyksQLYQGlJJzDMpaiy3AHVyB/CBqoqQa7CL/ZhzdElBWXzZTSgn91esWBCOi5utx9Yj63CDiNKuTQGRHaFLIjWlGf3G0MhNI2N7OW+L7wFnZASYkpYJyeSxKYQLie0j9IgmSRl7ne0kGXrAc1yvZFwr8cUIuOexC55fbmaX7u5qHRi6u21L2e3ldBu+RePK5D60rxQq+i8IBmswnc0AbEKu5St/TkkIx+GK6x35Y5qXrZYv2DMBhloY3J3DEm9jhxY6UZjRSe8Ysy0CIDfizMddsQSzPins8bf2zX7+LW2dh1Ei++EkUjmZjDeRDRpBRv+tPY2f9jsRVrkPzL+AVBLAQIfABQAAAAIALVVqE47w7TZgQMAAGwIAAAKACQAAAAAAAAAIAAAAAAAAABpbmRleC5odG1sCgAgAAAAAAABABgAMMcBRmEF1QEwxwFGYQXVAUPHgDRhBdUBUEsFBgAAAAABAAEAXAAAAKkDAAAAAA==</base64FileContent>
			<embeddedApplication xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ClientSideApplication">
				<embedded-application>ExternalApplication</embedded-application>
				<code>AppWithContent</code>
				<description lang="ru"/>
				<fullscreenAllowed>false</fullscreenAllowed>
				<initialHeight>400</initialHeight>
				<on>true</on>
				<title lang="ru">Встроенное приложение с контентом</title>
			</embeddedApplication>
			<fileName>index.zip</fileName>
		</application>
	</embedded-applications>
	<ui>
		<fqn>
			<id>root</id>
		</fqn>
		<code>__window__</code>
		<window uuid="f96e2faa-13ad-0076-0000-00007a056193">
			<profiles/>
			<tags/>
			<caption lang="ru">Карточка объекта</caption>
			<objectCardCaptionAttributeCode>title</objectCardCaptionAttributeCode>
			<tabBar hasHead="true" uuid="f96e2faa-13ad-0077-0000-00007a056193">
				<profiles/>
				<tags/>
				<position>FULL</position>
				<hideSingleTab>false</hideSingleTab>
				<tab additional="false" uuid="f96e2faa-13ad-0078-0000-00007a056193">
					<profiles/>
					<tags/>
					<caption lang="ru">Атрибуты компании</caption>
					<considerListFilter>false</considerListFilter>
					<toolPanel uuid="f7175931-1590-060e-0000-00006429cffa">
						<profiles/>
						<tags/>
						<ignoreInImport>false</ignoreInImport>
						<showToolCaptions>true</showToolCaptions>
						<useSystemSettings>true</useSystemSettings>
					</toolPanel>
					<layout uuid="f96e2fab-13ad-0079-0000-00007a056193">
						<profiles/>
						<tags/>
						<embedded-application-content uuid="myApp">
							<profiles/>
							<tags/>
							<position>FULL</position>
							<application>AppWithContent</application>
							<caption lang="ru">Приложение с контентом</caption>
							<parametersValues/>
							<showCaption>true</showCaption>
						</embedded-application-content>
						<leftColumnWidth>60</leftColumnWidth>
					</layout>
					<internalScrolling>INHERITED</internalScrolling>
				</tab>
			</tabBar>
		</window>
	</ui>
	<custom-forms/>
	<advimport/>
	<script-modules/>
	<scripts/>
	<customJSElements/>
	<fast-link-settings/>
	<objects/>
	<transfer-values/>
</metainfoContainer>
