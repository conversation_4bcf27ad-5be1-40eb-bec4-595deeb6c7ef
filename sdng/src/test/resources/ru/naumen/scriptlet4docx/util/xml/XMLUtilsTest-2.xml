</w:t>
</w:r>
<w:proofErr w:type="spellStart"/>
<w:r w:rsidR="00164122">
	<w:rPr>
		<w:lang w:val="en-US"/>
	</w:rPr>
	<w:t>out.pr</w:t>
</w:r>
<w:bookmarkStart w:id="0" w:name="_GoBack"/>
<w:bookmarkEnd w:id="0"/>
<w:r w:rsidR="00164122">
	<w:rPr>
		<w:lang w:val="en-US"/>
	</w:rPr>
	<w:t>int</w:t>
</w:r>
<w:proofErr w:type="spellEnd"/>
<w:r w:rsidR="00164122">
	<w:rPr>
		<w:lang w:val="en-US"/>
	</w:rPr>
	<w:t>(</w:t>
</w:r>
<w:r w:rsidR="00164122" w:rsidRPr="00164122">
	<w:rPr>
		<w:lang w:val="en-US"/>
	</w:rPr>
	<w:t>'</w:t>
</w:r>
<w:r w:rsidR="00164122" w:rsidRPr="00164122">
	<w:rPr>
		<w:lang w:val="en-US"/>
	</w:rPr>
	<w:t>like dogs</w:t>
</w:r>
<w:r w:rsidR="00164122" w:rsidRPr="00164122">
	<w:rPr>
		<w:lang w:val="en-US"/>
	</w:rPr>
	<w:t>'</w:t>
</w:r>
<w:r w:rsidR="00164122">
	<w:rPr>
		<w:lang w:val="en-US"/>
	</w:rPr>
	<w:t>); 