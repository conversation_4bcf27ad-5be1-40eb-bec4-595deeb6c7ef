<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<w:document xmlns:ve="http://schemas.openxmlformats.org/markup-compatibility/2006" xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" xmlns:m="http://schemas.openxmlformats.org/officeDocument/2006/math" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:wp="http://schemas.openxmlformats.org/drawingml/2006/wordprocessingDrawing" xmlns:w10="urn:schemas-microsoft-com:office:word" xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main" xmlns:wne="http://schemas.microsoft.com/office/word/2006/wordml"><w:body><w:p><w:r><w:t>This is a scriplet4docx example document.</w:t></w:r></w:p><w:p><w:pPr><w:outlineLvl w:val="0"/></w:pPr><w:r><w:t xml:space="preserve">Current date is: ${new </w:t></w:r><w:proofErr w:type="gramStart"/><w:r><w:t>Date(</w:t></w:r><w:proofErr w:type="gramEnd"/><w:r><w:t>)}</w:t></w:r></w:p><w:p><w:r><w:t>Try to output some provided value: &lt;%= contract</w:t></w:r><w:proofErr w:type="gramStart"/><w:r><w:t>?.number</w:t></w:r><w:proofErr w:type="gramEnd"/><w:r><w:t xml:space="preserve"> %&gt;</w:t></w:r></w:p><w:p><w:r><w:t>Try to use some more advanced scripting: &lt;% if (contract</w:t></w:r><w:proofErr w:type="gramStart"/><w:r><w:t>?.number</w:t></w:r><w:proofErr w:type="gramEnd"/><w:r><w:t xml:space="preserve"> == 1) { %&gt; this is the first contract  &lt;% } else { %&gt; this is contract number ${contract?.</w:t></w:r><w:proofErr w:type="gramStart"/><w:r><w:t>number }&lt;</w:t></w:r><w:proofErr w:type="gramEnd"/><w:r><w:t>% } %&gt;</w:t></w:r></w:p><w:p><w:r><w:t>Try to output all person collection data:</w:t></w:r></w:p><w:tbl><w:tblPr><w:tblStyle w:val="a3"/><w:tblW w:w="0" w:type="auto"/><w:tblLook w:val="04A0"/></w:tblPr><w:tblGrid><w:gridCol w:w="4788"/><w:gridCol w:w="4788"/></w:tblGrid><w:tr><w:tc><w:tcPr><w:tcW w:w="4788" w:type="dxa"/></w:tcPr><w:p><w:r><w:t>$[@employee.name]</w:t></w:r></w:p></w:tc><w:tc><w:tcPr><w:tcW w:w="4788" w:type="dxa"/></w:tcPr><w:p><w:r><w:t>$[@</w:t></w:r><w:proofErr w:type="spellStart"/><w:r><w:t>employee.address</w:t></w:r><w:proofErr w:type="spellEnd"/><w:r><w:t>]</w:t></w:r></w:p></w:tc></w:tr></w:tbl><w:p/><w:p><w:pPr><w:outlineLvl w:val="0"/></w:pPr><w:r><w:t>Try to output some escaped data: ${escapeTest</w:t></w:r><w:bookmarkStart w:id="0" w:name="_GoBack"/><w:bookmarkEnd w:id="0"/><w:r><w:t>}</w:t></w:r></w:p><w:p/><w:p/><w:sectPr><w:pgSz w:w="12240" w:h="15840"/><w:pgMar w:top="1440" w:right="1440" w:bottom="1440" w:left="1440" w:header="720" w:footer="720" w:gutter="0"/><w:cols w:space="720"/><w:docGrid w:linePitch="360"/></w:sectPr></w:body></w:document>