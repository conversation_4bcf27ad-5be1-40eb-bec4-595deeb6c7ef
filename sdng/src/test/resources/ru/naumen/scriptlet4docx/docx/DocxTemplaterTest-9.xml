<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<w:document xmlns:wpc="http://schemas.microsoft.com/office/word/2010/wordprocessingCanvas" xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" xmlns:m="http://schemas.openxmlformats.org/officeDocument/2006/math" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:wp14="http://schemas.microsoft.com/office/word/2010/wordprocessingDrawing" xmlns:wp="http://schemas.openxmlformats.org/drawingml/2006/wordprocessingDrawing" xmlns:w10="urn:schemas-microsoft-com:office:word" xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main" xmlns:w14="http://schemas.microsoft.com/office/word/2010/wordml" xmlns:wpg="http://schemas.microsoft.com/office/word/2010/wordprocessingGroup" xmlns:wpi="http://schemas.microsoft.com/office/word/2010/wordprocessingInk" xmlns:wne="http://schemas.microsoft.com/office/word/2006/wordml" xmlns:wps="http://schemas.microsoft.com/office/word/2010/wordprocessingShape" mc:Ignorable="w14 wp14">
	<w:body>
		<w:p w:rsidR="00851669" w:rsidRPr="00851669" w:rsidRDefault="00851669">
			<w:pPr>
				<w:rPr>
					<w:lang w:val="en-US"/>
				</w:rPr>
			</w:pPr>
			<w:r>
				<w:t xml:space="preserve">mom and dad</w:t>
			</w:r>
			<w:r w:rsidRPr="00851669">
				<w:t xml:space="preserve">&lt;%</w:t>
			</w:r>
			<w:r>
				<w:rPr>
					<w:lang w:val="en-US"/>
				</w:rPr>
				<w:t>if</w:t>
			</w:r>
			<w:r w:rsidRPr="00851669">
				<w:t xml:space="preserve">(</w:t>
			</w:r>
			<w:r>
				<w:rPr>
					<w:lang w:val="en-US"/>
				</w:rPr>
				<w:t>value</w:t>
			</w:r>
			<w:r w:rsidRPr="00851669">
				<w:t xml:space="preserve">== 1) { %&gt;</w:t>
			</w:r>
			<w:r>
				<w:t>like kitties</w:t>
			</w:r>
			<w:r w:rsidRPr="00851669">
				<w:t xml:space="preserve">&lt;% }</w:t>
			</w:r>
			<w:r>
				<w:rPr>
					<w:lang w:val="en-US"/>
				</w:rPr>
				<w:t>else</w:t>
			</w:r>
			<w:r w:rsidRPr="00851669">
				<w:t xml:space="preserve">{ %&gt;</w:t>
			</w:r>
			<w:r>
				<w:t>like dogs</w:t>
			</w:r>
			<w:r>
				<w:rPr>
					<w:lang w:val="en-US"/>
				</w:rPr>
				<w:t xml:space="preserve">&lt;% } %&gt;</w:t>
			</w:r>
			<w:bookmarkStart w:id="0" w:name="_GoBack"/>
			<w:bookmarkEnd w:id="0"/>
		</w:p>
		<w:sectPr w:rsidR="00851669" w:rsidRPr="00851669">
			<w:pgSz w:w="11906" w:h="16838"/>
			<w:pgMar w:top="1134" w:right="850" w:bottom="1134" w:left="1701" w:header="708" w:footer="708" w:gutter="0"/>
			<w:cols w:space="708"/>
			<w:docGrid w:linePitch="360"/>
		</w:sectPr>
	</w:body>
</w:document>