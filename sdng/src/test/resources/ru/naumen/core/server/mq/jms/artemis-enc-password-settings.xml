<?xml version="1.0" encoding="UTF-8"?>
<n:configuration xmlns:n="http://www.naumen.ru/jmsSettings" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.naumen.ru/jmsSettings jmsSettings.xsd ">
  <jmsEnabled>true</jmsEnabled>       <!-- Признак использования скриптового модуля -->
  <connections>
    <connection>
       <code>localMQ</code>        <!-- Код настройки подключения -->
       <type>ARTEMIS</type>         <!-- Тип подключения принимает значения: (IBM_MQ, ARTEMIS) -->
       <module>jmsMQ</module>      <!-- Код скриптового модуля -->
       <login>app</login>
       <encPassword>8kHTn6E1B22K4fa1yZT5wg==</encPassword>
       <host>127.0.0.1</host> 
       <port>1414</port> 
      
       <queues>
          <queue> <!-- Пример настройки для записи и чтения в очередь--> 
              <name>QUEUE</name>     <!-- Имя очереди --> 
              <listen>true</listen>           <!-- Включает обработку сообщений из очереди -->
              <readOnly>false</readOnly>      <!-- Признак режима "только чтение" --> 
	            <messageListenerConfigurator>listenerConfigurator</messageListenerConfigurator>
              <messageHandler>processMsg</messageHandler> <!--метод-обработчик сообщений -->
          </queue>
        </queues>
      </connection>
    </connections>
</n:configuration>
