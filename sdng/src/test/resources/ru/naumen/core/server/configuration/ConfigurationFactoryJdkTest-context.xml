<?xml version="1.0" encoding="UTF-8"?>

<!--$Id$ -->
<beans
    xmlns="http://www.springframework.org/schema/beans"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns:aop="http://www.springframework.org/schema/aop"
    xmlns:context="http://www.springframework.org/schema/context"
    xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
        http://www.springframework.org/schema/context
        http://www.springframework.org/schema/context/spring-context-3.0.xsd">

    <context:annotation-config />
    
    <import resource="classpath:/ru/naumen/core/server/configuration/spring-beans.xml"/>

    <bean
        id="configuration"
        class="ru.naumen.core.server.configuration.ConfigurationFactory">
        <property
            name="configurationClass"
            value="ru.naumen.core.server.configuration.ConfigurationFactoryJdkTest.Configuration" />
        <property
            name="configurationFile"
            value="test.properties" />
        <property
            name="manager"
            ref="configurationManager" />
    </bean>

    <bean
        id="bootstrapProperties"
        class="ru.naumen.core.server.configuration.FxBootstrapProperties">
        <property
            name="configurationPath"
            value="classpath:/ru/naumen/core/server/configuration/" />
    </bean>

</beans>