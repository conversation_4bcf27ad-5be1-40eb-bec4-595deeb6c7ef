<?xml version="1.0" encoding="UTF-8"?>
<!--$Id$ -->
<beans
        xmlns="http://www.springframework.org/schema/beans"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns:context="http://www.springframework.org/schema/context"
        xsi:schemaLocation="http://www.springframework.org/schema/beans
	 	http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
	 	http://www.springframework.org/schema/context
        http://www.springframework.org/schema/context/spring-context-3.0.xsd">


    <import
            resource="classpath:/test-application-data.xml"/>

    <context:annotation-config/>

    <bean id="infinispanCacheManager" class="ru.naumen.core.server.InfinispanCacheManager"/>

    <bean
            id="sessionFactory"
            class="org.springframework.orm.hibernate5.LocalSessionFactoryBean">
        <property
                name="dataSource"
                ref="dataSource"/>
        <property
                name="annotatedClasses">
            <list>
                <value>ru.naumen.core.server.SimpleEntity</value>
            </list>
        </property>
        <property
                name="hibernateProperties">
            <value>
                hibernate.dialect=${hibernate.dialect}
                hibernate.show_sql=${hibernate.show_sql}
                hibernate.format_sql=${hibernate.format_sql}
                hibernate.use_sql_comments=${hibernate.use_sql_comments}
                hibernate.connection.charSet=utf8
                hibernate.id.new_generator_mappings=${hibernate.id.new_generator_mappings}
                hibernate.transaction.jta.platform=ru.naumen.core.server.HibernateJtaPlatform
                hibernate.current_session_context_class=org.springframework.orm.hibernate5.SpringJtaSessionContext
                hibernate.column_ordering_strategy=org.hibernate.boot.model.relational.ColumnOrderingStrategyLegacy
                hibernate.transaction.coordinator_class=org.hibernate.resource.transaction.backend.jta.internal.JtaTransactionCoordinatorBuilderImpl
                hibernate.cache.infinispan.cfg=infinispan-hibernate.xml
                hibernate.cache.region.factory_class=${hibernate.cache.region.factory_class}
                jakarta.persistence.sharedCache.mode=${jakarta.persistence.sharedCache.mode}
                hibernate.cache.default_cache_concurrency_strategy=${hibernate.cache.default_cache_concurrency_strategy}
                hibernate.cache.use_second_level_cache=${hibernate.cache.use_second_level_cache}
                jakarta.persistence.schema-generation.database.action=update
            </value>
        </property>
    </bean>
    <bean name="clusterInfoService" class="ru.naumen.core.server.cluster.external.ClusterInfoService.ClusterInfoServiceStub"/>
    <bean name="reportDbPasswordProvider" class="ru.naumen.reports.server.TestPasswordEncryptedValueProvider"/>
    <bean name="dbPasswordProvider" class="ru.naumen.reports.server.TestPasswordEncryptedValueProvider"/>
    <bean name="dataBaseInfo" class="ru.naumen.core.server.hibernate.DataBaseInfo"/>
    <bean name="oracleDDLConfiguration" class="ru.naumen.core.server.hibernate.OracleDDLConfiguration"/>
    <bean id="noOpServices" class="ru.naumen.core.server.cluster.ClusterServicesConfiguration.NoOpServices"/>
    <bean id="clusterServiceManager" class="ru.naumen.core.server.cluster.external.ClusterServiceManager">
        <constructor-arg index="0" ref="noOpServices"/>
    </bean>
</beans>