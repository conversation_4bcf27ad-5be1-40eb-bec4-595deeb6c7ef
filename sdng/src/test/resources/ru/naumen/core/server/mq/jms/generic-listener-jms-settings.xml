<?xml version="1.0" encoding="UTF-8"?>
<n:configuration xmlns:n="http://www.naumen.ru/jmsSettings" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.naumen.ru/jmsSettings jmsSettings.xsd ">
    <jmsEnabled>true</jmsEnabled>       <!-- Общий флаг включения механизма -->
    <connections>
        <connection>
            <code>simpleArtemis</code>        <!-- Код настройки подключения -->
            <type>ARTEMIS</type>         <!-- Тип подключения принимает значения: (IBM_MQ, ARTEMIS) -->
            <module>simpleScriptModule</module>      <!-- Код скриптового модуля -->
            <!-- Не обязательные параметры
            <connectionFactoryConfigurator>connectionFactoryConfigurator</connectionFactoryConfigurator>
            -->
            <host>127.0.0.1</host> <!-- Адрес сервера очередей -->
            <port>5445</port>           <!-- Код подключения -->
            
            <login>guest</login>
            <password>guest</password>
            
            <queues>
                <queue>
                    <name>read_queue</name>
                    <listen>true</listen>
                    <readOnly>true</readOnly>
                    <genericListener>true</genericListener>
                    <messageHandler>processMessage</messageHandler>
                </queue>
            </queues>
        </connection>
    </connections>
</n:configuration>