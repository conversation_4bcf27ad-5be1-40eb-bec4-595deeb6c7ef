<configuration xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="urn:activemq"
    xsi:schemaLocation="urn:activemq /schema/artemis-server.xsd">

    <core xmlns="urn:activemq:core">
   		
   		<!-- Максм "объем" памаяти для всех очередей -->
        <global-max-size>2621440</global-max-size>
   		
        <!-- Other config -->
        <security-enabled>false</security-enabled>

        <address-settings>
    
            <!-- применяется к очереди планируемых событий PEForSubjectCreateMessage -->
            <address-setting match="jms.queue.Queue.PlannedEvent.PEForSubjectCreateMessage">
            <!-- Ожидание перед повторной отправкой (первая попытка) сообщения = 5s -->
                <redelivery-delay>5000</redelivery-delay>
            <!-- Увеличение интервала ожидания до следующей попытки(во сколько раз) -->
                <redelivery-delay-multiplier>5.0</redelivery-delay-multiplier>
            <!-- Максимальное количество попыток отправки -->
                <max-delivery-attempts>5</max-delivery-attempts>
            </address-setting>     
    
            <!--применяется ко всем очередям (wildcard #) -->
            <address-setting match="#">

                <redelivery-delay>5000</redelivery-delay>
                <max-delivery-attempts>1</max-delivery-attempts>  
         		
         		 <!-- размер "страницы" в байтах при пэйджинге. Должно быть на порядок мешье max-size-bytes/global-max-size-->
                <page-size-bytes>262144</page-size-bytes>
         		
         		<!-- для экономии IO артемис кеширует страницы в памяти. в данном случае в кеше всего 1 страница из  262144 байт-->
                <page-max-cache-size>1</page-max-cache-size>
         		
                <!-- Размер после которого включается пйджинг = 1 Mb -->
                <max-size-bytes>1310720</max-size-bytes>       
         
                 <!-- PAGE, DROP or BLOCK
                  Что мы должны делать с сообщением больше max-size-bytes? 
                  PAGE - использовать пейджинг
                  DROP - удалять 
                  BLOCK - блокировать клиент при попытке записи такого сообщения -->
                <address-full-policy>PAGE</address-full-policy>

            </address-setting>
			<address-setting match="Queue.CustomTestQueue">
				<max-delivery-attempts>5</max-delivery-attempts>
				<dead-letter-address>Queue.CustomTestDLQ</dead-letter-address>
			</address-setting>
        </address-settings>
       <addresses>
       		<address name="Queue.CustomTestQueue">
       			<anycast>
       				<queue name="Queue.CustomTestQueue"/>
       			</anycast>
       		</address>
       		<address name="Queue.CustomTestDLQ">
       			<anycast>
       				<queue name="Queue.CustomTestDLQ"/>
       			</anycast>
       		</address>
       </addresses>
        <connection-ttl-override>-1</connection-ttl-override>
    </core>
</configuration>