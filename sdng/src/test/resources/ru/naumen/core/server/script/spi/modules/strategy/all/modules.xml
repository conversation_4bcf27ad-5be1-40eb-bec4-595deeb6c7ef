<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<metainfoContainer>
	<head>
		<date>2021-02-01T16:22:38.092+05:00</date>
		<exportMode>partial</exportMode>
		<version>4.12.6-SNAPSHOT</version>
	</head>
	<tags/>
	<mail-processor-rules/>
	<style-templates/>
	<list-templates/>
	<user-events/>
	<event-actions/>
	<embedded-applications/>
	<custom-forms/>
	<advimport/>
	<script-modules>
		<module seg-detach="true" seg-id="TestModuleTypes" seg-type="script-modules">
			<code>TestModuleTypes</code>
			<description/>
			<active>false</active>
			<script checksum="a2ea4df8ad3e410fac476540b65ffab942dec5de72bc96d44c014977ef748dfc"><![CDATA[package ru.naumen.modules
import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonTypeInfo
import com.fasterxml.jackson.databind.DatabindContext
import com.fasterxml.jackson.databind.JavaType
import com.fasterxml.jackson.databind.annotation.JsonTypeIdResolver
import com.fasterxml.jackson.databind.jsontype.impl.TypeIdResolverBase
import com.fasterxml.jackson.databind.type.TypeFactory
import groovy.transform.Canonical
import groovy.transform.CompileStatic
import groovy.transform.TypeChecked
import ru.naumen.modules.BaseStrategy
import ru.naumen.modules.DefaultServiceIsNewStrategy
import ru.naumen.jsonschema.annotations.*

import jakarta.annotation.Nullable


enum Schema
{
    Corp,
    Unique
}


@Canonical
@JsonSchemaMeta(requiredFields = ['strategyType'])
class StrategySettings<T extends BaseStrategy>
{
    @JsonSchemaMeta(title = 'Стратегия')
    T strategyType
    @UiSchemaMeta(widget = 'textarea')
    @JsonSchemaMeta(title = 'Текст скрипта', description = 'Применяется только при стратегии "Скрипт"')
    String strategyScript = ''

    def <FT> FT  getAt(Integer index)
    {
        switch (index)
        {
            case 0:
                return (FT) strategyType
            case 1:
                return (FT) strategyScript
            default:
                return null
        }
    }
}


@Canonical
@JsonSchemaMeta(title = 'Объекты для выбора на первом шаге', additional = false)
class ServiceSettings
{

    @UiSchemaMeta(disabled = true)
    final String portalFqn = "PortalFqns.SERVICE"
    @UiSchemaMeta(hidden = 'Corp')
    @JsonSchemaMeta(title = 'Стратегия, определяющая считается ли объект "Новым"', description = '"Новые" объекты отмечаются в интерфейсе специальным флажком')
    StrategySettings<DefaultServiceIsNewStrategy> serviceIsNewStrategy = new StrategySettings<>(
            DefaultServiceIsNewStrategy.NOT_USED
    )
    @UiSchemaMeta(widget = 'status-select', paramsPath = '../fqn')
    @JsonSchemaMeta(title = 'Статусы объектов, доступных для поиска')
    List<String> statesForSearch = []
}]]></script>
			<view_by_superusers>false</view_by_superusers>
			<edit_by_superusers>false</edit_by_superusers>
			<author>naumen</author>
		</module>
		<module seg-detach="true" seg-id="TestUseModule" seg-type="script-modules">
			<code>TestUseModule</code>
			<description/>
			<active>false</active>
			<script checksum="7085a0dd6a34e822e0f8a1f6af07be97900e702c5fa1f33939d407cfc19ec594"><![CDATA[package ru.naumen.modules

import ru.naumen.core.server.filestorage.FileStorageHelper
import ru.naumen.core.server.script.ScriptService
import ru.naumen.core.server.script.api.criteria.IApiCriteria
import ru.naumen.core.server.script.api.injection.InjectApi
import ru.naumen.core.shared.IUUIDIdentifiable
import ru.naumen.core.shared.SecConstants
import ru.naumen.jsonschema.annotations.UiTitle
import ru.naumen.metainfo.server.MetainfoService
import ru.naumen.metainfo.shared.ClassFqn
import ru.naumen.metainfo.shared.IClassFqn
import ru.naumen.metainfo.shared.script.Script
import ru.naumen.modules.Schema


import jakarta.annotation.Nullable


//Автор: mnagovitsin
//Дата создания: 15.02.2019
//Код:
//Назначение:
/**
 * Реализация сервиса по работе со стратегиями
 */
//Версия: >=*********

/**
 * Базовая стратегия
 *
 * Для использования методов, полей этого модуля внутри стратегий (Closure strategy)
 * необходимо проставить им delegate = this перед непосредственным вызовом
 */
trait BaseStrategy
{
    Closure strategy
    Collection<String> parameters = ['user']

    static BaseStrategy byName(String name)
    {
        def result = name ? values().find { value ->
            value.name().toUpperCase() == name.trim().toUpperCase()
        } : null
        return result
    }

    static BaseStrategy bySchema(Schema schema)
    {
        switch (schema)
        {
            case Schema.Unique:
                return this.values().find { it.name() == 'ITSM_365' }
            case Schema.Corp:
                return this.values().find { it.name() == 'CORP' }
            default:
                return this.values().find { it.name() == 'DEFAULT' }
        }
    }
}

/**
 * Стратегии получения информации о новизне услуги
 *
 * @param user - пользователь, инициировавший вызов метода
 * @param service - услуга
 * @return true - новая, false - старая
 */
enum DefaultServiceIsNewStrategy implements BaseStrategy
{
    @UiTitle(title = 'Скрипт')
    SCRIPT(null),
    @UiTitle(title = 'Не использовать')
    NOT_USED({ IUUIDIdentifiable user, service ->
        return false
    })

    DefaultServiceIsNewStrategy(String strategy)
    {
        //this.strategy = strategy
        this.parameters = ['user', 'subject']
    }
}]]></script>
			<view_by_superusers>false</view_by_superusers>
			<edit_by_superusers>false</edit_by_superusers>
			<author>naumen</author>
		</module>
	</script-modules>
	<scripts/>
	<customJSElements/>
	<fast-link-settings/>
	<objects/>
	<transfer-values/>
	<structured-objects-views/>
	<libraries/>
</metainfoContainer>
