<?xml version="1.0" encoding="UTF-8"?>
<!--$Id$ -->
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	   xmlns:tx="http://www.springframework.org/schema/tx" xmlns:context="http://www.springframework.org/schema/context"
	   xmlns:task="http://www.springframework.org/schema/task" xmlns="http://www.springframework.org/schema/beans"
	   xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
    					http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.0.xsd
    					http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd
    					http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task.xsd">


	<context:component-scan base-package="ru.naumen.core.server.jta,ru.naumen.core.server.transaction"/>
	<tx:annotation-driven transaction-manager="txManager"/>
	<task:annotation-driven/>


	<import resource="classpath:/ru/naumen/core/server/configuration/bootstrap-spring-beans.xml"/>
	<import resource="classpath:/ru/naumen/core/server/configuration/spring-beans.xml"/>

	<bean id="springContext" class="ru.naumen.core.server.SpringContext"/>
	<bean id="infinispanCacheManager" class="ru.naumen.core.server.InfinispanCacheManager"/>
	<bean id="dataBaseInfo" class="ru.naumen.core.server.hibernate.DataBaseInfo"/>
	<bean id="oracleDDLConfiguration" class="ru.naumen.core.server.hibernate.OracleDDLConfiguration"/>
	<bean id="NauHibernateDialectHelper" class="ru.naumen.core.server.hibernate.dialect.NauHibernateDialectHelper"/>

	<bean id="sessionFactory" class="ru.naumen.core.server.flex.spi.SessionFactoryBean">
		<property name="dataSource" ref="dataSource"/>
		<property name="annotatedClasses">
			<list>
				<value>ru.naumen.core.server.SimpleEntity</value>
			</list>
		</property>
		<property name="dataBaseInfo" ref="dataBaseInfo"/>
		<property name="hibernateProperties">
			<value>
				hibernate.dialect=${hibernate.dialect}
				hibernate.show_sql=${hibernate.show_sql}
				hibernate.format_sql=${hibernate.format_sql}
				hibernate.use_sql_comments=${hibernate.use_sql_comments}
				hibernate.connection.charSet=utf8
				jakarta.persistence.schema-generation.database.action=update
				hibernate.id.new_generator_mappings=${hibernate.id.new_generator_mappings}
				hibernate.transaction.jta.platform=ru.naumen.core.server.HibernateJtaPlatform
				hibernate.implicit_naming_strategy=ru.naumen.core.server.flex.spi.NauImplicitNamingStrategy
				hibernate.transaction.coordinator_class=org.hibernate.resource.transaction.backend.jta.internal.JtaTransactionCoordinatorBuilderImpl
				hibernate.cache.infinispan.cfg=infinispan-hibernate.xml
				hibernate.cache.region.factory_class=${hibernate.cache.region.factory_class}
				jakarta.persistence.sharedCache.mode=${jakarta.persistence.sharedCache.mode}
				hibernate.cache.default_cache_concurrency_strategy=${hibernate.cache.default_cache_concurrency_strategy}
				hibernate.cache.use_second_level_cache=${hibernate.cache.use_second_level_cache}
				hibernate.current_session_context_class=org.springframework.orm.hibernate5.SpringJtaSessionContext
				hibernate.column_ordering_strategy=org.hibernate.boot.model.relational.ColumnOrderingStrategyLegacy
				hibernate.jdbc.batch.builder=${hibernate.jdbc.batch.builder}
			</value>
		</property>
	</bean>

	<bean class="ru.naumen.core.server.SpringPropertyPlaceholderConfigurer" name="propertyConfigurer">
		<property name="ignoreResourceNotFound" value="true"/>
		<property name="locations">
			<list>
				<value>classpath:/async-default.properties</value>
				<value>classpath:/auth-default.properties</value>
				<value>classpath:/dbaccess-default.properties</value>
				<value>classpath:/integration-default.properties</value>
				<value>classpath:/cache-default.properties</value>
				<value>classpath:/cluster-default.properties</value>
				<value>classpath:/jms-default.properties</value>
				<value>classpath:/log-default.properties</value>
				<value>classpath:/mail-default.properties</value>
				<value>classpath:/mobile-default.properties</value>
				<value>classpath:/search-default.properties</value>
				<value>classpath:/settings-default.properties</value>
				<value>classpath:/smia-default.properties</value>
				<value>classpath:/ndap-default.properties</value>
				<value>classpath:/omnichannel-default.properties</value>
				<value>classpath:/report-default.properties</value>
				<value>classpath:/planned-versions-default.properties</value>
				<value>classpath:/websocket-default.properties</value>
				<value>classpath:/common-test.properties</value>
				<value>classpath:/ui2-default.properties</value>
				<value>classpath:/dbaccess.properties</value>
				<value>file://${dbaccess}</value>
			</list>
		</property>
	</bean>

	<bean id="uRestMappings" class="org.springframework.beans.factory.config.PropertiesFactoryBean">
		<property name="ignoreResourceNotFound" value="true"/>
		<property name="locations">
			<list>
				<value>classpath:/urestmappings-default.properties</value>
				<value>classpath:/urestmappings.properties</value>
			</list>
		</property>
	</bean>


	<bean class="ru.naumen.core.server.CommonConfiguration"/>
	<bean class="ru.naumen.core.server.TransactionalConfiguration"/>
	<bean id="ddlConfiguration" class="ru.naumen.core.server.hibernate.DDLConfiguration"/>
	<bean name="clusterInfoService"
		  class="ru.naumen.core.server.cluster.external.ClusterInfoService.ClusterInfoServiceStub"/>
	<bean id="dbPasswordProvider" class="ru.naumen.sec.server.encryption.DbPasswordProvider"/>
	<bean class="ru.naumen.sec.server.encryption.EncryptionServiceBean"/>
	<bean class="ru.naumen.core.server.SQLServerReadCommittedEnabler"/>
	<bean class="ru.naumen.core.server.hibernate.ConnectionPermissionCheckerStub"/>
	<bean id="forbiddenIndexRegistry" class="ru.naumen.core.server.flex.spi.ForbiddenIndexRegistry"/>
	<bean name="reportDbPasswordProvider" class="ru.naumen.sec.server.encryption.ReportDbPasswordProvider"/>
	<bean name="eventsDbPasswordProvider" class="ru.naumen.sec.server.encryption.EventsDbPasswordProvider"/>
	<bean name="clusterJDBCDbPasswordProvider" class="ru.naumen.sec.server.encryption.ClusterJDBCDbPasswordProvider"/>
	<bean class=" ru.naumen.sec.server.session.CurrentSessionProviderImpl"/>
	<bean class="ru.naumen.core.server.actioncontext.ActionContextHolderImpl"/>
	<bean class="ru.naumen.core.server.util.BraveAdapter"/>
	<bean class="ru.naumen.core.server.ClusterJDBCDbProvider"/>
	<bean id="noOpServices" class="ru.naumen.core.server.cluster.ClusterServicesConfiguration.NoOpServices"/>
	<bean id="clusterServiceManager" class="ru.naumen.core.server.cluster.external.ClusterServiceManager">
		<constructor-arg index="0" ref="noOpServices"/>
	</bean>
	<bean name="schedulerProperties" class="ru.naumen.core.server.scheduler.SchedulerProperties"/>
	<bean id="configurationProperties" class="ru.naumen.core.server.ConfigurationProperties"/>
	<bean id="schemaUpdateScriptService" class="ru.naumen.core.server.flex.spi.SchemaUpdateScriptService">
		<constructor-arg name="configurationProperties" ref="configurationProperties"/>
		<constructor-arg name="dataSource" ref="dataSource"/>
		<constructor-arg name="dataBaseInfo" ref="dataBaseInfo"/>
		<constructor-arg name="forbiddenIndexRegistry" ref="forbiddenIndexRegistry"/>
		<constructor-arg name="ddlDialect" ref="ddlDialect"/>
		<constructor-arg name="transactionManager" ref="jtaTxManager"/>
	</bean>
</beans>
