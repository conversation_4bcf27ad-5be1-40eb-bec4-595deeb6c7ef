<?xml version="1.0" encoding="UTF-8"?>
<!--$Id$ -->
<beans
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns:context="http://www.springframework.org/schema/context"
        xmlns="http://www.springframework.org/schema/beans"
        xsi:schemaLocation="http://www.springframework.org/schema/beans
	 	http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
	 	http://www.springframework.org/schema/context
        http://www.springframework.org/schema/context/spring-context-3.0.xsd">

    <context:component-scan base-package="ru.naumen.core.server.jta,ru.naumen.core.server.transaction"/>
    <context:annotation-config/>
    <import
            resource="classpath:/test-application-data.xml"/>

    <bean id="springContext" class="ru.naumen.core.server.SpringContext"/>
    <bean id="infinispanCacheManager" class="ru.naumen.core.server.InfinispanCacheManager"/>
    <bean id="NauHibernateDialectHelper" class="ru.naumen.core.server.hibernate.dialect.NauHibernateDialectHelper"/>

    <bean
            id="sessionFactory"
            class="ru.naumen.core.server.flex.spi.SessionFactoryBean" depends-on="springContext">

        <property
                name="dataSource"
                ref="dataSource"/>
        <property
                name="annotatedClasses">
            <list>
                <value>ru.naumen.core.server.SimpleEntity</value>
            </list>
        </property>
        <property name="dataBaseInfo" ref="dataBaseInfo"/>
        <property
                name="hibernateProperties">
            <value>
                hibernate.dialect=${hibernate.dialect}
                hibernate.show_sql=${hibernate.show_sql}
                hibernate.format_sql=${hibernate.format_sql}
                hibernate.use_sql_comments=${hibernate.use_sql_comments}
                jakarta.persistence.schema-generation.database.action=update
                hibernate.connection.charSet=utf8
                hibernate.id.new_generator_mappings=${hibernate.id.new_generator_mappings}
                hibernate.transaction.jta.platform=ru.naumen.core.server.HibernateJtaPlatform
                hibernate.implicit_naming_strategy=ru.naumen.core.server.flex.spi.NauImplicitNamingStrategy
                hibernate.transaction.coordinator_class=org.hibernate.resource.transaction.backend.jta.internal.JtaTransactionCoordinatorBuilderImpl
                hibernate.cache.infinispan.cfg=infinispan-hibernate.xml
                hibernate.cache.region.factory_class=${hibernate.cache.region.factory_class}
                jakarta.persistence.sharedCache.mode=${jakarta.persistence.sharedCache.mode}
                hibernate.cache.default_cache_concurrency_strategy=${hibernate.cache.default_cache_concurrency_strategy}
                hibernate.cache.use_second_level_cache=${hibernate.cache.use_second_level_cache}
                hibernate.current_session_context_class=org.springframework.orm.hibernate5.SpringJtaSessionContext
                hibernate.column_ordering_strategy=org.hibernate.boot.model.relational.ColumnOrderingStrategyLegacy
                hibernate.jdbc.batch.builder=${hibernate.jdbc.batch.builder}
            </value>
        </property>
    </bean>

    <bean id="metastorageSessionFactory" class="ru.naumen.core.server.flex.spi.SessionFactoryBean">
        <property name="dataSource" ref="dataSource"/>
        <property name="annotatedClasses">
            <list>
                <value>ru.naumen.core.server.SimpleEntity</value>
            </list>
        </property>
        <property name="dataBaseInfo" ref="dataBaseInfo"/>
        <property name="hibernateProperties">
            <value>
                hibernate.dialect=${hibernate.dialect}
                hibernate.show_sql=${hibernate.show_sql}
                hibernate.format_sql=${hibernate.format_sql}
                hibernate.use_sql_comments=${hibernate.use_sql_comments}
                hibernate.connection.charSet=utf8
                jakarta.persistence.schema-generation.database.action=update
                hibernate.id.new_generator_mappings=${hibernate.id.new_generator_mappings}
                hibernate.transaction.jta.platform=ru.naumen.core.server.HibernateJtaPlatform
                hibernate.transaction.coordinator_class=org.hibernate.resource.transaction.backend.jta.internal.JtaTransactionCoordinatorBuilderImpl
                hibernate.implicit_naming_strategy=ru.naumen.core.server.flex.spi.NauImplicitNamingStrategy
                hibernate.cache.infinispan.cfg=infinispan-hibernate.xml
                hibernate.cache.region.factory_class=${hibernate.cache.region.factory_class}
                jakarta.persistence.sharedCache.mode=${jakarta.persistence.sharedCache.mode}
                hibernate.cache.default_cache_concurrency_strategy=${hibernate.cache.default_cache_concurrency_strategy}
                hibernate.cache.use_second_level_cache=${hibernate.cache.use_second_level_cache}
                hibernate.current_session_context_class=org.springframework.orm.hibernate5.SpringJtaSessionContext
                hibernate.column_ordering_strategy=org.hibernate.boot.model.relational.ColumnOrderingStrategyLegacy
                hibernate.jdbc.batch.builder=${hibernate.jdbc.batch.builder}
            </value>
        </property>
    </bean>

    <bean id="dbPasswordProvider" class="ru.naumen.sec.server.encryption.DbPasswordProvider"/>
    <bean name="clusterInfoService"
          class="ru.naumen.core.server.cluster.external.ClusterInfoService.ClusterInfoServiceStub"/>
    <bean class="ru.naumen.sec.server.encryption.EncryptionServiceBean"/>
    <bean name="reportDbPasswordProvider" class="ru.naumen.sec.server.encryption.ReportDbPasswordProvider"/>
    <bean name="eventsDbPasswordProvider" class="ru.naumen.sec.server.encryption.EventsDbPasswordProvider"/>
    <bean name="clusterJDBCDbPasswordProvider" class="ru.naumen.sec.server.encryption.ClusterJDBCDbPasswordProvider"/>
    <bean class="ru.naumen.sec.server.session.CurrentSessionProviderImpl"/>
    <bean class="ru.naumen.core.server.SQLServerReadCommittedEnabler"/>
    <bean name="dataBaseInfo" class="ru.naumen.core.server.hibernate.DataBaseInfo"/>
    <bean name="oracleDDLConfiguration" class="ru.naumen.core.server.hibernate.OracleDDLConfiguration"/>
    <bean class="ru.naumen.core.server.ClusterJDBCDbProvider"/>
    <bean id="noOpServices" class="ru.naumen.core.server.cluster.ClusterServicesConfiguration.NoOpServices"/>
    <bean id="clusterServiceManager" class="ru.naumen.core.server.cluster.external.ClusterServiceManager">
        <constructor-arg index="0" ref="noOpServices"/>
    </bean>
    <bean name="schedulerProperties" class="ru.naumen.core.server.scheduler.SchedulerProperties"/>
    <bean id="configurationProperties" class="ru.naumen.core.server.ConfigurationProperties"/>
    <bean id="schemaUpdateScriptService" class="ru.naumen.core.server.flex.spi.SchemaUpdateScriptService">
        <constructor-arg name="configurationProperties" ref="configurationProperties"/>
        <constructor-arg name="dataSource" ref="dataSource"/>
        <constructor-arg name="dataBaseInfo" ref="dataBaseInfo"/>
        <constructor-arg name="forbiddenIndexRegistry" ref="forbiddenIndexRegistry"/>
        <constructor-arg name="ddlDialect" ref="ddlDialect"/>
        <constructor-arg name="transactionManager" ref="jtaTxManager"/>
    </bean>
</beans>
