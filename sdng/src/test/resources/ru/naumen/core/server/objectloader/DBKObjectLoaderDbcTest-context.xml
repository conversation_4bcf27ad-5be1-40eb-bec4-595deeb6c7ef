<?xml version="1.0" encoding="UTF-8"?>
<!--$Id$ -->
<beans
        xmlns="http://www.springframework.org/schema/beans"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns:context="http://www.springframework.org/schema/context"
        xsi:schemaLocation="http://www.springframework.org/schema/beans
	 	http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
	 	http://www.springframework.org/schema/context
        http://www.springframework.org/schema/context/spring-context-3.0.xsd">

    <context:annotation-config/>

    <import
            resource="classpath:/test-application-data.xml"/>

    <bean id="infinispanCacheManager" class="ru.naumen.core.server.InfinispanCacheManager"/>
    <bean id="NauHibernateDialectHelper" class="ru.naumen.core.server.hibernate.dialect.NauHibernateDialectHelper"/>
    <bean id="dataBaseInfo" class="ru.naumen.core.server.hibernate.DataBaseInfo"/>
    <bean id="oracleDDLConfiguration" class="ru.naumen.core.server.hibernate.OracleDDLConfiguration"/>
    <bean
            id="sessionFactory"
            class="ru.naumen.core.server.flex.spi.SessionFactoryBean">
        <property
                name="dataSource"
                ref="dataSource"/>
        <property
                name="annotatedClasses">
            <list>
                <value>ru.naumen.core.server.SimpleEntity</value>
            </list>
        </property>
        <property name="dataBaseInfo" ref="dataBaseInfo"/>
        <property
                name="hibernateProperties">
            <value>
                hibernate.dialect=${hibernate.dialect}
                hibernate.show_sql=${hibernate.show_sql}
                hibernate.format_sql=${hibernate.format_sql}
                hibernate.use_sql_comments=${hibernate.use_sql_comments}
                hibernate.connection.charSet=utf8
                jakarta.persistence.schema-generation.database.action=update
                hibernate.id.new_generator_mappings=${hibernate.id.new_generator_mappings}
                hibernate.transaction.jta.platform=ru.naumen.core.server.HibernateJtaPlatform
                hibernate.current_session_context_class=org.springframework.orm.hibernate5.SpringJtaSessionContext
                hibernate.column_ordering_strategy=org.hibernate.boot.model.relational.ColumnOrderingStrategyLegacy
                hibernate.transaction.coordinator_class=org.hibernate.resource.transaction.backend.jta.internal.JtaTransactionCoordinatorBuilderImpl
                hibernate.implicit_naming_strategy=ru.naumen.core.server.flex.spi.NauImplicitNamingStrategy
                hibernate.cache.region.factory_class=${hibernate.cache.region.factory_class}
                jakarta.persistence.sharedCache.mode=${jakarta.persistence.sharedCache.mode}
                hibernate.cache.default_cache_concurrency_strategy=${hibernate.cache.default_cache_concurrency_strategy}
                hibernate.cache.use_second_level_cache=${hibernate.cache.use_second_level_cache}
                hibernate.jdbc.batch.builder=${hibernate.jdbc.batch.builder}
            </value>
        </property>
    </bean>

    <bean id="configurationProperties" class="ru.naumen.core.server.ConfigurationProperties"/>
    <bean id="schemaUpdateScriptService" class="ru.naumen.core.server.flex.spi.SchemaUpdateScriptService">
        <constructor-arg name="configurationProperties" ref="configurationProperties"/>
        <constructor-arg name="dataSource" ref="dataSource"/>
        <constructor-arg name="dataBaseInfo" ref="dataBaseInfo"/>
        <constructor-arg name="forbiddenIndexRegistry" ref="forbiddenIndexRegistry"/>
        <constructor-arg name="ddlDialect" ref="ddlDialect"/>
        <constructor-arg name="transactionManager" ref="jtaTxManager"/>
    </bean>

    <bean name="clusterInfoService"
          class="ru.naumen.core.server.cluster.external.ClusterInfoService.ClusterInfoServiceStub"/>
    <bean class="ru.naumen.core.server.ClusterJDBCDbProvider"/>

    <bean id="eventsSessionFactory"
          parent="sessionFactory">
    </bean>
    <bean name="dBKDBKObjectLoaderDefaultStrategy"
          class="ru.naumen.core.server.objectloader.DBKDBKObjectLoaderDefaultStrategy"/>
    <bean name="dBKObjectLoaderStrategyFactory"
          class="ru.naumen.core.server.objectloader.DBKObjectLoaderStrategyFactoryImpl"/>
    <bean name="simpleEntityLoader" class="ru.naumen.core.server.objectloader.DBKObjectLoader">
        <property name="sessionFactory" ref="sessionFactory"/>
        <property name="eventsSessionFactory" ref="eventsSessionFactory"/>
        <property name="clazz" value="ru.naumen.core.server.SimpleEntity"/>
        <property name="loaderStrategyFactory" ref="dBKObjectLoaderStrategyFactory"/>
    </bean>
    <bean name="reportDbPasswordProvider" class="ru.naumen.reports.server.TestPasswordEncryptedValueProvider"/>
    <bean name="dbPasswordProvider" class="ru.naumen.reports.server.TestPasswordEncryptedValueProvider"/>
    <bean name="clusterJDBCDbPasswordProvider" class="ru.naumen.reports.server.TestPasswordEncryptedValueProvider"/>
    <bean name="schedulerProperties" class="ru.naumen.core.server.scheduler.SchedulerProperties"/>
</beans>