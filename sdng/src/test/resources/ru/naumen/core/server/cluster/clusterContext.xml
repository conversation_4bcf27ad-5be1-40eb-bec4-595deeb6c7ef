<?xml version="1.0" encoding="UTF-8"?>
<!--$Id$ -->
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:util="http://www.springframework.org/schema/util"
	   xmlns="http://www.springframework.org/schema/beans"
	   xsi:schemaLocation="http://www.springframework.org/schema/beans
	 	http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
        http://www.springframework.org/schema/util
        http://www.springframework.org/schema/util/spring-util.xsd">

	<bean class="ru.naumen.core.server.SpringPropertyPlaceholderConfigurer" name="propertyConfigurer">
		<property name="ignoreResourceNotFound" value="true"/>
		<property name="ignoreUnresolvablePlaceholders" value="true"/>
		<property name="locations">
			<list>
				<value>classpath:/async-default.properties</value>
				<value>classpath:/auth-default.properties</value>
				<value>classpath:/dbaccess-default.properties</value>
				<value>classpath:/integration-default.properties</value>
				<value>classpath:/cache-default.properties</value>
				<value>classpath:/cluster-default.properties</value>
				<value>classpath:/jms-default.properties</value>
				<value>classpath:/log-default.properties</value>
				<value>classpath:/mail-default.properties</value>
				<value>classpath:/mobile-default.properties</value>
				<value>classpath:/search-default.properties</value>
				<value>classpath:/settings-default.properties</value>
				<value>classpath:/smia-default.properties</value>
				<value>classpath:/ndap-default.properties</value>
				<value>classpath:/omnichannel-default.properties</value>
				<value>classpath:/report-default.properties</value>
				<value>classpath:/planned-versions-default.properties</value>
				<value>classpath:/websocket-default.properties</value>
				<value>classpath:/dbaccess.properties</value>
				<value>classpath:/ui2-default.properties</value>
				<value>classpath:/cluster-invalidator.properties</value>
				<value>file://${dbaccess}</value>
			</list>
		</property>
	</bean>

	<bean id="uRestMappings" class="org.springframework.beans.factory.config.PropertiesFactoryBean">
		<property name="ignoreResourceNotFound" value="true"/>
		<property name="locations">
			<list>
				<value>classpath:/urestmappings-default.properties</value>
				<value>classpath:/urestmappings.properties</value>
			</list>
		</property>
	</bean>

	<bean class="org.springframework.beans.factory.config.CustomScopeConfigurer">
		<property name="scopes">
			<map>
				<entry key="application">
					<bean class="org.springframework.context.support.SimpleThreadScope"/>
				</entry>
				<entry key="session">
					<bean class="org.springframework.web.context.request.SessionScope"/>
				</entry>
				<entry key="request">
					<bean class="org.springframework.web.context.request.RequestScope"/>
				</entry>
			</map>
		</property>
	</bean>

	<bean class="org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter">
		<property name="messageConverters">
			<util:list id="beanList">
				<bean id="stringHttpMessageConverter"
					  class="org.springframework.http.converter.StringHttpMessageConverter"/>
				<bean id="formHttpMessageConverter"
					  class="org.springframework.http.converter.FormHttpMessageConverter"/>
				<bean id="byteArrayMessageConverter"
					  class="org.springframework.http.converter.ByteArrayHttpMessageConverter"/>
				<bean id="xmlFormHttpMessageConverter"
					  class="org.springframework.http.converter.support.AllEncompassingFormHttpMessageConverter"/>
				<bean id="jacksonMessageConverter"
					  class="org.springframework.http.converter.json.MappingJackson2HttpMessageConverter"/>
			</util:list>
		</property>
	</bean>

	<import resource="classpath:/ru/naumen/core/spring-beans.xml"/>
</beans>