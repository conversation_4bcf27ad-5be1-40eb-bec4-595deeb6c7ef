<?xml version="1.0" encoding="UTF-8"?>
<s:configuration xmlns:s="http://www.naumen.ru/fileStorageSettings" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.naumen.ru/fileStorageSettings fileStorageSettings.xsd ">
    <settings>
        <active>teststorage1</active>
    </settings>
    
    <storages>
        <storage>
            <code>teststorage1</code>
            <description>teststorage1</description>
            <path>${data.dir}/teststorage1</path>
            <compress>false</compress>
        </storage>
        <storage>
            <code>teststorage2</code>
            <description>teststorage2</description>
            <path>${data.dir}/teststorage2</path>
            <compress>true</compress>
            <type>disk</type>
        </storage>
    </storages>

</s:configuration>