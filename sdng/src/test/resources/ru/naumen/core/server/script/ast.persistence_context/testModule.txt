class Foo
{
    @ru.naumen.core.server.script.api.injection.InjectApi
    private static class Bar
    {
        private final String title
        private final String fqn

        Bar(final String title, final String fqn)
        {
            this.title = title;
            this.fqn = fqn
        }

        void create()
        {
            utils.create(fqn, [title: title])
        }
    }

    private final int iterations
    private final String fqn

    Foo(final int iterations, final String fqn)
    {
        this.iterations = iterations
        this.fqn = fqn
    }

    void forLoop()
    {
        for (int i = 0; i < iterations; ++i)
        {
            final Bar bar = new Bar('titleForLoop' + i, fqn)
            bar.create()
        }
    }

    void whileLoop()
    {
        int i = 0
        while (i < iterations)
        {
            ++i
            final Bar bar = new Bar('titleWhileLoop' + i, fqn)
            bar.create()
        }
    }

    void closure()
    {
        (1..iterations).each
            {
                final Bar bar = new Bar('closure' + it, fqn)
                bar.create()
            }
    }
}

void forLoop(String fqn, int iterations)
{
    final Foo foo = new Foo(iterations, fqn)
    foo.forLoop()
}

void whileLoop(String fqn, int iterations)
{
    final Foo foo = new Foo(iterations, fqn)
    foo.whileLoop()
}

void closure(String fqn, int iterations)
{
    final Foo foo = new Foo(iterations, fqn)
    foo.closure()
}