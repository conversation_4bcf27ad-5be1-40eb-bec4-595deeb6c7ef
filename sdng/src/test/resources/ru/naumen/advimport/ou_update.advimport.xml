<?xml version="1.0" encoding="UTF-8"?>
<config
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:noNamespaceSchemaLocation="../../../../../../target/generated-sources/jaxb/advimport/schema1.xsd"
	save-log="false">
	
	<mode>UPDATE</mode>
	
	<class name="ou" threads-number="1">
	
		<csv-data-source id-column="id" with-header="true" file-name="classpath:/ru/naumen/advimport/ou_update.test.csv">
			<column name="id" src-key="id"/>
			<column name="title" src-key="title"/>
			<column name="removed" src-key="removed"/>
			<column name="removalDate" src-key="removalDate"/>
		</csv-data-source>
		
		<constant-metaclass-resolver metaclass="ou$ouImportTest"/>
		<object-searcher attr="idHolder" metaclass="ou$ouImportTest"/>

		<attr name="title" column="title"  />
		<attr name="removed" column="removed" />
		<attr name="removalDate" column="removalDate" >
			<datetime-converter format="yyyy-MM-dd"/>
		</attr>
		<attr name="idHolder" column="id"  />
	</class>
</config>
