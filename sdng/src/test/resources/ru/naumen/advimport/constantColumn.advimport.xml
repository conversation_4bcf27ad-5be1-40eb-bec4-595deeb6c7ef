<?xml version="1.0" encoding="UTF-8"?>
<config
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:noNamespaceSchemaLocation="../../../../../../target/generated-sources/jaxb/advimport/schema1.xsd"
	save-log="false" >
	
	<mode>CREATE</mode>
	
	<class name="ou" threads-number="2">
	
		<parameter name="defTitle">default title</parameter>
	
		<csv-data-source with-header="true" file-name="classpath:/ru/naumen/advimport/constantColumn.test.csv">
			<column name="id" src-key="id"/>
			<column name="title" default-value="$defTitle"/>
		</csv-data-source>
		
		<constant-metaclass-resolver metaclass="ou$ouImportTest"/>
		<object-searcher attr="idHolder" metaclass="ou$ouImportTest"/>

		<attr name="idHolder" column="id"  />
		<attr name="title" column="title"  />
	</class>
</config>
