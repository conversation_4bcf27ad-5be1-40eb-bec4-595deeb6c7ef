<?xml version="1.0" encoding="UTF-8"?>
<config
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:noNamespaceSchemaLocation="../../../../../../target/classes/advimport/schema1.xsd"
	save-log="false" >
	
	<mode>CREATE</mode>
	
	<class name="ou" threads-number="1">
	
		<csv-data-source id-column="id" with-header="true" file-name="classpath:/ru/naumen/advimport/boolean.test.csv">
			<column name="id" src-key="id"/>
			<column name="removed" src-key="removed"/>
			<column name="title" src-key="title"/>
		</csv-data-source>
		
		<constant-metaclass-resolver metaclass="ou$ouImportTest"/>
		<object-searcher attr="idHolder" metaclass="ou$ouImportTest"/>

		<attr name="idHolder" column="id" />
		<attr name="title" column="title" />
		<attr name="removed" column="removed">
			<boolean-converter true-value="good" />
		</attr>
	</class>
</config>
