<?xml version="1.0" encoding="UTF-8"?>
<config
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="../../../../../../target/generated-sources/jaxb/advimport/schema1.xsd"
    save-log="true">
    
    <mode>CREATE</mode>
    <mode>UPDATE</mode>
    
    <class name="ou" threads-number="2">
        <csv-data-source id-column="idHolder" with-header="true" file-name="classpath:/ru/naumen/advimport/ou_immutable_metaclass.test.csv" delimiter=";" encoding="UTF8">
            <column name="title" src-key="title"/>
            <column name="removed" src-key="removed"/>
            <column name="removalDate" src-key="removalDate"/>
            <column name="idHolder" src-key="id"/>
            <column name="author" src-key="author"/>
        </csv-data-source>
        <constant-metaclass-resolver metaclass="ou$ouImportTest"/>
        <object-searcher attr="idHolder" metaclass="ou$ouImportTest"/>
        <attr name="title" column="title" />
        <attr name="idHolder" column="idHolder" />
        <attr name="metaClass">
            <script-converter>
                <![CDATA[return api.types.newClassFqn('ou$ouImportTest');]]>
            </script-converter>
        </attr>
    </class>
</config>