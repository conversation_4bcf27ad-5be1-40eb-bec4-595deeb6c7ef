<?xml version="1.0" encoding="UTF-8"?>
<config
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:noNamespaceSchemaLocation="../../../../../../target/classes/advimport/schema1.xsd"
	save-log="false" >
	
	<class name="importOU" threads-number="1">
 		<!-- Задаем режим импорта на создание новых объектов -->
		<mode>CREATE</mode>
		<!-- &&& -->
 		<csv-data-source id-column="id" with-header="false" file-name="classpath:/ru/naumen/advimport/ou_withEmployeeOU.test.csv">
			<column name="id" src-key="0"/>
			<column name="title" src-key="1"/>
			<column name="parent" src-key="2" />
		</csv-data-source>
		
		<!-- &&& -->
		<hierarchical-filter parent-column="parent"/>
		
		<!-- &&& -->
		<constant-metaclass-resolver metaclass="ou$ouImportTest"/>
		<object-searcher attr="idHolder" metaclass="ou$ouImportTest"/>
		
		<!-- &&& -->
		<attr name="idHolder" column="id" />
		<attr name="title" column="title" />
		<attr name="parent" column="parent">
			<object-converter attr="idHolder" metaclass="ou$ouImportTest"/>
		</attr>
 	</class>
 	<!-- Задаем режим импорта на создание новых объектов -->
 	<class name="importEmployee" threads-number="1">
 	<mode>CREATE</mode>
 		<csv-data-source id-column="id" with-header="true" file-name="classpath:/ru/naumen/advimport/ou_withEmployeeEmpl.test.csv">
			<column name="id" src-key="id"/>
			<column name="parent" src-key="parent"/>
			<column name="lastName" src-key="lastName"/>
			<column name="firstName" src-key="firstName"/>
			<column name="middleName" src-key="middleName"/>
		</csv-data-source>

		<constant-metaclass-resolver metaclass="employee$forImport"/>
		<object-searcher attr="idHolder" metaclass="employee$forImport"/>

		<attr name="lastName" column="lastName" />
		<attr name="firstName" column="firstName" />
		<attr name="middleName" column="middleName" />
		<attr name="parent" column="parent" >
			<object-converter attr="idHolder" metaclass="ou$ouImportTest" required="true" />
		</attr>
		<attr name="idHolder" column="id" >
		</attr>
	</class>
	<!-- Задаем режим импорта на создание новых объектов -->
 	<class name="editImportOU" threads-number="1">
 		<!-- Задаем режим импорта на создание новых объектов -->
		<mode>UPDATE</mode>
		<!-- &&& -->
 		<csv-data-source id-column="id" with-header="false" file-name="classpath:/ru/naumen/advimport/ou_withEmployeeOU.test.csv">
			<column name="id" src-key="0"/>
			<column name="head" src-key="3" />
		</csv-data-source>

		<!-- &&& -->
		<constant-metaclass-resolver metaclass="ou$ouImportTest"/>
		<object-searcher attr="idHolder" metaclass="ou$ouImportTest"/>
		
		<!-- &&& -->
		<attr name="head" column="head">
			<object-converter attr="idHolder" metaclass="employee$forImport" required="true" />
		</attr>
	</class>
</config>
