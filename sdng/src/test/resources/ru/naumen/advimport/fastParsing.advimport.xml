<?xml version="1.0" encoding="UTF-8"?>
<config description="XMLImport"
    threads-number="1"
    save-log="true"
    skip-workflow="false"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="../../../target/classes/advimport/schema1.xsd">
    <class name="port" threads-number="1">
        <mode>CREATE</mode>
        <mode>UPDATE</mode>
        <xml-data-source xpath="/SPECTRUM_Export/Update/Device/Port" id-column="model_handle" file-name="classpath:/ru/naumen/advimport/fastParsing.test.xml" fast-parsing="true">
            <column name="model_handle" src-key="@model_handle" />
            <column name="createtime" src-key="@createtime" />
            <column name="port_desc" src-key="@PORT_DESC" />
            <column name="ifIndex" src-key="@ifIndex" />
            <column name="model_class" src-key="@model_class" />
            <column name="PortLinkStatus" src-key="@PortLinkStatus" />
            <column name="port_type" src-key="@PORT_TYPE" />
            <column name="ifName" src-key="@ifName" />
            <column name="deviceSpectr" src-key="../@model_handle" />
        </xml-data-source>
        <constant-metaclass-resolver metaclass="ou$ouImportTest3"/>
        <script-object-searcher>
            <![CDATA[
                return utils.findFirst('ou$ouImportTest3', ['idHolder' : item.properties.model_handle]);
            ]]>
        </script-object-searcher>
        <attr name="idHolder" column="model_handle"/>
        <attr name="title" column="ifName" default-value="Отдел"/>
    </class>
</config>
