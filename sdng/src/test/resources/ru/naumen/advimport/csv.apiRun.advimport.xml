<?xml version="1.0" encoding="UTF-8"?>
<config save-log="true" threads-number="4" xsi:noNamespaceSchemaLocation="schema1.xsd"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

    <mode>CREATE</mode>

    <gui-parameter name="fileForProcess" type="FILE" title="fileTitle" />
    <gui-parameter name="defaultValue" type="STRING" title="defaultValue" />

    <parameter name="metaClass">ou$ouImportTest</parameter>

    <class name="ImportAT" threads-number="2">
        <csv-data-source file-name="${fileForProcess}" delimiter=";" with-header="true" encoding="UTF-8" id-column="title">
            <column name="title" src-key="title" />
            <column name="id" src-key="id" />
        </csv-data-source>

        <constant-metaclass-resolver metaclass="${metaClass}" />
        <object-searcher attr="title" metaclass="${metaClass}" />
        <attr name="title" column="title" default-value="${defaultValue}" />
        <attr name="idHolder" column="id" />
    </class>
</config>