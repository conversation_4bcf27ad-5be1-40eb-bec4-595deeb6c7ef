<?xml version="1.0" encoding="UTF-8"?>
<config
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:noNamespaceSchemaLocation="../../../../../../target/generated-sources/jaxb/advimport/schema1.xsd"
	save-log="false" >
	
	<mode>UPDATE</mode>
	
	<class name="ou" threads-number="2">
	
		<mode>CREATE</mode>
	
		<sql-data-source id-column="id" url="{test changed}" driver="{test changed}" user="{test changed}" password="{test changed}">
			<column name="id" src-key="1"/>
			<column name="title" src-key="2"/>

			<query>select id, title from tbl_date_import</query>
		</sql-data-source>
		
		<constant-metaclass-resolver metaclass="ou$ouImportTest"/>
		<object-searcher attr="title" metaclass="ou"/>

		<attr name="idHolder" column="id" />
		<attr name="title" column="title" />
	</class>
</config>
