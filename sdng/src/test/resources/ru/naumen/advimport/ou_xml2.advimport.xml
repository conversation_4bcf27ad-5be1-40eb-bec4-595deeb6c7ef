<?xml version="1.0" encoding="UTF-8"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../target/generated-sources/jaxb/advimport/schema1.xsd"
    save-log="false">

    <mode>CREATE</mode>

    <class name="ou" threads-number="1">

        <xml-data-source file-name="classpath:/ru/naumen/advimport/ou_xml2.test.xml" xpath="/Items/OU">
            <column name="id" src-key="./@id" />
            <column name="links" src-key=".//Links/@id" />
        </xml-data-source>

        <constant-metaclass-resolver metaclass="ou$ouImportTest" />
        <object-searcher attr="idHolder" metaclass="ou$ouImportTest" />

        <attr name="title" column="id" />
        <attr name="idHolder" column="id" />
        <attr name="ouLinks" column="links">
            <collection-converter>
                <object-converter attr="idHolder" metaclass="ou$ouImportTest" />
            </collection-converter>
        </attr>
    </class>
</config>
