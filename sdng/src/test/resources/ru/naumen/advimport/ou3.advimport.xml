<?xml version="1.0" encoding="UTF-8"?>
<config
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:noNamespaceSchemaLocation="../../../../../../target/generated-sources/jaxb/advimport/schema1.xsd"
	save-log="false" >
	
	<mode>CREATE</mode>
	
	<class name="ou" threads-number="1">
	
		<csv-data-source id-column="id" with-header="true" file-name="classpath:/ru/naumen/advimport/ou3.test.csv">
			<column name="id" src-key="id"/>
			<column name="parent" src-key="parent"/>
			<column name="title" src-key="title"/>
		</csv-data-source>
		
		<constant-metaclass-resolver metaclass="ou$ouImportTest"/>
		<object-searcher attr="idHolder" metaclass="ou$ouImportTest"/>

		<attr name="idHolder" column="id" />
		<attr name="title" column="title" />
		<attr name="parent" column="parent">
			<complex-object-converter>
				<object-converter attr="idHolder" metaclass="ou$ouImportTest" required="false"/>
				<object-converter attr="title" metaclass="ou" required="false"/>
			</complex-object-converter>
		</attr>
	</class>
</config>
