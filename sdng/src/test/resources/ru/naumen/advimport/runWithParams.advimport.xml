<?xml version="1.0" encoding="UTF-8"?>
<config
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:noNamespaceSchemaLocation="../../../../../../target/classes/advimport/schema1.xsd"
	save-log="true" >
	
	<mode>CREATE</mode>
	
	<gui-parameter name="file" type="FILE" title="file for import"/>
	<gui-parameter name="defTitle" type="STRING" title="def title"/>
	
	<class name="ou" threads-number="1">
	
		<csv-data-source with-header="true" file-name="$file">
			<column name="title" src-key="title" default-value="$defTitle"/>
		</csv-data-source>
		
		<constant-metaclass-resolver metaclass="ou$ouImportTest"/>

		<attr name="title" column="title"  />
	</class>
</config>
