<?xml version="1.0" encoding="UTF-8"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		xsi:noNamespaceSchemaLocation="../../../../../../target/classes/advimport/schema1.xsd"
		save-log="false">

	<mode>CREATE</mode>

	<parameter name="rootDN">ou=OlgaPivovarova,dc=dc2,dc=local</parameter>

	<class name="ou" threads-number="1">

		<parameter name="metaClass">ou$ouImportTest</parameter>

		<ldap-data-source id-column="id" check-user-disabled="false"
						  user="administrator@dc2" passwd="Ytyfdbcnm!" url="ldap://localhost"
						  full-domain="true" import-root="true" domain="dc2.local">

			<column name="id" src-key="objectGUID"/>
			<column name="parent" src-key="parent"/>
			<column name="name" src-key="name"/>

			<root-element>${rootDN}</root-element>
			<import-tag>ou</import-tag>
			<import-tag>dc</import-tag>
		</ldap-data-source>

		<constant-metaclass-resolver/>
		<object-searcher attr="idHolder"/>

		<attr name="idHolder" column="id"/>
		<attr name="title" column="name"/>
		<attr name="parent" column="parent">
			<object-converter attr="idHolder" required="false"/>
		</attr>
	</class>
</config>
