<?xml version="1.0" encoding="UTF-8"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:noNamespaceSchemaLocation="../../../../../../target/classes/advimport/schema1.xsd"
	save-log="false" >

	<mode>CREATE</mode>

	<parameter name="rootDN">ou=OlgaPivovarova,dc=dc2,dc=local</parameter>

	<class name="ou" threads-number="2">

		<parameter name="metaClass">ou$ouImportTest</parameter>

		<ldap-data-source id-column="id" check-user-disabled="false"
			user="administrator@dc2" passwd="Ytyfdbcnm!" url="ldap://192.168.240.26"
			full-domain="true" import-root="true" domain="dc2.local">

            <column name="id" src-key="objectGUID" />
            <column name="parent" src-key="parent" />
            <column name="name" src-key="name" />

            <root-element>${rootDN}</root-element>
            <import-tag>ou</import-tag>
            <import-tag>dc</import-tag>
		</ldap-data-source>

		<constant-metaclass-resolver />
		<object-searcher attr="idHolder" />

		<attr name="idHolder" column="id" />
		<attr name="title" column="name" />
		<attr name="parent" column="parent">
			<object-converter attr="idHolder" required="false"/>
		</attr>
	</class>

	<class threads-number="1" name="employee">

		<parameter name="metaClass">employee$forImport</parameter>

		<ldap-data-source id-column="id" check-user-disabled="false"
			user="administrator@dc2" passwd="Ytyfdbcnm!" url="ldap://192.168.240.26"
			full-domain="true" import-root="true" domain="dc2.local">

			<column name="id" src-key="objectGUID" />
			<column name="parent" src-key="parent" />
			<column name="lastName" src-key="sn" />
			<column name="firstName" src-key="givenName" />
			<column name="middleName" src-key="initials" />
			<column name="email" src-key="mail" />
			<column name="userAccountControl" src-key="userAccountControl" />
			<column name="login" src-key="login" />
			<column name="mobile" src-key="mobile" />
			<column name="homePhone" src-key="homePhone" />
			<column name="ipPhone" src-key="ipPhone" />
			<column name="telephoneNumber" src-key="telephoneNumber" />
			<column name="agreement_column_name" src-key="ldap_agreement_attribute_name" />

            <root-element>${rootDN}</root-element>
            <import-tag>cn</import-tag>
		</ldap-data-source>

		<constant-metaclass-resolver />
		<object-searcher attr="idHolder" />

		<attr name="idHolder" column="id" />
		<attr name="parent" column="parent">
			<object-converter attr="idHolder" metaclass="ou$ouImportTest" />
		</attr>
		<attr name="lastName" column="lastName" />
		<attr name="firstName" column="firstName" />
		<attr name="middleName" column="middleName" />
		<attr name="email" column="email" />
		<attr name="mobilePhoneNumber" column="mobile" />
		<attr name="internalPhoneNumber" column="ipPhone" />
		<attr name="homePhoneNumber" column="homePhone" />
		<attr name="cityPhoneNumber" column="telephoneNumber" />
	</class>
</config>
