<?xml version="1.0" encoding="UTF-8"?>
<config
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:noNamespaceSchemaLocation="../../../../../../target/generated-sources/jaxb/advimport/schema1.xsd"
	save-log="false" >
	
	<mode>CREATE</mode>
	
	<class name="ou" threads-number="2">
	
		<csv-data-source with-header="true" file-name="classpath:/ru/naumen/advimport/ou_script.test.csv">
			<column name="title" src-key="title"/>
		</csv-data-source>
		
		<constant-metaclass-resolver metaclass="ou$ouImportTest"/>
		<object-searcher attr="title" metaclass="ou"/>

		<attr name="title" column="title"  >
			<script-converter mime-type="application/x-groovy">value + " (script changed)"</script-converter>
		</attr>
	</class>
</config>
