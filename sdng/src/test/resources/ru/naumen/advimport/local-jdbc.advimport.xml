<?xml version="1.0" encoding="UTF-8"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		xsi:noNamespaceSchemaLocation="../../../../../../target/classes/advimport/schema1.xsd"
		save-log="false">

	<mode>CREATE</mode>
	<class name="ou" threads-number="1">

		<sql-data-source id-column="id" url="***************************************" driver="org.postgresql.Driver"
						 user="test" password="test">
			<column name="id" src-key="1"/>
			<column name="title" src-key="2"/>

			<query>select id, title from tbl_date_import</query>
		</sql-data-source>
		<constant-metaclass-resolver/>
		<object-searcher attr="idHolder"/>

		<attr name="idHolder" column="id"/>
		<attr name="title" column="name"/>
		<attr name="parent" column="parent">
			<object-converter attr="idHolder" required="false"/>
		</attr>
	</class>
</config>
