<?xml version="1.0" encoding="UTF-8"?>
<config
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:noNamespaceSchemaLocation="../../../../../../target/classes/advimport/schema1.xsd"
	save-log="false" >
	
 	<!-- Задаем режим импорта на создание новых объектов -->
 	<class name="importEmployee" threads-number="1">
 	<mode>CREATE</mode>
 		<csv-data-source id-column="id" with-header="true" file-name="classpath:/ru/naumen/advimport/employee_remove.test.csv">
			<column name="id" src-key="id"/>
			<column name="parent" src-key="parent"/>
			<column name="lastName" src-key="lastName"/>
			<column name="firstName" src-key="firstName"/>
			<column name="middleName" src-key="middleName"/>
		</csv-data-source>

		<constant-metaclass-resolver metaclass="employee$forImport"/>
		<object-searcher attr="idHolder" metaclass="employee$forImport"/>

		<attr name="lastName" column="lastName" />
		<attr name="firstName" column="firstName" />
		<attr name="middleName" column="middleName" />
		<attr name="parent" column="parent" >
			<object-converter attr="idHolder" metaclass="ou$ouImportTest" required="true" />
		</attr>
		<attr name="idHolder" column="id" >
		</attr>
		<remove-customizer hierarchy-root="{changet in test}" metaclass="employee$forImport" attr="idHolder" />
	</class>
</config>
