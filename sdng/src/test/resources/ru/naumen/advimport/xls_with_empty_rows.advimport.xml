<?xml version="1.0" encoding="UTF-8"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../target/classes/advimport/schema1.xsd" save-log="true" threads-number="1" skip-workflow="true">

    <mode>UPDATE</mode>
  
    <parameter name="metaClass">ou$ouImportTest</parameter>

	<class name="obj" threads-number="1">
        <xls-data-source file-name="classpath:/ru/naumen/advimport/xls_with_empty_rows.xls" sheet-number="1" start-row="1" id-column="id">
            <column name="id" src-key="0" />
		</xls-data-source>

        <constant-metaclass-resolver />
		<object-searcher attr="str" metaclass="${metaClass}" />
		<attr name="title" column="id" />	
	</class>
</config>
