<?xml version="1.0" encoding="UTF-8"?>
<config
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:noNamespaceSchemaLocation="../../../../../../target/generated-sources/jaxb/advimport/schema1.xsd"
	save-log="false" >
	
	<mode>CREATE</mode>
	
	<class name="ou" threads-number="1">
	
		<parameter name="prefix">importPrefix:</parameter>
	
		<csv-data-source id-column="id" with-header="true" file-name="classpath:/ru/naumen/advimport/idPrefix.test.csv">
			<column name="id" src-key="id"/>
			<column name="parent" src-key="parent"/>
			<column name="title" src-key="title"/>
		</csv-data-source>
		
		<hierarchical-filter parent-column="parent"/>
		<id-prefix prefix="$prefix"/>
		
		<constant-metaclass-resolver metaclass="ou$ouImportTest"/>
		<object-searcher attr="idHolder" metaclass="ou$ouImportTest"/>

		<attr name="idHolder" column="id"  />
		<attr name="parent" column="parent"  >
			<object-converter attr="idHolder" metaclass="ou$ouImportTest" required="false" />
		</attr>
		<attr name="title" column="title"  />
	</class>
</config>
