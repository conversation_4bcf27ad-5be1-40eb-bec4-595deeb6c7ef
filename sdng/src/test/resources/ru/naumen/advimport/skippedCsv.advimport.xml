<?xml version="1.0" encoding="UTF-8"?>
<config save-log="true" threads-number="4" xsi:noNamespaceSchemaLocation="schema1.xsd"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

    <mode>CREATE</mode>
    <mode>UPDATE</mode>
  
    <parameter name="metaClass">ou$ouImportTest</parameter>

    <class name="ou" threads-number="1">
         <mode>UPDATE</mode>
         <mode>EMPTY</mode>

         <csv-data-source file-name="classpath:/ru/naumen/advimport/skippedCsv.test.csv" delimiter=";" with-header="true" encoding="UTF-8" id-column="title">
            <column name="title" src-key="title" />
            <column name="stringAttr" src-key="advString" />
        </csv-data-source>
      
      <script-filter><![CDATA[
          return item.properties.stringAttr ? true : false ;
      ]]></script-filter>

        <column-notempty-filter column="title"/>
        <constant-metaclass-resolver metaclass="${metaClass}" />
        <object-searcher attr="title" metaclass="${metaClass}" />

        <attr name="title" column="title">
             <script-converter><![CDATA[
                ctx.getLogger().info("attr title '${value}'");
                item.properties.title = value + ' attr';
                return value;
            ]]></script-converter>
        </attr>

        <script-customizer>
            <before-process-item><![CDATA[
                item.properties.title = item.properties.title + ' before-process-item';
            ]]></before-process-item>
            <after-process><![CDATA[
                ctx.getLogger().info("after-process title '${item.properties.title}'");
             ]]></after-process>
            <after-import><![CDATA[
                ctx.getLogger().info("execute after-import");
             ]]></after-import>
        </script-customizer>
    </class>
</config>
