<?xml version="1.0" encoding="UTF-8"?>
<!-- author d<PERSON><PERSON><PERSON> -->
<!-- since 29.08.2012 -->
<config
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:noNamespaceSchemaLocation="../../../../../../target/classes/advimport/schema1.xsd"
	save-log="false" >
	
	<mode>CREATE</mode>
    <mode>UPDATE</mode>
	
	<class name="ou" threads-number="1">
	
		<csv-data-source id-column="id" with-header="true" file-name="classpath:/ru/naumen/advimport/dtInterval.test.csv">
			<column name="id" src-key="id"/>
			<column name="interval" src-key="interval"/>
			<column name="title" src-key="title"/>
		</csv-data-source>
		
		<constant-metaclass-resolver metaclass="ou$ouImportTest"/>
		<object-searcher attr="idHolder" metaclass="ou$ouImportTest"/>

		<attr name="idHolder" column="id" />
		<attr name="title" column="title" />
		<attr name="interval12" column="interval">
			<time-interval-converter interval="SECOND" />
		</attr>
	</class>
</config>