<?xml version="1.0" encoding="UTF-8"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../classes/advimport/schema1.xsd"
    save-log="false">

    <mode>CREATE</mode>

    <class name="ou" threads-number="1">

        <csv-data-source with-header="true" file-name="classpath:/ru/naumen/advimport/ou2.test.csv">
            <column name="id" src-key="id" />
            <column name="title" src-key="title" />
            <column name="links" src-key="links" />
        </csv-data-source>

        <constant-metaclass-resolver metaclass="ou$ouImportTest" />
        <object-searcher attr="idHolder" metaclass="ou$ouImportTest" />

        <attr name="title" column="title" />
        <attr name="idHolder" column="id" />
        <attr name="ouLinks" column="links">
            <collection-converter separator=",">
                <object-converter attr="idHolder" metaclass="ou$ouImportTest" />
            </collection-converter>
        </attr>
    </class>
</config>
