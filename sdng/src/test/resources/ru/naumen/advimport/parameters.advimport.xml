<?xml version="1.0" encoding="UTF-8"?>
<config
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:noNamespaceSchemaLocation="../../../../../../target/generated-sources/jaxb/advimport/schema1.xsd"
	save-log="false" >
	
	<mode>CREATE</mode>
	
	<parameter name="file">classpath:/ru/naumen/advimport/parameters.test.csv</parameter>
	<parameter name="metaClass">ou$ouImportTest</parameter>
	
	<class name="ou" threads-number="1">
	
		<parameter name="defTitle">defaultTitle</parameter>
		<parameter name="titleColumn">title</parameter>
		<parameter name="holderAttr">idHolder</parameter>
	
		<csv-data-source id-column="id" with-header="true" file-name="$file">
			<column name="id" src-key="id"/>
			<column name="parent" src-key="parent"/>
			<column name="title" src-key="$titleColumn" default-value="$defTitle"/>
		</csv-data-source>
		
		<constant-metaclass-resolver metaclass="$metaClass"/>
		<object-searcher attr="$holderAttr" metaclass="$metaClass"/>

		<attr name="idHolder" column="id" />
		<attr name="title" column="title" />
		<attr name="parent" column="parent">
			<complex-object-converter>
				<object-converter attr="$holderAttr" metaclass="$metaClass" required="false"/>
				<object-converter attr="title" metaclass="ou" required="false"/>
			</complex-object-converter>
		</attr>
	</class>
</config>
