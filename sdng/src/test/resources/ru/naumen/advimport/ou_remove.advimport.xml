<?xml version="1.0" encoding="UTF-8"?>
<config
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:noNamespaceSchemaLocation="../../../../../../target/classes/advimport/schema1.xsd"
	save-log="false">
	
	<mode>CREATE</mode>
	<mode>UPDATE</mode>
	
	<class name="ou" threads-number="2">
	
		<csv-data-source id-column="id" with-header="true" file-name="classpath:/ru/naumen/advimport/ou_remove.test.csv">
			<column name="id" src-key="id"/>
			<column name="title" src-key="title"/>
			<column name="parent" src-key="parent" default-value="importRoot"/>
		</csv-data-source>
		
		<hierarchical-filter parent-column="parent" root-id="importRoot"/>
		
		<constant-metaclass-resolver metaclass="ou$ouImportTest"/>
		<object-searcher attr="idHolder" metaclass="ou$ouImportTest" />

		<attr name="idHolder" column="id" />
		<attr name="title" column="title" />
		<attr name="parent" column="parent" >
			<object-converter attr="idHolder" metaclass="ou$ouImportTest" required="false"/>
		</attr>
		
		<remove-customizer hierarchy-root="{changet in test}" metaclass="ou$ouImportTest" attr="idHolder" />
	</class>
</config>
