<?xml version="1.0" encoding="UTF-8"?>
<config
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:noNamespaceSchemaLocation="../../../../../../target/classes/advimport/schema1.xsd"
	save-log="false" >
	
	<mode>CREATE</mode>
	
	<class name="ou" threads-number="1">
	
		<csv-data-source id-column="id" with-header="true" file-name="classpath:/ru/naumen/advimport/ouWithoutTitle.test.csv">
			<column name="id" src-key="id"/>
		</csv-data-source>
		
		<constant-metaclass-resolver metaclass="ou$ouImportTest"/>

		<attr name="title" default-value="" />
	</class>
</config>
