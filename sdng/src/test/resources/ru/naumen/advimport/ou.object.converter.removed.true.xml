<?xml version="1.0" encoding="UTF-8"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../target/generated-sources/jaxb/advimport/schema1.xsd"
    save-log="false" >

    <mode>CREATE</mode>

    <class name="ou" threads-number="1">

        <xml-data-source file-name="classpath:/ru/naumen/advimport/ou_object_converter.test.xml" xpath="/Items/OU">
            <column name="id" src-key="./@id" />
            <column name="parent" src-key="./Parent/text()" />
            <column name="removed" src-key="./Removed/@value" />
            <column name="removalDate" src-key="./Removed/RemovalDate/text()" />
        </xml-data-source>

        <constant-metaclass-resolver metaclass="ou$ouImportTest" />
        <object-searcher attr="idHolder" metaclass="ou$ouImportTest" />

        <attr name="title" column="id" />
        <attr name="idHolder" column="id" />
        <attr name="parent" column="parent">
            <object-converter attr="idHolder" metaclass="ou$ouImportTest" required="false" removed="true"/>
        </attr>
        <attr name="removed" column="removed">
            <boolean-converter />
        </attr>
        <attr name="removalDate" column="removalDate" />
    </class>
</config>
