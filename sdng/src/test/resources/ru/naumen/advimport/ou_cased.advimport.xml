<?xml version="1.0" encoding="UTF-8"?>
<config
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:noNamespaceSchemaLocation="../../../../../../target/generated-sources/jaxb/advimport/schema1.xsd"
	save-log="false">
	
	<mode>CREATE</mode>
	
	<class name="ou">
	
		<csv-data-source with-header="true" file-name="classpath:/ru/naumen/advimport/ou_cased.test.csv">
			<column name="title" src-key="title"/>
			<column name="case" src-key="case"/>
		</csv-data-source>
		
		<by-column-metaclass-resolver metaclass="ou" case-column="case" default-case="ouImportTest"/>
		<object-searcher attr="title" metaclass="ou"/>

		<attr name="title" column="title" />
	</class>
</config>
