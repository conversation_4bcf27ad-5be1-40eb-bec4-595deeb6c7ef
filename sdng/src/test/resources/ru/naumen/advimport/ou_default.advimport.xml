<?xml version="1.0" encoding="UTF-8"?>
<config
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:noNamespaceSchemaLocation="../../../../../../target/generated-sources/jaxb/advimport/schema1.xsd"
	save-log="false"
	threads-number="4" >
	
	<mode>CREATE</mode>
	<mode>UPDATE</mode>
	
	<class name="ou" threads-number="4">
		<csv-data-source id-column="id" with-header="true" file-name="classpath:/ru/naumen/advimport/ou_default_ou.test.csv" encoding="UTF8">
			<column name="id" src-key="id"/>
			<column name="parent" src-key="parent"/>
			<column name="name" src-key="name"/>
			<column name="ouhead" src-key="ouhead"/>
			<column name="profiles" src-key="profiles"/>
		</csv-data-source>
		
		<hierarchical-filter parent-column="parent"/>
		
		<constant-metaclass-resolver metaclass="ou$ouImportTest"/>
		<object-searcher attr="idHolder" metaclass="ou$ouImportTest"/>

		<attr name="title" column="name"/>
		<attr name="parent" column="parent">
			<object-converter attr="idHolder" metaclass="ou$ouImportTest" required="false"/>
		</attr>
		<attr name="idHolder" column="id" >
			<string-converter trim="true" />
		</attr>
	</class>
	
	<class name="employee" threads-number="4">
		<csv-data-source id-column="id" with-header="true" file-name="classpath:/ru/naumen/advimport/ou_default_employee.test.csv" encoding="UTF8">
			<column name="id" src-key="id"/>
			<column name="parent" src-key="parent"/>
			<column name="lastName" src-key="lastName"/>
			<column name="firstName" src-key="firstName"/>
			<column name="middleName" src-key="middleName"/>
			<column name="login" src-key="login"/>
			<column name="password" src-key="password"/>
			<column name="post" src-key="post"/>
			<column name="innerPhoneNumber" src-key="innerPhoneNumber"/>
			<column name="cityPhoneNumber" src-key="cityPhoneNumber"/>
			<column name="email" src-key="email"/>
			<column name="licenseType" src-key="licenseType"/>
		</csv-data-source>
		
		<constant-metaclass-resolver metaclass="employee$forImport"/>
		<object-searcher attr="idHolder" metaclass="employee$forImport"/>
		
		<attr name="lastName" column="lastName" />
		<attr name="firstName" column="firstName" />
		<attr name="middleName" column="middleName" />
		<attr name="post" column="post" />
		<attr name="parent" column="parent" >
			<object-converter attr="idHolder" metaclass="ou$ouImportTest" />
		</attr>
		<attr name="login" column="login" />
		<attr name="password" column="password" />
		<attr name="internalPhoneNumber" column="innerPhoneNumber" />
		<attr name="cityPhoneNumber" column="cityPhoneNumber" />
		<attr name="email" column="email" />
		<attr name="idHolder" column="id" >
			<string-converter trim="true" />
		</attr>
	</class>
</config>
