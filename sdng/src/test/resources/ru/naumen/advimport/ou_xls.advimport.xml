<?xml version="1.0" encoding="UTF-8"?>
<config
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:noNamespaceSchemaLocation="../../../../../../target/generated-sources/jaxb/advimport/schema1.xsd"
	save-log="false" >
	
	<mode>CREATE</mode>
	
	<class name="ou" threads-number="2">
	
		<xls-data-source file-name="classpath:/ru/naumen/advimport/ou_xls.test.xls" start-row="1">
			<column name="id" src-key="0"/>
			<column name="title" src-key="1"/>
			<column name="removed" src-key="2" default-value="false"/>
			<column name="removalDate" src-key="3"/>
		</xls-data-source>
		
		<constant-metaclass-resolver metaclass="ou$ouImportTest"/>
		<object-searcher attr="idHolder" metaclass="ou$ouImportTest"/>

		<attr name="idHolder" column="id"  />
		<attr name="title" column="title"  />
		<attr name="removed" column="removed" />
		<attr name="removalDate" column="removalDate" />
	</class>
</config>
