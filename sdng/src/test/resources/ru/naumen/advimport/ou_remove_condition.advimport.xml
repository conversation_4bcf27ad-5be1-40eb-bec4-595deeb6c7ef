<?xml version="1.0" encoding="UTF-8"?>
<config
		xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		xsi:noNamespaceSchemaLocation="../../../../../../target/classes/advimport/schema1.xsd"
		save-log="false">

	<mode>CREATE</mode>
	<mode>UPDATE</mode>

	<parameter name="metaClass">ou$ouImportTest</parameter>

	<class name="ou">
		<csv-data-source id-column="id" with-header="true"
						 file-name="classpath:/ru/naumen/advimport/ou_remove_skipObject.test.csv">
			<column name="id" src-key="id"/>
			<column name="title" src-key="title"/>
			<column name="parent" src-key="parent" default-value="importRoot"/>
		</csv-data-source>

		<hierarchical-filter parent-column="parent" root-id="importRoot"/>

		<constant-metaclass-resolver metaclass="${metaClass}"/>
		<object-searcher attr="idHolder" metaclass="${metaClass}"/>

		<attr name="idHolder" column="id"/>
		<attr name="title" column="title"/>
		<attr name="parent" column="parent">
			<object-converter attr="idHolder" metaclass="${metaClass}" required="false"/>
		</attr>

		<remove-customizer hierarchy-root="{changed in test}" metaclass="${metaClass}">
			<!-- Отмена архивации объектов являющихся дочерними для hierarchy-root -->
			<skip-objects-script><![CDATA[
			return utils.find(parameters.metaClass, ['parent' : parent] )
			]]></skip-objects-script>
			<!-- Для архивируемых объектов выполнится скрипт изменяющий их title-->
			<remove-condition-script><![CDATA[
			utils.edit(subject, ['title': 'changed']);
			return true;
			]]></remove-condition-script>
		</remove-customizer>
	</class>
</config>