<?xml version="1.0" encoding="UTF-8"?>
<config
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:noNamespaceSchemaLocation="../../../../../../target/generated-sources/jaxb/advimport/schema1.xsd"
	save-log="false" >
	
	<mode>CREATE</mode>
	
	<parameter name="defVal">default title</parameter>
	
	<class name="ou" threads-number="2">
	
		<csv-data-source id-column="id" with-header="true" file-name="classpath:/ru/naumen/advimport/hyperlink.test.csv">
			<column name="id" src-key="id"/>
			<column name="hl" src-key="link"/>
		</csv-data-source>
		
		<constant-metaclass-resolver metaclass="ou$ouImportTest"/>
		<object-searcher attr="idHolder" metaclass="ou$ouImportTest"/>

		<attr name="idHolder" column="id" />
		<attr name="title" default-value="$defVal"/>
		<attr name="hl" column="hl">
			<hyperlink-converter delimiter=";"/>
		</attr>
	</class>
</config>
