<?xml version="1.0" encoding="UTF-8"?>
<config
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:noNamespaceSchemaLocation="../../../../../../target/generated-sources/jaxb/advimport/schema1.xsd"
	save-log="false" >
	
	<mode>CREATE</mode>
	
	<class name="ou" threads-number="1">
	
		<xml-data-source id-column="id" file-name="classpath:/ru/naumen/advimport/ou_xml_hierarchy.test.xml" xpath="//OU">
			<column name="id" src-key="./@id"/>
			<column name="title" src-key="@title" default-value="default title"/>
			<column name="parent" src-key="../@id"/>
		</xml-data-source>
		
		<hierarchical-filter parent-column="parent"/>
		
		<constant-metaclass-resolver metaclass="ou$ouImportTest"/>
		<object-searcher attr="idHolder" metaclass="ou$ouImportTest"/>

		<attr name="title" column="title"  />
		<attr name="idHolder" column="id"  />
		<attr name="parent" column="parent" >
			<object-converter attr="idHolder" metaclass="ou$ouImportTest"/>
		</attr>
	</class>
</config>
