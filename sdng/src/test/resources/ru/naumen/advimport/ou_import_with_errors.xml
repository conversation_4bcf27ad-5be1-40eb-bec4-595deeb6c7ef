<?xml version="1.0" encoding="UTF-8"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../target/classes/advimport/schema1.xsd" save-log="true" threads-number="5" >
   <mode>CREATE</mode>
   <mode>UPDATE</mode>
   <parameter name="metaClass">ou$errorImportOu</parameter>
   <class name="errorImportOu" threads-number="5">
       <csv-data-source id-column="idHolder" with-header="false" file-name="classpath:/ru/naumen/advimport/ou_import_with_errors.csv" delimiter="," url-timeout="60" encoding='UTF-8'>
           <column name="idHolder" src-key="0"/>
           <column name="title" src-key="1"/>
       </csv-data-source>
       <constant-metaclass-resolver metaclass="${metaClass}"/>
       <object-searcher attr="idHolder" metaclass="${metaClass}"/>

       <attr name="stringId" column="idHolder" />
       <attr name="title" column="title" />
   </class>
</config>
