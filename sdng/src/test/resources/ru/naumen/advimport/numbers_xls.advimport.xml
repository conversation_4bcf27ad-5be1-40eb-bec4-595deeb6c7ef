<?xml version="1.0" encoding="UTF-8"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../target/classes/advimport/schema1.xsd"
    save-log="true" threads-number="1">

    <mode>CREATE</mode>
    <mode>UPDATE</mode>

    <class name="model" threads-number="2">
        <xls-data-source file-name="classpath:/ru/naumen/advimport/numbers_xls.test.xls" start-row="1">
            <column name="id" src-key="0" />
            <column name="title" src-key="1" />
        </xls-data-source>

        <constant-metaclass-resolver metaclass="ou$ouImportTest"/>
        <object-searcher attr="idHolder" metaclass="ou$ouImportTest"/>

        <attr name="title" column="title"/>
        <attr name="idHolder" column="id"/>
    </class>
</config>
