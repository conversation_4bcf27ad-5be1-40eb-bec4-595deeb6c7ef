<?xml version="1.0" encoding="UTF-8"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="../../../../../../target/classes/advimport/schema1.xsd" save-log="false">

    <mode>UPDATE</mode>
    <class name="ou" threads-number="1">
        <csv-data-source id-column="id" with-header="true"
                         file-name="classpath:/ru/naumen/advimport/localizedTitleUpdate.test.csv">
            <column name="id" src-key="id"/>
            <column name="title" src-key="title"/>
            <column name="title_ru" src-key="title_ru"/>
            <column name="title_en" src-key="title_en"/>
            <column name="title_client" src-key="title_client"/>
            <column name="number" src-key="number"/>
        </csv-data-source>

        <constant-metaclass-resolver metaclass="ou$ouImportTest"/>
        <object-searcher attr="idHolder" metaclass="ou$ouImportTest"/>

        <attr name="idHolder" column="id"/>
        <attr name="title" column="title">
            <localized-column lang="ru" column="title_ru"/>
            <localized-column lang="en" column="title_en"/>
            <localized-column lang="client" column="title_client"/>
        </attr>
        <attr name="number" column="number"/>
    </class>
</config>