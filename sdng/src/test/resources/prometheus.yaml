---
lowercaseOutputLabelNames: true
lowercaseOutputName: true

whitelistObjectNames:
- "Catalina:type=Server"
- "Catalina:type=GlobalRequestProcessor,*"
- "Catalina:type=ThreadPool,*"
- "Catalina:type=Manager,*"
- "Catalina:j2eeType=Servlet,*"
- "org.apache.activemq.artemis:*"
- "com.mchange.v2.c3p0:*"
- "com.zaxxer.hikari:*"
- "ru.naumen.monitoring:type=StartupInfo"
- "ru.naumen.core.server.mbean.authentication:type=AuthenticationStatistics,name=authenticationStatistics"
- "ru.naumen.core.server.mbean.cluster:type=ClusterStatistics,name=clusterStatistics"
- "ru.naumen.core.server.mbean.employee:type=EmployeeStatistics,name=employeeStatistics"
- "ru.naumen.core.server.mbean.plannedevent:type=PlannedEventStatistics,name=plannedEventStatistics"
- "ru.naumen.core.server.mbean.query:type=SqlQueryStatistics,name=sqlQueryStatistics"
- "ru.naumen.core.server.mbean.mail.inbound:type=InboundMailConnectionStatistics,name=inboundMailConnectionStatistics"
- "ru.naumen.core.server.mbean.mail.inbound:type=IncomingMailStatistics,name=incomingMailStatistics"
- "ru.naumen.core.server.mbean.mail.outgoing:type=OutgoingMailStatistics,name=outgoingMailStatistics"
- "ru.naumen.core.server.mbean.notification:type=NotificationStatistics,name=notificationStatistics"
- "ru.naumen.core.server.mbean.license:type=LicenseStatistics,name=licenseStatistics"
- "ru.naumen.core.server.mbean.hibernate:type=HibernateStatistics,name=hibernateStatistics"
- "ru.naumen.core.server.mbean.keystore:type=AppKeyStore,name=appKeyStore"
- "ru.naumen.core.server.mbean.heapusage:type=HeapUsageStatistics,name=heapUsageStatistics"

rules:

### Tomcat

- pattern: 'Catalina<type=Server><>serverNumber: (.*)'
  name: tomcat_info
  type: GAUGE
  value: 1
  labels:
    version: $1

- pattern: 'Catalina<type=GlobalRequestProcessor, name=\"(\w+-\w+)-(\d+)\"><>bytes(Received|Sent):'
  name: tomcat_request_processor_$3_bytes_total
  type: COUNTER
  labels:
    port: $2
    protocol: $1
  help: Tomcat global $3

- pattern: 'Catalina<type=GlobalRequestProcessor, name=\"(\w+-\w+)-(\d+)\"><>(request|error)Count:'
  name: tomcat_request_processor_$3s_total
  type: COUNTER
  labels:
    port: $2
    protocol: $1
  help: Tomcat global $3

- pattern: 'Catalina<type=GlobalRequestProcessor, name=\"(\w+-\w+)-(\d+)\"><>processingTime:'
  name: tomcat_request_processor_processing_time_seconds_total
  type: COUNTER
  valueFactor: 0.001
  labels:
    port: $2
    protocol: $1
  help: Tomcat global processing time

- pattern: 'Catalina<type=ThreadPool, name="(\w+-\w+)-(\d+)"><>max(Connection|Thread)s:'
  name: tomcat_threadpool_$3_limit
  type: GAUGE
  labels:
    port: $2
    protocol: $1
  help: Tomcat threadpool $3

- pattern: 'Catalina<type=ThreadPool, name="(\w+-\w+)-(\d+)"><>(?>current)?(connection|Thread|ThreadsBusy)(?>Count)?:'
  name: tomcat_threadpool_$3
  type: GAUGE
  labels:
    port: $2
    protocol: $1
  help: Tomcat threadpool $3

- pattern: 'Catalina<type=Manager, context=((?!/(?>docs|examples|host-manager|manager))\S+), host=(\S+)><>(rejected|expired)Sessions:'
  name: tomcat_session_total
  type: COUNTER
  labels:
    context: $1
    host: $2
    status: $3
  help: Tomcat session total

- pattern: 'Catalina<type=Manager, context=((?!/(?>docs|examples|host-manager|manager))\S+), host=(\S+)><>sessionCounter:'
  name: tomcat_session_total
  type: COUNTER
  labels:
    context: $1
    host: $2
    status: created
  help: Tomcat session total

- pattern: 'Catalina<type=Manager, context=((?!/(?>docs|examples|host-manager|manager))\S+), host=(\S+)><>processingTime:'
  name: tomcat_session_processing_time_seconds_seconds_total
  valueFactor: 0.001
  type: COUNTER
  labels:
    context: $1
    host: $2
  help: Tomcat session processing time total

- pattern: 'Catalina<j2eeType=Servlet, name=(\S+), WebModule=//((?!localhost/(?>docs|examples|host-manager|manager))\S+), J2EEApplication=none, J2EEServer=none><>(request|error)Count:'
  name: tomcat_servlet_$3s_total
  type: COUNTER
  labels:
    module: $2
    servlet: $1
  help: Tomcat servlet $3s total

- pattern: 'Catalina<j2eeType=Servlet, name=(\S+), WebModule=//((?!localhost/(?>docs|examples|host-manager|manager))\S+), J2EEApplication=none, J2EEServer=none><>processingTime:'
  name: tomcat_servlet_processing_time_seconds_total
  type: COUNTER
  valueFactor: 0.001
  labels:
    module: $2
    servlet: $1
  help: Tomcat servlet processing time total



### Artemis

- pattern: 'org.apache.activemq.artemis<broker="\S+", component=addresses, address="(\S+)"><>AddressSize:'
  name: artemis_address_size_bytes
  type: GAUGE
  labels:
    address: $1
  help: The number of estimated bytes being used by the queue(s), used to control paging and blocking

- pattern: 'org.apache.activemq.artemis<broker="\S+", component=addresses, address="(\S+)", subcomponent=(?>queue|topic)s, routing-type="\S+", (queue|topic)="(\S+)"><>FirstMessageAge:'
  name: artemis_age_first_message_seconds
  type: GAUGE
  valueFactor: 0.001
  labels:
    address: $1
    $2: $3
  help: Age of the first message in seconds

- pattern: 'org.apache.activemq.artemis<broker="\S+", component=addresses, address="(\S+)", subcomponent=(?>queue|topic)s, routing-type="\S+", (queue|topic)="(\S+)"><>MessageCount:'
  name: artemis_message
  type: GAUGE
  labels:
    address: $1
    $2: $3
  help: Number of messages currently in this queue (includes scheduled, paged, and in-delivery messages)

- pattern: 'org.apache.activemq.artemis<broker="\S+", component=addresses, address="(\S+)", subcomponent=(?>queue|topic)s, routing-type="\S+", (queue|topic)="(\S+)"><>(Delivering|Scheduled)Count:'
  name: artemis_message_$4
  type: GAUGE
  labels:
    address: $1
    $2: $3
  help: Number of $4 messages in this queue

- pattern: 'org.apache.activemq.artemis<broker="\S+", component=addresses, address="(\S+)", subcomponent=(?>queue|topic)s, routing-type="\S+", (queue|topic)="(\S+)"><>Messages(Acknowledged|Added|Expired|Killed):'
  name: artemis_messages_$4_total
  type: COUNTER
  labels:
    address: $1
    $2: $3
  help: Number of messages $4 from this queue since it was created

- pattern: 'org.apache.activemq.artemis<broker="\S+", component=addresses, address="(\S+)", subcomponent=(?>queue|topic)s, routing-type="\S+", (queue|topic)="(\S+)"><>ConsumerCount:'
  name: artemis_consumer
  type: GAUGE
  labels:
    address: $1
    $2: $3
  help: Number of consumers consuming messages from this queue



### Pool c3p0

- pattern: 'com.mchange.v2.c3p0<type=\S+, .*name=(\S+)><>maxPoolSize:'
  name: pool_connection_limit
  type: COUNTER
  labels:
    name: $1

- pattern: 'com.mchange.v2.c3p0<type=\S+, .*name=(\S+)><>num(Busy|Idle)Connections:'
  name: pool_connection_$2
  type: COUNTER
  labels:
    name: $1



### Pool hikari

- pattern: 'com.zaxxer.hikari<type=\S+ \((\S+)\)><>ActiveConnections:'
  name: pool_connection_busy
  type: GAUGE
  labels:
    name: $1

- pattern: 'com.zaxxer.hikari<type=\S+ \((\S+)\)><>IdleConnections:'
  name: pool_connection_idle
  type: GAUGE
  labels:
    name: $1

- pattern: 'com.zaxxer.hikari<type=\S+ \((\S+)\)><>MaximumPoolSize:'
  name: pool_connection_limit
  type: GAUGE
  labels:
    name: $1

- pattern: 'com.zaxxer.hikari<type=\S+ \((\S+)\)><>ThreadsAwaitingConnection:'
  name: pool_thread_awaiting_connection
  type: GAUGE
  labels:
    name: $1



### SMP

- pattern: 'ru.naumen.monitoring<type=StartupInfo><>StartupTimeMs:'
  name: smp_startup_time_seconds
  type: GAUGE
  valueFactor: 0.001

- pattern: 'ru.naumen.core.server.mbean.authentication<name=authenticationStatistics, type=AuthenticationStatistics><>(Failed|Successful):'
  name: smp_authentication_attempts_$1_total
  type: COUNTER
  help: $1 SMP authentication attempts (login page)

- pattern: 'ru.naumen.core.server.mbean.employee<name=employeeStatistics, type=EmployeeStatistics><>CountUsers:'
  name: smp_employee_users_count
  type: GAUGE
  help: Total amount of created users

- pattern: 'ru.naumen.core.server.mbean.employee<name=employeeStatistics, type=EmployeeStatistics, stringKey=(.+)><CountUsersByLicense>longValue:'
  name: smp_employee_count_users_by_licence
  type: GAUGE
  labels:
    licenseId: $1
  help: Amount of created users for each license type

- pattern: 'ru.naumen.core.server.mbean.query<name=sqlQueryStatistics, type=SqlQueryStatistics><>Count:'
  name: smp_sql_queries_executed_total
  type: COUNTER
  help: Total amount of executed SQL queries

- pattern: 'ru.naumen.core.server.mbean.query<name=sqlQueryStatistics, type=SqlQueryStatistics><>Time:'
  name: smp_sql_queries_execution_time_seconds_total
  type: COUNTER
  help: Total execution time for all SQL queries in seconds
  valueFactor: 0.001

- pattern: 'ru.naumen.core.server.mbean.cluster<name=clusterStatistics, type=ClusterStatistics><>RequestsSent:'
  name: smp_cluster_request_sent_total
  type: COUNTER
  help: Total amount of sent cluster requests

- pattern: 'ru.naumen.core.server.mbean.cluster<name=clusterStatistics, type=ClusterStatistics><>SuccessfulRequests:'
  name: smp_cluster_request_successful_total
  type: COUNTER
  help: Total amount of successful cluster requests

- pattern: 'ru.naumen.core.server.mbean.cluster<name=clusterStatistics, type=ClusterStatistics><>FailedRequests:'
  name: smp_cluster_request_failed_total
  type: COUNTER
  help: Total amount of failed cluster requests

- pattern: 'ru.naumen.core.server.mbean.cluster<name=clusterStatistics, type=ClusterStatistics><>InvalidateAllEventsSent:'
  name: smp_cluster_invalidate_events_sent_total
  type: COUNTER
  help: Total amount of "invalidate all" cluster events sent

- pattern: 'ru.naumen.core.server.mbean.mail.inbound<name=inboundMailConnectionStatistics, type=InboundMailConnectionStatistics><>Successful:'
  name: smp_inbound_mail_connections_successful_total
  type: COUNTER
  help: Total amount of successful attempts to connect to inbound mail server

- pattern: 'ru.naumen.core.server.mbean.mail.inbound<name=inboundMailConnectionStatistics, type=InboundMailConnectionStatistics><>Failed:'
  name: smp_inbound_mail_connections_failed_total
  type: COUNTER
  help: Total amount of failed attempts to connect to inbound mail server

- pattern: 'ru.naumen.core.server.mbean.mail.inbound<name=incomingMailStatistics, type=IncomingMailStatistics><>Successful:'
  name: smp_incoming_mail_successful_total
  type: COUNTER
  help: Total amount of successfully received letters

- pattern: 'ru.naumen.core.server.mbean.mail.inbound<name=incomingMailStatistics, type=IncomingMailStatistics><>Failed:'
  name: smp_incoming_mail_failed_total
  type: COUNTER
  help: Total amount of letters failed to receive

- pattern: 'ru.naumen.core.server.mbean.mail.inbound<name=incomingMailStatistics, type=IncomingMailStatistics><>WithIssues:'
  name: smp_incoming_mail_with_issues_total
  type: COUNTER
  help: Total amount of letters received with issues

- pattern: 'ru.naumen.core.server.mbean.mail.outgoing<name=outgoingMailStatistics, type=OutgoingMailStatistics><>Successful:'
  name: smp_outoing_mail_successful_total
  type: COUNTER
  help: Total amount of successfully sent letters

- pattern: 'ru.naumen.core.server.mbean.mail.outgoing<name=outgoingMailStatistics, type=OutgoingMailStatistics><>Failed:'
  name: smp_outoing_mail_failed_total
  type: COUNTER
  help: Total amount of letters failed to be sent

- pattern: 'ru.naumen.core.server.mbean.mail.outgoing<name=outgoingMailStatistics, type=OutgoingMailStatistics><>LengthQueue:'
  name: smp_outoing_mail_queue_length
  type: GAUGE
  help: Length of outgoing mail queue

- pattern: 'ru.naumen.core.server.mbean.notification<name=notificationStatistics, type=NotificationStatistics><>SendSuccessful:'
  name: smp_notification_sent_successful_total
  type: COUNTER
  help: Total amount of successfully sent notifications

- pattern: 'ru.naumen.core.server.mbean.notification<name=notificationStatistics, type=NotificationStatistics><>SendFailed:'
  name: smp_notification_sent_failed_total
  type: COUNTER
  help: Total amount of notifications failed to be sent

- pattern: 'ru.naumen.core.server.mbean.notification<name=notificationStatistics, type=NotificationStatistics><>(InvalidEmails|AttemptFailed):'
  name: smp_notification_failures_total
  type: COUNTER
  labels:
    reason: $1
  help: SMP notifications that were not sent

- pattern: 'ru.naumen.core.server.mbean.notification<name=notificationStatistics, type=NotificationStatistics><>SendFailed(Partially|EmailNotExist|SystemEmail):'
  name: smp_notification_sent_failures_total
  type: COUNTER
  labels:
    reason: $1
  help: SMP notifications that were sent with errors

- pattern: 'ru.naumen.core.server.mbean.plannedevent<name=plannedEventStatistics, type=PlannedEventStatistics><>OverdueTasks:'
  name: smp_planned_event_overdue_tasks
  type: GAUGE
  help: Current amount of overdue quartz tasks

- pattern: 'ru.naumen.core.server.mbean.plannedevent<name=plannedEventStatistics, type=PlannedEventStatistics, stringKey=(.+)><OverdueTimePerTasks>longValue:'
  name: smp_planned_event_overdue_time_per_task_seconds
  type: GAUGE
  labels:
    job: $1
  valueFactor: 0.001
  help: Current amount of overdue time for each quartz task

- pattern: 'ru.naumen.core.server.mbean.license<name=licenseStatistics, type=LicenseStatistics, stringKey=(.+)><LicenseUsagePerUser>longValue:'
  name: smp_license_usage_per_user
  type: GAUGE
  labels:
    licenseId: $1
  help: How many licences are currently used by unique users

- pattern: 'ru.naumen.core.server.mbean.license<name=licenseStatistics, type=LicenseStatistics, stringKey=(.+)><LicenseUsagePerSession>longValue:'
  name: smp_license_usage_per_session
  type: GAUGE
  labels:
    licenseId: $1
  help: How many licences are currently used by all active sessions

- pattern: 'ru.naumen.core.server.mbean.license<name=licenseStatistics, type=LicenseStatistics, stringKey=(.+)><Session>longValue:'
  name: smp_active_sessions
  type: GAUGE
  labels:
    sessionType: $1
  help: How many sessions are currently active

- pattern: 'ru.naumen.core.server.mbean.license<name=licenseStatistics, type=LicenseStatistics, stringKey=(.+)><LicenseGroups>longValue:'
  name: smp_license_limit
  type: GAUGE
  labels:
    group: $1
  help: Maximum number of available licences

- pattern: 'ru.naumen.core.server.mbean.hibernate<name=hibernateStatistics, type=HibernateStatistics><>StatisticsEnabled:'
  name: hibernate_statistics_enabled
  type: GAUGE
  help: Is Hibernate statistics active

- pattern: 'ru.naumen.core.server.mbean.hibernate<name=hibernateStatistics, type=HibernateStatistics><>(.+)Count:'
  name: hibernate_count_total
  type: COUNTER
  labels:
    event: $1
  help: Hibernate event counters

- pattern: 'ru.naumen.core.server.mbean.hibernate<name=hibernateStatistics, type=HibernateStatistics><>(.+)ExecutionMaxTime:'
  name: hibernate_execution_max_time_seconds
  type: GAUGE
  labels:
    query: $1
  valueFactor: 0.001
  help: Hibernate maximum execution time for queries

  ### KeyStore

- pattern: 'ru.naumen.core.server.mbean.keystore<name=appKeyStore, type=AppKeyStore, stringKey=(.+)><Certificates>longValue:'
  name: smp_cert_expiry
  type: GAUGE
  labels:
    alias: $1
  help: Certificates in app keystore

  ### AllocationRate

- pattern: 'ru.naumen.core.server.mbean.heapusage<name=heapUsageStatistics, type=HeapUsageStatistics><>AllocationRate:'
  name: smp_allocation_rate_statistics
  type: GAUGE
  help: Allocation rate stats

  ### PromotionRate

- pattern: 'ru.naumen.core.server.mbean.heapusage<name=heapUsageStatistics, type=HeapUsageStatistics><>PromotionRate:'
  name: smp_promotion_rate_statistics
  type: GAUGE
  help: Promotion rate stats