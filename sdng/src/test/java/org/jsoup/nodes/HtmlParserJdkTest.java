package org.jsoup.nodes;

import org.jsoup.parser.HtmlParser;
import org.junit.Assert;
import org.junit.Test;

/**
 * Тесты на работу HtmlParser
 * <AUTHOR>
 *
 */
public class HtmlParserJdkTest
{
    @Test
    public void testAddSpace()
    {
        //@formatter:off
        String html = "<p class=\"MsoNormal\">Прошу установить <span lang=\"EN-US\">Microsoft Office 2013<o:p></o:p></span>"
                +"</p><p class=\"MsoNormal\"><o:p><br></o:p></p>";
        //@formatter:on

        String result = HtmlParser.parse(html).text();
        String expected = "Прошу установить Microsoft Office 2013";
        Assert.assertEquals(expected, result);
    }

    @Test
    public void testAddSpaceWhenMultipleSpaces()
    {
        //@formatter:off
        String html = "<p class=\"MsoNormal\">Прошу установить    <span lang=\"EN-US\">Microsoft Office 2013<o:p></o:p></span>"
                +"</p><p class=\"MsoNormal\"><o:p><br></o:p></p>";
        //@formatter:on

        String result = HtmlParser.parse(html).text();
        String expected = "Прошу установить Microsoft Office 2013";
        Assert.assertEquals(expected, result);
    }
}
