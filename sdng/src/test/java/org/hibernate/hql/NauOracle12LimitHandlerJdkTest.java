package org.hibernate.hql;

import static org.junit.Assert.*;

import org.hibernate.LockOptions;
import org.hibernate.dialect.pagination.Oracle12LimitHandler;
import org.hibernate.query.internal.SimpleQueryOptions;
import org.hibernate.query.spi.Limit;
import org.hibernate.query.spi.QueryOptions;
import org.junit.BeforeClass;
import org.junit.Test;

/**
 * Тест, проверяющий корректность добавления к запросу
 * {@code FETCH FIRST m ROWS ONLY} и {@code OFFSET n ROWS FETCH NEXT m ROWS ONLY}.
 *
 * <AUTHOR>
 * @since 22.09.2023
 */
public class NauOracle12LimitHandlerJdkTest
{
    private static final Limit parameters = new Limit();
    private static final QueryOptions options = new SimpleQueryOptions(new LockOptions(), false);

    @BeforeClass
    public static void setUp()
    {
        parameters.setFirstRow(0);
        parameters.setMaxRows(5);
    }

    /**
     *  Исходная строка с {@code for update}, котора заключена в кавычки, сохраняется в итоговом запросе, и оконные
     *  функции подставляются в нужное место, т.е. получаем запрос вида
     *  {@code select a.prop from A a where a.name = 'this is fetch first ? rows only for update'}
     */
    @Test
    public void testSqlWithForUpdateInsideQuotedString()
    {
        String sql = "select p.name from Person p where p.name =  ' this is a  string with spaces  ' for update";
        String expectedOracle = "select * from (select p.name from Person p where p.name =  "
                + "' this is a  string with spaces  ') where rownum<=? for update";
        assertEquals(expectedOracle, Oracle12LimitHandler.INSTANCE.processSql(sql, parameters, options));

        sql = "select a.prop from A a where a.name =  'this is for update '";
        expectedOracle = "select a.prop from A a where a.name =  'this is for update ' fetch first ? rows only";
        assertEquals(expectedOracle, Oracle12LimitHandler.INSTANCE.processSql(sql, parameters, options));
    }

    /**
     * Исходная строка с пробелами и {@code for update}, заключенная в кавычки, сохраняется в итоговом запросе.
     */
    @Test
    public void testSqlWithForUpdateInsideAndOutsideQuotedString()
    {
        String sql = "select a.prop from A a where a.name =  'this is for update ' for update";
        String expectedOracle = "select * from (select a.prop from A a where a.name =  "
                + "'this is for update ') where rownum<=? for update";
        assertEquals(expectedOracle, Oracle12LimitHandler.INSTANCE.processSql(sql, parameters, options));
    }

    /**
     * Тестируется обычный select на корректность добавления ключевых слов {@code OFFSET m ROWS FETCH NEXT n ROWS ONLY}.
     */
    @Test
    public void testSqlWithSimpleSelect()
    {
        String sql = "select distinct a.id as p from A a where a.prop=? and a.test=?";
        String expectedOracle = "select distinct a.id as p from A a where a.prop=? and a.test=? fetch first ? rows only";
        assertEquals(expectedOracle, Oracle12LimitHandler.INSTANCE.processSql(sql, parameters, options));

        sql = "select  p.name from Person p where p.id = 1 for update";
        expectedOracle = "select * from (select  p.name from Person p where p.id = 1) where rownum<=? for update";
        assertEquals(expectedOracle, Oracle12LimitHandler.INSTANCE.processSql(sql, parameters, options));
    }
}