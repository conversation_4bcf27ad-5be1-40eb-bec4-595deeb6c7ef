package org.hibernate;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.sql.Blob;
import java.sql.SQLException;

import org.apache.commons.fileupload2.core.FileItem;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import ru.naumen.Assert;
import ru.naumen.core.server.ConfigurationProperties;
import ru.naumen.core.server.SpringContext;
import ru.naumen.core.server.filestorage.File;
import ru.naumen.core.server.filestorage.MimeTypesUtilsJdkTest;
import ru.naumen.core.server.filestorage.spi.DBFileContent;
import ru.naumen.core.server.filestorage.spi.storages.operations.database.DBStorageOperationsImpl;
import ru.naumen.core.server.hibernate.DDLTool;
import ru.naumen.core.server.upload.DiskFileInputStream;
import ru.naumen.core.server.upload.spi.DBFileItem;
import ru.naumen.core.server.upload.spi.DBFileItemDao;
import ru.naumen.core.server.upload.spi.UploadFileItemContext;

/**
 * Тестирование получения корректной длины стрима при использовании кастомного {@link BlobProxy}
 *
 * <AUTHOR>
 * @since 03.05.2023
 */
public class BlobJdkTest
{
    @Mock
    SessionFactory sessionFactory;
    @Mock
    ConfigurationProperties configurationProperties;
    @Mock
    Session session;
    @Mock
    DiskFileInputStream diskInputStream;
    @Mock
    InputStream inputStream;
    @Mock
    DBFileContent dbFileContent;
    @Mock
    SpringContext springContext;
    @InjectMocks
    DBStorageOperationsImpl dbStorageOperations;
    @Mock
    FileItem fileItem;
    @InjectMocks
    DBFileItemDao dbFileItemDao;

    private UploadFileItemContext uploadFileItemContext;
    private AutoCloseable openMocks;
    // это значение - 5 ГБайт в байтах - даёт при приведении к int положительное число
    // именно это нужно для воспроизведения дефекта, который закрывают тесты ниже
    private static final long streamLength = 1024L * 1024 * 1024 * 5;

    @Before
    public void setUp() throws IOException
    {
        openMocks = MockitoAnnotations.openMocks(this);
        Mockito.when(session.get(Mockito.any(Class.class), Mockito.any(), Mockito.nullable(LockOptions.class)))
                .thenReturn(dbFileContent);
        Mockito.when(sessionFactory.getCurrentSession()).thenReturn(session);
        uploadFileItemContext = new UploadFileItemContext(null, null, fileItem, false, false,
                false);
        Mockito.when(fileItem.isInMemory()).thenReturn(false);
        Mockito.when(fileItem.getSize()).thenReturn(streamLength);
        Mockito.when(fileItem.getInputStream()).thenReturn(new FileInputStream(MimeTypesUtilsJdkTest.class.getResource(
                MimeTypesUtilsJdkTest.IMAGE_PNG).getPath()));
    }

    @After
    public void tearDown() throws Exception
    {
        openMocks.close();
    }

    @Test
    public void checkUsingCustomBlobInSetContentViaStream() throws SQLException
    {
        Mockito.when(diskInputStream.length()).thenReturn(streamLength);
        dbStorageOperations.setContent(new File(), diskInputStream);

        ArgumentCaptor<Blob> blobArgumentCaptor = ArgumentCaptor.forClass(Blob.class);
        Mockito.verify(dbFileContent).setContent(blobArgumentCaptor.capture());
        Assert.assertEquals(streamLength, blobArgumentCaptor.getValue().length());
    }

    @Test
    public void saveThroughDbFileItemDao() throws SQLException
    {
        dbFileItemDao.save(uploadFileItemContext, null);
        ArgumentCaptor<DBFileItem> saveArgumentCaptor = ArgumentCaptor.forClass(DBFileItem.class);
        Mockito.verify(session).persist(saveArgumentCaptor.capture());
        Assert.assertEquals(streamLength, saveArgumentCaptor.getValue().getContent().length());
    }

    @Test
    public void checkUsingCustomBlobInSetContentViaMemory() throws SQLException
    {
        File file = new File();
        file.setFileSize(streamLength);
        try (MockedStatic<SpringContext> mockedSpringContext = Mockito.mockStatic(SpringContext.class))
        {
            mockedSpringContext.when(SpringContext::getInstance).thenReturn(springContext);
            try (MockedStatic<DDLTool> mockedDDLTool = Mockito.mockStatic(DDLTool.class))
            {
                mockedDDLTool.when(DDLTool::isOracle).thenReturn(false);
                dbStorageOperations.setContent(file, inputStream);
            }
        }
        ArgumentCaptor<Blob> blobArgumentCaptor = ArgumentCaptor.forClass(Blob.class);
        Mockito.verify(dbFileContent).setContent(blobArgumentCaptor.capture());
        Assert.assertEquals(streamLength, blobArgumentCaptor.getValue().length());
    }
}