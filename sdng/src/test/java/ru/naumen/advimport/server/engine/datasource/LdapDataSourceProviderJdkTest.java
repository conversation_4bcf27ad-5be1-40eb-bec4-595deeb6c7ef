package ru.naumen.advimport.server.engine.datasource;

import static javax.naming.Context.SECURITY_PROTOCOL;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;
import static ru.naumen.advimport.server.engine.datasource.LdapDataSourceProviderBean.LDAP_SOCKET_FACTORY;

import java.lang.reflect.Field;
import java.util.Hashtable;
import java.util.List;

import javax.naming.Context;
import javax.naming.InitialContext;
import javax.naming.NamingException;
import javax.naming.ldap.LdapContext;
import javax.naming.spi.InitialContextFactory;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.util.ReflectionUtils;

import ru.naumen.Assert;
import ru.naumen.advimport.server.engine.ImportContext;
import ru.naumen.advimport.shared.config.datasource.Column;
import ru.naumen.advimport.shared.config.datasource.LdapDataSource;
import ru.naumen.advimport.shared.connect.LDAPConnection;
import ru.naumen.core.server.ldap.LdapDummySSLSocketFactory;
import ru.naumen.core.server.ldap.LdapUnitedSSLSocketFactory;
import ru.naumen.core.shared.TestUtils;

/**
 * Класс для тестирования источника данных, сформированного LDAP-запросом
 * <AUTHOR>
 * @since 26.08.2019
 */
@RunWith(MockitoJUnitRunner.class)
public class LdapDataSourceProviderJdkTest
{
    private static final String SSL_SEC_PROTOCOL = "ssl";
    private static final String PLAIN_SEC_PROTOCOL = "plain";
    private static final String EMPTY_SEC_PROTOCOL = "";

    public static class StubContextFactory
            implements InitialContextFactory
    {
        @Override
        public Context getInitialContext(Hashtable hashtable) throws NamingException
        {
            InitialContext mockInitialContext = mock(InitialContext.class);
            when(mockInitialContext.getEnvironment()).thenReturn(hashtable);
            return mockInitialContext;
        }
    }

    public static class LdapDataSourceProviderBeanStub extends LdapDataSourceProviderBean
    {
        protected void initResolvers()
        {
        }

        protected void afterInit()
        {
        }

        protected void prepareImportTags()
        {
        }

        protected void buildQueue()
        {
            super.buildQueue();
        }
    }

    @Mock
    private LdapDataSource ldapDataSource;

    /**
     * Тестирование инициализации {@link LdapContext} в зависимости от параметров "Протокол безопасности" и
     * "Игнорировать проверку сертификата"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00357
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$123647921
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$78391682
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$78391683
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать LdapDatasourceProvider: sec protocol = "ssl"; skip cert verification = true</li>
     * <b>Проверки</b>
     * <li>В качестве SocketFactory установлен LdapDummySSLSocketFactory</li>
     * <li>В SECURITY_PROTOCOL установлен "ssl"</li>
     * <br>
     * <b>Подготовка.</b>
     * <li>Создать LdapDatasourceProvider: sec protocol = "ssl"; skip cert verification = false</li>
     * <b>Проверки</b>
     * <li>В качестве SocketFactory установлен LdapUnitedSSLSocketFactory</li>
     * <li>В SECURITY_PROTOCOL установлен "ssl"</li>
     * <br>
     * <b>Подготовка.</b>
     * <li>Создать LdapDatasourceProvider: sec protocol = "plain"; skip cert verification = false</li>
     * <b>Проверки</b>
     * <li>В качестве SocketFactory установлен null</li>
     * <li>В SECURITY_PROTOCOL установлен "plain"</li>
     * <br>
     * <b>Подготовка.</b>
     * <li>Создать LdapDatasourceProvider: sec protocol = ""; skip cert verification = false</li>
     * <b>Проверки</b>
     * <li>В качестве SocketFactory установлен null</li>
     * <li>В SECURITY_PROTOCOL установлен null</li>
     * <b>Подготовка.</b>
     * <li>Создать LdapDatasourceProvider: в sec protocol установить случайную строку; skip cert verification =
     * false</li>
     * <b>Проверки</b>
     * <li>В качестве SocketFactory установлен null</li>
     * <li>В SECURITY_PROTOCOL установлен переданный sec protocol</li>
     * <ol>
     *
     * @throws NamingException
     */
    @Test
    public void testInitContextDependsOnSecProtocol() throws NamingException
    {
        // sec protocol = "ssl"; skip cert verification = true
        var ldapDataSourceProviderBean = createLdapDataSourceProviderBean(SSL_SEC_PROTOCOL, true);
        var environment = (ldapDataSourceProviderBean.context).getEnvironment();
        Assert.assertEquals(LdapDummySSLSocketFactory.class.getName(), environment.get(LDAP_SOCKET_FACTORY));
        Assert.assertEquals(SSL_SEC_PROTOCOL, environment.get(SECURITY_PROTOCOL));

        // sec protocol = "ssl"; skip cert verification = false
        ldapDataSourceProviderBean = createLdapDataSourceProviderBean(SSL_SEC_PROTOCOL, false);
        environment = ldapDataSourceProviderBean.context.getEnvironment();
        Assert.assertEquals(LdapUnitedSSLSocketFactory.class.getName(), environment.get(LDAP_SOCKET_FACTORY));
        Assert.assertEquals(SSL_SEC_PROTOCOL, environment.get(SECURITY_PROTOCOL));

        // sec protocol = "plain"; skip cert verification = false
        ldapDataSourceProviderBean = createLdapDataSourceProviderBean(PLAIN_SEC_PROTOCOL, false);
        environment = ldapDataSourceProviderBean.context.getEnvironment();
        Assert.assertNull(environment.get(LDAP_SOCKET_FACTORY));
        Assert.assertEquals(PLAIN_SEC_PROTOCOL, environment.get(SECURITY_PROTOCOL));

        // sec protocol = ""; skip cert verification = false
        ldapDataSourceProviderBean = createLdapDataSourceProviderBean(EMPTY_SEC_PROTOCOL, false);
        environment = ldapDataSourceProviderBean.context.getEnvironment();
        Assert.assertNull(environment.get(LDAP_SOCKET_FACTORY));
        Assert.assertNull(environment.get(SECURITY_PROTOCOL));

        // sec protocol = random; skip cert verification = false
        var randomSecProtocol = TestUtils.uniqueRandomString("test");
        ldapDataSourceProviderBean = createLdapDataSourceProviderBean(randomSecProtocol, false);
        environment = ldapDataSourceProviderBean.context.getEnvironment();
        Assert.assertNull(environment.get(LDAP_SOCKET_FACTORY));
        Assert.assertEquals(randomSecProtocol, environment.get(SECURITY_PROTOCOL));
    }

    /**
     * Тестируем, что если в списке колонок есть parent - кэш включен
     */
    @Test
    public void testLdapDataSourceProviderWithCache() throws IllegalAccessException
    {
        when(ldapDataSource.getColumns()).thenReturn(List.of(new Column("parent", "parent")));
        LdapDataSourceProviderBean ldapDataSourceProviderBean =
                createLdapDataSourceProviderBean(PLAIN_SEC_PROTOCOL, false);
        ldapDataSourceProviderBean.buildQueue();
        Field f = ReflectionUtils.findField(LdapDataSourceProviderBeanStub.class, "needAttributesCache");
        f.setAccessible(true);
        Assert.assertTrue(f.getBoolean(ldapDataSourceProviderBean));
    }

    /**
     * Тестируем, что если в списке колонок нет parent - кэш выключен
     */
    @Test
    public void testLdapDataSourceProviderWithoutCache() throws IllegalAccessException
    {
        when(ldapDataSource.getColumns()).thenReturn(List.of(new Column("test", "test")));
        LdapDataSourceProviderBean ldapDataSourceProviderBean =
                createLdapDataSourceProviderBean(PLAIN_SEC_PROTOCOL, false);
        ldapDataSourceProviderBean.buildQueue();
        Field f = ReflectionUtils.findField(LdapDataSourceProviderBeanStub.class, "needAttributesCache");
        f.setAccessible(true);
        Assert.assertFalse(f.getBoolean(ldapDataSourceProviderBean));
    }

    private LdapDataSourceProviderBeanStub createLdapDataSourceProviderBean(String secProtocol, boolean skipCertVerification)
    {
        LDAPConnection ldapConnection = new LDAPConnection();
        ldapConnection.setSkipCertVerification(Boolean.toString(skipCertVerification));
        ldapConnection.setSecProtocol(secProtocol);
        ldapConnection.setReferral("follow");
        ldapConnection.setCode("");

        when(ldapDataSource.getLDAPConnection()).thenReturn(ldapConnection);
        when(ldapDataSource.getRootElements()).thenReturn(List.of());
        ImportContext importContext = mock(ImportContext.class);
        when(importContext.evaluate((String)any())).thenAnswer(invocation ->
        {
            Object input = invocation.getArguments()[0];
            return (input == null) ? "" : input;
        });

        LdapDataSourceProviderBeanStub ldapDataSourceProviderBean = spy(new LdapDataSourceProviderBeanStub());
        when(ldapDataSourceProviderBean.getContextFactory()).thenReturn(StubContextFactory.class.getName());
        ldapDataSourceProviderBean.start(importContext, ldapDataSource);
        return ldapDataSourceProviderBean;
    }
}
