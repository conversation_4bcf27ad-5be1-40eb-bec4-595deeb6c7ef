package ru.naumen.advimport.server.engine.datasource;

import static org.junit.Assert.assertNull;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.junit.Test;
import org.mockito.Mockito;

import ru.naumen.advimport.server.engine.datasource.AbstractDataSourceProvider.ColumnResolver;
import ru.naumen.advimport.server.engine.datasource.SQLDataSourceProviderBean.ByNameSQLColumnResolver;
import ru.naumen.advimport.server.engine.datasource.SQLDataSourceProviderBean.ByNumberSQLColumnResolver;

/**
 * Тестирование логики извлечения данных для импорта из SQL БД.
 * <AUTHOR>
 * @since Dec 07, 2020
 */
public class SQLDataSourceProviderBeanJdkTest
{
    /**
     * Тестирование извлечения значения через {@link ByNameSQLColumnResolver}, если значение по умолчанию задано как
     * <code>null</code>.
     */
    @Test
    public void testByNameColumnResolverWithNullDefaultValue() throws SQLException
    {
        ColumnResolver<ResultSet> columnResolver = new ByNameSQLColumnResolver("testCol", "testCol", null);
        ResultSet resultSet = Mockito.mock(ResultSet.class);
        Mockito.when(resultSet.getObject("testCol")).thenReturn(null);
        assertNull(columnResolver.getValue(resultSet));
    }

    /**
     * Тестирование извлечения значения через {@link ByNumberSQLColumnResolver}, если значение по умолчанию задано как
     * <code>null</code>.
     */
    @Test
    public void testByNumberColumnResolverWithNullDefaultValue() throws SQLException
    {
        ColumnResolver<ResultSet> columnResolver = new ByNumberSQLColumnResolver("testCol", 2, null);
        ResultSet resultSet = Mockito.mock(ResultSet.class);
        Mockito.when(resultSet.getObject(2)).thenReturn(null);
        assertNull(columnResolver.getValue(resultSet));
    }
}
