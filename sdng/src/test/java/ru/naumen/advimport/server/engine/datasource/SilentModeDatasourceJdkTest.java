package ru.naumen.advimport.server.engine.datasource;

import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static ru.naumen.advimport.server.AdvImportUtils.*;

import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.net.ConnectException;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import jakarta.inject.Inject;

import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.junit.After;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.Parameterized;
import org.junit.runners.Parameterized.Parameters;
import org.mockito.Mockito;
import org.springframework.test.util.ReflectionTestUtils;

import ru.naumen.Assert;
import ru.naumen.advimport.server.AdvImportUtils;
import ru.naumen.advimport.server.AdvimportTasksState;
import ru.naumen.advimport.server.engine.AdvImportException;
import ru.naumen.advimport.server.engine.ClassProcessor;
import ru.naumen.advimport.server.engine.ClassProcessorBase;
import ru.naumen.advimport.server.engine.Engine;
import ru.naumen.advimport.server.engine.ImportContext;
import ru.naumen.advimport.server.engine.mcresolvers.ClassProcessorFactory;
import ru.naumen.advimport.shared.config.ClassConfig;
import ru.naumen.advimport.shared.config.Config;
import ru.naumen.advimport.shared.config.MetainfoClassConfig;
import ru.naumen.advimport.shared.config.Parameter;
import ru.naumen.advimport.shared.config.datasource.AbstractDataSource;
import ru.naumen.advimport.shared.config.datasource.LdapDataSource;
import ru.naumen.common.server.config.SilentModeConfiguration;
import ru.naumen.commons.server.utils.XmlUtils;
import ru.naumen.core.server.SpringContext;
import ru.naumen.core.server.jms.JmsConfiguration;
import ru.naumen.core.server.net.ConnectionPermissionCheckerImpl;
import ru.naumen.core.server.net.SilentModeViolationException;
import ru.naumen.sec.server.manager.NauSecurityManager;

/**
 * Тестирование работы импортов под silent mode
 */
@Ignore("NSDPRD-15330")
@RunWith(Parameterized.class)
@SuppressWarnings({ "rawtypes", "unchecked" })
public class SilentModeDatasourceJdkTest
{
    private class TestClassProcessorFactory extends ClassProcessorFactory
    {
        public TestClassProcessorFactory()
        {
            super(new SpringContext());
        }

        @Override
        public <C extends ClassConfig> ClassProcessorBase<C> create(Engine engine, C config, List<Parameter> params)
        {
            final ClassProcessorBase<C> cClassProcessorBase = (ClassProcessorBase<C>)new ClassProcessor(engine,
                    (MetainfoClassConfig)config, params);
            autowireWithMockOrExplicit(cClassProcessorBase, SilentModeDatasourceJdkTest.this.explicitDependencies);
            return cClassProcessorBase;
        }
    }

    /**
     * Параметры тестов:
     * <table>
     *     <tr>
     *         <td>путь до файла импорта</td>
     *         <td>реализация {@link AbstractDataSourceProvider}</td>
     *         <td>Набор разрешенных ip</td>
     *         <td>Ожидаемый класс ошибки</td>
     *     </tr>
     * </table>
     * Ожидаемые ошибки:
     * <ul>
     *     <li>{@link SilentModeViolationException} если набор разрешенных ip пустой</li>
     *     <li>{@link ConnectException} если набор разрешенных ip не пустой</li>
     * </ul>
     * @return параметры
     */
    @Parameters(name = "DSProvider [{1}] Suitable ips [{2}], Expected exception [{3}]")
    public static Collection<Object[]> data()
    {
        final String localLdapImport = "/ru/naumen/advimport/local-ldap.advimport.xml";
        final LdapDataSourceProviderBean ldapDataSourceProviderBean = new LdapDataSourceProviderBean();

        final String localSqlImport = "/ru/naumen/advimport/local-jdbc.advimport.xml";
        final SQLDataSourceProviderBean sqlDataSourceProviderBean = new SQLDataSourceProviderBean(null, 0, null );

        final Set<String> emptySuitableIps = new HashSet<>();
        final Set<String> localHostIp = new HashSet<>();
        localHostIp.add("localhost");
        return Arrays.asList(new Object[][] {
                { localLdapImport, ldapDataSourceProviderBean, emptySuitableIps, SilentModeViolationException.class },
                { localLdapImport, ldapDataSourceProviderBean, localHostIp, ConnectException.class },
                { localSqlImport, sqlDataSourceProviderBean, emptySuitableIps, SilentModeViolationException.class },
                { localSqlImport, sqlDataSourceProviderBean, localHostIp, ConnectException.class }
        });
    }

    private final XmlUtils xmlUtils = new XmlUtils();
    private final Map<Class<?>, Object> explicitDependencies = new HashMap<>();
    private SilentModeConfiguration silentModeConfiguration;
    private final String importPath;
    private final AbstractDataSourceProvider provider;
    private final Set<String> suitableIps;
    private final Class<? extends Throwable> expectedExceptionClass;

    @Before
    public void setUp() throws Exception
    {
        explicitDependencies.put(ClassProcessorFactory.class, new TestClassProcessorFactory());
        final AdvimportTasksState tasksState = mock(AdvimportTasksState.class);
        explicitDependencies.put(AdvimportTasksState.class, tasksState);

        final AdvImportUtils utils = mock(AdvImportUtils.class);
        when(evaluate(Mockito.any(), Mockito.anyString())).thenCallRealMethod();
        when(evaluate(Mockito.anyString(), Mockito.anyMap())).thenCallRealMethod();
        explicitDependencies.put(AdvImportUtils.class, utils);
        silentModeConfiguration = mock(SilentModeConfiguration.class);
        when(silentModeConfiguration.useLegacy()).thenReturn(false);

        explicitDependencies.put(SilentModeConfiguration.class, silentModeConfiguration);
        when(tasksState.isImportActive(anyString())).thenReturn(true);
        final ConnectionPermissionCheckerImpl permissionChecker = new ConnectionPermissionCheckerImpl();
        suitableIps.forEach(permissionChecker::registerHostname);
        autowireWithMockOrExplicit(permissionChecker, explicitDependencies);
        final NauSecurityManager nauSecurityManager = new NauSecurityManager(permissionChecker,
                mock(JmsConfiguration.class));
        System.setSecurityManager(nauSecurityManager);
    }

    @After
    public void tearDown() throws Exception
    {
        System.setSecurityManager(null);
    }

    public SilentModeDatasourceJdkTest(String importPath, AbstractDataSourceProvider provider,
            Set<String> suitableIps, Class<? extends Throwable> expectedExceptionClass)
    {
        this.importPath = importPath;
        this.provider = provider;
        this.suitableIps = suitableIps;
        this.expectedExceptionClass = expectedExceptionClass;
    }

    @Test
    public void testImport() throws IOException
    {
        if (suitableIps.isEmpty())
        {
            when(silentModeConfiguration.isSilent()).thenReturn(true);
        }
        final DataSourceProviderRegistry dataSourceProvider = mock(DataSourceProviderRegistry.class);
        Mockito.doAnswer(invocation ->
        {
            final Object[] arguments = invocation.getArguments();
            try
            {
                provider.start((ImportContext)arguments[0], (AbstractDataSource)arguments[1]);
            }
            catch (AdvImportException silentModeViolationException)
            {
                final Throwable rootCause = ExceptionUtils.getRootCause(silentModeViolationException);
                Assert.assertEquals("Unexpected root cause", expectedExceptionClass, rootCause.getClass());
            }
            return provider;
        }).when(dataSourceProvider).<LdapDataSource> create(Mockito.any(), Mockito.any());
        explicitDependencies.put(DataSourceProviderRegistry.class, dataSourceProvider);
        final InputStream resourceAsStream = getClass().getResourceAsStream(importPath);
        final String config = IOUtils.toString(resourceAsStream, StandardCharsets.UTF_8);
        final Config parseXml = xmlUtils.parseXml(config, Config.class, false, "/xsd/advimport.xsd");

        final Engine engine = new Engine(parseXml);
        autowireWithMockOrExplicit(engine, explicitDependencies);
        engine.run();
        verify(dataSourceProvider).create(Mockito.any(), Mockito.any());
    }

    private static void autowireWithMockOrExplicit(Object object, Map<Class<?>, Object> explicitDependencies)
    {
        Class<?> clz = object.getClass();
        while (!clz.equals(Object.class))
        {
            for (Field field : clz.getDeclaredFields())
            {
                final Inject inject = field.getAnnotation(Inject.class);
                if (inject != null)
                {
                    final Class<?> type = field.getType();
                    final Object o = explicitDependencies.get(type);
                    if (o != null)
                    {
                        ReflectionTestUtils.setField(object, field.getName(), o);
                    }
                    else if (!Modifier.isFinal(type.getModifiers()))
                    {
                        final Object mock = Mockito.mock(type);
                        ReflectionTestUtils.setField(object, field.getName(), mock);
                    }
                }
            }
            clz = clz.getSuperclass();
        }
    }
}
