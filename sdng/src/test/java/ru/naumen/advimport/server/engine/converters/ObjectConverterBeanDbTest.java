package ru.naumen.advimport.server.engine.converters;

import jakarta.inject.Inject;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.config.AutowireCapableBeanFactory;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.transaction.annotation.Transactional;

import ru.naumen.advimport.server.engine.ImportContext;
import ru.naumen.advimport.shared.config.converters.ObjectConverter;
import ru.naumen.core.server.dispatch.ObjectTestUtils;
import ru.naumen.core.server.dispatch.SecurityTestHelper;
import ru.naumen.core.server.dispatch.SecurityTestUtils;
import ru.naumen.core.server.script.spi.ScriptUtils;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.elements.AttributeType;

/**
 * Проверка ObjectConverter'а на реальной БД.
 * проверка создания конвертера для разных типов атрибутов
 * преобразование UUID объекта в объекта (на примере root$101)
 * <AUTHOR>
 *
 */
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "/ru/naumen/core/server/dispatch/fullContext.xml")
public class ObjectConverterBeanDbTest
{
    @Mock
    private AttributeType type0;
    @Mock
    private AttributeType type1;
    @Mock
    private AttributeType type2;

    @Inject
    SecurityTestUtils securityTestUtils;

    @Inject
    ScriptUtils utils;

    @Inject
    ObjectTestUtils testUtils;

    @Inject
    AutowireCapableBeanFactory beanFactory;

    @Before
    public void setUp()
    {
        MockitoAnnotations.initMocks(this);
        Mockito.when(type0.getCode()).thenReturn(Constants.CatalogItemAttributeType.CODE);
        Mockito.when(type1.getCode()).thenReturn(Constants.ObjectAttributeType.CODE);
        Mockito.when(type2.getCode()).thenReturn(Constants.DoubleAttributeType.CODE);

        SecurityTestHelper.autenticateAsSuperUser();
        securityTestUtils.initLicensing();
    }

    @Test
    public void testConstruct()
    {
        boolean result0 = new ObjectConverterBean.ConverterFactory().canConvert(type0);
        boolean result1 = new ObjectConverterBean.ConverterFactory().canConvert(type1);
        boolean result2 = new ObjectConverterBean.ConverterFactory().canConvert(type2);

        Assert.assertTrue(result0);
        Assert.assertTrue(result1);
        Assert.assertFalse(result2);
    }

    @Test
    @Transactional
    public void testConvert()
    {
        String metaClass = "root";
        String attr = "UUID";

        ImportContext ctx = Mockito.mock(ImportContext.class);

        Mockito.when(ctx.evaluate(Mockito.eq(metaClass))).thenReturn(metaClass);
        Mockito.when(ctx.evaluate(Mockito.eq(attr))).thenReturn(attr);

        ObjectConverter cfg = new ObjectConverter();

        cfg.setMetaClass(metaClass);
        cfg.setAttribute(attr);
        cfg.setRequired(true);

        ObjectConverterBean bean = new ObjectConverterBean(cfg, true, true);
        beanFactory.autowireBean(bean);
        bean.init(ctx);

        IUUIDIdentifiable result = bean.convert(testUtils.getRoot().getUUID(), null);

        Assert.assertNotNull(result);
    }

}
