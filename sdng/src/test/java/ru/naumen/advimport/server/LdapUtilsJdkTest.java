package ru.naumen.advimport.server;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

import org.apache.commons.io.IOUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

import ru.naumen.commons.server.utils.LdapUtils;
import ru.naumen.commons.server.utils.XmlUtils;
import ru.naumen.core.server.ldap.conf.Configuration;

/**
 * Тестирование вспомогательных методов для работы с LDAP.
 *
 * <AUTHOR>
 * @since 03.08.16
 */
public class LdapUtilsJdkTest
{
    private XmlUtils xmlUtils;
    
    @Before
    public void setUp()
    {
        xmlUtils = new XmlUtils();
    }
    
    @Test
    public void testEscapeSpecialCharacters()
    {
        //Проверка замены запятой в названии (замена не производится - остается вид \\,)
        String test = "CN=q\\,w,OU=test,DC=dc1,DC=local";
        String actual = LdapUtils.escapeSpecialCharacters(test);
        Assert.assertEquals("Error escaping value: " + test, "CN=q\\,w,OU=test,DC=dc1,DC=local", actual);

        //Проверка замены знака "=" в названии (замена не производится)
        test = "CN=q\\=w,OU=test,DC=dc1,DC=local";
        actual = LdapUtils.escapeSpecialCharacters(test);
        Assert.assertEquals("Error escaping value: " + test, "CN=q\\=w,OU=test,DC=dc1,DC=local", actual);

        //Проверка замены обратного слеша в названии (замена производится)
        test = "CN=q\\\\,OU=test,DC=dc1,DC=local";
        actual = LdapUtils.escapeSpecialCharacters(test);
        Assert.assertEquals("Error escaping value: " + test, "CN=q\\5C,OU=test,DC=dc1,DC=local", actual);

        //Проверка замены обратного слеша, если после него стоит один из "#+<>;
        //(замена слеша, после которого стоит спец. символ, не производится)
        test = "CN=test,OU=test\\\"\\#\\+\\<\\>\\;,DC=dc1,DC=local";
        actual = LdapUtils.escapeSpecialCharacters(test);
        Assert.assertEquals("Error escaping value: " + test, "CN=test,OU=test\\22\\23\\2B\\3C\\3E\\3B,DC=dc1,DC=local",
                actual);

        //Проверка замены спец символов: "#+<>; (замена проивзодится)
        test = "CN=test,OU=test\"#+<>;,DC=dc1,DC=local";
        actual = LdapUtils.escapeSpecialCharacters(test);
        Assert.assertEquals("Error escaping value: " + test, "CN=test,OU=test\\22\\23\\2B\\3C\\3E\\3B,DC=dc1,DC=local",
                actual);

        //Проверка замены спец символов: "#+<>; (замена проивзодится)
        test = "CN=test\\\\,OU=test\\,OU=test1,DC=dc1,DC=local";
        actual = LdapUtils.escapeSpecialCharacters(test);
        Assert.assertEquals("Error escaping value: " + test, "CN=test\\5C,OU=test\\,OU=test1,DC=dc1,DC=local", actual);
    }

    @Test
    public void testGetParentDN()
    {
        String test = "OU=q\\,OU=w,OU=test,DC=dc1,DC=local";
        String actual = LdapUtils.getParentDN(test);
        Assert.assertEquals("Error getParentDN: " + test, "OU=test,DC=dc1,DC=local", actual);

        test = "CN=q\\\\\\,\\\\,OU=test,DC=dc1,DC=local";
        actual = LdapUtils.getParentDN(test);
        Assert.assertEquals("Error getParentDN: " + test, "OU=test,DC=dc1,DC=local", actual);
    }

    @Test
    public void testPrepareDNStringFromConfig()
    {
        String test = "OU=\"#<>;\\\\+\\,\\=/,DC=dc1,DC=local";
        String actual = LdapUtils.prepareDNStringFromConfig(test);
        Assert.assertEquals("Error getParentDN: " + test, "OU=\\\"\\#\\<\\>\\;\\\\\\+\\,\\=/,DC=dc1,DC=local", actual);
    }

    @Test
    public void testPrepareFilterStringFromConfig()
    {
        String test = "OU=\"#<>;\\\\+\\,\\=/,DC=dc1,DC=local";
        String actual = LdapUtils.prepareFilterStringFromConfig(test);
        Assert.assertEquals("Error getParentDN: " + test,
                "OU=\\\\\"\\\\#\\\\<\\\\>\\\\;\\\\\\\\\\\\+\\\\,\\\\=/,DC=dc1,DC=local", actual);
    }
    
    @Test
    public void testLdapSettingsWithoutSequence() throws IOException
    {
        String content = IOUtils.toString(getClass().getResourceAsStream("ldap-settings.xml"), StandardCharsets.UTF_8);
        xmlUtils.parseXml(content, Configuration.class, false, "/xsd/ldapSettings.xsd");
    }
}
