package ru.naumen.advimport.server.engine.converters;

import static org.junit.Assert.assertEquals;

import java.util.Date;

import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import ru.naumen.advimport.server.engine.ImportContext;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.elements.AttributeType;

/**
 * Конвертация элемента ДатаВремя.
 * 
 * проверяется фабрика конвертера(CanConvert())
 * проверяется работа конвертера 
 * <AUTHOR>
 *
 */
public class DateTimeConverterBeanJdkTest
{
    @Mock
    AttributeType type0;
    @Mock
    AttributeType type1;
    @Mock
    AttributeType type2;
    @Mock
    MessageFacade messages;
    @Mock
    ImportContext ctx;

    @Before
    public void setUp()
    {
        MockitoAnnotations.initMocks(this);

        Mockito.when(type0.getCode()).thenReturn(Constants.DateAttributeType.CODE);
        Mockito.when(type1.getCode()).thenReturn(Constants.DateTimeAttributeType.CODE);
        Mockito.when(type2.getCode()).thenReturn(Constants.StringAttributeType.CODE);
    }

    @Test
    public void testConvert()
    {
        String dateTimePattern = "yyyy-MM-dd HH:mm:ss";
        String datePattern = "yyyy-MM-dd";
        Date date0 = new Date();
        Date date1 = new Date();
        DateTimeFormatter format0 = DateTimeFormat.forPattern(datePattern);
        DateTimeFormatter format1 = DateTimeFormat.forPattern(dateTimePattern);

        String dateString0 = format0.print(date0.getTime());
        String dateString1 = format1.print(date1.getTime());

        DateTimeConverterBean bean0 = new DateTimeConverterBean(type0);
        DateTimeConverterBean bean1 = new DateTimeConverterBean(type1);

        Mockito.when(ctx.evaluate(Mockito.eq(datePattern))).thenReturn(datePattern);
        Mockito.when(ctx.evaluate(Mockito.eq(dateTimePattern))).thenReturn(dateTimePattern);

        bean0.init(ctx);
        bean1.init(ctx);

        Date result0 = bean0.convert(dateString0, null);
        Date result1 = bean1.convert(dateString1, null);

        // 86400000 - 24 часа * 3600 * 1000 - число миллисекунд в сутках
        Assert.assertTrue(date0.getTime() - result0.getTime() < 86400000);
        // 1000 - число миллисекунд в секундах
        Assert.assertTrue(date1.getTime() - result1.getTime() < 1000);
    }

    /**
     * тест на датах для которых для часового пояса Екатеринбурга ещё действовал DST
     */
    @Test
    public void testConvertWhen198x()
    {
        String datePattern = "yyyy-MM-dd";
        DateTimeFormatter format = DateTimeFormat.forPattern(datePattern);
        String dateStr = "1981-04-01";
        Date date = format.parseLocalDateTime(dateStr).toDate();
        DateTimeConverterBean bean = new DateTimeConverterBean(type0);
        Mockito.when(ctx.evaluate(Mockito.eq(datePattern))).thenReturn(datePattern);
        bean.init(ctx);
        assertEquals(date, bean.convert(dateStr, null));
    }

    @Test
    public void testCreation()
    {

        DateTimeConverterBean.Factory factory = new DateTimeConverterBean.Factory();
        boolean value0 = factory.canConvert(type0);
        boolean value1 = factory.canConvert(type1);
        boolean value2 = factory.canConvert(type2);

        Assert.assertTrue(value0);
        Assert.assertTrue(value1);
        Assert.assertFalse(value2);
    }
}
