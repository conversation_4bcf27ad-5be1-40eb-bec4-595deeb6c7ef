package ru.naumen.advimport.server.engine.converters;

import java.io.ByteArrayInputStream;
import java.util.Arrays;
import java.util.Collections;

import org.apache.commons.codec.DecoderException;
import org.apache.commons.codec.binary.Hex;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import ru.naumen.advimport.server.engine.Item;
import ru.naumen.advimport.shared.config.Attribute;
import ru.naumen.advimport.shared.config.converters.ADImageConverter;
import ru.naumen.advimport.shared.config.converters.decoders.ADImageDecoder;
import ru.naumen.core.server.common.AccessorHelper;
import ru.naumen.core.server.filestorage.File;
import ru.naumen.core.server.filestorage.FileContentStorage;
import ru.naumen.core.server.preview.FilePreviewValidationServiceImpl;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.metainfo.server.spi.store.FileDto;

/**
 * Тестирование импорта картинок из АД в режимах: Create, Update
 * <AUTHOR>
 */
public class ADImageConverterBeanJdkTest
{

    private class TestObject implements IUUIDIdentifiable
    {
        private static final long serialVersionUID = -8209262600364103422L;

        @Override
        public String getUUID()
        {
            return "testUuid";
        }
    }

    @Mock
    Item item;
    @Mock
    TestObject object;
    @Mock
    Attribute attr;
    @Mock
    FileContentStorage storage;
    @Mock
    AccessorHelper accessor;
    @Mock
    ADImageDecoder decoder;

    /**
     * Тест проверяет, что при создании объекта конвертер создает объект FileDto и возвращает его.
     * @throws DecoderException
     */
    @Test
    public void adImageConverterForCreateModeTest() throws DecoderException
    {
        ADImageConverter conf = new ADImageConverter();
        ADImageConverterBean converter = new ADImageConverterBean(conf);
        converter.decoder = decoder;
        converter.accessor = accessor;
        converter.filePreview = new FilePreviewValidationServiceImpl();
        String content = "FF\\D8\\FF\\E0\\00";
        FileDto newFile = createFile(content, "picture.jpeg", "image/jpeg");
        FileDto converted = (FileDto)((Iterable<?>)converter.convert(content, item, object, attr)).iterator().next();
        byte[] expected = newFile.getContent().getValue();
        byte[] value = converted.getContent().getValue();

        Assert.assertTrue("Wrong file content", Arrays.equals(expected, value));
        Assert.assertEquals("Wrong file title", newFile.getTitle(), converted.getTitle());

    }

    /**
     * Тест проверяет, что при обновлении объекта конвертер проверяет значение контента "старого файла" и
     * если оно совпадает с декодированным значением нового то конвертер возвращает старый файл,
     *  чтобы редактирование не произошло.
     * @throws DecoderException
     */
    @Test
    public void adImageConverterForUpdateModeTest() throws DecoderException
    {
        ADImageConverter conf = new ADImageConverter();
        ADImageConverterBean converter = new ADImageConverterBean(conf);
        converter.filePreview = new FilePreviewValidationServiceImpl();
        converter.decoder = decoder;
        converter.storage = storage;
        converter.accessor = accessor;
        String title = "picture.jpeg";
        String content = "FF\\D8\\FF\\E0\\00";
        System.out.println(content.toString());
        FileDto newFile = createFile(content, title, "image/jpeg");
        File oldFile = new File();
        oldFile.setTitle(title);
        Mockito.when(storage.getContent(oldFile)).thenReturn(new ByteArrayInputStream(newFile.getContent().getValue()));
        Mockito.when(accessor.getAttributeValue(object, attr.getName())).thenReturn(Collections.singleton(oldFile));
        Assert.assertEquals(converter.convert(content, item, object, attr), Collections.singleton(oldFile));
    }

    /**
     * Тест проверяет, что при обновлении объекта при неизменном контенте происходит обновление названия файла.
     * @throws DecoderException
     */
    @Test
    public void adImageConverterTitleChangeTest() throws DecoderException
    {
        String newTitle = "newTitle.jpg";
        String oldTitle = "oldTitle.jpg";
        ADImageConverter conf = new ADImageConverter();
        conf.setTitle(newTitle);
        ADImageConverterBean converter = new ADImageConverterBean(conf);
        converter.filePreview = new FilePreviewValidationServiceImpl();
        converter.decoder = decoder;
        converter.accessor = accessor;
        String content = "FF\\D8\\FF\\E0\\00";
        FileDto newFile = createFile(content, oldTitle, "image/jpeg");
        FileDto converted = (FileDto)((Iterable<?>)converter.convert(content, item, object, attr)).iterator().next();
        byte[] expected = newFile.getContent().getValue();
        byte[] value = converted.getContent().getValue();
        Assert.assertTrue("Wrong file content", Arrays.equals(expected, value));
        Assert.assertFalse("Files' titles are equal.", newFile.getTitle().equals(converted.getTitle()));
    }

    @Before
    public void setUp()
    {
        MockitoAnnotations.initMocks(this);
        Mockito.when(decoder.decode(Mockito.anyString(), Mockito.anyString())).thenCallRealMethod();
    }

    @After
    public void tearDown()
    {

    }

    private FileDto createFile(String value, String title, String mimeType) throws DecoderException
    {
        FileDto newFile = new FileDto();
        newFile.getContent().setValue(Hex.decodeHex(value.replace("\\", "").toCharArray()));
        newFile.setTitle(title);
        newFile.setMimeType(mimeType);
        return newFile;
    }

}
