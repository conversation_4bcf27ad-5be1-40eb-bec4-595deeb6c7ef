package ru.naumen.advimport.server.engine.converters;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

import jakarta.inject.Inject;

import org.apache.commons.fileupload2.core.FileItem;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.config.AutowireCapableBeanFactory;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.transaction.annotation.Transactional;

import ru.naumen.advimport.server.engine.DefaultItem;
import ru.naumen.advimport.server.engine.ImportContext;
import ru.naumen.advimport.shared.config.converters.FileContentConverter;

/**
 * Проверка конвертера файлов: создать файл загрузить его конвертером в хранилище, получить из хранилища и сравнить с оригиналом.
 * <AUTHOR>
 *
 */
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "/ru/naumen/core/server/dispatch/fullContext.xml")
public class FileContentConverterBeanDbTest
{
    @Inject
    AutowireCapableBeanFactory beanFactory;

    @Test
    @Transactional
    public void testConvert()
    {
        String titleColumn = "title";
        String mimeTypeColumn = "mimeType";

        ImportContext ctx = Mockito.mock(ImportContext.class);
        Mockito.when(ctx.evaluate(Mockito.eq(titleColumn))).thenReturn(titleColumn);
        Mockito.when(ctx.evaluate(Mockito.eq(mimeTypeColumn))).thenReturn(mimeTypeColumn);

        String contentType = "application/octet-stream";
        String fileContent = "FileContentConverterbean test";
        String templFileName = null;
        File tempFile;
        try
        {
            tempFile = File.createTempFile("junit", "");
            templFileName = tempFile.getAbsolutePath();
            FileOutputStream fos = new FileOutputStream(tempFile);
            fos.write(fileContent.getBytes(StandardCharsets.UTF_8));
            fos.flush();
            fos.close();

            FileContentConverter conf = new FileContentConverter();
            conf.setMimeTypeColumn(mimeTypeColumn);
            conf.setTitleColumn(titleColumn);

            FileContentConverterBean bean = new FileContentConverterBean(conf);

            DefaultItem converterItem = new DefaultItem("id");
            converterItem.getProperties().setProperty(titleColumn, tempFile.getName());
            converterItem.getProperties().setProperty(mimeTypeColumn, contentType);

            bean.init(ctx);
            beanFactory.autowireBean(bean);
            String uuid = bean.convert(templFileName, converterItem);

            FileItem item = bean.uploadService.get(uuid);
            Assert.assertEquals(tempFile.getName(), item.getName());
            Assert.assertEquals(contentType, item.getContentType());
            Assert.assertArrayEquals(fileContent.getBytes(StandardCharsets.UTF_8), item.get());

            tempFile.deleteOnExit();
        }
        catch (IOException e)
        {
            e.printStackTrace();
        }

    }

}
