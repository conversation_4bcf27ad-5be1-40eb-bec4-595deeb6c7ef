package ru.naumen.advimport.server.engine.converters;

import java.util.Arrays;
import java.util.List;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.transaction.annotation.Transactional;

import ru.naumen.advimport.server.engine.ImportContext;
import ru.naumen.advimport.server.engine.Item;
import ru.naumen.advimport.shared.config.converters.CollectionConverter;
import ru.naumen.advimport.shared.config.converters.ObjectConverter;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.elements.AttributeType;

/**
 * Тест CollectionConverter'а.
 * Т.к. данный конвертер использует ObjectConverter для преобразования элементов коллекции, тест синтетический, 
 * проверяет что ObjectConveter вызывается и получает что нужно
 * 
 * проверяется фабрика конвертера (CanConvert())
 * проверяется преобразование
 * <AUTHOR>
 *
 */
public class CollectionConverterBeanJdkTest
{
    @Mock
    private AttributeType type0;
    @Mock
    private AttributeType type1;
    @Mock
    private AttributeType type2;
    @Mock
    private ConverterFactory converterFactory;

    @Before
    public void setUp()
    {
        MockitoAnnotations.initMocks(this);
        Mockito.when(type0.getCode()).thenReturn(Constants.CatalogItemsAttributeType.CODE);
        Mockito.when(type1.getCode()).thenReturn(Constants.BOLinksAttributeType.CODE);
        Mockito.when(type2.getCode()).thenReturn(Constants.IntegerAttributeType.CODE);
    }

    @Test
    public void testConstruct()
    {
        boolean result0 = new CollectionConverterBean.Factory().canConvert(type0);
        boolean result1 = new CollectionConverterBean.Factory().canConvert(type1);
        boolean result2 = new CollectionConverterBean.Factory().canConvert(type2);

        Assert.assertTrue(result0);
        Assert.assertTrue(result1);
        Assert.assertFalse(result2);
    }

    /**
     * Проверка конвертера на одном объекте в виде списка.
     */
    @SuppressWarnings({ "unchecked", "rawtypes" })
    @Test
    public void testConvertCollection()
    {
        IUUIDIdentifiable convertResult = Mockito.mock(IUUIDIdentifiable.class);
        IUUIDIdentifiable convertResult2 = Mockito.mock(IUUIDIdentifiable.class);

        ImportContext ctx = Mockito.mock(ImportContext.class);
        String sampleUUID = "test$1234";
        String sampleUUID2 = "test$09876";

        String metaClass = "root";
        String attr = "UUID";

        ObjectConverterBean subConverter = Mockito.mock(ObjectConverterBean.class);
        ObjectConverter subCfg = new ObjectConverter();

        subCfg.setMetaClass(metaClass);
        subCfg.setAttribute(attr);
        subCfg.setRequired(true);

        CollectionConverter cfg = new CollectionConverter();
        String delim = ",";
        cfg.setDelimiter(delim);

        cfg.getConvertors().add(subCfg);

        Mockito.when(converterFactory.get(Mockito.eq(ctx), Mockito.eq(subCfg))).thenReturn((Converter)subConverter);
        Mockito.when(subConverter.convert(Mockito.eq(sampleUUID), (Item)Mockito.eq(null))).thenReturn(convertResult);
        Mockito.when(subConverter.convert(Mockito.eq(sampleUUID2), (Item)Mockito.eq(null)))
                .thenReturn(convertResult2);
        Mockito.when(ctx.evaluate(Mockito.eq(delim))).thenReturn(delim);

        CollectionConverterBean bean = new CollectionConverterBean(cfg);
        bean.converterFactory = converterFactory;
        bean.init(ctx);

        List<String> toConvert = Arrays.asList(sampleUUID, sampleUUID2);
        List<IUUIDIdentifiable> result = (List<IUUIDIdentifiable>)bean.convert(toConvert, null);

        Mockito.verify(subConverter, Mockito.times(1)).convert(Mockito.eq(sampleUUID), (Item)Mockito.eq(null));
        Mockito.verify(subConverter, Mockito.times(1)).convert(Mockito.eq(sampleUUID2), (Item)Mockito.eq(null));
        Assert.assertEquals(2, result.size());
        Assert.assertEquals(convertResult, result.get(0));
        Assert.assertEquals(convertResult2, result.get(1));
    }

    /**
     * Проверка конвертера на двух объектах
     */
    @SuppressWarnings({ "unchecked", "rawtypes" })
    @Test
    public void testConvertMultiple()
    {
        IUUIDIdentifiable convertResult = Mockito.mock(IUUIDIdentifiable.class);
        IUUIDIdentifiable convertResult2 = Mockito.mock(IUUIDIdentifiable.class);

        ImportContext ctx = Mockito.mock(ImportContext.class);
        String sampleUUID = "test$1234";
        String sampleUUID2 = "test$09876";

        String metaClass = "root";
        String attr = "UUID";

        ObjectConverterBean subConverter = Mockito.mock(ObjectConverterBean.class);
        ObjectConverter subCfg = new ObjectConverter();

        subCfg.setMetaClass(metaClass);
        subCfg.setAttribute(attr);
        subCfg.setRequired(true);

        CollectionConverter cfg = new CollectionConverter();
        String delim = ",";
        cfg.setDelimiter(delim);

        cfg.getConvertors().add(subCfg);

        Mockito.when(converterFactory.get(Mockito.eq(ctx), Mockito.eq(subCfg))).thenReturn((Converter)subConverter);
        Mockito.when(subConverter.convert(Mockito.eq(sampleUUID), (Item)Mockito.eq(null))).thenReturn(convertResult);
        Mockito.when(subConverter.convert(Mockito.eq(sampleUUID2), (Item)Mockito.eq(null)))
                .thenReturn(convertResult2);
        Mockito.when(ctx.evaluate(Mockito.eq(delim))).thenReturn(delim);

        CollectionConverterBean bean = new CollectionConverterBean(cfg);
        bean.converterFactory = converterFactory;
        bean.init(ctx);

        List<IUUIDIdentifiable> result = (List<IUUIDIdentifiable>)bean.convert(
                String.format("%s,%s", sampleUUID, sampleUUID2), null);

        Mockito.verify(subConverter, Mockito.times(1)).convert(Mockito.eq(sampleUUID), (Item)Mockito.eq(null));
        Mockito.verify(subConverter, Mockito.times(1)).convert(Mockito.eq(sampleUUID2), (Item)Mockito.eq(null));
        Assert.assertEquals(2, result.size());
        Assert.assertEquals(convertResult, result.get(0));
        Assert.assertEquals(convertResult2, result.get(1));
    }

    /**
     * Проверка конвертера на одном объекте
     */
    @SuppressWarnings({ "unchecked", "rawtypes" })
    @Test
    @Transactional
    public void testConvertSingle()
    {
        IUUIDIdentifiable convertResult = Mockito.mock(IUUIDIdentifiable.class);

        ImportContext ctx = Mockito.mock(ImportContext.class);
        String sampleUUID = "test$1234";

        String metaClass = "root";
        String attr = "UUID";

        ObjectConverterBean subConverter = Mockito.mock(ObjectConverterBean.class);
        ObjectConverter subCfg = new ObjectConverter();

        subCfg.setMetaClass(metaClass);
        subCfg.setAttribute(attr);
        subCfg.setRequired(true);

        CollectionConverter cfg = new CollectionConverter();
        String delim = ",";
        cfg.setDelimiter(delim);

        cfg.getConvertors().add(subCfg);

        Mockito.when(converterFactory.get(Mockito.eq(ctx), Mockito.eq(subCfg))).thenReturn((Converter)subConverter);
        Mockito.when(subConverter.convert(Mockito.eq(sampleUUID), (Item)Mockito.eq(null))).thenReturn(convertResult);
        Mockito.when(ctx.evaluate(Mockito.eq(delim))).thenReturn(delim);

        CollectionConverterBean bean = new CollectionConverterBean(cfg);
        bean.converterFactory = converterFactory;
        bean.init(ctx);

        List<IUUIDIdentifiable> result = (List<IUUIDIdentifiable>)bean.convert(sampleUUID, null);

        Mockito.verify(subConverter, Mockito.times(1)).convert(Mockito.eq(sampleUUID), (Item)Mockito.eq(null));
        Assert.assertEquals(1, result.size());
        Assert.assertEquals(convertResult, result.get(0));
    }
}
