package ru.naumen.advimport.server.engine.converters;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import ru.naumen.advimport.shared.config.converters.StringConverter;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.elements.AttributeType;

/**
 * Тест StringConverter'а.
 * 
 * проверка создания конвертера для разных типов атрибутов
 * проверка преобразование строки в строку
 * <AUTHOR>
 *
 */
public class StringConverterBeanJdkTest
{
    private final StringConverter cfg = new StringConverter();
    @Mock
    private AttributeType type0;
    @Mock
    private AttributeType type1;

    @Before
    public void setUp()
    {
        MockitoAnnotations.initMocks(this);
        Mockito.when(type0.getCode()).thenReturn(Constants.StringAttributeType.CODE);
        Mockito.when(type1.getCode()).thenReturn(Constants.IntegerAttributeType.CODE);
    }

    @Test
    public void testConstruct()
    {
        StringConverterBean.Factory factory = new StringConverterBean.Factory();
        boolean value0 = factory.canConvert(type0);
        boolean value1 = factory.canConvert(type1);

        Assert.assertTrue(value0);
        Assert.assertFalse(value1);
    }

    @Test
    public void testConvert()
    {
        cfg.setTrim(true);
        StringConverterBean bean0 = (StringConverterBean)new StringConverterBean.Factory().create(cfg);

        String result0 = bean0.convert(" test ", null);
        Assert.assertEquals("test", result0);

        cfg.setTrim(false);
        StringConverterBean bean1 = (StringConverterBean)new StringConverterBean.Factory().create(cfg);

        String result1 = bean1.convert(" test ", null);
        Assert.assertEquals(" test ", result1);
    }
}
