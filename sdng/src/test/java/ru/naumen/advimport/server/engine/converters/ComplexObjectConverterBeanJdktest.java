package ru.naumen.advimport.server.engine.converters;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import ru.naumen.advimport.server.engine.ImportContext;
import ru.naumen.advimport.server.engine.Item;
import ru.naumen.advimport.shared.config.converters.ComplexObjectConverter;
import ru.naumen.advimport.shared.config.converters.ObjectConverter;
import ru.naumen.advimport.shared.config.converters.ScriptConverter;
import ru.naumen.core.shared.IUUIDIdentifiable;

/**
 * Тест ComplexObjectConverter'а
 * Конвертер использует ScriptConverter и ObjectConverter. Тест синтетический проверяет что оба конвертера были использованы.
 * 
 * <AUTHOR>
 *
 */
public class ComplexObjectConverterBeanJdktest
{
    @Mock
    private ConverterFactory converterFactory;

    @Before
    public void setUp()
    {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * Проверка что конвертер вызовет convert() у двух дочерних конвертеров
     */
    @SuppressWarnings({ "unchecked", "rawtypes" })
    @Test
    public void testConvert()
    {
        IUUIDIdentifiable convertResult = Mockito.mock(IUUIDIdentifiable.class);

        ScriptConverterBean converter1 = Mockito.mock(ScriptConverterBean.class);
        ObjectConverterBean converter2 = Mockito.mock(ObjectConverterBean.class);

        ImportContext ctx = Mockito.mock(ImportContext.class);
        String sampleUUID = "test$1234";

        String metaClass = "root";
        String attr = "UUID";

        ScriptConverter subCfg1 = new ScriptConverter();
        ObjectConverter subCfg2 = new ObjectConverter();

        subCfg2.setMetaClass(metaClass);
        subCfg2.setAttribute(attr);
        subCfg2.setRequired(true);

        Mockito.when(converterFactory.get(Mockito.eq(ctx), Mockito.eq(subCfg1))).thenReturn((Converter)converter1);
        Mockito.when(converterFactory.get(Mockito.eq(ctx), Mockito.eq(subCfg2))).thenReturn((Converter)converter2);
        Mockito.when(converter1.convert(Mockito.eq(sampleUUID), (Item)Mockito.eq(null))).thenReturn(null);
        Mockito.when(converter2.convert(Mockito.eq(sampleUUID), (Item)Mockito.eq(null))).thenReturn(convertResult);

        ComplexObjectConverter cfg = new ComplexObjectConverter();
        cfg.getConvertors().add(subCfg1);
        cfg.getConvertors().add(subCfg2);

        ComplexObjectConverterBean bean = new ComplexObjectConverterBean(cfg);
        bean.convertorFactory = converterFactory;
        bean.init(ctx);
        IUUIDIdentifiable result = bean.convert(sampleUUID, null);

        // проверка что каждый из дочерних конвертеров получил вызов convert()
        Mockito.verify(converter1, Mockito.times(1)).convert(Mockito.eq(sampleUUID), (Item)Mockito.eq(null));
        Mockito.verify(converter2, Mockito.times(1)).convert(Mockito.eq(sampleUUID), (Item)Mockito.eq(null));

        Assert.assertEquals(convertResult, result);
    }

}
