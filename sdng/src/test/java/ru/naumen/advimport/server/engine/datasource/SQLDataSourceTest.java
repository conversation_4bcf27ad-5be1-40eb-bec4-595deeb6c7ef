package ru.naumen.advimport.server.engine.datasource;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.SQLException;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import ru.naumen.advimport.shared.config.datasource.SQLDataSource;
import ru.naumen.core.server.EncryptedValueProvider;
import ru.naumen.core.server.SpringContext;
import ru.naumen.sec.server.encryption.DbPasswordProvider;

/**
 * Класс для тестирования источника данных, сформированного SQL-запросом
 * <AUTHOR>
 * @since 13.11.2013
 */
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "/ru/naumen/core/server/dispatch/fullContext.xml")
public class SQLDataSourceTest
{
    @Inject
    SpringContext context;

    Connection conn;
    PreparedStatement stmt;
    SQLDataSourceProviderBean provider;
    String query;
    SQLDataSource conf;

    @Value("${db.driver}")
    String dbDriver;
    @Value("${db.url}")
    String dbURL;
    @Value("${db.user}")
    String dbUser;

    @Inject
    @Named(DbPasswordProvider.NAME)
    EncryptedValueProvider dbPasswd;

    @Before
    public void setUp() throws SQLException
    {
        context.createBean(SQLDataSourceProviderBean.class);

        conn = DriverManager.getConnection(dbURL, dbUser, dbPasswd.get());
        provider.connection = conn;

        query = "select * from ?";

        conf = new SQLDataSource();
        conf.setQuery(query);
        provider.config = conf;
    }

    /**
     * Тестирование параметризации SQL-запроса скриптом, заключенным в тегах script
     * @throws SQLException
     */
    @Test
    public void testPrepareStatementByScript() throws SQLException
    {
        String scriptBody = "query.setString(1, 'test');";
        conf.setScript(scriptBody);

        PreparedStatement stmt1 = provider.createStatement();
        stmt = conn.prepareStatement(query);
        Assert.assertFalse(stmt1.toString().equals(stmt.toString()));
        stmt.setString(1, "test");
        Assert.assertTrue(stmt1.toString().equals(stmt.toString()));
    }

    /**
     * Тестирование формирования SQL-запроса без скрипта.
     * @throws SQLException
     */
    @Test
    public void testPrepareStatementWithEmptyScript() throws SQLException
    {
        conf.setScript("");

        PreparedStatement stmt1 = provider.createStatement();
        stmt = conn.prepareStatement(query);
        Assert.assertTrue(stmt1.toString().equals(stmt.toString()));
    }
}
