package ru.naumen.advimport.server.engine.converters;

import java.math.BigDecimal;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import ru.naumen.advimport.shared.config.converters.DoubleConverter;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.elements.AttributeType;

/**
 * Проверка Double конвертера.
 * проверка возможности создать конвертер для разных типов атрибутов
 * проверка конвертации
 * <AUTHOR>
 *
 */
public class DoubleConverterBeanJdkTest
{
    @Mock
    private AttributeType type0;
    @Mock
    private AttributeType type1;
    @Mock
    private AttributeType type2;
    @Mock
    private DoubleConverter cfg;

    @Before
    public void setUp()
    {
        MockitoAnnotations.initMocks(this);
        Mockito.when(type0.getCode()).thenReturn(Constants.BooleanAttributeType.CODE);
        Mockito.when(type1.getCode()).thenReturn(Constants.StringAttributeType.CODE);
        Mockito.when(type2.getCode()).thenReturn(Constants.DoubleAttributeType.CODE);
    }

    @Test
    public void testConstruct()
    {
        DoubleConverterBean.Factory factory = new DoubleConverterBean.Factory();

        boolean value0 = factory.canConvert(type0);
        boolean value1 = factory.canConvert(type1);
        boolean value2 = factory.canConvert(type2);

        Assert.assertFalse(value0);
        Assert.assertFalse(value1);
        Assert.assertTrue(value2);
    }

    @Test
    public void testConvert()
    {
        DoubleConverterBean.Factory factory = new DoubleConverterBean.Factory();
        DoubleConverterBean bean = (DoubleConverterBean)factory.create(cfg);

        Double value0 = bean.convert("01.23", null);
        Double value1 = bean.convert(12.34, null);
        Double value2 = bean.convert(new BigDecimal(3.14), null);

        Assert.assertEquals((Double)1.23, value0);
        Assert.assertEquals((Double)12.34, value1);
        Assert.assertEquals((Double)3.14, value2);
    }
}
