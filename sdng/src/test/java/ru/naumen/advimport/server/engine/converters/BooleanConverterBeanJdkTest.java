package ru.naumen.advimport.server.engine.converters;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import ru.naumen.advimport.shared.config.converters.BooleanConverter;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.elements.AttributeType;

/**
 * Тест на работу BooleanConvert'а.
 * 
 * проверяется фабрика конвертера (CanConvert())
 * проверяется непосредственное преобразование текста в boolean при различных настройках
 * <AUTHOR>
 *
 */
public class BooleanConverterBeanJdkTest
{
    @Mock
    private AttributeType type0;
    @Mock
    private AttributeType type1;

    @Before
    public void setUp()
    {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testCanConvert()
    {
        Mockito.when(type0.getCode()).thenReturn(Constants.BooleanAttributeType.CODE);
        Mockito.when(type1.getCode()).thenReturn(Constants.StringAttributeType.CODE);

        BooleanConverterBean.Factory beanFactory = new BooleanConverterBean.Factory();
        boolean value0 = beanFactory.canConvert(type0);
        boolean value1 = beanFactory.canConvert(type1);

        Assert.assertTrue(value0);
        Assert.assertFalse(value1);
    }

    /**
     * Проверка настройки, где значению true соответсвует "yeah":
     * проверка регистрозависимости trueValue
     * проверка конвертации текста не равного trueValue 
     */
    @Test
    public void testTrueValue1()
    {
        BooleanConverter conf = new BooleanConverter();
        conf.setTrueValue("yeah");

        BooleanConverterBean bean = new BooleanConverterBean(conf);

        Assert.assertFalse(bean.convert("no-no", null));
        Assert.assertTrue(bean.convert("yeah", null));
        Assert.assertFalse(bean.convert("Yeah", null));
    }

    /**
     * Проверка настройки, где значению true соответсвует "yeah":
     * проверка регистрозависимости trueValue
     * проверка конвертации текста не равного trueValue 
     */
    public void testTrueValue2()
    {
        BooleanConverter conf = new BooleanConverter();
        conf.setTrueValue("true");

        BooleanConverterBean bean = new BooleanConverterBean(conf);

        Assert.assertFalse(bean.convert("no-way", null));
        Assert.assertTrue(bean.convert("true", null));

    }
}
