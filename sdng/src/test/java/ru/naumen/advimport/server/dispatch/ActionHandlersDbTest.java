package ru.naumen.advimport.server.dispatch;

import java.io.InputStream;
import java.util.Collection;
import java.util.Collections;

import jakarta.transaction.Transactional;

import org.apache.commons.io.IOUtils;
import org.junit.After;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

import com.google.common.collect.Lists;

import jakarta.inject.Inject;
import net.customware.gwt.dispatch.server.Dispatch;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.advimport.AdvImportTestHelper;
import ru.naumen.advimport.server.Constants;
import ru.naumen.advimport.shared.AdvImportConfigContainer;
import ru.naumen.advimport.shared.ImportConfigContainer;
import ru.naumen.advimport.shared.dispatch.AddConfigurationAction;
import ru.naumen.advimport.shared.dispatch.DelConfigurationAction;
import ru.naumen.advimport.shared.dispatch.EditConfigurationAction;
import ru.naumen.advimport.shared.dispatch.GetConfigurationListAction;
import ru.naumen.advimport.shared.dispatch.GetConfigurationListResponse;
import ru.naumen.advimport.shared.dispatch.GetConfigurationResponse;
import ru.naumen.advimport.shared.dispatch.RunImportAction;
import ru.naumen.common.CreatedListener;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.dispatch.SecurityTestHelper;
import ru.naumen.core.server.filestorage.File;
import ru.naumen.core.server.filestorage.FileDao;
import ru.naumen.core.server.jta.TransactionRunner;
import ru.naumen.core.server.metastorage.MetaStorageException;
import ru.naumen.core.server.metastorage.impl.metainfo.MetaStorageService;
import ru.naumen.core.server.upload.NestedFileItem;
import ru.naumen.core.server.upload.UploadService;
import ru.naumen.core.shared.common.I18nUtil;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.core.shared.utils.UUIDGenerator;
import ru.naumen.metainfo.server.spi.MetainfoServiceBean;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.ui.LocalizedString;

/**
 *
 * <AUTHOR>
 *
 */
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class ActionHandlersDbTest
{
    @Inject
    Dispatch dispatch;
    @Inject
    MetaStorageService metaStorageService;
    @Inject
    FileDao fileDao;
    @Inject
    UploadService uploadService;
    @Inject
    CreatedListener createdListener;
    @Inject
    PlatformTransactionManager txManager;
    @Inject
    MetainfoServiceBean metainfoService;
    @Inject
    I18nUtil i18nUtil;

    TransactionTemplate tt;

    String goodConfig;

    // @formatter:off
    String goodConfig2 = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>" +
            "<config xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" save-log=\"false\" threads-number=\"1\" >" +
            "<mode>CREATE</mode>" +
            "<gui-parameter name=\"idHolder\" type=\"STRING\" title=\"File for import\" />" +
            "<class name=\"ou\" threads-number=\"4\">" +
            "<csv-data-source with-header=\"true\" file-name=\"file\">" +
            "</csv-data-source>" +
            "<constant-metaclass-resolver metaclass=\"ou\"/>" +
            "<object-searcher attr=\"${idHolder}\" metaclass=\"ou\"/>" +
            "<attr name=\"idHolder\" column=\"id\" />" +
            "</class></config>";
    // @formatter:on

    // @formatter:off
    String badConfig = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>" +
            "<config xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" save-log=\"false\" threads-number=\"1\" >" +
            "<mode>CREATE</mode>" +
            "</config>";
    // @formatter:on

    @Inject
    AdvImportTestHelper helper;

    ClassFqn ouFqn = ClassFqn.parse("ou$ouImportTest");

    @Test(expected = Exception.class)
    public void addBad() throws Exception
    {
        // настройка системы
        final String uuid = UUIDGenerator.get().nextUUID();
        final String title = UUIDGenerator.get().nextUUID();
        // вызов системы
        AddConfigurationAction a = new AddConfigurationAction(uuid, title, badConfig);
        dispatch.execute(a);
        // проверка утверждений
        // очистка
    }

    @Test(expected = FxException.class)
    public void addBadConf() throws Exception
    {
        // настройка системы
        final String uuid = UUIDGenerator.get().nextUUID();
        final String title = UUIDGenerator.get().nextUUID();
        // вызов системы
        AddConfigurationAction a = new AddConfigurationAction(uuid, title, "");
        dispatch.execute(a);
        // проверка утверждений
        // очистка
    }

    @Test(expected = FxException.class)
    public void addBadTitle() throws Exception
    {
        // настройка системы
        final String uuid = UUIDGenerator.get().nextUUID();
        // вызов системы
        AddConfigurationAction a = new AddConfigurationAction(uuid, "", badConfig);
        dispatch.execute(a);
        // проверка утверждений
        // очистка
    }

    @Test(expected = FxException.class)
    public void addBadUuid() throws Exception
    {
        // настройка системы
        // Только пользователю naumen разрешено использовать спецсимволы,
        // при этом код должен обязательно начинаться с буквы.
        final String uuid = "a`@#$^&*()_=-[]{};\"|,.<>/\\?";
        // вызов системы
        AddConfigurationAction a = new AddConfigurationAction(uuid, "", badConfig);
        dispatch.execute(a);
        // проверка утверждений
        // очистка
    }

    @Test
    public void addGood() throws Exception
    {
        // настройка системы
        final String uuid = UUIDGenerator.get().nextUUID();
        final String title = UUIDGenerator.get().nextUUID();
        // вызов системы
        AddConfigurationAction a = new AddConfigurationAction(uuid, title, goodConfig);
        GetConfigurationResponse response = dispatch.execute(a);
        // проверка утверждений
        ImportConfigContainer cnt =
                metaStorageService.get(Constants.ADVIMPORT_METASTORAGE_TYPE, response.get().get().getUUID());
        org.junit.Assert.assertEquals(response.get().get().getUUID(), cnt.getUUID());
        org.junit.Assert.assertEquals(title, i18nUtil.getLocalizedTitle(cnt));
        org.junit.Assert.assertEquals(goodConfig, cnt.getConfigContainer().getConfig());
        // очистка
        metaStorageService.remove(Constants.ADVIMPORT_METASTORAGE_TYPE, response.get().get().getUUID());
    }

    @Test(expected = FxException.class)
    public void addNonUnique() throws DispatchException
    {
        // настройка системы
        final String uuid = UUIDGenerator.get().nextUUID();
        final String title = UUIDGenerator.get().nextUUID();
        final AddConfigurationAction addAction = new AddConfigurationAction(uuid, title, "");
        dispatch.execute(addAction);
        // вызов системы
        dispatch.execute(addAction);
        // проверка утверждений
        // очистка
    }

    @Test(expected = MetaStorageException.class)
    public void delExists() throws Exception
    {
        // настройка системы
        String uuid = UUIDGenerator.get().nextUUID();
        String title = UUIDGenerator.get().nextUUID();

        ImportConfigContainer cnt = new ImportConfigContainer(uuid,
                new LocalizedString(metainfoService.getCurrentLang(), title), new AdvImportConfigContainer(goodConfig));
        metaStorageService.save(cnt, Constants.ADVIMPORT_METASTORAGE_TYPE, uuid);
        // вызов системы
        dispatch.execute(new DelConfigurationAction(Lists.newArrayList(uuid)));
        // проверка утверждений
        metaStorageService.get(Constants.ADVIMPORT_METASTORAGE_TYPE, uuid);
        // очистка
    }

    @Test
    public void delNotExists() throws Exception
    {
        // настройка системы
        String uuid = UUIDGenerator.get().nextUUID();
        // вызов системы
        dispatch.execute(new DelConfigurationAction(Lists.newArrayList(uuid)));
        // проверка утверждений
        // очистка
    }

    @Transactional
    @Test(expected = Exception.class)
    public void editBad() throws Exception
    {
        // настройка системы
        String uuid = UUIDGenerator.get().nextUUID();
        String title = UUIDGenerator.get().nextUUID();

        ImportConfigContainer cnt = new ImportConfigContainer(uuid,
                new LocalizedString(metainfoService.getCurrentLang(), title), new AdvImportConfigContainer(goodConfig));
        metaStorageService.save(cnt, Constants.ADVIMPORT_METASTORAGE_TYPE, uuid);
        // вызов системы
        EditConfigurationAction a = new EditConfigurationAction(uuid, UUIDGenerator.get().nextUUID(), badConfig);
        dispatch.execute(a);
        // проверка утверждений
        // очистка
    }

    @Test
    public void editGood() throws Exception
    {
        // настройка системы
        String uuid = UUIDGenerator.get().nextUUID();
        String title = UUIDGenerator.get().nextUUID();

        ImportConfigContainer cnt = new ImportConfigContainer(uuid,
                new LocalizedString(metainfoService.getCurrentLang(), title), new AdvImportConfigContainer(goodConfig));
        metaStorageService.save(cnt, Constants.ADVIMPORT_METASTORAGE_TYPE, uuid);
        // вызов системы
        EditConfigurationAction a = new EditConfigurationAction(uuid, UUIDGenerator.get().nextUUID(), goodConfig2);
        dispatch.execute(a);
        // проверка утверждений
        cnt = metaStorageService.get(Constants.ADVIMPORT_METASTORAGE_TYPE, uuid);
        org.junit.Assert.assertEquals(uuid, cnt.getUUID());
        org.junit.Assert.assertEquals(a.getTitle(), i18nUtil.getLocalizedTitle(cnt));
        org.junit.Assert.assertEquals(goodConfig2, cnt.getConfigContainer().getConfig());
        // очистка
        metaStorageService.remove(Constants.ADVIMPORT_METASTORAGE_TYPE, uuid);
    }

    @Test
    public void getExists() throws Exception
    {
        // настройка системы
        String uuid = UUIDGenerator.get().nextUUID();
        String title = UUIDGenerator.get().nextUUID();

        ImportConfigContainer cnt = new ImportConfigContainer(uuid,
                new LocalizedString(metainfoService.getCurrentLang(), title), new AdvImportConfigContainer(goodConfig));
        metaStorageService.save(cnt, Constants.ADVIMPORT_METASTORAGE_TYPE, uuid);
        // вызов системы
        GetConfigurationListResponse response = dispatch.execute(new GetConfigurationListAction());
        // проверка утверждений
        org.junit.Assert.assertTrue(response.get().size() >= 1);
        // очистка
    }

    @Ignore("NSDWRK-27553")
    @Test
    public void runImport() throws Exception
    {
        // настройка системы
        final String uuid = UUIDGenerator.get().nextUUID();
        String title = UUIDGenerator.get().nextUUID();

        ImportConfigContainer cnt = new ImportConfigContainer(uuid,
                new LocalizedString(metainfoService.getCurrentLang(), title), new AdvImportConfigContainer(goodConfig));
        metaStorageService.save(cnt, Constants.ADVIMPORT_METASTORAGE_TYPE, uuid);
        // вызов системы
        dispatch.execute(new RunImportAction(uuid, IProperties.EMPTY));
        // проверка утверждений
        Thread.sleep(5000); // ожидаем прохождение импорта в паралелльном потоке
        Collection<File> files = getFiles(uuid);
        org.junit.Assert.assertEquals("Должны сохранить результат импорта файлов", 1, files.size());
        // очистка
    }

    @Test(expected = MetaStorageException.class)
    public void runImportNotExists() throws Exception
    {
        // настройка системы
        String uuid = UUIDGenerator.get().nextUUID();
        // вызов системы
        dispatch.execute(new RunImportAction(uuid, IProperties.EMPTY));
        // проверка утверждений
        // очистка
    }

    @Ignore("NSDWRK-27553")
    @Test
    public void runImportUploaded() throws Exception
    {
        final String uuid = UUIDGenerator.get().nextUUID();
        String title = UUIDGenerator.get().nextUUID();

        InputStream is = getClass().getResourceAsStream("/ru/naumen/advimport/runWithParams.advimport.xml");
        ImportConfigContainer cnt = new ImportConfigContainer(uuid,
                new LocalizedString(metainfoService.getCurrentLang(), title),
                new AdvImportConfigContainer(IOUtils.toString(is)));
        metaStorageService.save(cnt, Constants.ADVIMPORT_METASTORAGE_TYPE, uuid);

        InputStream is2 = getClass().getResourceAsStream("/ru/naumen/advimport/runWithParams.test.csv");
        NestedFileItem fileItem = new NestedFileItem(IOUtils.toString(is2), "import", "text/plain");
        String uploadedUuid = TransactionRunner.call(() -> uploadService.add(fileItem, false));
        // вызов системы
        IProperties prop = new MapProperties();
        prop.setProperty("file", Collections.singletonList(new SimpleDtObject(uploadedUuid, title, null)));
        prop.setProperty("defTitle", UUIDGenerator.get().nextUUID());
        dispatch.execute(new RunImportAction(uuid, prop));
        // проверка утверждений
        Thread.sleep(5000); // ожидаем прохождение импорта в паралелльном потоке
        Collection<File> files = getFiles(uuid);
        org.junit.Assert.assertEquals("Должны сохранить результат импорта файлов", 1, files.size());

        org.junit.Assert.assertEquals("Должны создать 3 объекта импортом + прикрепить файл", 4,
                createdListener.getCreatedCount());
        // очистка
    }

    @Before
    public void setUp() throws Exception
    {
        SecurityTestHelper.autenticateAsSuperUser();
        // настройка системы
        helper.createCase(ouFqn);
        createdListener.setUp();
        InputStream is = getClass().getResourceAsStream("/ru/naumen/advimport/double.advimport.xml");
        goodConfig = IOUtils.toString(is);

        tt = new TransactionTemplate(txManager);
    }

    @After
    public void tearDown()
    {
        createdListener.tearDown();
    }

    protected Collection<File> getFiles(final String uuid)
    {
        return tt.execute((TransactionCallback<Collection<File>>)status -> fileDao.get("advimport$" + uuid));
    }
}
