package ru.naumen.advimport.server.engine.converters;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import ru.naumen.advimport.shared.config.converters.HyperlinkConverter;
import ru.naumen.common.shared.utils.Hyperlink;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.elements.AttributeType;

/**
 * Проверка HyperlinkConverter'а.
 * проверка создания конвертера для разных типов атрибутов
 * проверка преобразования
 * 
 * <AUTHOR>
 *
 */
public class HyperlinkConverterBeanJdkTest
{
    @Mock
    private AttributeType type0;
    @Mock
    private AttributeType type1;

    private final HyperlinkConverter cfg = new HyperlinkConverter();

    @Before
    public void setUp()
    {
        MockitoAnnotations.initMocks(this);
        Mockito.when(type0.getCode()).thenReturn(Constants.HyperlinkAttributeType.CODE);
        Mockito.when(type1.getCode()).thenReturn(Constants.StringAttributeType.CODE);
    }

    @Test
    public void testConstruct()
    {
        HyperlinkConverterBean.Factory factory = new HyperlinkConverterBean.Factory();
        boolean value0 = factory.canConvert(type0);
        boolean value1 = factory.canConvert(type1);

        Assert.assertTrue(value0);
        Assert.assertFalse(value1);
    }

    @Test
    public void testConvert()
    {
        cfg.setDelimiter(";");

        HyperlinkConverterBean bean = (HyperlinkConverterBean)new HyperlinkConverterBean.Factory().create(cfg);
        Hyperlink value0 = bean.convert("test;test.ru", null);
        Hyperlink value1 = bean.convert("test1.ru", null);

        Assert.assertEquals("test.ru", value0.getURL());
        Assert.assertEquals("test", value0.getText());

        Assert.assertEquals("test1.ru", value1.getURL());
        Assert.assertEquals(0, value1.getText().length());
    }

}
