package ru.naumen.advimport.server.engine.converters;

import java.util.Map;

import jakarta.inject.Inject;

import com.google.common.collect.Maps;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import ru.naumen.advimport.server.engine.ImportContext;
import ru.naumen.advimport.server.engine.Item;
import ru.naumen.advimport.shared.config.converters.ScriptConverter;
import ru.naumen.core.server.script.ScriptService;

/**
 * Тест ScriptConverter'а
 * 
 * проверка конвертера разными скриптами.
 * проверка что в скрипте доступны контекстные переменные: parameters, value, item, ctx.
 * 
 * <AUTHOR>
 *
 */
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "/ru/naumen/core/server/dispatch/fullContext.xml")
public class ScriptConverterBeanDbTest
{
    private final ScriptConverter cfg = new ScriptConverter();
    @Mock
    private ImportContext ctx;
    private Map<String, Object> params = Maps.newHashMap();

    @Inject
    ScriptService scriptService;

    @Before
    public void setUp()
    {
        MockitoAnnotations.initMocks(this);

        params.put("test1", "testvalue1");
        params.put("test2", "testvalue2");

        Mockito.when(ctx.getParameters()).thenReturn(params);
    }

    @Test
    public void testConstruct()
    {
        String testScript = "return 1;";

        cfg.setScript(testScript);
        ScriptConverterBean bean = (ScriptConverterBean)new ScriptConverterBean.Factory().create(cfg);

        Assert.assertEquals(testScript, bean.script.getBody());
    }

    @SuppressWarnings("unchecked")
    @Test
    public void testConvert()
    {
        String testScript0 = "return parameters;";

        Map<String, Object> expect0 = params;

        cfg.setScript(testScript0);
        ScriptConverterBean bean = (ScriptConverterBean)new ScriptConverterBean.Factory().create(cfg);
        bean.scriptService = scriptService;
        bean.init(ctx);
        Map<String, String> result0 = (Map<String, String>)bean.convert(null, null);
        Assert.assertEquals(expect0, result0);
    }

    @Test
    public void testConvertCtxVariable()
    {
        ImportContext expect3 = ctx;
        String testScript3 = "return ctx";
        cfg.setScript(testScript3);
        ScriptConverterBean bean = (ScriptConverterBean)new ScriptConverterBean.Factory().create(cfg);
        bean.scriptService = scriptService;
        bean.init(ctx);
        ImportContext result3 = (ImportContext)bean.convert(null, null);
        Assert.assertEquals(expect3, result3);
    }

    @Test
    public void testConvertItemVariable()
    {
        Item expect2 = Mockito.mock(Item.class);
        String testScript2 = "return item";
        cfg.setScript(testScript2);
        ScriptConverterBean bean = (ScriptConverterBean)new ScriptConverterBean.Factory().create(cfg);
        bean.scriptService = scriptService;
        bean.init(ctx);
        Item result2 = (Item)bean.convert(null, expect2);
        Assert.assertEquals(expect2, result2);

    }

    @Test
    public void testConvertValueVariable()
    {
        Object expect1 = new Object();
        String testScript1 = "return value";
        cfg.setScript(testScript1);
        ScriptConverterBean bean = (ScriptConverterBean)new ScriptConverterBean.Factory().create(cfg);
        bean.scriptService = scriptService;
        bean.init(ctx);
        Object result1 = bean.convert(expect1, null);
        Assert.assertEquals(expect1, result1);
    }
}
