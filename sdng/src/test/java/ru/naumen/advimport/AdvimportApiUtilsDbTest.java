package ru.naumen.advimport;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

import jakarta.inject.Inject;

import org.apache.commons.io.IOUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import ru.naumen.advimport.server.AdvImportApiUtils;
import ru.naumen.advimport.server.dispatch.RunImportActionHandler;
import ru.naumen.advimport.server.engine.AdvImportException;
import ru.naumen.advimport.shared.config.Parameter;
import ru.naumen.commons.shared.FxException;

/**
 * Тесты на простые методы AdvimportApi
 * Более сложные тесты находятся в {@link AdvImportDbTest}
 * <AUTHOR>
 */
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class AdvimportApiUtilsDbTest extends AdvimportTestBase
{
    private static final String FILE_FOR_TEST = "/ru/naumen/advimport/ou2.test.csv";

    @Inject
    private AdvImportApiUtils advImportApiUtils;

    /**
     * Проверка поведения при использовании допустимого значения параметра типа byte[]
     * @throws IOException 
     */
    @Test
    public void prepareParametersUseByteArrayParam() throws IOException
    {
        // настройка системы
        Map<String, Object> parameters = new HashMap<String, Object>();

        InputStream is = getClass().getResourceAsStream(FILE_FOR_TEST);
        byte[] value = IOUtils.toByteArray(is);
        parameters.put("key", value);

        // вызов системы
        ArrayList<Parameter> result = advImportApiUtils.prepareParameters(parameters);

        // проверка утверждений
        Assert.assertEquals(1, result.size());

        String uploadUuid = result.get(0).getValue();
        Assert.assertTrue(uploadUuid.contains(RunImportActionHandler.UPLOADED));
    }

    /**
     * Проверка поведения при использовании пустой Map
     */
    @Test
    public void prepareParametersUseEmptyMap()
    {
        // настройка системы
        Map<String, Object> parameters = new HashMap<String, Object>();

        // вызов системы
        ArrayList<Parameter> result = advImportApiUtils.prepareParameters(parameters);

        // проверка утверждений
        Assert.assertEquals(new ArrayList<Parameter>(), result);
    }

    /**
     * Проверка поведения при использовании допустимого значения параметра типа InputStream
     */
    @Test
    public void prepareParametersUseInputStreamParam()
    {
        // настройка системы
        Map<String, Object> parameters = new HashMap<String, Object>();

        InputStream value = getClass().getResourceAsStream(FILE_FOR_TEST);
        parameters.put("key", value);

        // вызов системы
        ArrayList<Parameter> result = advImportApiUtils.prepareParameters(parameters);

        // проверка утверждений
        Assert.assertEquals(1, result.size());

        String uploadUuid = result.get(0).getValue();
        Assert.assertTrue(uploadUuid.contains(RunImportActionHandler.UPLOADED));
    }

    /**
     * Проверка поведения при использовании недопустимого значения ключа параметра - null
     */
    @Test
    public void prepareParametersUseNullParamKey()
    {
        // настройка системы
        Map<String, Object> parameters = new HashMap<String, Object>();
        parameters.put(null, "value");

        // вызов системы и проверка утверждений
        try
        {
            advImportApiUtils.prepareParameters(parameters);
            throw new FxException("Ошибка не появилась");
        }
        catch (AdvImportException e)
        {
        }
    }

    /**
     * Проверка поведения при использовании недопустимого значения параметра - null
     */
    @Test
    public void prepareParametersUseNullParamValue() throws Exception
    {
        // настройка системы
        Map<String, Object> parameters = new HashMap<String, Object>();
        parameters.put("key", null);

        // вызов системы и проверка утверждений
        try
        {
            advImportApiUtils.prepareParameters(parameters);
            throw new FxException("Ошибка не появилась");
        }
        catch (AdvImportException e)
        {
        }
    }

    /**
     * Проверка поведения при использовании допустимого параметра типа String
     */
    @Test
    public void prepareParametersUseStringParam()
    {
        // настройка системы
        Map<String, Object> parameters = new HashMap<String, Object>();
        String value = "file$0000";
        parameters.put("key", value);

        // вызов системы
        ArrayList<Parameter> result = advImportApiUtils.prepareParameters(parameters);

        // проверка утверждений
        Assert.assertEquals(1, result.size());
        Assert.assertEquals(value, result.get(0).getValue());
    }

    /**
     * Проверка поведения при использовании недопустимого параметра типа Integer
     */
    @Test
    public void prepareParametersUseWrongParamType()
    {
        // настройка системы
        Map<String, Object> parameters = new HashMap<String, Object>();
        parameters.put("key", 1);

        // вызов системы и проверка утверждений
        try
        {
            advImportApiUtils.prepareParameters(parameters);
            throw new FxException("Ошибка не появилась");
        }
        catch (AdvImportException e)
        {
        }
    }
}