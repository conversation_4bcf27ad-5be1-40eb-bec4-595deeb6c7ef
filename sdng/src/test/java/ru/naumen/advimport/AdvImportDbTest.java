package ru.naumen.advimport;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.SQLSyntaxErrorException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.sql.DataSource;

import org.apache.commons.io.IOUtils;
import org.joda.time.format.DateTimeFormat;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

import com.google.common.collect.Sets;

import jakarta.inject.Inject;
import jakarta.inject.Named;
import net.customware.gwt.dispatch.server.Dispatch;
import ru.naumen.advimport.server.AdvImportApiUtils;
import ru.naumen.advimport.server.engine.Engine;
import ru.naumen.advimport.server.engine.customizer.RemoveCustomizerBean;
import ru.naumen.advimport.shared.config.Config;
import ru.naumen.advimport.shared.config.Parameter;
import ru.naumen.advimport.shared.config.customizers.RemoveCustomizer;
import ru.naumen.advimport.shared.config.datasource.SQLDataSource;
import ru.naumen.common.shared.utils.DateTimeInterval;
import ru.naumen.common.shared.utils.Hyperlink;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.EncryptedValueProvider;
import ru.naumen.core.server.bo.employee.Employee;
import ru.naumen.core.server.bo.ou.OU;
import ru.naumen.core.server.bo.root.Root;
import ru.naumen.core.server.bo.service.SlmService;
import ru.naumen.core.server.dispatch.ObjectTestUtils;
import ru.naumen.core.server.dispatch.SecurityTestUtils;
import ru.naumen.sec.server.encryption.DbPasswordProvider;
import ru.naumen.core.server.objectloader.IPrefixObjectLoaderService;
import ru.naumen.core.server.script.api.AdvimportApi;
import ru.naumen.core.server.script.api.ITransactionApi;
import ru.naumen.core.shared.Constants.AbstractBO;
import ru.naumen.core.shared.HasLocalizedTitleAsFlexProperties;
import ru.naumen.core.shared.criteria.DtoCriteria;
import ru.naumen.core.shared.dispatch.AddObjectAction;
import ru.naumen.core.shared.dispatch.GetDtObjectResponse;
import ru.naumen.core.shared.filters.Filters;
import ru.naumen.core.shared.utils.ILocaleInfo;
import ru.naumen.core.shared.utils.UUIDGenerator;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.Constants.DateTimeIntervalAttributeType.Interval;
import ru.naumen.metainfo.shared.Constants.Presentations;
import ru.naumen.metainfo.shared.dispatch2.AddAttributeAction;

/**
 *
 * <AUTHOR>
 *
 */
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class AdvImportDbTest extends AdvimportTestBase
{
    @Inject
    private ObjectTestUtils testUtils;
    @Inject
    private Dispatch dispatch;
    @Inject
    private MetainfoService metainfoService;
    @Inject
    private IPrefixObjectLoaderService prefixLoader;
    @Inject
    private DataSource dataSource;
    @Inject
    private AdvImportTestHelper helper;
    @Inject
    private AdvimportApi advimportApi;

    @Inject
    private AdvImportApiUtils advImportApiUtils;
    @Value("${db.driver}")
    String dbDriver;
    @Value("${db.url}")
    String dbURL;

    @Value("${db.user}")
    String dbUser;

    @Inject
    @Named(DbPasswordProvider.NAME)
    EncryptedValueProvider dbPasswd;
    ClassFqn ouFqn = ClassFqn.parse("ou$ouImportTest");

    ClassFqn ouFqn2 = ClassFqn.parse("ou$ouImportTest2");

    ClassFqn ouFqn3 = ClassFqn.parse("ou$ouImportTest3");

    ClassFqn employeeFqn = ClassFqn.parse("employee$forImport");

    ClassFqn srvFqn = ClassFqn.parse("slmService$srvImportTest");

    ClassFqn errorOuFqn = ClassFqn.parse("ou$errorImportOu");

    Logger LOG = LoggerFactory.getLogger(this.getClass());

    private static final String INT_ATTR_CODE = "int1";

    private static final String INTERVAL_ATTR_CODE = "interval12";

    @Inject
    ITransactionApi txApi;

    @Inject
    SecurityTestUtils securityTestUtils;

    @Test
    public void booleanConvertor() throws Exception
    {
        // настройка системы
        Config conf = prepareConf("boolean.advimport.xml");
        // вызов системы
        try (Engine engine = runImport(conf))
        {
            // проверка утверждений
            Assert.assertEquals("Импортируемые данные содержат 0 ошибки", 0, engine.getErrors().size());
        }
        Assert.assertEquals("Должны создать 3 объекта (по кол-ву валидных импортируемых строк)", 3,
                createdListener.getCreatedCount());

        OU ou = getOU("bool1");
        Assert.assertFalse("Должно соответствовать импортируемым данным", ou.isRemoved());

        ou = getOU("bool2");
        Assert.assertTrue("Должно соответствовать импортируемым данным", ou.isRemoved());

        ou = getOU("bool3");
        Assert.assertFalse("Должно соответствовать импортируемым данным", ou.isRemoved());
        // очистка
    }

    @Test
    public void constantColumn() throws Exception
    {
        // настройка системы
        Config conf = prepareConf("constantColumn.advimport.xml");
        // вызов системы
        try (Engine engine = runImport(conf))
        {

            // проверка утверждений
            Assert.assertTrue(engine.getErrors().isEmpty());
        }
        Assert.assertEquals("Должны создать 1 объекта (по кол-ву импортируемых строк)", 1,
                createdListener.getCreatedCount());

        OU ou = getOU("1");
        Assert.assertEquals("default title", ou.getTitle());
        // очистка
    }

    /**
     * Импорт из csv файла в UTF-8 с BOM кодировке
     * @throws Exception
     */
    @Test
    public void csvUTF8BOM() throws Exception
    {
        // настройка системы
        Config conf = prepareConf("boolean.utf8.bom.advimport.xml");
        // вызов системы
        try (Engine engine = runImport(conf))
        {
            // проверка утверждений
            Assert.assertEquals("Импортируемые данные содержат 0 ошибки", 0, engine.getErrors().size());
        }
        Assert.assertEquals("Должны создать 3 объекта (по кол-ву валидных импортируемых строк)", 3,
                createdListener.getCreatedCount());

        OU ou = getOU("bool1");
        Assert.assertFalse("Должно соответствовать импортируемым данным", ou.isRemoved());

        ou = getOU("bool2");
        Assert.assertTrue("Должно соответствовать импортируемым данным", ou.isRemoved());

        ou = getOU("bool3");
        Assert.assertFalse("Должно соответствовать импортируемым данным", ou.isRemoved());
        // очистка
    }

    /**
     * Импорт из csv файла в Windows-1251 кодировке
     * @throws Exception
     */
    @Test
    public void csvWindows1251() throws Exception
    {
        // настройка системы
        Config conf = prepareConf("csv.w1251.advimport.xml");
        // вызов системы
        try (Engine engine = runImport(conf))
        {
            // проверка утверждений
            Assert.assertEquals("Импортируемые данные содержат 0 ошибки", 0, engine.getErrors().size());
        }
        Assert.assertEquals("Должны создать 1 объект (по кол-ву валидных импортируемых строк)", 1,
                createdListener.getCreatedCount());

        OU ou = getOU("bool1");
        Assert.assertFalse("Должно соответствовать импортируемым данным", ou.isRemoved());

        Assert.assertEquals("Названия должны совпадать", "Отдел с названием по русски", ou.getTitle());
        // очистка
    }

    /**
     * Проверяем отображение статусбара в логах импорта при выключении и повторном включении статусбара
     *
     * @throws Exception
     */
    @Test
    public void disableEnableStatusbar() throws Exception
    {
        // настройка системы
        Config conf = prepareConf("double.advimport.xml");
        advimportApi.showStatusBar(false);
        advimportApi.showStatusBar(true);
        // вызов системы
        try (Engine engine = runImport(conf))
        {
            // проверка утверждений
            String log = getLog(engine);
            LOG.debug(log);
            String resultRegex = "^\\[\\d+/3\\] ID=[^\\n]*\\nИмпорт 'ou' завершен за [^\\n]* сек. Строк обработано: 3"
                    + ". Проимпортировано: 3. Изменено: 0. Пропущено: 0. Возникло ошибок: 0.";
            Matcher matcher = Pattern.compile(resultRegex, Pattern.MULTILINE).matcher(log);
            Assert.assertTrue("В логе не содержится ожидаемое сообщение", matcher.find());
        }
        // очистка
    }

    @Test
    public void doubleFlx() throws Exception
    {
        // настройка системы
        Config conf = prepareConf("double.advimport.xml");
        // вызов системы
        try (Engine engine = runImport(conf))
        {
            // проверка утверждений
            Assert.assertEquals("Импортируемые данные содержат 0 ошибки", 0, engine.getErrors().size());
        }
        Assert.assertEquals("Должны создать 3 объекта (по кол-ву валидных импортируемых строк)", 3,
                createdListener.getCreatedCount());

        OU ou = getOU("1");
        Assert.assertEquals("Должно соответствовать импортируемым данным", "default title", ou.getTitle());
        Assert.assertNull("Должно соответствовать импортируемым данным", ou.getParent());
        Assert.assertNull("Должно соответствовать импортируемым данным", ou.getFlexes().getProperty("dbl"));

        ou = getOU("2");
        Assert.assertEquals("Должно соответствовать импортируемым данным", "default title", ou.getTitle());
        Assert.assertNull("Должно соответствовать импортируемым данным", ou.getParent());
        Assert.assertEquals("Должно соответствовать импортируемым данным", 2d,
                (double)ou.getFlexes().getProperty("dbl"), 0);

        ou = getOU("3");
        Assert.assertEquals("Должно соответствовать импортируемым данным", "default title", ou.getTitle());
        Assert.assertNull("Должно соответствовать импортируемым данным", ou.getParent());
        Assert.assertEquals("Должно соответствовать импортируемым данным", 3.1415,
                (double)ou.getFlexes().getProperty("dbl"), 0);
        // очистка
    }

    @Test
    public void doubleFlx2() throws Exception
    {
        // настройка системы
        Config conf = prepareConf("double2.advimport.xml");
        // вызов системы
        try (Engine engine = runImport(conf))
        {
            // проверка утверждений
            Assert.assertEquals("Импортируемые данные содержат 0 ошибки", 0, engine.getErrors().size());
        }
        Assert.assertEquals("Должны создать 3 объекта (по кол-ву валидных импортируемых строк)", 3,
                createdListener.getCreatedCount());

        OU ou = getOU("1");
        Assert.assertEquals("Должно соответствовать импортируемым данным", "default title", ou.getTitle());
        Assert.assertNull("Должно соответствовать импортируемым данным", ou.getParent());
        Assert.assertNull("Должно соответствовать импортируемым данным", ou.getFlexes().getProperty("dbl"));

        ou = getOU("2");
        Assert.assertEquals("Должно соответствовать импортируемым данным", "default title", ou.getTitle());
        Assert.assertNull("Должно соответствовать импортируемым данным", ou.getParent());
        Assert.assertEquals("Должно соответствовать импортируемым данным", 2d,
                (double)ou.getFlexes().getProperty("dbl"), 0);

        ou = getOU("3");
        Assert.assertEquals("Должно соответствовать импортируемым данным", "default title", ou.getTitle());
        Assert.assertNull("Должно соответствовать импортируемым данным", ou.getParent());
        Assert.assertEquals("Должно соответствовать импортируемым данным", 3.1415,
                (double)ou.getFlexes().getProperty("dbl"), 0);
        // очистка
    }

    /**
     * Тестирование RemoveCustomizer. Проверка архивирования всех непроимпортированных сотрудников в рамках отдела
     */
    @Test
    public void employeeRemove() throws Exception
    {
        employeeRemove(false, true);
    }

    /**
     * Тестирование RemoveCustomizer. Проверка архивирования всех непроимпортированных сотрудников в рамках компании
     */
    @Test
    public void employeeRemoveAfterRoot() throws Exception
    {
        employeeRemove(true, true);
    }

    /**
     * Тестирование RemoveCustomizer. Проверка архивирования всех непроимпортированных сотрудников в рамках отдела.
     * Используется только метод CREATE.
     */
    @Test
    public void employeeRemoveCreateModeTest() throws Exception
    {
        employeeRemove(false, false);
    }

    /**
     * Тестирование RemoveCustomizer. Проверка архивирования всех непроимпортированных сотрудников в рамках отдела.
     * Используются методы CREATE, UPDATE.
     */
    @Test
    public void employeeRemoveCreateUpdateModesTest() throws Exception
    {
        employeeRemove(false, true);
    }

    /**
     * Проверяем работу режима EMPTY в классе конфигурации импорта
     *
     * @throws Exception
     */
    @Test
    public void emptyModeInClassMode() throws Exception
    {
        // настройка системы
        Config conf = prepareConf("skippedCsv.advimport.xml");

        // вызов системы
        try (Engine engine = runImport(conf))
        {

            // проверка утверждений
            Assert.assertEquals("Импортируемые данные содержат 0 ошибки", 0, engine.getErrors().size());
            String log = getLog(engine);

            //@formatter:off
        String resultRegex = "^Режим импорта 'EMPTY'. Создание и изменение объектов отключено.\\n"
                + "attr title 'title1 before-process-item'\\n"
                + "\\[[1-2]/2\\] ID=title1 before-process-item attr skipped. Mode 'EMPTY' is enabled.\\n"
                + "after-process title 'title1 before-process-item attr'\\n"
                + "attr title 'title3 before-process-item'\\n"
                + "\\[[1-2]/2\\] ID=title3 before-process-item attr skipped. Mode 'EMPTY' is enabled.\\n"
                + "after-process title 'title3 before-process-item attr'\\n"
                + "execute after-import\\n"
                + "Импорт 'ou' завершен за [0-2] сек. Строк обработано: 2. Проимпортировано: 0. Изменено: 0. Пропущено: 2. Возникло ошибок: 0.";
        //@formatter:on

            Matcher matcher = Pattern.compile(resultRegex, Pattern.MULTILINE).matcher(log);
            Assert.assertTrue("В логе не содержится ожидаемое сообщение. Получено: " + log, matcher.find());
        }
        // очистка
    }

    public String getLog(Engine engine)
    {
        try
        {
            return IOUtils.toString(engine.getLogAsStream(), StandardCharsets.UTF_8);
        }
        catch (IOException e)
        {
            return null;
        }
    }

    @Test
    public void hyperlink() throws Exception
    {
        // настройка системы
        Config conf = prepareConf("hyperlink.advimport.xml");
        // вызов системы
        try (Engine engine = runImport(conf))
        {
            // проверка утверждений
            Assert.assertEquals("Импортируемые данные содержат 0 ошибки", 0, engine.getErrors().size());
        }
        Assert.assertEquals("Должны создать 2 объекта (по кол-ву валидных импортируемых строк)", 2,
                createdListener.getCreatedCount());

        OU ou = getOU("2");
        Assert.assertEquals("Должно соответствовать импортируемым данным", "default title", ou.getTitle());
        Assert.assertNull("Должно соответствовать импортируемым данным", ou.getParent());
        Assert.assertEquals("Должно соответствовать импортируемым данным", new Hyperlink("text", "http://naumen.ru"),
                ou.getFlexes().getProperty("hl"));
        // очистка
    }

    @Test
    public void hyperlink2() throws Exception
    {
        // настройка системы
        Config conf = prepareConf("hyperlink2.advimport.xml");
        // вызов системы
        try (Engine engine = runImport(conf))
        {
            // проверка утверждений
            Assert.assertEquals("Импортируемые данные содержат 0 ошибки", 0, engine.getErrors().size());
        }
        Assert.assertEquals("Должны создать 2 объекта (по кол-ву валидных импортируемых строк)", 2,
                createdListener.getCreatedCount());

        OU ou = getOU("2");
        Assert.assertEquals("Должно соответствовать импортируемым данным", "default title", ou.getTitle());
        Assert.assertNull("Должно соответствовать импортируемым данным", ou.getParent());
        Assert.assertEquals("Должно соответствовать импортируемым данным", new Hyperlink("text", "http://naumen.ru"),
                ou.getFlexes().getProperty("hl"));
        // очистка
    }

    @Test
    public void hyperlink3() throws Exception
    {
        // настройка системы
        Config conf = prepareConf("hyperlink3.advimport.xml");
        // вызов системы
        try (Engine engine = runImport(conf))
        {
            // проверка утверждений
            Assert.assertEquals("Импортируемые данные содержат 0 ошибки", 0, engine.getErrors().size());
        }
        Assert.assertEquals("Должны создать 2 объекта (по кол-ву валидных импортируемых строк)", 2,
                createdListener.getCreatedCount());

        OU ou = getOU("4");

        Hyperlink hl = ou.getFlexes().getProperty("hl");
        System.err.println("ou.getProperty(hl): text: " + hl.getText());
        System.err.println("ou.getProperty(hl): url: " + hl.getURL());

        Assert.assertEquals("Должно соответствовать импортируемым данным", "default title", ou.getTitle());
        Assert.assertNull("Должно соответствовать импортируемым данным", ou.getParent());
        Assert.assertEquals("Должно соответствовать импортируемым данным", new Hyperlink(null, "http://naumen.ru"),
                ou.getFlexes().getProperty("hl"));
        // очистка
    }

    @Test
    public void idPrefix() throws Exception
    {
        // настройка системы
        Config conf = prepareConf("idPrefix.advimport.xml");
        // вызов системы
        try (Engine engine = runImport(conf))
        {
            // проверка утверждений
            Assert.assertTrue(engine.getErrors().isEmpty());
        }
        Assert.assertEquals("Должны создать 3 объекта (по кол-ву импортируемых строк)", 3,
                createdListener.getCreatedCount());

        TransactionTemplate tt = new TransactionTemplate(txManager);
        tt.execute(new TransactionCallback<Void>()
        {
            @Override
            public Void doInTransaction(TransactionStatus status)
            {
                OU ou = getOU("importPrefix:1");
                Assert.assertEquals("Должно соответствовать импортируемым данным", "t1", ou.getTitle());
                Assert.assertNull("Должно соответствовать импортируемым данным", ou.getParent());

                OU ou2 = getOU("importPrefix:2");
                Assert.assertEquals("Должно соответствовать импортируемым данным", "t2", ou2.getTitle());
                Assert.assertEquals("Должно соответствовать импортируемым данным", ou, ou2.getParent());

                OU ou3 = getOU("importPrefix:3");
                Assert.assertEquals("Должно соответствовать импортируемым данным", "t3", ou3.getTitle());
                Assert.assertEquals("Должно соответствовать импортируемым данным", ou, ou3.getParent());

                return null;
            }
        });

        // очистка
    }

    /**
     * Проверяем импорт иерархических структур
     *
     * @throws Exception
     */
    @Test
    public void importFromSQL() throws Exception
    {
        try (Connection connection = dataSource.getConnection())
        {
            // настройка системы

            try
            {
                //Запрос проверен на PostgreSQL и Oracle
                connection.createStatement()
                        .execute("create table tbl_date_import (id varchar(255), title varchar(255))");
            }
            catch (SQLSyntaxErrorException e)
            {
                throw e;
            }
            catch (SQLException e)
            {
                // таблица уже существует
                connection.createStatement().execute("delete from  tbl_date_import");
            }
            PreparedStatement stmt = connection.prepareStatement("insert into  tbl_date_import values  (?, ?)");

            stmt.setString(1, "from sql 1 id");
            stmt.setString(2, "from sql 1 title");

            stmt.execute();

            Config conf = prepareConf("ou_fromSQL.advimport.xml");

            SQLDataSource sqlDS = (SQLDataSource)conf.getClasses().get(0).getDataSource();
            sqlDS.setDriverClass(dbDriver);
            sqlDS.setUrl(dbURL);
            sqlDS.setUser(dbUser);
            sqlDS.setPassword(dbPasswd.get());
            // вызов системы
            try (Engine engine = runImport(conf))
            {
                // проверка утверждений
                Assert.assertTrue(engine.getErrors().isEmpty());
            }
            // сверяем данные с импортируемым файлом
            OU ou1 = getOU("from sql 1 id");

            Assert.assertEquals("from sql 1 title", ou1.getTitle());
            // очистка
        }
    }

    @Test
    public void integerFlx() throws Exception
    {
        // настройка системы
        Config conf = prepareConf("integer.advimport.xml");
        // вызов системы
        try (Engine engine = runImport(conf))
        {
            // проверка утверждений
            Assert.assertEquals("Импортируемые данные содержат 0 ошибки", 0, engine.getErrors().size());
        }
        Assert.assertEquals("Должны создать 3 объекта (по кол-ву валидных импортируемых строк)", 3,
                createdListener.getCreatedCount());

        OU ou = getOU("1");
        Assert.assertNull("Должно соответствовать импортируемым данным", ou.getFlexes().getProperty(INT_ATTR_CODE));

        ou = getOU("2");
        Assert.assertEquals("Должно соответствовать импортируемым данным", 2l, (long)ou.getFlexes().getProperty(INT_ATTR_CODE));

        ou = getOU("3");
        Assert.assertEquals("Должно соответствовать импортируемым данным", -3l,
                (long)ou.getFlexes().getProperty(INT_ATTR_CODE));
        // очистка
    }

    @Test
    public void integerFlx2() throws Exception
    {
        // настройка системы
        Config conf = prepareConf("integer2.advimport.xml");
        // вызов системы
        try (Engine engine = runImport(conf))
        {
            // проверка утверждений
            Assert.assertEquals("Импортируемые данные содержат 0 ошибки", 0, engine.getErrors().size());
        }
        Assert.assertEquals("Должны создать 3 объекта (по кол-ву валидных импортируемых строк)", 3,
                createdListener.getCreatedCount());

        OU ou = getOU("1");
        Assert.assertNull("Должно соответствовать импортируемым данным", ou.getFlexes().getProperty(INT_ATTR_CODE));

        ou = getOU("2");
        Assert.assertEquals("Должно соответствовать импортируемым данным", 2l, (long)ou.getFlexes().getProperty(INT_ATTR_CODE));

        ou = getOU("3");
        Assert.assertEquals("Должно соответствовать импортируемым данным", -3l,
                (long)ou.getFlexes().getProperty(INT_ATTR_CODE));
        // очистка
    }

    @Test
    public void notNull() throws Exception
    {
        // настройка системы
        Config conf = prepareConf("notNull.advimport.xml");
        // вызов системы
        try (Engine engine = runImport(conf))
        {
            // проверка утверждений
            Assert.assertEquals("Импортируемые данные содержат 4 ошибки", 4, engine.getErrors().size());
        }
        Assert.assertEquals("Должны создать 1 объекта (по кол-ву валидных импортируемых строк)", 1,
                createdListener.getCreatedCount());

        OU ou = getOU("3");
        Assert.assertEquals("Должно соответствовать импортируемым данным", "good", ou.getTitle());
        Assert.assertNull("Должно соответствовать импортируемым данным", ou.getParent());
        // очистка
    }

    @Test
    public void ou2() throws Exception
    {
        // настройка системы
        Config conf = prepareConf("ou2.advimport.xml");
        // вызов системы
        try (Engine engine = runImport(conf))
        {
            // проверка утверждений
            Assert.assertTrue(engine.getErrors().isEmpty());
        }
        Assert.assertEquals("Должны создать 3 объекта (по кол-ву импортируемых строк)", 3,
                createdListener.getCreatedCount());

        TransactionTemplate tt = new TransactionTemplate(txManager);
        tt.execute(new TransactionCallback<Void>()
        {
            @Override
            public Void doInTransaction(TransactionStatus status)
            {
                OU ou = getOU("ou1");
                Assert.assertEquals("Должно соответствовать импортируемым данным", "ou1 title", ou.getTitle());
                Assert.assertEquals("Должно соответствовать импортируемым данным", 0,
                        ou.getFlexes().<Collection<?>> getProperty("ouLinks").size());

                OU ou2 = getOU("ou2");
                Assert.assertEquals("Должно соответствовать импортируемым данным", "ou2 title", ou2.getTitle());
                Assert.assertEquals("Должно соответствовать импортируемым данным", 1,
                        ou2.getFlexes().<Collection<?>> getProperty("ouLinks").size());
                Assert.assertTrue("Должно соответствовать импортируемым данным",
                        ou2.getFlexes().<Collection<?>> getProperty("ouLinks").contains(ou));

                OU ou3 = getOU("ou3");
                Assert.assertEquals("Должно соответствовать импортируемым данным", "ou3 title", ou3.getTitle());
                Assert.assertEquals("Должно соответствовать импортируемым данным", 2,
                        ou3.getFlexes().<Collection<?>> getProperty("ouLinks").size());
                Assert.assertTrue("Должно соответствовать импортируемым данным",
                        ou3.getFlexes().<Collection<?>> getProperty("ouLinks").contains(ou));
                Assert.assertTrue("Должно соответствовать импортируемым данным",
                        ou3.getFlexes().<Collection<?>> getProperty("ouLinks").contains(ou2));

                return null;
            }
        });
        // очистка
    }

    @Test
    public void ou3() throws Exception
    {
        // настройка системы
        Config conf = prepareConf("ou3.advimport.xml");
        // вызов системы
        try (Engine engine = runImport(conf))
        {
            // проверка утверждений
            Assert.assertTrue(engine.getErrors().isEmpty());

        }
        Assert.assertEquals("Должны создать 3 объекта (по кол-ву импортируемых строк)", 3,
                createdListener.getCreatedCount());

        TransactionTemplate tt = new TransactionTemplate(txManager);
        tt.execute(new TransactionCallback<Void>()
        {
            @Override
            public Void doInTransaction(TransactionStatus status)
            {
                OU ou = getOU("ou1");
                Assert.assertEquals("Должно соответствовать импортируемым данным", "ou1 title", ou.getTitle());
                Assert.assertNull("Должно соответствовать импортируемым данным", ou.getParent());

                OU ou2 = getOU("ou2");
                Assert.assertEquals("Должно соответствовать импортируемым данным", "ou2 title", ou2.getTitle());
                Assert.assertEquals("Должно соответствовать импортируемым данным", ou, ou2.getParent());

                OU ou3 = getOU("ou3");
                Assert.assertEquals("Должно соответствовать импортируемым данным", "ou3 title", ou3.getTitle());
                Assert.assertEquals("Должно соответствовать импортируемым данным", ou2, ou3.getParent());

                return null;
            }
        });
        // очистка
    }

    @Test
    public void ouCased() throws Exception
    {
        // настройка системы
        Config conf = prepareConf("ou_cased.advimport.xml");
        // вызов системы
        try (Engine engine = runImport(conf))
        {
            // проверка утверждений
            Assert.assertTrue(engine.getErrors().isEmpty());
        }
        Assert.assertEquals("Должны создать три объекта (по кол-ву импортируемых строк)", 3,
                createdListener.getCreatedCount());

        TransactionTemplate tt = new TransactionTemplate(txManager);
        tt.execute(new TransactionCallback<Void>()
        {
            @Override
            public Void doInTransaction(TransactionStatus status)
            {
                OU ou1 = prefixLoader.get(createdListener.created.get(0));
                OU ou2 = prefixLoader.get(createdListener.created.get(1));
                OU ou3 = prefixLoader.get(createdListener.created.get(2));

                checkOus("case not set", ouFqn, ou1, ou2, ou3);
                checkOus("case 1", ouFqn, ou1, ou2, ou3);
                checkOus("case 2", ouFqn2, ou1, ou2, ou3);

                return null;
            }
        });
        // очистка
    }

    @Test
    public void ouDefault() throws Exception
    {
        // настройка системы
        Config conf = prepareConf("ou_default.advimport.xml");
        // вызов системы
        try (Engine engine = runImport(conf))
        {
            // проверка утверждений
            Assert.assertEquals(0, engine.getErrors().size());
        }
        // очистка
    }

    @Test
    public void ouFastParsingXml() throws Exception
    {
        // настройка системы
        Config conf = prepareConf("fastParsing.advimport.xml");
        // вызов системы
        try (Engine engine = runImport(conf))
        {
            // проверка утверждений
            Assert.assertTrue(engine.getErrors().isEmpty());
        }
        Assert.assertEquals("Должны создать 2 объекта (по кол-ву импортируемых строк)", 2,
                createdListener.getCreatedCount());

        TransactionTemplate tt = new TransactionTemplate(txManager);
        tt.execute(new TransactionCallback<Void>()
        {
            @Override
            public Void doInTransaction(TransactionStatus status)
            {
                OU ou1 = prefixLoader.get(createdListener.created.get(0));
                OU ou2 = prefixLoader.get(createdListener.created.get(1));

                Assert.assertEquals("Должно соответствовать импортируемым данным", "Vo0", ou1.getTitle());
                Assert.assertEquals("Должно соответствовать импортируемым данным", "Lo0", ou2.getTitle());

                return null;
            }
        });
        // очистка
    }

    @Test
    public void ouFiltered() throws Exception
    {
        // настройка системы
        Config conf = prepareConf("ou_filtered.advimport.xml");
        // вызов системы
        try (Engine engine = runImport(conf))
        {
            // проверка утверждений
            Assert.assertTrue(engine.getErrors().isEmpty());
        }
        Assert.assertEquals("Должны создать только один объект у которого заполнена колонка forFilter", 1,
                createdListener.getCreatedCount());

        TransactionTemplate tt = new TransactionTemplate(txManager);
        tt.execute(new TransactionCallback<Void>()
        {
            @Override
            public Void doInTransaction(TransactionStatus status)
            {
                OU ou = prefixLoader.get(createdListener.created.get(0));
                Assert.assertEquals("название созданного отдела должно соответствовать импортируемым данным",
                        "notFiltered", ou.getTitle());

                return null;
            }
        });
        // очистка
    }

    /**
     * ou2 не будет искать архивного родителя
     * @throws Exception
     */
    @Test
    public void ouObjectConverterRemovedParamFalse() throws Exception
    {
        // настройка системы
        Config conf = prepareConf("ou.object.converter.removed.false.xml");
        // вызов системы
        try (Engine engine = runImport(conf))
        {
            // проверка утверждений
            Assert.assertTrue(engine.getErrors().isEmpty());
        }
        Assert.assertEquals("Должны создать 2 объекта (по кол-ву импортируемых строк)", 2,
                createdListener.getCreatedCount());

        OU ou = getOU("itm1");
        Assert.assertEquals("itm1", ou.getTitle());
        Assert.assertTrue(ou.isRemoved());
        Assert.assertEquals(null, ou.getParent());

        OU ou2 = getOU("itm2");
        Assert.assertEquals("itm2", ou2.getTitle());
        Assert.assertFalse(ou2.isRemoved());
        Assert.assertEquals(null, ou.getParent());
        // очистка
    }

    /**
     * ou2 не будет создан т.к. объект ищется без учета состояния архивности, а его родитель в архиве
     * @throws Exception
     */
    @Test
    public void ouObjectConverterRemovedParamNotSet() throws Exception
    {
        // настройка системы
        Config conf = prepareConf("ou.object.converter.removed.notset.xml");
        // вызов системы
        try (Engine engine = runImport(conf))
        {
            // проверка утверждений
            Assert.assertEquals("Импортируемые данные не содержат 1 ошибку", 1, engine.getErrors().size());
            Assert.assertEquals("Должен создать 1 объект", 1, createdListener.getCreatedCount());
        }

        OU ou = getOU("itm1");
        Assert.assertEquals("itm1", ou.getTitle());
        Assert.assertTrue(ou.isRemoved());
        Assert.assertEquals(null, ou.getParent());

        OU ou2 = getOU("itm2");
        Assert.assertEquals(null, ou2);
    }

    /**
     * ou2 не будет создан т.к. ищется архивный объект, а его родитель в архиве
     * @throws Exception
     */
    @Test
    public void ouObjectConverterRemovedParamTrue() throws Exception
    {
        // настройка системы
        Config conf = prepareConf("ou.object.converter.removed.true.xml");
        // вызов системы
        try (Engine engine = runImport(conf))
        {
            // проверка утверждений
            Assert.assertEquals("Импортируемые данные не содержат 1 ошибку", 1, engine.getErrors().size());
            Assert.assertEquals("Должен создать 1 объект", 1, createdListener.getCreatedCount());
        }

        OU ou = getOU("itm1");
        Assert.assertEquals("itm1", ou.getTitle());
        Assert.assertTrue(ou.isRemoved());
        Assert.assertEquals(null, ou.getParent());

        OU ou2 = getOU("itm2");
        Assert.assertEquals(null, ou2);
    }

    /**
     * Проверяем правильность работы  {@link RemoveCustomizerBean}
     *
     * @throws Exception
     */
    @Test
    public void ouRemoveAfter() throws Exception
    {
        // настройка системы
        MapProperties ouProps = new MapProperties();
        ouProps.setProperty(AbstractBO.TITLE, UUIDGenerator.get().nextUUID());
        ouProps.setProperty(AdvImportTestHelper.ID_HOLDER_ATTR, "importRoot");
        String ouUuid = dispatch.execute(new AddObjectAction(ouProps, ouFqn)).getObj().getUUID();

        OU ou = prefixLoader.get(ouUuid);
        final OU childOu = testUtils.createOU(ou, ouFqn);

        IProperties idSet = new MapProperties();
        idSet.setProperty("idHolder", UUIDGenerator.get().nextUUID());
        testUtils.edit(ou, idSet);
        idSet.setProperty("idHolder", UUIDGenerator.get().nextUUID());
        testUtils.edit(childOu, idSet);

        Config conf = prepareConf("ou_remove.advimport.xml");
        RemoveCustomizer removeCustomizerConf = (RemoveCustomizer)conf.getClasses().get(0).getCustomizers().get(0);
        removeCustomizerConf.setRoot(ou.getUUID());
        // вызов системы
        try (Engine engine = runImport(conf))
        {
            // проверка утверждений
            Assert.assertTrue("Иморт должен пройти без ошибок", engine.getErrors().isEmpty());
        }
        Assert.assertEquals("Должны создать 4 объекта: 2 принастройке теста и 2 во время импорта", 4,
                createdListener.getCreatedCount());

        TransactionTemplate tt = new TransactionTemplate(txManager);
        tt.execute(new TransactionCallback<Void>()
        {
            @Override
            public Void doInTransaction(TransactionStatus status)
            {
                OU cou = prefixLoader.get(childOu.getUUID());
                Assert.assertTrue("Должен быть в архиве т.к. не участвовал в импорте", cou.isRemoved());

                OU ou1 = getOU("1");
                OU ou2 = getOU("2");

                Assert.assertFalse("Проимортированный объект не должен быть помечен на удаление", ou1.isRemoved());
                Assert.assertFalse("Проимортированный объект не должен быть помечен на удаление", ou2.isRemoved());

                return null;
            }
        });

        // очистка
    }

    /**
     * Проверяем правильность работы  {@link RemoveCustomizerBean}
     *
     * @throws Exception
     */
    @Test
    public void ouRemoveAfterRoot() throws Exception
    {
        // настройка системы
        final OU childOu = testUtils.createOU(ouFqn);

        Root root = testUtils.getRoot();

        IProperties idSet = new MapProperties();
        idSet.setProperty("idHolder", UUIDGenerator.get().nextUUID());
        testUtils.edit(childOu, idSet);

        Config conf = prepareConf("ou_remove.advimport.xml");
        RemoveCustomizer removeCustomizerConf = (RemoveCustomizer)conf.getClasses().get(0).getCustomizers().get(0);
        removeCustomizerConf.setRoot(root.getUUID());
        // вызов системы
        try (Engine engine = runImport(conf))
        {
            // проверка утверждений
            Assert.assertTrue("Иморт должен пройти без ошибок", engine.getErrors().isEmpty());
        }
        Assert.assertEquals("Должны создать 3 объекта: 1 принастройке теста и 2 во время импорта", 3,
                createdListener.getCreatedCount());

        TransactionTemplate tt = new TransactionTemplate(txManager);
        tt.execute(new TransactionCallback<Void>()
        {
            @Override
            public Void doInTransaction(TransactionStatus status)
            {
                OU cou = prefixLoader.get(childOu.getUUID());
                Assert.assertTrue("Должен быть в архиве т.к. не участвовал в импорте", cou.isRemoved());

                OU ou1 = getOU("1");
                OU ou2 = getOU("2");

                Assert.assertFalse("Проимортированный объект не должен быть помечен на удаление", ou1.isRemoved());
                Assert.assertFalse("Проимортированный объект не должен быть помечен на удаление", ou2.isRemoved());

                return null;
            }
        });
        // очистка
    }

    /**
     * Проверяем правильность работы {@link RemoveCustomizerBean} при SkipImport
     *
     * @throws Exception
     */
    @Test
    public void ouRemoveCustomizerAndSkipImport() throws Exception
    {
        // настройка системы
        MapProperties ouProps = new MapProperties();
        ouProps.setProperty(AbstractBO.TITLE, UUIDGenerator.get().nextUUID());
        ouProps.setProperty(AdvImportTestHelper.ID_HOLDER_ATTR, "importRoot");
        String ouUuid = dispatch.execute(new AddObjectAction(ouProps, ouFqn)).getObj().getUUID();

        OU ou = prefixLoader.get(ouUuid);

        IProperties idSet = new MapProperties();
        idSet.setProperty("idHolder", UUIDGenerator.get().nextUUID());
        testUtils.edit(ou, idSet);

        MapProperties childOu2Props = new MapProperties();
        childOu2Props.setProperty(AbstractBO.TITLE, "first ou");
        childOu2Props.setProperty(AdvImportTestHelper.ID_HOLDER_ATTR, "1");
        dispatch.execute(new AddObjectAction(childOu2Props, ouFqn)).getObj().getUUID();

        Config conf = prepareConf("ou_remove.advimport.xml");
        RemoveCustomizer removeCustomizerConf = (RemoveCustomizer)conf.getClasses().get(0).getCustomizers().get(0);
        removeCustomizerConf.setRoot(ou.getUUID());

        // вызов системы
        try (Engine engine = runImport(conf))
        {

            // проверка утверждений
            Assert.assertTrue("Иморт должен пройти без ошибок", engine.getErrors().isEmpty());
        }
        Assert.assertEquals("Должны создать 3 объекта: 2 принастройке теста и 1 во время импорта", 3,
                createdListener.getCreatedCount());

        TransactionTemplate tt = new TransactionTemplate(txManager);
        tt.execute(new TransactionCallback<Void>()
        {
            @Override
            public Void doInTransaction(TransactionStatus status)
            {
                OU ou1 = getOU("1");
                OU ou2 = getOU("2");

                Assert.assertFalse("Проимортированный объект не должен быть помечен на удаление", ou1.isRemoved());
                Assert.assertFalse("Проимортированный объект не должен быть помечен на удаление", ou2.isRemoved());

                return null;
            }
        });

        // очистка
    }

    /**
     * Проверяем правильность работы  {@link RemoveCustomizerBean}
     *
     * @throws Exception
     */
    @Test
    public void ouRemoveHierachyAfterRoot() throws Exception
    {
        // настройка системы
        IProperties idSet = new MapProperties();
        OU ou3 = testUtils.createOU(ouFqn); //+1
        idSet.setProperty("idHolder", "3");
        testUtils.edit(ou3, idSet);

        OU ou4 = testUtils.createOU(ou3, ouFqn); //+1
        idSet.setProperty("idHolder", "4");
        testUtils.edit(ou4, idSet);

        final Set<String> ouNotInImport = Sets.newHashSet();
        for (OU ou : Sets.newHashSet(null, ou3, ou4))
        {
            for (int i = 0; i < 2; i++)
            {
                OU childOu = testUtils.createOU(ou, ouFqn);
                idSet.setProperty("idHolder", UUIDGenerator.get().nextUUID());
                testUtils.edit(childOu, idSet);
                ouNotInImport.add(childOu.getUUID());
            }
        } //+6

        Root root = testUtils.getRoot();

        Config conf = prepareConf("ou_hierarchy_remove.advimport.xml"); //+2
        RemoveCustomizer removeCustomizerConf = (RemoveCustomizer)conf.getClasses().get(0).getCustomizers().get(0);
        removeCustomizerConf.setRoot(root.getUUID());
        // вызов системы
        try (Engine engine = runImport(conf))
        {
            // проверка утверждений
            Assert.assertTrue("Иморт должен пройти без ошибок", engine.getErrors().isEmpty());
        }
        Assert.assertEquals("Должны создать 10 объектов: 8 принастройке теста и 2 во время импорта", 10,
                createdListener.getCreatedCount());

        TransactionTemplate tt = new TransactionTemplate(txManager);
        tt.execute(new TransactionCallback<Void>()
        {
            @Override
            public Void doInTransaction(TransactionStatus status)
            {
                for (String ouUUID : ouNotInImport)
                {
                    OU cou = prefixLoader.get(ouUUID);
                    Assert.assertTrue("Должен быть в архиве т.к. не участвовал в импорте", cou.isRemoved());
                }

                OU ou1 = getOU("1");
                OU ou2 = getOU("2");
                OU ou3 = getOU("3");
                OU ou4 = getOU("4");
                Assert.assertFalse("Проимортированный объект не должен быть помечен на удаление", ou1.isRemoved());
                Assert.assertFalse("Проимортированный объект не должен быть помечен на удаление", ou2.isRemoved());
                Assert.assertFalse("Проимортированный объект не должен быть помечен на удаление", ou3.isRemoved());
                Assert.assertFalse("Проимортированный объект не должен быть помечен на удаление", ou4.isRemoved());

                return null;
            }
        });
        // очистка
    }

    @Test
    public void ouScript() throws Exception
    {
        // настройка системы
        Config conf = prepareConf("ou_script.advimport.xml");
        // вызов системы
        try (Engine engine = runImport(conf))
        {
            // проверка утверждений
            Assert.assertTrue(engine.getErrors().isEmpty());

        }
        Assert.assertEquals("Должны создать только один объект т.к. импортируем только один объект", 1,
                createdListener.getCreatedCount());

        TransactionTemplate tt = new TransactionTemplate(txManager);
        tt.execute(new TransactionCallback<Void>()
        {
            @Override
            public Void doInTransaction(TransactionStatus status)
            {
                OU ou = prefixLoader.get(createdListener.created.get(0));
                Assert.assertEquals("название созданного отдела должно соответствовать импортируемым данным",
                        "title from import (script changed)", ou.getTitle());

                return null;
            }
        });
        // очистк
    }

    @Test
    public void ouScript2() throws Exception
    {
        // настройка системы
        Config conf = prepareConf("ou_script2.advimport.xml");
        // вызов системы
        try (Engine engine = runImport(conf))
        {
            // проверка утверждений
            Assert.assertTrue(engine.getErrors().isEmpty());
        }
        Assert.assertEquals("Должны создать только один объект т.к. импортируем только один объект", 1,
                createdListener.getCreatedCount());

        TransactionTemplate tt = new TransactionTemplate(txManager);
        tt.execute(new TransactionCallback<Void>()
        {
            @Override
            public Void doInTransaction(TransactionStatus status)
            {
                OU ou = prefixLoader.get(createdListener.created.get(0));
                Assert.assertEquals("название созданного отдела должно соответствовать импортируемым данным",
                        "title from import (script changed)", ou.getTitle());

                return null;
            }
        });
        // очистк
    }

    @Test
    public void ouSkipImportWhenMetaClassIsImmutable() throws Exception
    {
        // настройка системы
        MapProperties ouProps = new MapProperties();
        ouProps.setProperty(AbstractBO.TITLE, UUIDGenerator.get().nextUUID());
        ouProps.setProperty(AdvImportTestHelper.ID_HOLDER_ATTR, "importRoot");
        String ouUuid = dispatch.execute(new AddObjectAction(ouProps, ouFqn)).getObj().getUUID();

        OU ou = prefixLoader.get(ouUuid);

        IProperties idSet = new MapProperties();
        idSet.setProperty("idHolder", UUIDGenerator.get().nextUUID());
        testUtils.edit(ou, idSet);

        MapProperties childOuProps = new MapProperties();
        childOuProps.setProperty(AbstractBO.TITLE, "first ou");
        childOuProps.setProperty(AdvImportTestHelper.ID_HOLDER_ATTR, "1");
        dispatch.execute(new AddObjectAction(childOuProps, ouFqn)).getObj().getUUID();

        Config conf = prepareConf("ou_immutable_metaclass.advimport.xml");

        // вызов системы
        try (Engine engine = runImport(conf))
        {

            // проверка утверждений
            Assert.assertTrue(engine.getErrors().isEmpty());

            String regexp = "ID=\\d skipped: Object.*is immutable.";
            Pattern p = Pattern.compile(regexp);
            Matcher m = p.matcher(getLog(engine));
            Assert.assertTrue("Объект не должен обновиться", m.find());
        }
    }

    /**
     * Проверяем импорт иерархических структур и их последующее обновление
     * @throws Exception
     */
    @Test
    public void ouWithEmployee() throws Exception
    {
        // настройка системы
        Config conf = prepareConf("ou_withEmployee.advimport.xml");
        // вызов системы
        try (Engine engine = runImport(conf))
        {
            // проверка утверждений
            Assert.assertTrue(engine.getErrors().isEmpty());
        }
        // сверяем данные с импортируемыми файлами
        Assert.assertEquals("Должны создать 8 объектов (по кол-ву импортируемых строк в одном и другом файле)", 8,
                createdListener.getCreatedCount());
        // сверяем данные с импортируемым файлом

        TransactionTemplate tt = new TransactionTemplate(txManager);
        tt.execute(new TransactionCallback<Void>()
        {
            @Override
            public Void doInTransaction(TransactionStatus status)
            {
                OU ou1 = getOU("ou1");
                OU ou2 = getOU("ou2");
                OU ou3 = getOU("ou3");
                OU ou4 = getOU("ou4");
                Employee empl1 = getEmployee("empl1");
                Employee empl2 = getEmployee("empl2");
                Employee empl3 = getEmployee("empl3");
                Employee empl4 = getEmployee("empl4");
                Assert.assertEquals(ou1, ou2.getParent());
                Assert.assertEquals(ou1, empl1.getParent());
                Assert.assertEquals(ou2, empl2.getParent());
                Assert.assertEquals(ou3, empl3.getParent());
                Assert.assertEquals(ou4, empl4.getParent());
                Assert.assertEquals(empl1, ou1.getHead());
                Assert.assertEquals(empl2, ou2.getHead());
                Assert.assertEquals(empl3, ou3.getHead());
                Assert.assertEquals(empl4, ou4.getHead());

                return null;
            }
        });
        // очистка
    }

    @Test
    public void ouXls() throws Exception
    {
        // настройка системы
        Config conf = prepareConf("ou_xls.advimport.xml");
        // вызов системы
        try (Engine engine = runImport(conf))
        {
            // проверка утверждений
            if (!engine.getErrors().isEmpty())
            {
                StringBuilder sb = new StringBuilder().append("Ошибки импорта\n");
                for (Throwable t : engine.getErrors())
                {
                    sb.append(t.getMessage()).append('\n');
                }
                Assert.fail(sb.toString());
            }
        }
        Assert.assertEquals("Должны создать 3 объекта (по кол-ву импортируемых строк)", 3,
                createdListener.getCreatedCount());

        OU ou = getOU("ou1");
        Assert.assertEquals("ou1 title", ou.getTitle());
        Assert.assertTrue(ou.isRemoved());
        Date expected = DateTimeFormat.forPattern("dd.MM.yy HH:mm").parseDateTime("01.01.2001 00:00").toDate();
        Assert.assertEquals(expected, ou.getRemovalDate()); // 2001-01-01 0:0:00

        OU ou2 = getOU("ou2");
        Assert.assertEquals("ou2 title", ou2.getTitle());
        Assert.assertFalse(ou2.isRemoved());

        OU ou3 = getOU("ou3");
        Assert.assertEquals("ou3 title", ou3.getTitle());
        Assert.assertFalse(ou3.isRemoved());
        // очистка
    }

    @Test
    public void ouXlsNumbersConversion() throws Exception
    {
        // Настройка системы
        String ouTitle1 = "50000009035";
        String ouTitle2 = "50";
        Config conf = prepareConf("numbers_xls.advimport.xml");
        // Вызов системы
        try (Engine engine = runImport(conf))
        {
            // проверка утверждений
            if (!engine.getErrors().isEmpty())
            {
                StringBuilder sb = new StringBuilder().append("Ошибки импорта\n");
                for (Throwable t : engine.getErrors())
                {
                    sb.append(t.getMessage()).append('\n');
                }
                Assert.fail(sb.toString());
            }
        }
        Assert.assertEquals("Должны создать 2 объекта (по кол-ву импортируемых строк)", 2,
                createdListener.getCreatedCount());

        OU ou1 = getOU("ou1");
        Assert.assertEquals("Название отдела не совпадает с названием в источнике данных.", ouTitle1, ou1.getTitle());
        OU ou2 = getOU("ou2");
        Assert.assertEquals("Название отдела не совпадает с названием в источнике данных.", ouTitle2, ou2.getTitle());
    }

    @Test
    public void ouXml() throws Exception
    {
        // настройка системы
        Config conf = prepareConf("ou_xml.advimport.xml");
        // вызов системы
        try (Engine engine = runImport(conf))
        {
            // проверка утверждений
            Assert.assertTrue(engine.getErrors().isEmpty());
        }
        Assert.assertEquals("Должны создать 2 объекта (по кол-ву импортируемых строк)", 2,
                createdListener.getCreatedCount());

        OU ou = getOU("itm1");
        Assert.assertEquals("itm1", ou.getTitle());
        Assert.assertTrue(ou.isRemoved());
        Date expected = DateTimeFormat.forPattern("dd.MM.yyyy HH:mm:ss").parseDateTime("01.01.2011 12:23:00").toDate();
        Assert.assertEquals(expected, ou.getRemovalDate()); // 2011-01-01 12:23:00
        // очистка
    }

    @Test
    public void ouXml2() throws Exception
    {
        // настройка системы
        Config conf = prepareConf("ou_xml2.advimport.xml");
        // вызов системы
        try (Engine engine = runImport(conf))
        {
            // проверка утверждений
            Assert.assertTrue(engine.getErrors().isEmpty());
        }
        Assert.assertEquals("Должны создать 3 объекта (по кол-ву импортируемых строк)", 3,
                createdListener.getCreatedCount());

        TransactionTemplate tt = new TransactionTemplate(txManager);
        tt.execute(new TransactionCallback<Void>()
        {
            @Override
            public Void doInTransaction(TransactionStatus status)
            {
                OU ou = getOU("ou1");
                Assert.assertEquals("Должно соответствовать импортируемым данным", "ou1", ou.getTitle());
                Assert.assertEquals("Должно соответствовать импортируемым данным", 0,
                        ou.getFlexes().<Collection<?>> getProperty("ouLinks").size());

                OU ou2 = getOU("ou2");
                Assert.assertEquals("Должно соответствовать импортируемым данным", "ou2", ou2.getTitle());
                Assert.assertEquals("Должно соответствовать импортируемым данным", 1,
                        ou2.getFlexes().<Collection<?>> getProperty("ouLinks").size());
                Assert.assertTrue("Должно соответствовать импортируемым данным",
                        ou2.getFlexes().<Collection<?>> getProperty("ouLinks").contains(ou));

                OU ou3 = getOU("ou3");
                Assert.assertEquals("Должно соответствовать импортируемым данным", "ou3", ou3.getTitle());
                Assert.assertEquals("Должно соответствовать импортируемым данным", 2,
                        ou3.getFlexes().<Collection<?>> getProperty("ouLinks").size());
                Assert.assertTrue("Должно соответствовать импортируемым данным",
                        ou3.getFlexes().<Collection<?>> getProperty("ouLinks").contains(ou));
                Assert.assertTrue("Должно соответствовать импортируемым данным",
                        ou3.getFlexes().<Collection<?>> getProperty("ouLinks").contains(ou2));

                return null;
            }
        });
        // очистка
    }

    /**
     * Конфигурация и данные в UTF с BOM
     * @throws Exception
     */
    @Test
    public void ouXml_UTF8BOM() throws Exception
    {
        // настройка системы
        Config conf = prepareConf("ou_xml.utf8.bom.advimport.xml");
        // вызов системы
        try (Engine engine = runImport(conf))
        {
            // проверка утверждений
            Assert.assertTrue(engine.getErrors().isEmpty());
        }
        Assert.assertEquals("Должны создать 2 объекта (по кол-ву импортируемых строк)", 2,
                createdListener.getCreatedCount());

        OU ou = getOU("itm1");
        Assert.assertEquals("itm1", ou.getTitle());
        Assert.assertTrue(ou.isRemoved());
        Date expected = DateTimeFormat.forPattern("dd.MM.yyyy HH:mm:ss").parseDateTime("01.01.2011 12:23:00").toDate();
        Assert.assertEquals(expected, ou.getRemovalDate()); // 2011-01-01 12:23:00
        // очистка
    }

    @Test
    public void ouXmlH() throws Exception
    {
        // настройка системы
        Config conf = prepareConf("ou_xml_hierarchy.advimport.xml");
        // вызов системы
        try (Engine engine = runImport(conf))
        {
            // проверка утверждений
            Assert.assertTrue(engine.getErrors().isEmpty());
        }
        Assert.assertEquals("Должны создать 4 объекта (по кол-ву импортируемых строк)", 4,
                createdListener.getCreatedCount());

        TransactionTemplate tt = new TransactionTemplate(txManager);
        tt.execute(new TransactionCallback<Void>()
        {
            @Override
            public Void doInTransaction(TransactionStatus status)
            {
                OU ou = getOU("ou1");
                Assert.assertEquals("ou1 title", ou.getTitle());
                Assert.assertFalse(ou.isRemoved());
                Assert.assertNull(ou.getParent());

                OU ou2 = getOU("ou2");
                Assert.assertEquals("ou2 title", ou2.getTitle());
                Assert.assertFalse(ou2.isRemoved());
                Assert.assertEquals(ou, ou2.getParent());

                OU ou3 = getOU("ou3");
                Assert.assertEquals("ou3 title", ou3.getTitle());
                Assert.assertFalse(ou3.isRemoved());
                Assert.assertEquals(ou2, ou3.getParent());

                OU ou4 = getOU("ou4");
                Assert.assertEquals("default title", ou4.getTitle());
                Assert.assertFalse(ou4.isRemoved());
                Assert.assertNull(ou4.getParent());
                return null;
            }
        });
        // очистка
    }

    @Test
    public void parameters() throws Exception
    {
        // настройка системы
        Config conf = prepareConf("parameters.advimport.xml");
        // вызов системы
        try (Engine engine = runImport(conf))
        {
            // проверка утверждений
            Assert.assertTrue(engine.getErrors().isEmpty());
        }
        Assert.assertEquals("Должны создать 3 объекта (по кол-ву импортируемых строк)", 3,
                createdListener.getCreatedCount());

        OU ou = getOU("ou1");
        Assert.assertEquals("defaultTitle", ou.getTitle());
        // очистка
    }

    /**
     * Проверяем правильность работы  {@link RemoveCustomizerBean} для объектов, у которых нет родителей(атрибута
     * parent) на примере Услуг
     *
     * @throws Exception
     */
    @Test
    public void serivceRemoveAfter() throws Exception
    {
        // настройка системы
        final SlmService srv = testUtils.createSlmService(srvFqn);

        Root root = testUtils.getRoot();

        IProperties idSet = new MapProperties();
        idSet.setProperty("idHolder", UUIDGenerator.get().nextUUID());
        testUtils.edit(srv, idSet);

        Config conf = prepareConf("service_remove.advimport.xml");
        RemoveCustomizer removeCustomizerConf = (RemoveCustomizer)conf.getClasses().get(0).getCustomizers().get(0);
        removeCustomizerConf.setRoot(root.getUUID());
        // вызов системы
        try (Engine engine = runImport(conf))
        {
            // проверка утверждений
            Assert.assertTrue("Иморт должен пройти без ошибок" + engine.getErrors().toString(),
                    engine.getErrors().isEmpty());
        }
        Assert.assertEquals("Должны создать 3 объекта: 1 принастройке теста и 2 во время импорта", 3,
                createdListener.getCreatedCount());

        TransactionTemplate tt = new TransactionTemplate(txManager);
        tt.execute(new TransactionCallback<Void>()
        {
            @Override
            public Void doInTransaction(TransactionStatus status)
            {
                SlmService service = prefixLoader.get(srv.getUUID());
                Assert.assertTrue("Должен быть в архиве т.к. не участвовал в импорте", service.isRemoved());

                SlmService srv1 = getService("1");
                SlmService srv2 = getService("2");

                Assert.assertFalse("Проимортированный объект не должен быть помечен на удаление", srv1.isRemoved());
                Assert.assertFalse("Проимортированный объект не должен быть помечен на удаление", srv2.isRemoved());
                return null;
            }
        });
        // очистка
    }

    /**
     * Запуск конфигурации импорта с заполнением GUI параметров скриптом(в API методе)
     * В качестве источника импорта используется byte[]
     * @throws Exception
     */
    @Test
    public void setAdvimportGuiParamsFromApiParams1() throws Exception
    {
        // настройка системы
        Config conf = prepareConf("csv.apiRun.advimport.xml");

        Map<String, Object> params = new HashMap<>();
        InputStream is = getClass().getResourceAsStream("apiRun.test.csv");
        String defaultValue = "defaultValue";
        params.put("fileForProcess", IOUtils.toByteArray(is));
        params.put("defaultValue", defaultValue);

        ArrayList<Parameter> parameters = advImportApiUtils.prepareParameters(params);

        // вызов системы
        runImport(conf, parameters).close();

        // проверка утверждений
        OU ou1 = getOU("ou1");
        Assert.assertEquals("title1", ou1.getTitle());

        OU ou2 = getOU("ou2");
        Assert.assertEquals(defaultValue, ou2.getTitle());

        // очистка
    }

    /**
     * Запуск конфигурации импорта с заполнением GUI параметров скриптом(в API методе)
     * В качестве источника импорта используется InputStream
     * @throws Exception
     */
    @Test
    public void setAdvimportGuiParamsFromApiParams2() throws Exception
    {
        // настройка системы
        Config conf = prepareConf("csv.apiRun.advimport.xml");

        Map<String, Object> params = new HashMap<>();
        InputStream is = getClass().getResourceAsStream("apiRun.test.csv");
        String defaultValue = "defaultValue";
        params.put("fileForProcess", is);
        params.put("defaultValue", defaultValue);

        ArrayList<Parameter> parameters = advImportApiUtils.prepareParameters(params);

        // вызов системы
        runImport(conf, parameters).close();

        // проверка утверждений
        OU ou1 = getOU("ou1");
        Assert.assertEquals("title1", ou1.getTitle());

        OU ou2 = getOU("ou2");
        Assert.assertEquals(defaultValue, ou2.getTitle());

        // очистка
    }

    @Override
    @Before
    public void setUp() throws Exception
    {
        super.setUp();

        TransactionTemplate tt = new TransactionTemplate(txManager);
        tt.execute(new TransactionCallback<Void>()
        {
            @Override
            public Void doInTransaction(TransactionStatus status)
            {
                try
                {
                    helper.createCase(ouFqn);
                    helper.createCase(ouFqn2);
                    helper.createCase(ouFqn3);
                    helper.createCase(employeeFqn);
                    helper.createCase(srvFqn);
                    helper.createCase(errorOuFqn);

                    createCollectionFlex(ouFqn, "ouLinks");
                    createHyperlinkFlex(ouFqn, "hl");
                    createDoubleFlex(ouFqn, "dbl");
                    createIntegerFlex(ouFqn, INT_ATTR_CODE);
                    createDtIntervalFlex(ouFqn, INTERVAL_ATTR_CODE);
                    createStringFlex(errorOuFqn, "stringId");

                    return null;
                }
                catch (Exception e)
                {
                    throw new FxException(e);
                }
            }
        });
    }

    @Test
    public void simpleOU() throws Exception
    {
        // настройка системы
        Config conf = prepareConf("ou.advimport.xml");
        // вызов системы
        try (Engine engine = runImport(conf))
        {
            // проверка утверждений
            Assert.assertTrue(engine.getErrors().isEmpty());
        }
        // очистка
    }

    @Test
    public void simpleOUBadParent() throws Exception
    {
        // настройка системы
        IProperties properties = new MapProperties();
        properties.setProperty("title", "ouImportRoot");
        testUtils.create(ouFqn, properties);

        Config conf = prepareConf("ouBadParent.advimport.xml");
        // вызов системы
        try (Engine engine = runImport(conf))
        {
            // проверка утверждений
            LOG.debug(getLog(engine));
            Assert.assertEquals("Должна возникнуть ошибка для отдела с родителем 'ou not exists'", 1,
                    engine.getErrors().size());
        }
        // очистка
    }

    /**
     * Проверяем импорт иерархических структур
     *
     * @throws Exception
     */
    @Test
    public void simpleOUWithHierarchy() throws Exception
    {
        // настройка системы
        Config conf = prepareConf("ou_hierarchy.advimport.xml");
        // вызов системы
        try (Engine engine = runImport(conf))
        {
            // проверка утверждений
            Assert.assertTrue(engine.getErrors().isEmpty());
        }
        // сверяем данные с импортируемым файлом

        TransactionTemplate tt = new TransactionTemplate(txManager);
        tt.execute(new TransactionCallback<Void>()
        {
            @Override
            public Void doInTransaction(TransactionStatus status)
            {
                OU ou1 = getOU("h1");
                OU ou2 = getOU("h2");
                OU ou3 = getOU("h3");
                OU ou4 = getOU("h4");
                OU ou5 = getOU("h5");

                Assert.assertEquals(ou1, ou3.getParent());
                Assert.assertEquals(ou3, ou4.getParent());
                Assert.assertEquals(ou2, ou5.getParent());
                Assert.assertNull(ou1.getParent());
                Assert.assertNull(ou2.getParent());

                return null;
            }
        });
        // очистка
    }

    @Test
    public void simpleOUWithoutTitle() throws Exception
    {
        // настройка системы
        Config conf = prepareConf("ouWithoutTitle.advimport.xml");
        // вызов системы
        try (Engine engine = runImport(conf))
        {
            // проверка утверждений
            LOG.debug(getLog(engine));
            Assert.assertEquals("Ни одна строчкане должна быть проимпортирована", 1, engine.getErrors().size());
        }
        // очистка
    }

    /**
     * Проверяем что при импорте уже существующих объектов происходит обновление существующих, а не создание новых
     * @throws Exception
     */
    @Test
    public void simpleOUWithUpdate() throws Exception
    {
        // настройка системы
        MapProperties properties = new MapProperties();
        properties.setProperty(ru.naumen.core.shared.Constants.AbstractBO.TITLE, UUIDGenerator.get().nextUUID());
        properties.setProperty(AdvImportTestHelper.ID_HOLDER_ATTR, "forUpdateTest");
        final GetDtObjectResponse result = dispatch.execute(new AddObjectAction(properties, ouFqn));

        Config conf = prepareConf("ou_update.advimport.xml");
        // вызов системы
        try (Engine engine = runImport(conf))
        {
            // проверка утверждений
            Assert.assertTrue(engine.getErrors().isEmpty());
        }

        // сверяем данные с импортируемым файлом
        TransactionTemplate tt = new TransactionTemplate(txManager);
        tt.execute(new TransactionCallback<Void>()
        {
            @Override
            public Void doInTransaction(TransactionStatus status)
            {
                OU ou = prefixLoader.get(result.getObj().getUUID());
                Assert.assertEquals("title from import", ou.getTitle());
                Assert.assertTrue(ou.isRemoved());
                Date expected;
                try
                {
                    expected = DateTimeFormat.forPattern("dd.MM.yyyy").parseDateTime("01.01.2000").toDate();
                }
                catch (Exception e)
                {
                    throw new FxException("Error parsing date", e);
                }
                Assert.assertEquals(expected, ou.getRemovalDate()); // 2000-01-01
                Assert.assertEquals("forUpdateTest", ou.getFlexes().getProperty(AdvImportTestHelper.ID_HOLDER_ATTR));
                return null;
            }
        });
        // очистка
    }

    /**
     * Проверяем скрытия статусбара в логах импорта
     *
     * @throws Exception
     */
    @Test
    public void statusbarDisabled() throws Exception
    {
        // настройка системы
        Config conf = prepareConf("double.advimport.xml");
        advimportApi.showStatusBar(false);
        // вызов системы
        try (Engine engine = runImport(conf))
        {
            // проверка утверждений
            String log = getLog(engine);
            LOG.debug(log);

            String resultRegex = "^ID=[^\\n]*\\n^ID=[^\\n]*\\n^Импорт 'ou' завершен за [^\\n]* сек. Строк обработано:"
                    + " 3. Проимпортировано: 3. Изменено: 0. Пропущено: 0. Возникло ошибок: 0.";
            Matcher matcher = Pattern.compile(resultRegex, Pattern.MULTILINE).matcher(log);
            try
            {
                Assert.assertTrue("В логе не содержится ожидаемое сообщение", matcher.find());
            }
            finally
            {
                // очистка
                advimportApi.showStatusBar(true);
            }
        }
    }

    /**
     * Проверяем отображение статусбара в логах импорта
     *
     * @throws Exception
     */
    @Test
    public void statusbarEnabled() throws Exception
    {
        // настройка системы
        Config conf = prepareConf("double.advimport.xml");
        // вызов системы
        try (Engine engine = runImport(conf))
        {
            // проверка утверждений
            String log = getLog(engine);
            LOG.debug(log);
            String resultRegex = "^\\[\\d+/3\\] ID=[^\\n]*\\nИмпорт 'ou' завершен за [^\\n]* сек. Строк обработано: 3"
                    + ". Проимпортировано: 3. Изменено: 0. Пропущено: 0. Возникло ошибок: 0.";
            Matcher matcher = Pattern.compile(resultRegex, Pattern.MULTILINE).matcher(log);
            Assert.assertTrue("В логе не содержится ожидаемое сообщение", matcher.find());
        }
        // очистка
    }

    /**
     * Проверяем сообщение при попытке остановить не существующую конфигурацию импорта
     */
    @Test
    public void stopNotExistentAdvimportConfig()
    {
        // настройка системы
        // вызов системы
        String code = "00000000-0000-0000-0000-000000000000";
        String result = advimportApi.stop(code);
        // проверка утверждений
        String expectedPattern = "Процедура импорта '%s' не может быть остановлена. Не существует конфигурации "
                + "импорта с таким идентификатором.";
        Assert.assertEquals(String.format(expectedPattern, code), result);
    }

    @Override
    @After
    public void tearDown() throws Exception
    {
        super.tearDown();
    }

    /**
     * Тестирование импорта объектов с локализованным title в режиме CREATE
     */
    @Test
    public void testCreateObjectsWithLocalizedTitle() throws Exception
    {
        // настройка системы
        TransactionTemplate tt = new TransactionTemplate(txManager);
        tt.execute((TransactionCallback<Void>)status ->
        {
            localizationService.enableTitleLocalization();
            return null;
        });
        Config conf = prepareConf("configLocalizedTitleCreate.xml");
        // вызов системы
        try (Engine engine = runImport(conf))
        {
            // проверка утверждений
            Assert.assertTrue(engine.getErrors().isEmpty());
        }
        Assert.assertEquals("Должны создать 3 объекта (по кол-ву импортируемых строк)", 3,
                createdListener.getCreatedCount());

        tt.execute((TransactionCallback<Void>)status ->
        {
            String errMsg = "Должно соответствовать импортируемым данным";
            HasLocalizedTitleAsFlexProperties ou = getOU("ou1");
            Assert.assertEquals(errMsg, "ouTitleBase", ou.getTitleForBaseLang());
            Assert.assertEquals(errMsg, "ouTitleRu", ou.getTitle(ILocaleInfo.DEFAULT_LANG));
            Assert.assertEquals(errMsg, "ouTitleEn", ou.getTitle(ILocaleInfo.ENGLISH));
            Assert.assertEquals(errMsg, "ouTitleClient", ou.getTitle(ILocaleInfo.CLIENT_LANG));

            ou = getOU("ou2");
            Assert.assertEquals(errMsg, "ouTitleBase", ou.getTitleForBaseLang());
            Assert.assertEquals(errMsg, "ouTitleRu", ou.getTitle(ILocaleInfo.DEFAULT_LANG));
            Assert.assertEquals(errMsg, "ouTitleEn", ou.getTitle(ILocaleInfo.ENGLISH));

            ou = getOU("ou3");
            Assert.assertEquals(errMsg, "ouTitleBase", ou.getTitleForBaseLang());
            Assert.assertEquals(errMsg, "ouTitleRu", ou.getTitle(ILocaleInfo.DEFAULT_LANG));
            return null;
        });
    }

    /**
     * Проверка записи ошибок импорта в лог
     */
    @Test
    public void testImportLogsErrors() throws Exception
    {
        Config conf = prepareConf("ou_import_with_errors.xml");

        try (Engine engine = runImport(conf))
        {

            Pattern regex = Pattern.compile(
                    ".*(Error ID=test_id: ru.naumen.advimport.server.engine.AdvImportException: "
                            + "Отдел не может быть добавлен по следующим причинам:).*");

            Matcher matcher = regex.matcher(getLog(engine));
            int numberOfOccurences = 0;
            while (matcher.find())
            {
                numberOfOccurences++;
            }

            Assert.assertEquals("В логе импорта должно быть 2 записи об ошибке", 2, numberOfOccurences);
        }
    }

    /**
     * Тестирование импорта объектов с локализованным title в режиме UPDATE
     */
    @Test
    public void testUpdateObjectsWithLocalizedTitle() throws Exception
    {
        Long expectedNumberValue = 333L;
        // настройка системы
        TransactionTemplate tt = new TransactionTemplate(txManager);
        tt.execute((TransactionCallback<Void>)status ->
        {
            localizationService.enableTitleLocalization();
            return null;
        });
        MapProperties properties = new MapProperties();
        String baseTitle = "ouTitleBase";
        properties.setProperty(AbstractBO.TITLE, baseTitle);
        properties.setProperty(AdvImportTestHelper.ID_HOLDER_ATTR, "ouForUpdate");
        final GetDtObjectResponse result = dispatch.execute(new AddObjectAction(properties, ouFqn));

        Config conf = prepareConf("configLocalizedTitleUpdate.xml");
        // вызов системы
        try (Engine engine = runImport(conf))
        {
            // проверка утверждений
            Assert.assertTrue(engine.getErrors().isEmpty());
        }
        tt = new TransactionTemplate(txManager);
        tt.execute((TransactionCallback<Void>)status ->
        {
            OU ou = prefixLoader.get(result.getObj().getUUID());
            String errMsg = "Должно соответствовать импортируемым данным";
            Assert.assertEquals(errMsg, baseTitle, ou.getTitleForBaseLang());
            Assert.assertEquals(errMsg, "ouTitleRu", ou.getTitle(ILocaleInfo.DEFAULT_LANG));
            Assert.assertEquals(errMsg, "ouTitleEn", ou.getTitle(ILocaleInfo.ENGLISH));
            Assert.assertEquals(errMsg, "ouTitleClient", ou.getTitle(ILocaleInfo.CLIENT_LANG));
            Assert.assertEquals(errMsg, expectedNumberValue, ou.getNumber());
            return null;
        });
    }

    @Test
    public void timeIntervalConvertor() throws Exception
    {
        // настройка системы
        Config conf = prepareConf("dtInterval.advimport.xml");
        // вызов системы
        try (Engine engine = runImport(conf))
        {
            // проверка утверждений
            Assert.assertEquals("Импортируемые данные содержат 1 ошибку", 1, engine.getErrors().size());
        }
        Assert.assertEquals("Должны создать 2 объекта (по кол-ву валидных импортируемых строк)", 2,
                createdListener.getCreatedCount());

        //т. к. все данные корректны - импортируется отдел со всеми атрибутами.
        OU ou = getOU("interval1");
        Assert.assertEquals("Должно соответствовать импортируемым данным", 567,
                ((DateTimeInterval)ou.getFlexes().getProperty(INTERVAL_ATTR_CODE)).getLength().longValue());
        Assert.assertEquals("Должно соответствовать импортируемым данным", Interval.SECOND,
                ((DateTimeInterval)ou.getFlexes().getProperty(INTERVAL_ATTR_CODE)).getInterval());
        //т. к. значение атрибута типа Интервал не заполнено - импортируется отдел, но атрибут Интервал = null.
        ou = getOU("interval2");
        Assert.assertNull("Значение типа временной интервал не должно быть заполнено",
                ou.getFlexes().getProperty(INTERVAL_ATTR_CODE));
        //т. к. значение атрибута типа Интервал не корректно - весь отдел со всеми атрибутами не импортируется.
        ou = getOU("interval3");
        Assert.assertTrue("Отдел не должен проимпортироваться", null == ou);
        // очистка
    }

    /**
     * Проверка исключения пустых строк при импорте из XLS
     *
     * @throws Exception
     */
    @Test
    public void xmlRemoveEmptyRows() throws Exception
    {
        // настройка системы
        Config conf = prepareConf("xls_with_empty_rows.advimport.xml");
        // вызов системы
        try (Engine engine = runImport(conf))
        {
            // проверка утверждений
            String log = getLog(engine);

            String resultRegex = "^Импорт 'obj' завершен за [^\\n]* сек. Строк обработано: 25. Проимпортировано: 0. "
                    + "Изменено: 0. Пропущено: 0. Возникло ошибок: 0.";
            Matcher matcher = Pattern.compile(resultRegex, Pattern.MULTILINE).matcher(log);
            Assert.assertTrue("В логе не содержится ожидаемое сообщение. Получено: " + log, matcher.find());
        }
    }

    /**
     * Проверка правильного отображения старого названия элемента каталога в логе при изменении элемента каталога
     *
     * @throws Exception
     */
    @Test
    public void testEditCatalogItemLog() throws Exception
    {
        //Создаем элемент каталога в разделе категории с заданными title и code
        testUtils.createCategoryCatalogItemWithTitle("Первый элемент", "firstElem");
        Config conf = prepareConf("configEditCatalogItem.xml");
        //вызов системы
        try (Engine engine = runImport(conf))
        {
            //проверка утверждений
            String log = getLog(engine);
            String resultRegex =
                    "\\[1/1] ID=firstElem updated: uuid=category\\$[^\\n]*, Название: 'Первый элемент' -> 'Новое "
                            + "название'.\n";
            Matcher matcher = Pattern.compile(resultRegex, Pattern.MULTILINE).matcher(log);
            Assert.assertTrue("В логе не содержится ожидаемое сообщение. Получено: " + log, matcher.find());
        }
    }

    protected void createCollectionFlex(ClassFqn fqn, String attr) throws Exception
    {
        if (!metainfoService.getMetaClass(fqn).hasAttribute(attr))
        {
            // создаем атрибут в котором будем хранить внешний идентификатор
            MapProperties typeProperties = new MapProperties();
            typeProperties.setProperty(Constants.ObjectAttributeType.METACLASS_FQN, "ou");

            IProperties viewPresentation = new MapProperties();
            viewPresentation.setProperty(Presentations.ATTR_CODE, Presentations.BO_LINKS_VIEW);

            IProperties editPresentation = new MapProperties();
            editPresentation.setProperty(Presentations.ATTR_CODE, Presentations.BO_LINKS_EDIT);

            //@formatter:off
            AddAttributeAction action = AddAttributeAction.create()
                    .setFqn(fqn)
                    .setCode(attr)
                    .setTypeCode(Constants.BOLinksAttributeType.CODE)
                    .setTitle("flex for id")
                    .setEditable(true)
                    .setTypeProperties(typeProperties)
                    .setViewPresentation(viewPresentation)
                    .setEditPresentation(editPresentation);
            //@formatter:on

            dispatch.execute(action);
        }
    }

    protected void createDoubleFlex(ClassFqn fqn, String attr) throws Exception
    {
        if (!metainfoService.getMetaClass(fqn).hasAttribute(attr))
        {
            // создаем атрибут в котором будем хранить внешний идентификатор
            MapProperties typeProperties = new MapProperties();

            IProperties viewPresentation = new MapProperties();
            viewPresentation.setProperty(Presentations.ATTR_CODE, Presentations.DOUBLE_VIEW);

            IProperties editPresentation = new MapProperties();
            editPresentation.setProperty(Presentations.ATTR_CODE, Presentations.DOUBLE_EDIT);

            //@formatter:off
            AddAttributeAction action = AddAttributeAction.create()
                    .setFqn(fqn)
                    .setCode(attr)
                    .setTypeCode(Constants.DoubleAttributeType.CODE)
                    .setTitle("flex for double")
                    .setEditable(true)
                    .setTypeProperties(typeProperties)
                    .setViewPresentation(viewPresentation)
                    .setEditPresentation(editPresentation);
            //@formatter:on

            dispatch.execute(action);
        }
    }

    protected void createDtIntervalFlex(ClassFqn fqn, String attr) throws Exception
    {
        if (!metainfoService.getMetaClass(fqn).hasAttribute(attr))
        {
            // создаем атрибут в котором будем хранить внешний идентификатор
            MapProperties typeProperties = new MapProperties();

            IProperties viewPresentation = new MapProperties();
            viewPresentation.setProperty(Presentations.ATTR_CODE, Presentations.DATETIME_INTERVAL_VIEW);

            IProperties editPresentation = new MapProperties();
            editPresentation.setProperty(Presentations.ATTR_CODE, Presentations.DATETIME_INTERVAL_EDIT);

            //@formatter:off
            AddAttributeAction action = AddAttributeAction.create()
                    .setFqn(fqn)
                    .setCode(attr)
                    .setTypeCode(Constants.DateTimeIntervalAttributeType.CODE)
                    .setTitle("flex for double")
                    .setEditable(true)
                    .setTypeProperties(typeProperties)
                    .setViewPresentation(viewPresentation)
                    .setEditPresentation(editPresentation);
            //@formatter:on

            dispatch.execute(action);
        }
    }

    protected void createHyperlinkFlex(ClassFqn fqn, String attr) throws Exception
    {
        if (!metainfoService.getMetaClass(fqn).hasAttribute(attr))
        {
            // создаем атрибут в котором будем хранить внешний идентификатор
            MapProperties typeProperties = new MapProperties();

            IProperties viewPresentation = new MapProperties();
            viewPresentation.setProperty(Presentations.ATTR_CODE, Presentations.HYPERLINK_VIEW);

            IProperties editPresentation = new MapProperties();
            editPresentation.setProperty(Presentations.ATTR_CODE, Presentations.HYPERLINK_EDIT);

            //@formatter:off
            AddAttributeAction action = AddAttributeAction.create()
                    .setFqn(fqn)
                    .setCode(attr)
                    .setTypeCode(Constants.HyperlinkAttributeType.CODE)
                    .setTitle("flex for hyperlink")
                    .setEditable(true)
                    .setTypeProperties(typeProperties)
                    .setViewPresentation(viewPresentation)
                    .setEditPresentation(editPresentation);
            //@formatter:on

            dispatch.execute(action);
        }
    }

    protected void createIntegerFlex(ClassFqn fqn, String attr) throws Exception
    {
        if (!metainfoService.getMetaClass(fqn).hasAttribute(attr))
        {
            // создаем атрибут в котором будем хранить внешний идентификатор
            MapProperties typeProperties = new MapProperties();

            IProperties viewPresentation = new MapProperties();
            viewPresentation.setProperty(Presentations.ATTR_CODE, Presentations.INTEGER_VIEW);

            IProperties editPresentation = new MapProperties();
            editPresentation.setProperty(Presentations.ATTR_CODE, Presentations.INTEGER_EDIT);

            //@formatter:off
            AddAttributeAction action = AddAttributeAction.create()
                    .setFqn(fqn)
                    .setCode(attr)
                    .setTypeCode(Constants.IntegerAttributeType.CODE)
                    .setTitle("flex for integer")
                    .setEditable(true)
                    .setTypeProperties(typeProperties)
                    .setViewPresentation(viewPresentation)
                    .setEditPresentation(editPresentation);
            //@formatter:on

            dispatch.execute(action);
        }
    }

    protected void createStringFlex(ClassFqn fqn, String attr) throws Exception
    {
        if (!metainfoService.getMetaClass(fqn).hasAttribute(attr))
        {
            // создаем атрибут в котором будем хранить внешний идентификатор
            MapProperties typeProperties = new MapProperties();

            IProperties viewPresentation = new MapProperties();
            viewPresentation.setProperty(Presentations.ATTR_CODE, Presentations.STRING_VIEW);

            IProperties editPresentation = new MapProperties();
            editPresentation.setProperty(Presentations.ATTR_CODE, Presentations.STRING_EDIT);

            //@formatter:off
            AddAttributeAction action = AddAttributeAction.create()
                    .setFqn(fqn)
                    .setCode(attr)
                    .setTypeCode(Constants.StringAttributeType.CODE)
                    .setTitle("flex for integer")
                    .setEditable(true)
                    .setTypeProperties(typeProperties)
                    .setViewPresentation(viewPresentation)
                    .setEditPresentation(editPresentation);
            //@formatter:on

            dispatch.execute(action);
        }
    }

    protected Employee getEmployee(final String holder)
    {
        return (Employee)testUtils
                .list(new DtoCriteria(employeeFqn, Filters.eq(AdvImportTestHelper.ID_HOLDER_ATTR, holder))).get(0);
    }

    protected OU getOU(final String holder)
    {
        List<?> list = testUtils.list(new DtoCriteria(ouFqn, Filters.eq(AdvImportTestHelper.ID_HOLDER_ATTR, holder)));
        return list.isEmpty() ? null : (OU)list.get(0);
    }

    protected SlmService getService(final String holder)
    {
        List<?> list = testUtils.list(new DtoCriteria(srvFqn, Filters.eq(AdvImportTestHelper.ID_HOLDER_ATTR, holder)));
        return list.isEmpty() ? null : (SlmService)list.get(0);
    }

    private void checkOus(String title, ClassFqn fqn, OU... ous)
    {
        for (OU ou : ous)
        {
            if (title.equals(ou.getTitle()))
            {
                Assert.assertEquals("Должны создать отдел с типом указанном в импортируемых данных", fqn.getCase(),
                        ou.getMetaCaseId());
                return;
            }
        }
        Assert.fail("Отдел '" + title + "' не найден");
    }

    private void employeeRemove(boolean isFromRoot, final boolean isUpdate) throws Exception
    {
        // настройка системы
        securityTestUtils.autenticateAsSuperUser();
        securityTestUtils.initLicensing();
        IProperties idSet = new MapProperties();
        OU ou1 = testUtils.createOU(ouFqn);
        idSet.setProperty("idHolder", "1");
        testUtils.edit(ou1, idSet);

        OU ou2 = testUtils.createOU(ou1, ouFqn);
        idSet.setProperty("idHolder", "2");
        testUtils.edit(ou2, idSet);

        final Set<String> emplNotInImport = Sets.newHashSet();
        for (OU ou : Sets.newHashSet(ou1, ou2))
        {
            for (int i = 0; i < 2; i++)
            {
                Employee empl = testUtils.createEmployee(ou, employeeFqn, false);
                idSet.setProperty("idHolder", UUIDGenerator.get().nextUUID());
                testUtils.edit(empl, idSet);
                emplNotInImport.add(empl.getUUID());
            }
        }

        Config conf;
        if (isUpdate)
        {
            conf = prepareConf("employee_remove_update.advimport.xml");
        }
        else
        {
            conf = prepareConf("employee_remove.advimport.xml");
        }
        RemoveCustomizer removeCustomizerConf = (RemoveCustomizer)conf.getClasses().get(0).getCustomizers().get(0);
        removeCustomizerConf.setRoot(isFromRoot ? testUtils.getRoot().getUUID() : ou1.getUUID());
        // вызов системы
        try (Engine engine = runImport(conf))
        {
            // проверка утверждений
            Assert.assertTrue("Импорт должен пройти без ошибок", engine.getErrors().isEmpty());
        }
        Assert.assertEquals("Должны создать 8 объектов: 6 принастройке теста и 2 во время импорта", 8,
                createdListener.getCreatedCount());

        TransactionTemplate tt = new TransactionTemplate(txManager);
        tt.execute(new TransactionCallback<Void>()
        {
            @Override
            public Void doInTransaction(TransactionStatus status)
            {
                for (String emplUUID : emplNotInImport)
                {
                    Employee cEmpl = prefixLoader.get(emplUUID);
                    if (isUpdate)
                    {
                        Assert.assertTrue("Должен быть в архиве т.к. не участвовал в импорте", cEmpl.isRemoved());
                    }
                    else
                    {
                        Assert.assertFalse("Не должен быть в архиве т.к. не использовался режим UPDATE",
                                cEmpl.isRemoved());
                    }
                }

                Employee empl1 = getEmployee("empl1");
                Employee empl2 = getEmployee("empl2");
                Assert.assertFalse("Проимпортированный объект не должен быть помечен на удаление", empl1.isRemoved());
                Assert.assertFalse("Проимпортированный объект не должен быть помечен на удаление", empl2.isRemoved());

                return null;
            }
        });
        // очистка
    }

    /**
     * Проверяем правильность работы <skip-objects-script> {@link RemoveCustomizerBean}
     *
     * @throws Exception
     */
    @Test
    public void ouRemoveSkip() throws Exception
    {
        MapProperties rootProps = new MapProperties();
        rootProps.setProperty(AbstractBO.TITLE, "first ou");
        rootProps.setProperty("idHolder", "1");
        OU rootOu = testUtils.createOU(null, ouFqn, rootProps);

        MapProperties firstChildProps = new MapProperties();
        firstChildProps.setProperty(AbstractBO.TITLE, "first-first ou");
        firstChildProps.setProperty("idHolder", "11");
        OU firstChildOu = testUtils.createOU(rootOu, ouFqn, firstChildProps);

        MapProperties secondChildProps = new MapProperties();
        secondChildProps.setProperty(AbstractBO.TITLE, "first-second ou");
        secondChildProps.setProperty("idHolder", "12");
        OU secondChildOu = testUtils.createOU(rootOu, ouFqn, secondChildProps);

        MapProperties secondFirstChildProps = new MapProperties();
        secondFirstChildProps.setProperty(AbstractBO.TITLE, "first-second-first ou");
        secondFirstChildProps.setProperty("idHolder", "121");
        OU secondFirstChildOu = testUtils.createOU(secondChildOu, ouFqn, secondFirstChildProps);

        Config conf = prepareConf("ou_remove_skip.advimport.xml");

        RemoveCustomizer removeCustomizerConf = (RemoveCustomizer)conf.getClasses().get(0).getCustomizers().get(0);
        removeCustomizerConf.setRoot(rootOu.getUUID());

        try (Engine engine = runImport(conf))
        {
            // проверка утверждений
            Assert.assertTrue("Импорт должен пройти без ошибок", engine.getErrors().isEmpty());
        }
        Assert.assertEquals("Должны создать 7 объектов: 4 при настройке теста и 3 во время импорта", 7,
                createdListener.getCreatedCount());

        TransactionTemplate tt = new TransactionTemplate(txManager);
        tt.execute(new TransactionCallback<Void>()
        {
            @Override
            public Void doInTransaction(TransactionStatus status)
            {
                OU fChildOu = prefixLoader.get(firstChildOu.getUUID());
                Assert.assertFalse("Не должен быть в архиве т.к. является дочерним для корневого элемента",
                        fChildOu.isRemoved());
                OU sChildOu = prefixLoader.get(secondChildOu.getUUID());
                Assert.assertFalse("Не должен быть в архиве т.к. является дочерним для корневого элемента",
                        sChildOu.isRemoved());

                OU sfChildOu = prefixLoader.get(secondFirstChildOu.getUUID());
                Assert.assertTrue("Должен быть в архиве так как не импортирован и не является дочерним для корневого "
                                + "элемента",
                        sfChildOu.isRemoved());
                return null;
            }
        });
    }

    /**
     * Проверяем правильность работы тэга {@link RemoveCustomizerBean}
     *
     * @throws Exception
     */
    @Test
    public void ouRemoveCondition() throws Exception
    {
        MapProperties rootProps = new MapProperties();
        rootProps.setProperty(AbstractBO.TITLE, "first ou");
        rootProps.setProperty("idHolder", "1");
        OU rootOu = testUtils.createOU(null, ouFqn, rootProps);

        MapProperties firstChildProps = new MapProperties();
        firstChildProps.setProperty(AbstractBO.TITLE, "first-first ou");
        firstChildProps.setProperty("idHolder", "11");
        OU firstChildOu = testUtils.createOU(rootOu, ouFqn, firstChildProps);

        MapProperties secondChildProps = new MapProperties();
        secondChildProps.setProperty(AbstractBO.TITLE, "first-second ou");
        secondChildProps.setProperty("idHolder", "12");
        OU secondChildOu = testUtils.createOU(rootOu, ouFqn, secondChildProps);

        MapProperties secondFirstChildProps = new MapProperties();
        secondFirstChildProps.setProperty(AbstractBO.TITLE, "first-second-first ou");
        secondFirstChildProps.setProperty("idHolder", "121");
        OU secondFirstChildOu = testUtils.createOU(secondChildOu, ouFqn, secondFirstChildProps);

        Config conf = prepareConf("ou_remove_condition.advimport.xml");

        RemoveCustomizer removeCustomizerConf = (RemoveCustomizer)conf.getClasses().get(0).getCustomizers().get(0);
        removeCustomizerConf.setRoot(rootOu.getUUID());

        try (Engine engine = runImport(conf))
        {
            // проверка утверждений
            Assert.assertTrue("Импорт должен пройти без ошибок", engine.getErrors().isEmpty());
        }
        Assert.assertEquals("Должны создать 7 объектов: 4 при настройке теста и 3 во время импорта", 7,
                createdListener.getCreatedCount());

        TransactionTemplate tt = new TransactionTemplate(txManager);
        tt.execute(new TransactionCallback<Void>()
        {
            @Override
            public Void doInTransaction(TransactionStatus status)
            {
                OU fChildOu = prefixLoader.get(firstChildOu.getUUID());
                Assert.assertFalse("Не должен быть в архиве т.к. является дочерним для корневого элемента",
                        fChildOu.isRemoved());
                Assert.assertNotEquals("Title не должен изменится так как объект исключается из архивирования",
                        "changed",
                        fChildOu.getTitle());
                OU sChildOu = prefixLoader.get(secondChildOu.getUUID());
                Assert.assertFalse("Не должен быть в архиве т.к. является дочерним для корневого элемента",
                        sChildOu.isRemoved());
                Assert.assertNotEquals("Title не должен изменится так как объект исключается из архивирования",
                        "changed",
                        sChildOu.getTitle());

                OU sfChildOu = prefixLoader.get(secondFirstChildOu.getUUID());
                Assert.assertTrue("Должен быть в архиве так как не импортирован и не является дочерним для корневого "
                                + "элемента",
                        sfChildOu.isRemoved());
                Assert.assertEquals("Title должен изменится так как объект не исключается из архивирования",
                        "changed",
                        sfChildOu.getTitle());
                return null;
            }
        });
    }
}
