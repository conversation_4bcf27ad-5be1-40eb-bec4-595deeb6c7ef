package ru.naumen.advimport;

import java.util.Collections;

import jakarta.inject.Inject;

import net.customware.gwt.dispatch.server.Dispatch;
import net.customware.gwt.dispatch.shared.DispatchException;

import org.springframework.stereotype.Component;

import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.Constants.Presentations;
import ru.naumen.metainfo.shared.dispatch2.AddAttributeAction;
import ru.naumen.metainfo.shared.dispatch2.AddMetaClassAction;

/**
 * 
 * <AUTHOR>
 *
 */
@Component
public class AdvImportTestHelper
{
    public static final String ID_HOLDER_ATTR = "idHolder";

    @Inject
    Dispatch dispatch;
    @Inject
    MetainfoService metainfoService;

    public void createCase(ClassFqn fqn) throws DispatchException
    {
        if (!metainfoService.isMetaclassExists(fqn))
        {
            dispatch.execute(new AddMetaClassAction(fqn, fqn.fqnOfClass(), null, "for test", "for test", Collections
                    .<String, String> emptyMap(), IProperties.EMPTY));

            // создаем атрибут в котором будем хранить внешний идентификатор
            IProperties viewPresentation = new MapProperties();
            viewPresentation.setProperty(Presentations.ATTR_CODE, Presentations.STRING_VIEW);

            IProperties editPresentation = new MapProperties();
            editPresentation.setProperty(Presentations.ATTR_CODE, Presentations.STRING_EDIT);

            //@formatter:off
            AddAttributeAction action = AddAttributeAction.create()
                    .setFqn(fqn)
                    .setCode(ID_HOLDER_ATTR)
                    .setTypeCode(Constants.StringAttributeType.CODE)
                    .setTitle("for external id")
                    .setEditable(true)
                    .setViewPresentation(viewPresentation)
                    .setEditPresentation(editPresentation);
            //@formatter:on

            dispatch.execute(action);
        }
    }

}
