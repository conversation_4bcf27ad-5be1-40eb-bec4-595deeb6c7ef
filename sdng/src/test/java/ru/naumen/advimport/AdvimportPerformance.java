package ru.naumen.advimport;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import ru.naumen.advimport.server.engine.Engine;
import ru.naumen.advimport.shared.config.Config;

/**
 * Тест на производительность advimport
 * Не содержит слова Test, чтобы не попадать в непрерывную интеграцию
 * <AUTHOR>
 * @since 22.09.2011
 *
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "/ru/naumen/core/server/dispatch/fullContext.xml")
public class AdvimportPerformance extends AdvimportTestBase
{
    @Override
    @Before
    public void setUp() throws Exception
    {
        super.setUp();
    }

    /**
     * тест просто создает много однотипных отделов. изначально написан для оценки скорости импорта, но так же проверяет
     *  многопоточный режим работы
     */
    @Test
    public void simpleOU10000() throws Exception
    {
        // настройка системы
        Config conf = prepareConf("ou10000.advimport.xml");
        // вызов системы
        try (Engine engine = runImport(conf))
        {
            // проверка утверждений
            Assert.assertTrue(engine.getErrors().isEmpty());
        }
        Assert.assertEquals(10000, createdListener.getCreatedCount());
        // очистка
    }

    @Override
    @After
    public void tearDown() throws Exception
    {
        super.tearDown();
    }

}
