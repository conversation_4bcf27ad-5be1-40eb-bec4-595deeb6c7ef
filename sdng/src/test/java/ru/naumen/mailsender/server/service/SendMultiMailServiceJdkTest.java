package ru.naumen.mailsender.server.service;

import static org.mockito.Mockito.when;

import java.util.HashMap;
import java.util.Random;

import org.apache.commons.lang3.RandomStringUtils;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import com.google.common.collect.Maps;

import jakarta.mail.internet.InternetAddress;
import ru.naumen.Assert;
import ru.naumen.core.server.ConfigurationProperties;
import ru.naumen.core.server.ConnectionValidationResultsStorage;
import ru.naumen.core.server.eventaction.jms.Notification;
import ru.naumen.core.server.filestorage.FileService;
import ru.naumen.core.server.mail.MimeMailWrapper;
import ru.naumen.core.server.net.ConnectionPermissionChecker;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.mailreader.server.processor.IMailProcessHelper;
import ru.naumen.mailsender.server.MailBeansConfig;
import ru.naumen.mailsender.server.outgoingmailserver.MailSettingsService;
import ru.naumen.metainfo.shared.elements.mail.MailProtocol;
import ru.naumen.metainfo.shared.elements.mail.OutgoingMailServerConfig;
import ru.naumen.metainfo.shared.elements.mail.SendMailParameters;

/**
 * Тестирование {@link SendMailServiceBase} с множеством подключений.
 * <AUTHOR>
 * @since 13.12.19
 */
public class SendMultiMailServiceJdkTest
{
    @Mock
    MailSettingsService mailSettings;
    @Mock
    ConnectionPermissionChecker connectionPermissionChecker;
    @Mock
    ConnectionValidationResultsStorage validationResultsStorage;
    @Mock
    MessageFacade messages;
    @Mock
    IMailProcessHelper mailProcessHelper;
    @Mock
    MailBeansConfig mailBeansConfig;
    @Mock
    JavaMailSenderCreator javaMailSenderCreator;
    @Mock
    ConfigurationProperties configurationProperties;
    @Mock
    MailAttachmentPreparer mailAttachmentPreparer;
    @Mock
    FileService fileService;

    SendMailServiceBase sendMailServiceBase;
    SendMailParameters parameters;

    static Random random = new Random();

    final String EMAIL_ADDRESS = "<EMAIL>";
    final String EMAIL_ADDRESS_UPPER_CASE = "<EMAIL>";

    @Before
    public void setUp()
    {
        MockitoAnnotations.initMocks(this);
        sendMailServiceBase = new SendMailServiceBaseJdkTest.SendMailServiceBaseStub(mailSettings,
            connectionPermissionChecker, validationResultsStorage, messages, mailProcessHelper,
                mailBeansConfig, configurationProperties, javaMailSenderCreator, mailAttachmentPreparer,
                fileService);

        when(configurationProperties.getUseEmailAllowUppercase()).thenReturn(false);
        parameters = new SendMailParameters();
        OutgoingMailServerConfig outgoingMailServerConfig = new OutgoingMailServerConfig();
        outgoingMailServerConfig.setConnectionProtocol(MailProtocol.SMTP);
        parameters.setMailServerConfig(outgoingMailServerConfig);
    }

    /**
     * Проверяем, что при создании пустого письма, основные поля (транслитерация заголовка, кодировка письма, отправитель)
     * заполняется из настроек указанного сервера исходящей почты, если их не переопределить
     */
    @Test
    public void testDefaultSettingsFromSpecificServer()
    {
        String codeServer = nextRandomAlphabeticString();
        String charset = nextRandomAlphabeticString();
        boolean transliterateSubject = random.nextBoolean();
        String name = nextRandomAlphabeticString();
        String email = nextRandomAlphabeticString();

        addSendMailParameters(codeServer, charset, transliterateSubject, name, email);

        MimeMailWrapper mail = (MimeMailWrapper)sendMailServiceBase.createEmptyMail();
        sendMailServiceBase.fillMailWrapperProperties(mail, codeServer);

        Assert.assertEquals(charset, mail.getCharSet());
        Assert.assertEquals(transliterateSubject, mail.isTransliterateSubject());
        Assert.assertEquals(name, mail.getFrom().getPersonal());
        Assert.assertEquals(email, mail.getFrom().getAddress());
    }

    /**
     * Проверяем, что при создании пустого письма, поля : транслитерация заголовка, отправитель
     * заполняется из настроек указанного сервера исходящей почты, а кодировку указываем явно в письме
     */
    @Test
    public void testDefaultSettingsFromSpecificServerWithOverriddenParametersCharset()
    {
        String codeServer = nextRandomAlphabeticString();
        String charset = nextRandomAlphabeticString();
        boolean transliterateSubject = random.nextBoolean();
        String name = nextRandomAlphabeticString();
        String email = nextRandomAlphabeticString();

        String newCharset = nextRandomAlphabeticString();

        addSendMailParameters(codeServer, charset, transliterateSubject, name, email);

        MimeMailWrapper mail = (MimeMailWrapper)sendMailServiceBase.createEmptyMail();
        mail.setCharSet(newCharset);
        sendMailServiceBase.fillMailWrapperProperties(mail, codeServer);

        Assert.assertEquals(newCharset, mail.getCharSet());
        Assert.assertEquals(transliterateSubject, mail.isTransliterateSubject());
        Assert.assertEquals(name, mail.getFrom().getPersonal());
        Assert.assertEquals(email, mail.getFrom().getAddress());
    }

    /**
     * Проверяем, что при создании пустого письма, поля : транслитерация заголовка, кодировка
     * заполняется из настроек указанного сервера исходящей почты, а отправителя указываем явно в письме
     */
    @Test
    public void testDefaultSettingsFromSpecificServerWithOverriddenParametersFrom()
    {
        String codeServer = nextRandomAlphabeticString();
        String charset = nextRandomAlphabeticString();
        boolean transliterateSubject = random.nextBoolean();
        String name = nextRandomAlphabeticString();
        String email = nextRandomAlphabeticString();

        addSendMailParameters(codeServer, charset, transliterateSubject, name, email);

        String newNameFrom = nextRandomAlphabeticString();
        String newEmailFrom = nextRandomAlphabeticString();

        MimeMailWrapper mail = (MimeMailWrapper)sendMailServiceBase.createEmptyMail();
        mail.setFrom(newNameFrom, newEmailFrom);
        sendMailServiceBase.fillMailWrapperProperties(mail, codeServer);

        Assert.assertEquals(charset, mail.getCharSet());
        Assert.assertEquals(transliterateSubject, mail.isTransliterateSubject());
        Assert.assertEquals(newNameFrom, mail.getFrom().getPersonal());
        Assert.assertEquals(newEmailFrom, mail.getFrom().getAddress());
    }

    /**
     * Проверяем, что при создании пустого письма, поля : колдировка, отправитель
     * заполняется из настроек указанного сервера исходящей почты, а транслитерацию заголовка указываем явно в письме
     */
    @Test
    public void testDefaultSettingsFromSpecificServerWithOverriddenParametersTransliterateSubject()
    {
        String codeServer = nextRandomAlphabeticString();
        String charset = nextRandomAlphabeticString();
        boolean transliterateSubject = random.nextBoolean();
        String name = nextRandomAlphabeticString();
        String email = nextRandomAlphabeticString();

        addSendMailParameters(codeServer, charset, transliterateSubject, name, email);

        boolean newTransliterateSubject = !transliterateSubject;

        MimeMailWrapper mail = (MimeMailWrapper)sendMailServiceBase.createEmptyMail();
        mail.setTransliterateSubject(newTransliterateSubject);
        sendMailServiceBase.fillMailWrapperProperties(mail, codeServer);

        Assert.assertEquals(charset, mail.getCharSet());
        Assert.assertEquals(newTransliterateSubject, mail.isTransliterateSubject());
        Assert.assertEquals(name, mail.getFrom().getPersonal());
        Assert.assertEquals(email, mail.getFrom().getAddress());
    }

    /**
     * Создаем объект для хранения параметров исходящей почты
     * @param code код сервера исходящей почты
     * @param charset кодировка
     * @param transliterateSubject транслитерация заголовков
     * @param nameFrom имя "от кого"
     * @param emailFrom email "от кого?"
     */
    private void addSendMailParameters(String code, String charset, boolean transliterateSubject, String nameFrom,
                                       String emailFrom)
    {
        SendMailParameters sendMailParameters = new SendMailParameters();
        sendMailParameters.setCharacterEncoding(charset);
        sendMailParameters.setTransliterateSubject(transliterateSubject);
        sendMailParameters.setName(nameFrom);
        sendMailParameters.setFrom(emailFrom);

        when(mailSettings.getSendMailParameters(code)).thenReturn(sendMailParameters);
    }

    /**
     * Генерирует рандомную строку без чисел
     * @return рандомная строка
     */
    private String nextRandomAlphabeticString()
    {
        return RandomStringUtils.randomAlphabetic(random.nextInt(Integer.SIZE) + 1);
    }

    /**
     * Тестирование приведение к нижнему регистру адреса эл.почты в зависимости заданного пользователем от параметра
     */
    @Test
    public void testUseLowCaseEmailsMailWrapper()
    {
        //настройка
        HashMap<String, String> emails = Maps.<String, String> newHashMap();
        emails.put(EMAIL_ADDRESS_UPPER_CASE, "test");
        //вызов

        Notification notification1 = new Notification.Builder()
                .setParameters(parameters)
                .setTo(emails)
                .build();

        MimeMailWrapper mimeMailWrapper = sendMailServiceBase.createMimeMailWrapper(notification1, null);
        InternetAddress internetAddress = mimeMailWrapper.getToRecipients().stream().findFirst().orElse(null);
        //проверка
        Assert.assertEquals(1, mimeMailWrapper.getToRecipients().size());
        Assert.assertEquals(internetAddress.getAddress(), EMAIL_ADDRESS);

        //вызов
        Notification notification2 = new Notification.Builder()
                .setParameters(parameters)
                .setTo(emails)
                .setUseUpperCase(true)
                .build();

        MimeMailWrapper mimeMailWrapperUpper = sendMailServiceBase.createMimeMailWrapper(notification2, null);
        InternetAddress internetAddressUpper = mimeMailWrapperUpper.getToRecipients().stream().findFirst().orElse(null);
        internetAddressUpper.getAddress().equals(EMAIL_ADDRESS_UPPER_CASE);
        Assert.assertEquals(1, mimeMailWrapperUpper.getToRecipients().size());
        Assert.assertEquals(EMAIL_ADDRESS_UPPER_CASE, internetAddressUpper.getAddress());
    }

    /**
     * Тестирование обработки исходящего письма, если email получателя равен null
     */
    @Test
    public void testProcessEmailAddressWithNull()
    {
        //настройка
        HashMap<String, String> emails = Maps.<String, String> newHashMap();
        emails.put(null, "test");
        emails.put("test email", "test2");
        //вызов

        Notification notification = new Notification.Builder()
                .setParameters(parameters)
                .setTo(emails)
                .build();

        MimeMailWrapper mimeMailWrapper = sendMailServiceBase.createMimeMailWrapper(notification, null);
        Assert.assertEmpty(mimeMailWrapper.getToRecipients());
    }

    /**
     * Тестирование обработки исходящего письма с почтовым адресом, у которого в домене один символ перед точкой
     */
    @Test
    public void testProcessEmailAddressWithOneLetterDomain()
    {
        //настройка
        HashMap<String, String> emails = new HashMap<>();
        emails.put("<EMAIL>", "test");

        //вызов
        Notification notification = new Notification.Builder()
                .setParameters(parameters)
                .setTo(emails)
                .build();
        MimeMailWrapper mimeMailWrapper = sendMailServiceBase.createMimeMailWrapper(notification, null);

        //проверка
        Assert.assertEquals(1, mimeMailWrapper.getToRecipients().size());
    }
}
