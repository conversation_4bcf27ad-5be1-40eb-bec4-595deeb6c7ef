package ru.naumen.mailsender.server.service;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import static ru.naumen.mailsender.shared.MailSenderConstants.OUTGOING_MAIL_SERVER_CONFIG;

import org.eclipse.angus.mail.util.MailSSLSocketFactory;
import org.hibernate.SessionFactory;
import org.junit.Test;
import org.springframework.mail.javamail.JavaMailSender;

import jakarta.annotation.Nullable;
import ru.naumen.Assert;
import ru.naumen.core.server.CommonUtils;
import ru.naumen.core.server.ConfigurationProperties;
import ru.naumen.core.server.ConnectionValidationResultsStorage;
import ru.naumen.core.server.bo.root.RootDao;
import ru.naumen.core.server.common.FormattersSrv;
import ru.naumen.core.server.filestorage.FileContentStorage;
import ru.naumen.core.server.filestorage.FileDao;
import ru.naumen.core.server.filestorage.FileService;
import ru.naumen.core.server.filestorage.FileServiceImpl;
import ru.naumen.core.server.filestorage.spi.FileStorageSettingsService;
import ru.naumen.core.server.mail.IMimeMailWrapper;
import ru.naumen.core.server.mail.SSLSocketMailConfigurator;
import ru.naumen.core.server.net.ConnectionPermissionChecker;
import ru.naumen.core.server.objectloader.IPrefixObjectLoaderService;
import ru.naumen.core.server.script.api.StringApi;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.common.Formatters;
import ru.naumen.mailreader.server.processor.IMailProcessHelper;
import ru.naumen.mailsender.server.MailBeansConfig;
import ru.naumen.mailsender.server.outgoingmailserver.MailSettingsService;
import ru.naumen.mailsender.server.service.sender.InterruptableJavaMailSender;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.server.utils.AttributeHelper;
import ru.naumen.metainfo.shared.elements.mail.MailProtocol;
import ru.naumen.metainfo.shared.elements.mail.OutgoingMailServerConfig;
import ru.naumen.metainfo.shared.elements.mail.OutgoingMailServerConfig.ConnectionSecurityProtocol;
import ru.naumen.metainfo.shared.elements.mail.SendMailParameters;
import ru.naumen.sec.server.AntivirusValidationService;
import ru.naumen.sec.server.autorize.AuthorizationService;
import ru.naumen.sec.server.autorize.cache.AuthorizeServiceUtils;

/**
 * Тестирование {@link SendMailServiceBase}
 * <AUTHOR>
 * @since 26.08.19
 */
public class SendMailServiceBaseJdkTest
{
    public static class SendMailServiceBaseStub extends SendMailServiceBase
    {
        SendMailServiceBaseStub(MailSettingsService mailSettings,
                ConnectionPermissionChecker connectionPermissionChecker,
                ConnectionValidationResultsStorage validationResultsStorage,
                MessageFacade messages,
                IMailProcessHelper mailProcessHelper,
                MailBeansConfig mailBeansConfig,
                ConfigurationProperties configurationProperties,
                JavaMailSenderCreator javaMailSenderCreator,
                MailAttachmentPreparer mailAttachmentPreparer,
                FileService fileService)
        {
            super(mailSettings, connectionPermissionChecker, validationResultsStorage, messages, mailProcessHelper,
                    mailBeansConfig, configurationProperties, javaMailSenderCreator, mailAttachmentPreparer,
                    fileService);
        }

        @Nullable
        @Override
        public Long sendSingleMail(IMimeMailWrapper wrapper)
        {
            return null;
        }

        @Nullable
        @Override
        public Long sendSingleMail(IMimeMailWrapper wrapper, String outgoingMailServerCode)
        {
            return null;
        }

        public JavaMailSender createExternalSmtpMailSenderForTest(@Nullable String outgoingMailSenderCode)
        {
            return super.createExternalMailSender(outgoingMailSenderCode);
        }
    }

    /**
     * Проверка создания сессии с игнорированием проверки сертификата для SSL+SMTP
     */
    @Test
    public void createJavaMailSenderWithSkipCertVerificationForSsl()
    {
        OutgoingMailServerConfig outgoingMailServerConfig = new OutgoingMailServerConfig();
        outgoingMailServerConfig.setSkipCertVerification(true);
        outgoingMailServerConfig.setSecurityProtocol(ConnectionSecurityProtocol.SSL);
        outgoingMailServerConfig.setSendMailParameters(new SendMailParameters());
        outgoingMailServerConfig.setConnectionProtocol(MailProtocol.SMTP);
        outgoingMailServerConfig.setCode(OUTGOING_MAIL_SERVER_CONFIG);

        MailSettingsService mailSettings = mock(MailSettingsService.class);
        when(mailSettings.getOutgoingMailServerConfigOrDefault(any())).thenReturn(outgoingMailServerConfig);

        ConnectionPermissionChecker connectionPermissionChecker = mock(ConnectionPermissionChecker.class);
        ConnectionValidationResultsStorage validationResultsStorage = mock(ConnectionValidationResultsStorage.class);
        MessageFacade messages = mock(MessageFacade.class);
        IMailProcessHelper mailProcessHelper = mock(IMailProcessHelper.class);
        MailBeansConfig mailBeansConfig = mock(MailBeansConfig.class);
        JavaMailSenderCreator javaMailSenderCreator = new JavaMailSenderCreator(mailBeansConfig, mock(
                ConfigurationProperties.class), mock(SSLSocketMailConfigurator.class));
        ConfigurationProperties configurationProperties = mock(ConfigurationProperties.class);
        MailAttachmentPreparer mailAttachmentPreparer = mock(MailAttachmentPreparer.class);

        FileService fileService = new FileServiceImpl(mock(MetainfoService.class),
                mock(Formatters.class), mock(FormattersSrv.class), mock(CommonUtils.class), mock(AttributeHelper.class),
                mock(IPrefixObjectLoaderService.class), mock(FileStorageSettingsService.class),
                mock(IPrefixObjectLoaderService.class), mock(SessionFactory.class), configurationProperties,
                mock(FileContentStorage.class), mock(StringApi.class), messages, mock(AntivirusValidationService.class),
                mock(AuthorizationService.class), mock(AuthorizeServiceUtils.class), mock(FileDao.class),
                mock(RootDao.class));

        SendMailServiceBaseStub sendMailServiceBase = new SendMailServiceBaseStub(mailSettings,
                connectionPermissionChecker, validationResultsStorage, messages, mailProcessHelper,
                mailBeansConfig, configurationProperties, javaMailSenderCreator, mailAttachmentPreparer,
                fileService);

        InterruptableJavaMailSender javaMailSender =
                (InterruptableJavaMailSender)sendMailServiceBase.createExternalSmtpMailSenderForTest(
                null);

        Assert.assertEquals(javaMailSender.getProtocol(), "smtps");
        Assert.assertTrue(Boolean.parseBoolean(
                javaMailSender.getJavaMailProperties().get("mail.smtps.ssl.enable").toString()));
        Assert.assertEquals(javaMailSender.getJavaMailProperties().get("mail.smtps.ssl.socketFactory").getClass(),
                MailSSLSocketFactory.class);
    }

    /**
     * Проверка создания сессии с игнорированием проверки сертификата для TLS+SMTP
     */
    @Test
    public void createJavaMailSenderWithSkipCertVerificationForTls()
    {
        OutgoingMailServerConfig outgoingMailServerConfig = new OutgoingMailServerConfig();
        outgoingMailServerConfig.setSkipCertVerification(true);
        outgoingMailServerConfig.setSecurityProtocol(ConnectionSecurityProtocol.TLS);
        outgoingMailServerConfig.setSendMailParameters(new SendMailParameters());
        outgoingMailServerConfig.setConnectionProtocol(MailProtocol.SMTP);
        outgoingMailServerConfig.setCode(OUTGOING_MAIL_SERVER_CONFIG);

        MailSettingsService mailSettings = mock(MailSettingsService.class);
        when(mailSettings.getOutgoingMailServerConfigOrDefault(any())).thenReturn(outgoingMailServerConfig);

        ConnectionPermissionChecker connectionPermissionChecker = mock(ConnectionPermissionChecker.class);
        ConnectionValidationResultsStorage validationResultsStorage = mock(ConnectionValidationResultsStorage.class);
        MessageFacade messages = mock(MessageFacade.class);
        IMailProcessHelper mailProcessHelper = mock(IMailProcessHelper.class);
        MailBeansConfig mailBeansConfig = mock(MailBeansConfig.class);

        JavaMailSenderCreator javaMailSenderCreator = new JavaMailSenderCreator(mailBeansConfig, mock(
                ConfigurationProperties.class), mock(SSLSocketMailConfigurator.class));
        ConfigurationProperties configurationProperties = mock(ConfigurationProperties.class);
        MailAttachmentPreparer mailAttachmentPreparer = mock(MailAttachmentPreparer.class);

        FileService fileService = new FileServiceImpl(mock(MetainfoService.class),
                mock(Formatters.class), mock(FormattersSrv.class), mock(CommonUtils.class), mock(AttributeHelper.class),
                mock(IPrefixObjectLoaderService.class), mock(FileStorageSettingsService.class),
                mock(IPrefixObjectLoaderService.class), mock(SessionFactory.class), configurationProperties,
                mock(FileContentStorage.class), mock(StringApi.class), messages, mock(AntivirusValidationService.class),
                mock(AuthorizationService.class), mock(AuthorizeServiceUtils.class), mock(FileDao.class),
                mock(RootDao.class));

        SendMailServiceBaseStub sendMailServiceBase = new SendMailServiceBaseStub(mailSettings,
                connectionPermissionChecker, validationResultsStorage, messages, mailProcessHelper,
                mailBeansConfig, configurationProperties, javaMailSenderCreator, mailAttachmentPreparer,
                fileService);

        InterruptableJavaMailSender javaMailSender =
                (InterruptableJavaMailSender)sendMailServiceBase.createExternalSmtpMailSenderForTest(
                null);

        Assert.assertEquals(javaMailSender.getProtocol(), "smtp");
        Assert.assertTrue(Boolean.parseBoolean(
                javaMailSender.getJavaMailProperties().get("mail.smtp.starttls.enable").toString()));
        Assert.assertEquals(javaMailSender.getJavaMailProperties().get("mail.smtp.ssl.socketFactory").getClass(),
                MailSSLSocketFactory.class);
    }

    /**
     * Проверка создания сессии без игнорирования проверки сертификата для TLS+SMTP
     */
    @Test
    public void createJavaMailSenderWithoutSslAndTls()
    {
        OutgoingMailServerConfig outgoingMailServerConfig = new OutgoingMailServerConfig();
        outgoingMailServerConfig.setSecurityProtocol(ConnectionSecurityProtocol.UNSECURED);
        outgoingMailServerConfig.setConnectionProtocol(MailProtocol.SMTP);
        outgoingMailServerConfig.setSendMailParameters(new SendMailParameters());
        outgoingMailServerConfig.setCode(OUTGOING_MAIL_SERVER_CONFIG);

        MailSettingsService mailSettings = mock(MailSettingsService.class);
        when(mailSettings.getOutgoingMailServerConfigOrDefault(any())).thenReturn(outgoingMailServerConfig);

        ConnectionPermissionChecker connectionPermissionChecker = mock(ConnectionPermissionChecker.class);
        ConnectionValidationResultsStorage validationResultsStorage = mock(ConnectionValidationResultsStorage.class);
        MessageFacade messages = mock(MessageFacade.class);
        IMailProcessHelper mailProcessHelper = mock(IMailProcessHelper.class);
        MailBeansConfig mailBeansConfig = mock(MailBeansConfig.class);

        JavaMailSenderCreator javaMailSenderCreator = new JavaMailSenderCreator(mailBeansConfig, mock(
                ConfigurationProperties.class), mock(SSLSocketMailConfigurator.class));
        ConfigurationProperties configurationProperties = mock(ConfigurationProperties.class);
        MailAttachmentPreparer mailAttachmentPreparer = mock(MailAttachmentPreparer.class);

        FileService fileService = new FileServiceImpl(mock(MetainfoService.class),
                mock(Formatters.class), mock(FormattersSrv.class), mock(CommonUtils.class), mock(AttributeHelper.class),
                mock(IPrefixObjectLoaderService.class), mock(FileStorageSettingsService.class),
                mock(IPrefixObjectLoaderService.class), mock(SessionFactory.class), configurationProperties,
                mock(FileContentStorage.class), mock(StringApi.class), messages, mock(AntivirusValidationService.class),
                mock(AuthorizationService.class), mock(AuthorizeServiceUtils.class), mock(FileDao.class),
                mock(RootDao.class));

        SendMailServiceBaseStub sendMailServiceBase = new SendMailServiceBaseStub(mailSettings,
                connectionPermissionChecker, validationResultsStorage, messages, mailProcessHelper,
                mailBeansConfig, configurationProperties, javaMailSenderCreator, mailAttachmentPreparer,
                fileService);

        InterruptableJavaMailSender javaMailSender =
                (InterruptableJavaMailSender)sendMailServiceBase.createExternalSmtpMailSenderForTest(
                null);

        Assert.assertEquals(javaMailSender.getProtocol(), "smtp");
        Assert.assertNull(
                javaMailSender.getJavaMailProperties().get("mail.smtps.ssl.enable"));
        Assert.assertNull(
                javaMailSender.getJavaMailProperties().get("mail.smtp.starttls.enable"));
        Assert.assertNull(javaMailSender.getJavaMailProperties().get("mail.smtps.ssl.socketFactory"));
    }
}
