package ru.naumen.mailsender.server.service;

import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.transaction.PlatformTransactionManager;

import ru.naumen.commons.shared.FxException;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.CommonUtils;
import ru.naumen.core.server.events.EventService;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.mailreader.server.processor.IMailProcessHelper;
import ru.naumen.mailsender.server.outgoingmailserver.MailSettingsService;
import ru.naumen.mailsender.server.service.sender.OutboundMailContainer;
import ru.naumen.mailsender.server.service.strategy.AbstractSendingStrategy;
import ru.naumen.mailsender.server.service.strategy.OneByOneSendingStrategy;

/**
 * JUnit тесты для {@link AbstractSendingStrategy}
 * <AUTHOR>
 * @since 04.12.2020
 **/
@RunWith(MockitoJUnitRunner.class)
public class AbstractSendingStrategyJdkTest
{
    /**
     * Проверим, что при попытке отправить уведомление об успехе отправки почты с OutboundMailContainer без UUID не
     * будет ошибки
     */
    @Test
    public void testDefferedEventNotificationSendSuccessful()
    {
        final CommonUtils commonUtils = mock(CommonUtils.class);
        final OutboundMailContainer container = new OutboundMailContainer();
        final EventService eventService = mock(EventService.class);

        final AbstractSendingStrategy sendingStrategy = new OneByOneSendingStrategy(null,
                mock(PlatformTransactionManager.class), mock(MailSettingsService.class),
                null, null, null,
                null, null, eventService, commonUtils, null);
        ReflectionTestUtils.invokeMethod(sendingStrategy, "txEventNotificationSendSuccessful", container,
                StringUtilities.EMPTY,StringUtilities.EMPTY);

        verify(commonUtils, never()).getByUUID(anyString());
    }

    /**
     * Проверим, что при попытке отправить уведомление о крахе отправки почты с OutboundMailContainer без UUID не
     * будет ошибки
     */
    @Test
    public void testDefferedEventNotificationSendFailed()
    {
        final CommonUtils commonUtils = mock(CommonUtils.class);
        final OutboundMailContainer container = new OutboundMailContainer();
        final EventService eventService = mock(EventService.class);

        final AbstractSendingStrategy sendingStrategy = new OneByOneSendingStrategy(null,
                mock(PlatformTransactionManager.class), mock(MailSettingsService.class), null, null,
                null, null, mock(IMailProcessHelper.class), eventService, commonUtils, mock(MessageFacade.class));
        ReflectionTestUtils.invokeMethod(sendingStrategy, "txEventNotificationSendFailed", StringUtilities.EMPTY,
                new FxException("test"), container, null, null, StringUtilities.EMPTY, StringUtilities.EMPTY);

        verify(commonUtils, never()).getByUUID(anyString());
    }
}