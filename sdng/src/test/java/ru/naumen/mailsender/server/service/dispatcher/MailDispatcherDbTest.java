package ru.naumen.mailsender.server.service.dispatcher;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.when;
import static ru.naumen.mailsender.shared.MailSenderConstants.DEFAULT_CONFIG_CODE;
import static ru.naumen.mailsender.shared.MailSenderConstants.OUTGOING_MAIL_SERVER_CONFIG;

import java.io.IOException;
import java.io.InputStream;
import java.net.URISyntaxException;
import java.util.Arrays;
import java.util.Base64;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import jakarta.activation.DataSource;
import jakarta.inject.Inject;
import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;

import org.junit.After;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.springframework.mail.MailSendException;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

import com.google.common.collect.Lists;

import ru.naumen.Assert;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.commons.shared.utils.MimeTypeRegistry;
import ru.naumen.core.server.CommonUtils;
import ru.naumen.core.server.ConfigurationProperties;
import ru.naumen.core.server.dispatch.SecurityTestUtils;
import ru.naumen.core.server.filestorage.FileUtils;
import ru.naumen.core.server.mail.IMimeMailWrapper;
import ru.naumen.core.server.mail.ISendMailService;
import ru.naumen.core.server.mail.MimeMailWrapper;
import ru.naumen.core.server.script.spi.IScriptDtObject;
import ru.naumen.core.server.script.spi.ScriptUtils;
import ru.naumen.core.server.upload.NestedFileItem;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.dto.ISDtObject;
import ru.naumen.mailsender.server.outgoingmailserver.MailSettingsService;
import ru.naumen.mailsender.server.service.sender.InterruptableJavaMailSender;
import ru.naumen.mailsender.server.service.sender.OutboundMailContainer;
import ru.naumen.mailsender.server.service.sender.OutboundMailContainerDao;
import ru.naumen.mailsender.server.service.strategy.AbstractSendingStrategy;
import ru.naumen.mailsender.server.service.strategy.BulkSendingStrategy;
import ru.naumen.mailsender.server.service.strategy.OneByOneSendingStrategy;
import ru.naumen.metainfo.shared.elements.mail.OutgoingMailServerConfig;
import ru.naumen.metainfo.shared.elements.mail.SendMailParameters;

/**
 * Тестирование класса {@link MailDispatcher}}
 *
 * <AUTHOR>
 * <AUTHOR>
 * @since  17.07.2015
 */
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class MailDispatcherDbTest
{
    private static final int MESSAGES_PER_ITERATION = 10;
    private static final String IMG = "iVBORw0KGgoAAAANSUhEUgAAACYAAAAmCAYAAACoPemuAAAC1UlEQVR4AWIYVsBvNYMEoBt7gJFkC6M4/ibP9tq2bdt2tLZt27Zt27Zt29bZf5JKcsdVvdW9mOQXXJ7S1zeDKhiNzTiP+5az2IBhKI//vR3GD0WwBu8gm15jCXJ5I1RG7IMMc1AdWRAd/1hiITvqYD5k2IrkbgT6Hv3xHsJONMNPDtb4Cx1wCMIbtMO3nob6GxshywgXLnQ0ZFmCX5wu8CcOQjiG1C6+Fnlw2Xi0v9id+APWQdiGiF54Z2MYj3YRvrUzaRCEk4hs9nkh3FUIbUIbnMF40VP4oB7mM0pKopDq1D77L7pr4cZAWB3cgEIQdvr4V+RnnIGQPqgBKyC0+AQ/cb0hTA3YERZvIfzm1oYxYsSIitL4NpRgUSE8w69mR0UIc10M9RsuQqhm466tgpDXbBwOoZaLwQZDeI9sNoK1h9DZbNwIIatLodLhLYRJNt+zUhDmmI3nIMS0uXFr9MIPQfR9j0MQbuNfm8HSQdhpNt6CENZGqLCQZSr8gggtS0UHX2YiCEfNxpsQwtm8Y1MgS1+jPQ5eQFjpsGQkhnDEbDwDIZbNYD9gHWRpCT+sh/AU0R0GSw9hu9m4BkJuBy/4nzhsfHmzIEsTD4psBQjTzcbBEBo4/PrC4gxk2IdvPQjWBUL7oD7VxR6Uhli4DeENPDnTmyUrW8Bj9CsI/3kQLhV2oIGHoeJDeIgfA3YugNDlE/yIj4AwOqjObMYZ/1sfhgoL4T0Sh/acR/sw2DQIc0Mrcm8gFPRBqCrGcSdKaINbQriKmF4MFR+vIVSz+z+KRRDOIJaXQt2EMMbpOXw1hFso4fLjew1hHr51usBPmAtZxuPPjwgUHtMgyxjHoczHikZG8b2EgYjlYI14GAlZnqCaW7c/NpZChnXohLLIiGSWTKiA7tgCWd5hNqJ440tKiQl4Atn0EGOQ0BdF8SfkRHvMxDYcxAFsxTS0QWb88M3X9PcBZ+9gGHm6fNEAAAAASUVORK5CYII=";
    @Inject
    MailSettingsService mailSettingsService;
    @Inject
    OutboundMailContainerDao dao;
    @Inject
    MailDispatcher dispatcher;
    @Inject
    ScriptUtils scriptUtils;
    @Inject
    ConfigurationProperties configurationProperties;
    @Mock
    InterruptableJavaMailSender mailSender;
    @Inject
    private SecurityTestUtils securityTestUtils;
    @Inject
    private MailSettingsService mailSettings;
    @Inject
    private PlatformTransactionManager txManager;
    @Inject
    private ISendMailService simpleSendMail;
    @Inject
    private CommonUtils utils;

    @Before
    public void setUp() throws Exception
    {
        securityTestUtils.autenticateAsSuperUser();
        securityTestUtils.initLicensing();
        MockitoAnnotations.initMocks(this);
        ((AbstractSendingStrategy)dispatcher.getStrategy()).setThreadPoolSize(1);

        final Properties props = new Properties();
        props.setProperty(OUTGOING_MAIL_SERVER_CONFIG,
                mailSettings.getOutgoingMailServerConfigOrDefault(DEFAULT_CONFIG_CODE).getCode());
        //Настройка
        when(mailSender.createMimeMessage()).thenCallRealMethod();
        when(mailSender.createMimeMessage(any(InputStream.class))).thenCallRealMethod();
        when(mailSender.getJavaMailProperties()).thenReturn(props);
        setSendMailParams(1, 10);
    }

    @After
    public void tearDown() throws Exception
    {
        configurationProperties.setMailDispatcherSendingStrategy(OneByOneSendingStrategy.STRATEGY_NAME);
    }

    /**
     * Тестирование неудачной отправки всех писем при стратегии отправки {@link BulkSendingStrategy}
     * @throws Exception
     */
    @Test
    public void testFailAllSendingsWithBulkSendingStrategy() throws Exception
    {
        configurationProperties.setMailDispatcherSendingStrategy(BulkSendingStrategy.STRATEGY_NAME);
        //создание письма
        MimeMessage email = createMessage();

        //выполнение действий
        List<OutboundMailContainer> containers = enqueueMails(email, 10);
        List<Long> failedContainers = setFailOnEachNmailBehaviour(containers, false, 1);
        dispatcher.work(mailSender);

        //проверка
        List<Long> listWaitingMessages = dao.listWaitingMessages(10);
        deleteMails(containers);
        Assert.assertContentEquals(failedContainers, listWaitingMessages);
    }

    /**
     * Тестирование неудачной отправки всех писем при стратегии отправки {@link OneByOneSendingStrategy}
     * @throws Exception
     */
    @Test
    public void testFailAllSendingsWithOneByOneStrategy() throws Exception
    {
        configurationProperties.setMailDispatcherSendingStrategy(OneByOneSendingStrategy.STRATEGY_NAME);
        //создание письма
        MimeMessage email = createMessage();

        //выполнение действий
        List<OutboundMailContainer> containers = enqueueMails(email, MESSAGES_PER_ITERATION);
        List<Long> failedContainers = setFailOnEachNmailBehaviour(containers, true, 1);
        dispatcher.work(mailSender);

        //проверка
        List<Long> listWaitingMessages = dao.listWaitingMessages(MESSAGES_PER_ITERATION);
        deleteMails(containers);
        Assert.assertContentEquals(failedContainers, listWaitingMessages);
    }

    /**
     * Тестирование неудачной отправки четных писем при стратегии отправки {@link BulkSendingStrategy}
     * @throws Exception
     */
    @Test
    public void testFailEachEvenSendingsWithBulkSendingStrategy() throws Exception
    {
        configurationProperties.setMailDispatcherSendingStrategy(BulkSendingStrategy.STRATEGY_NAME);
        //создание письма
        MimeMessage email = createMessage();

        //выполнение действий
        List<OutboundMailContainer> containers = enqueueMails(email, MESSAGES_PER_ITERATION);
        List<Long> failedContainers = setFailOnEachNmailBehaviour(containers, false, 2);
        dispatcher.work(mailSender);

        //проверка
        List<Long> listWaitingMessages = dao.listWaitingMessages(MESSAGES_PER_ITERATION);
        deleteMails(containers);
        Assert.assertContentEquals(failedContainers, listWaitingMessages);
    }

    /**
     * Тестирование неудачной отправки четных писем при стратегии отправки {@link OneByOneSendingStrategy}
     * @throws Exception
     */
    @Test
    public void testFailEachEvenSendingsWithOneByOneStrategy() throws Exception
    {
        configurationProperties.setMailDispatcherSendingStrategy(OneByOneSendingStrategy.STRATEGY_NAME);
        //создание письма
        MimeMessage email = createMessage();

        //выполнение действий
        List<OutboundMailContainer> containers = enqueueMails(email, MESSAGES_PER_ITERATION);
        List<Long> failedContainers = setFailOnEachNmailBehaviour(containers, true, 2);
        dispatcher.work(mailSender);

        //проверка
        List<Long> listWaitingMessages = dao.listWaitingMessages(MESSAGES_PER_ITERATION);
        deleteMails(containers);
        Assert.assertContentEquals(failedContainers, listWaitingMessages);
    }

    /**
     * Тестирование прерываня долгих отправок при стратегии {@link BulkSendingStrategy}
     * @throws Exception
     */
    @Test
    public void testInterruptLongSendingsWithBulkStrategy() throws Exception
    {
        configurationProperties.setMailDispatcherSendingStrategy(BulkSendingStrategy.STRATEGY_NAME);
        //создание письма
        MimeMessage email = createMessage();

        //выполнение действий
        List<OutboundMailContainer> containers = enqueueMails(email, MESSAGES_PER_ITERATION);
        setSendingTaskDurabiliry(false, 11);
        ((AbstractSendingStrategy)dispatcher.getStrategy()).setTaskTimeout(1);
        setSendMailParams(2, 10);
        dispatcher.work(mailSender);

        //проверка
        List<Long> listWaitingMessages = dao.listWaitingMessages(MESSAGES_PER_ITERATION);
        deleteMails(containers);
        Assert.assertContentEquals(containers.stream().map(OutboundMailContainer::getId).collect(Collectors.toList()),
                listWaitingMessages);
    }

    /**
     * Тестирование прерываня долгих отправок при стратегии {@link OneByOneSendingStrategy}
     * @throws Exception
     */
    @Test
    public void testInterruptLongSendingsWithOneByOneStrategy() throws Exception
    {
        configurationProperties.setMailDispatcherSendingStrategy(OneByOneSendingStrategy.STRATEGY_NAME);
        //создание письма
        MimeMessage email = createMessage();

        //выполнение действий
        List<OutboundMailContainer> containers = enqueueMails(email, MESSAGES_PER_ITERATION);
        setSendingTaskDurabiliry(true, 11);
        ((AbstractSendingStrategy)dispatcher.getStrategy()).setThreadPoolSize(5);
        ((AbstractSendingStrategy)dispatcher.getStrategy()).setTaskTimeout(1);
        setSendMailParams(2, 10);
        dispatcher.work(mailSender);

        //проверка
        List<Long> listWaitingMessages = dao.listWaitingMessages(MESSAGES_PER_ITERATION);
        deleteMails(containers);
        Assert.assertContentEquals(containers.stream().map(OutboundMailContainer::getId).collect(Collectors.toList()),
                listWaitingMessages);
    }

    /**
     * Тестирование удачной отправки всех писем при стратегии отправки {@link BulkSendingStrategy}
     * @throws Exception
     */
    @Test
    public void testSuccessAllMultithreadSendingsWithBulkSendingStrategy() throws Exception
    {
        configurationProperties.setMailDispatcherSendingStrategy(BulkSendingStrategy.STRATEGY_NAME);
        //создание письма
        MimeMessage email = createMessage();

        //выполнение действий
        List<OutboundMailContainer> containers = enqueueMails(email, 30);
        List<Long> failedContainers = setFailOnEachNmailBehaviour(containers, false, 0);
        ((AbstractSendingStrategy)dispatcher.getStrategy()).setThreadPoolSize(3);
        dispatcher.work(mailSender);

        //проверка
        List<Long> listWaitingMessages = dao.listWaitingMessages(30);
        deleteMails(containers);
        Assert.assertContentEquals(failedContainers, listWaitingMessages);
    }

    /**
     * Тестирование удачной отправки всех писем при стратегии отправки {@link BulkSendingStrategy}
     * @throws Exception
     */
    @Test
    @Ignore("NSDAT-11178 Исправление FileStorageApiDbTest и MailDispatcherDbTest")
    public void testSuccessAllSendingsWithBulkSendingStrategy() throws Exception
    {
        configurationProperties.setMailDispatcherSendingStrategy(BulkSendingStrategy.STRATEGY_NAME);
        //создание письма
        MimeMessage email = createMessage();

        //выполнение действий
        List<OutboundMailContainer> containers = enqueueMails(email, MESSAGES_PER_ITERATION);
        List<Long> failedContainers = setFailOnEachNmailBehaviour(containers, false, 0);
        dispatcher.work(mailSender);

        //проверка
        List<Long> listWaitingMessages = dao.listWaitingMessages(MESSAGES_PER_ITERATION);
        deleteMails(containers);
        Assert.assertContentEquals(failedContainers, listWaitingMessages);
    }

    /**
     * Тестирование удачной отправки всех писем при стратегии отправки {@link OneByOneSendingStrategy}
     * @throws Exception
     */
    @Test
    public void testSuccessAllSendingsWithOneByOneStrategy() throws Exception
    {
        configurationProperties.setMailDispatcherSendingStrategy(OneByOneSendingStrategy.STRATEGY_NAME);
        //создание письма
        MimeMessage email = createMessage();

        //выполнение действий
        List<OutboundMailContainer> containers = enqueueMails(email, MESSAGES_PER_ITERATION);
        List<Long> failedContainers = setFailOnEachNmailBehaviour(containers, true, 0);
        dispatcher.work(mailSender);

        //проверка
        List<Long> listWaitingMessages = dao.listWaitingMessages(MESSAGES_PER_ITERATION);
        deleteMails(containers);
        Assert.assertContentEquals(failedContainers, listWaitingMessages);
    }

    /**
     * Добавить файл на карточку компании
     * @return БО файл
     * @throws URISyntaxException
     */
    private IUUIDIdentifiable addFile() throws URISyntaxException
    {
        TransactionTemplate tt = new TransactionTemplate(txManager);
        tt.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
        final String fileName = "ok.png";
        IUUIDIdentifiable result = tt.execute(new TransactionCallback<IUUIDIdentifiable>()
        {
            @Override
            public IUUIDIdentifiable doInTransaction(TransactionStatus status)
            {
                MapProperties properties = new MapProperties();
                properties.setProperty(ru.naumen.core.shared.Constants.File.CREATION_DATE, new Date());
                properties.setProperty(ru.naumen.core.shared.Constants.File.SOURCE, "root");
                properties.setProperty(ru.naumen.core.shared.Constants.File.RELATION, null);
                properties.setProperty(ru.naumen.core.shared.Constants.File.CONTENT, createNestedFileItem(fileName));
                properties.setProperty(ru.naumen.core.shared.Constants.File.TITLE, fileName);
                properties.setProperty(ru.naumen.core.shared.Constants.File.DESCRIPTION, "test storage");

                IUUIDIdentifiable result = utils.create(ru.naumen.core.shared.Constants.File.FQN, properties);

                return result;
            }
        });

        return result;
    }

    /**
     * Создать объект письма в формате JavaMail
     * @return объект письма в формате JavaMail
     * @throws URISyntaxException
     * @throws MessagingException
     * @throws IOException
     */
    private MimeMessage createMessage() throws URISyntaxException, MessagingException, IOException
    {
        IMimeMailWrapper message = simpleSendMail.createMail();
        message.setSubject("Тема сообщения");
        IUUIDIdentifiable fileObj = addFile();
        ISDtObject file = scriptUtils.get(fileObj.getUUID());
        DataSource source = scriptUtils.getFileDataSource((IScriptDtObject)file);
        message.enableApacheCommonsEmail();
        message.setInline(source);
        message.attachFile(source);
        message.setText("text <img src='" + source.getName() + "'></img>text");
        message.addTo("test", "<EMAIL>");
        message.setFrom("test", "<EMAIL>");
        return ((MimeMailWrapper)message).toMimeMessage(mailSender);
    }

    /**
     * Создать объект файл для сохранения в БД
     * @param fileName имя файла
     * @return объект файл
     */
    private static NestedFileItem createNestedFileItem(String fileName)
    {
        return new NestedFileItem(Base64.getDecoder().decode(IMG), fileName,
                MimeTypeRegistry.getMimeTypeByFileExtension(FileUtils.getExtension(fileName)));
    }

    /**
     * Удалить письма из очереди на отправку
     * @param containers письма для удаления
     */
    private void deleteMails(List<OutboundMailContainer> containers)
    {
        for (OutboundMailContainer container : containers)
        {
            dao.delete(container.getId());
        }
    }

    /**
     * Добавляет письмо в очередь на отправку указанное количество раз
     * @param email объект письма в формате JavaMail
     * @param count количество раз
     * @return письма на отправку
     */
    @SuppressWarnings("deprecation")
    private List<OutboundMailContainer> enqueueMails(MimeMessage email, int count)
    {
        List<OutboundMailContainer> containers = Lists.newArrayList();
        for (int i = 0; i < count; i++)
        {
            containers.add(dao.save(email, null,
                    mailSettings.getOutgoingMailServerConfigOrDefault(DEFAULT_CONFIG_CODE)
                            .getCode()));
        }
        return containers;
    }

    /**
     * Установка поведения mailSender'у, отклоняющее каждое письмо кратное параметру divider
     * @param containers письма для отправки
     * @param oneByOne признак стратегии {@link OneByOneSendingStrategy}
     * @param divider делитель
     * @return идентификаторы писем которые будут отклонены
     */
    private List<Long> setFailOnEachNmailBehaviour(List<OutboundMailContainer> containers, boolean oneByOne,
            int divider)
    {
        if (divider == 0)
        {
            return Lists.newArrayList();
        }
        List<Long> failedContainers = IntStream.range(0, containers.size()).filter(i -> i % divider == 0)
                .mapToObj(i -> containers.get(i).getId()).collect(Collectors.toList());
        if (oneByOne)
        {
            doAnswer(new Answer<Void>()
            {
                private int counter = 0;

                @Override
                public Void answer(InvocationOnMock invocation) throws Throwable
                {
                    if (counter++ % divider == 0)
                    {
                        Map<Object, Exception> failedMessages = new LinkedHashMap<>();
                        failedMessages.put(invocation.getArguments(), new Exception());
                        throw new MailSendException(failedMessages);
                    }
                    return null;
                }
            }).when(mailSender).send(any(MimeMessage.class));
        }
        else
        {
            Answer<Void> answer = new Answer<Void>()
            {

                @Override
                public Void answer(InvocationOnMock invocation) throws Throwable
                {
                    Object[] messages = invocation.getArguments();
                    if (!failedContainers.isEmpty())
                    {
                        Map<Object, Exception> failedMessages = Arrays.stream(messages).filter(m ->
                        {
                            try
                            {
                                String messageId = ((MimeMessage)m).getMessageID();
                                return failedContainers.stream().anyMatch(cId -> messageId.contains("." + cId + "."));
                            }
                            catch (MessagingException e)
                            {
                                return false;
                            }
                        }).collect(Collectors.toMap(m -> m, m ->
                        {
                            return new Exception();
                        }));
                        throw new MailSendException(failedMessages);
                    }
                    return null;
                }
            };
            doAnswer(answer).when(mailSender).send(Mockito.any(MimeMessage[].class));
        }
        return failedContainers;
    }

    private void setSendMailParams(int sendAttempts, int messagesPerIter)
    {
        OutgoingMailServerConfig defaultConfig = mailSettings
                .getOutgoingMailServerConfigOrDefault(DEFAULT_CONFIG_CODE);
        SendMailParameters param = mailSettings.getSendMailParameters(DEFAULT_CONFIG_CODE);
        param.setFrom("<EMAIL>");
        param.setSendAttempts(sendAttempts);
        param.setMessagesPerIteration(messagesPerIter);
        final Properties props = new Properties();
        props.setProperty(OUTGOING_MAIL_SERVER_CONFIG, DEFAULT_CONFIG_CODE);
        defaultConfig.setSendMailParameters(param);
        mailSettingsService.saveOutgoingMailServerConfig(defaultConfig);
    }

    /**
     * Установка поведения mailSender'у, отклоняющее каждое письмо кратное параметру divider
     * @param oneByOne признак стратегии {@link OneByOneSendingStrategy}
     * @param waitSeconds делитель
     * @return идентификаторы писем которые будут отклонены
     */
    private void setSendingTaskDurabiliry(boolean oneByOne, int waitSeconds)
    {

        if (oneByOne)
        {
            doAnswer(new Answer<Void>()
            {
                @Override
                public Void answer(InvocationOnMock invocation) throws Throwable
                {
                    try
                    {
                        Thread.sleep(waitSeconds * 1000);
                    }
                    catch (InterruptedException e)
                    {
                        Map<Object, Exception> failedMessages = new LinkedHashMap<>();
                        failedMessages.put(invocation.getArguments(), e);
                        throw new MailSendException(failedMessages);
                    }
                    return null;
                }
            }).when(mailSender).send(any(MimeMessage.class));
        }
        else
        {
            Answer<Void> answer = new Answer<Void>()
            {
                @Override
                public Void answer(InvocationOnMock invocation) throws Throwable
                {
                    Object[] messages = invocation.getArguments();
                    try
                    {
                        Thread.sleep(waitSeconds * 1000);
                    }
                    catch (InterruptedException e)
                    {
                        Map<Object, Exception> failedMessages = Arrays.stream(messages)
                                .collect(Collectors.toMap(Function.identity(), m -> e));
                        throw new MailSendException(failedMessages);
                    }

                    return null;
                }
            };
            doAnswer(answer).when(mailSender).send(Mockito.any(MimeMessage[].class));
        }
    }

}
