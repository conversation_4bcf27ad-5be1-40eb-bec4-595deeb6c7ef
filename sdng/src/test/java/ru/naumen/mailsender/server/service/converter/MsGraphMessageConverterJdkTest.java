package ru.naumen.mailsender.server.service.converter;

import static org.junit.jupiter.api.Assertions.*;

import java.io.File;
import java.io.IOException;
import java.net.URISyntaxException;
import java.util.Base64;
import java.util.List;

import org.apache.commons.io.FileUtils;
import org.junit.Test;

import com.microsoft.graph.models.Attachment;
import com.microsoft.graph.models.FileAttachment;
import com.microsoft.graph.models.Message;

import jakarta.mail.MessagingException;
import ru.naumen.Assert;
import ru.naumen.core.server.mail.MimeMailWrapper;

/**
 * JUnit тесты для {@link MsGraphMessageConverter}
 * <AUTHOR>
 * @since 26.09.2024
 */
public class MsGraphMessageConverterJdkTest
{
    private String text = "<span style=\"font-family: Arial; font-size: small;\"><div> test message text&nbsp;"
            + "</div><div> <img src=\"data:image/png;base64,%s\" style=\"display: inline-block; vertical-align: "
            + "bottom; margin-right: 5px; margin-left: 5px; max-width: calc(100%% - 10px); text-align: center; "
            + "cursor: pointer; padding: 0px 1px; position: relative;\" width=\"700\" height=\"363\" "
            + "fr-original-style=\"display: inline-block; vertical-align: bottom; margin-right: 5px; margin-left: 5px; max-width: calc(100%% - 10px); text-align: center;\" fr-original-class=\"fr-draggable\"></div></span>";

    /**
     * Тестирование процесса конвертации MimeMailWrapper к Message
     */
    @Test
    public void toMsGraphMessageTest() throws MessagingException, IOException, URISyntaxException
    {
        byte[] fileContent = FileUtils.readFileToByteArray(
                new File(getClass().getResource("/ru/naumen/mailsender/server/service/converter/img.png").toURI()));
        String encodedString = Base64.getEncoder().encodeToString(fileContent);

        String expectedSubject  = "testSubject";
        String contentType = "text/html";
        String replyToEmail = "<EMAIL>";
        String recipientToEmail = "<EMAIL>";
        String bccEmail = "<EMAIL>";
        String ccToEmail = "<EMAIL>";
        String fromEmail = "<EMAIL>";

        MimeMailWrapper mimeMailWrapper = new MimeMailWrapper();
        mimeMailWrapper.setSubject(expectedSubject);
        mimeMailWrapper.setText(String.format(text, encodedString));
        mimeMailWrapper.setContentType(contentType);

        mimeMailWrapper.addReplyTo("replyTo", replyToEmail);
        mimeMailWrapper.addTo("recipientTo", recipientToEmail);
        mimeMailWrapper.setFrom("from", fromEmail);
        mimeMailWrapper.addBcc("bcc", bccEmail);
        mimeMailWrapper.addCc("cc", ccToEmail);

        Message message = mimeMailWrapper.toMsGraphMessage();
        Assert.assertEquals(message.getSubject(), expectedSubject);

        List<Attachment> attachmentList = message.getAttachments();
        assertNotNull(attachmentList);
        assertEquals(1, attachmentList.size());
        FileAttachment attachment = (FileAttachment)attachmentList.getFirst();
        assertNotNull(attachment);
        assertEquals(Boolean.TRUE, attachment.getIsInline());
        assertEquals("image/png", attachment.getContentType());
        assertEquals("#microsoft.graph.fileAttachment", attachment.getOdataType());

        assertEquals(fromEmail, message.getFrom().getEmailAddress().getAddress());
        assertEquals(recipientToEmail, message.getToRecipients().getFirst().getEmailAddress().getAddress());
        assertEquals(replyToEmail, message.getReplyTo().getFirst().getEmailAddress().getAddress());
        assertEquals(bccEmail, message.getBccRecipients().getFirst().getEmailAddress().getAddress());
    }
}