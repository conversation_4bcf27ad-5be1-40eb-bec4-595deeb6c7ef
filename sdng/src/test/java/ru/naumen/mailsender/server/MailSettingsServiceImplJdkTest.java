package ru.naumen.mailsender.server;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

import java.util.Collections;

import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import com.google.common.collect.Lists;

import net.customware.gwt.dispatch.server.Dispatch;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.core.server.ConfigurationProperties;
import ru.naumen.core.server.ConnectionValidationResultsStorage;
import ru.naumen.core.server.SpringContext;
import ru.naumen.core.server.mail.ConnectionValidationServiceImpl;
import ru.naumen.core.server.mail.EwsOAuthTokenService;
import ru.naumen.core.server.mail.SSLSocketMailConfigurator;
import ru.naumen.core.server.metastorage.impl.metainfo.MetaStorageService;
import ru.naumen.core.server.net.ConnectionPermissionChecker;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.common.I18nUtil;
import ru.naumen.mailreader.server.MailProcessingInfoCache;
import ru.naumen.mailreader.server.MailreaderBeansConfig;
import ru.naumen.mailsender.server.outgoingmailserver.MailSettingsServiceImpl;
import ru.naumen.mailsender.shared.dispatch.OutgoingMailServerConfigResult;
import ru.naumen.metainfo.server.Constants;
import ru.naumen.metainfo.shared.elements.mail.MailProtocol;
import ru.naumen.metainfo.shared.elements.mail.OutgoingMailServerConfig;
import ru.naumen.sec.server.autorize.AuthorizationRunnerServiceImpl;
import ru.naumen.sec.server.encryption.EncryptionServiceBean;

/**
 * Тестирование сервиса исходящих подключений почты.
 * <AUTHOR>
 * @since Feb 03, 2022
 */
public class MailSettingsServiceImplJdkTest
{
    @Mock
    private MetaStorageService metaStorageService;
    @Mock
    private EwsOAuthTokenService oAuthTokenService;
    @Mock
    private MessageFacade messages;
    @Mock
    private MailProcessingInfoCache cache;
    private MailSettingsServiceImpl service;

    @Before
    public void setUp()
    {
        MockitoAnnotations.initMocks(this);
        service = new MailSettingsServiceImpl(mock(I18nUtil.class), messages, cache,
                metaStorageService, oAuthTokenService, mock(EncryptionServiceBean.class));
    }

    /**
     * Тестирования наличия вызова обновления токена только в том случае, когда конфигурация почтового сервера настроена
     * на протокол EWS с OAuth.
     */
    @Test
    public void testOAuthTokenRefresh()
    {
        OutgoingMailServerConfig config = new OutgoingMailServerConfig();
        config.setConnectionProtocol(MailProtocol.EWS);
        config.setCode("mailConfig");
        config.setDefault(true);
        when(metaStorageService.<OutgoingMailServerConfig> get(Constants.OUTGOING_MAIL_SERVER_CONFIG))
                .thenReturn(Collections.singletonList(config));
        when(metaStorageService.<OutgoingMailServerConfig> get(eq(Constants.OUTGOING_MAIL_SERVER_CONFIG),
                eq("mailConfig"), any())).thenReturn(config);

        config.setNeedAuth(false);
        assertEquals(config, service.getOutgoingMailServerConfigOrDefault("mailConfig"));
        verify(oAuthTokenService, times(0)).refreshToken(any());
        config.setNeedAuth(true);
        assertEquals(config, service.getOutgoingMailServerConfigOrDefault("mailConfig"));
        verify(oAuthTokenService, times(1)).refreshToken(any());
    }

    /**
     * Тестирование вызова метода подключения к серверу исходящей почты с учетом таймаута
     */
    @Test
    public void testConnectionTimeOut() throws DispatchException
    {
        OutgoingMailServerConfig config = new OutgoingMailServerConfig();
        config.setConnectionProtocol(MailProtocol.SMTP);
        config.setCode("mailConfig");
        config.setDefault(true);
        Dispatch dispatch = Mockito.mock(Dispatch.class);
        OutgoingMailServerConfigResult dispatchResult = new OutgoingMailServerConfigResult(Lists.newArrayList(config));
        when(dispatch.execute(any())).thenReturn(dispatchResult);
        ConnectionPermissionChecker connectionPermissionChecker = Mockito.mock(ConnectionPermissionChecker.class);
        AuthorizationRunnerServiceImpl authorizationRunner = Mockito.mock(AuthorizationRunnerServiceImpl.class);
        SpringContext springContext = Mockito.mock(SpringContext.class);
        MailreaderBeansConfig mailreaderConfig = Mockito.mock(MailreaderBeansConfig.class);
        ConnectionValidationResultsStorage storageService = Mockito.mock(ConnectionValidationResultsStorage.class);
        SSLSocketMailConfigurator sslSocketMailConfigurator = Mockito.mock(SSLSocketMailConfigurator.class);
        ConfigurationProperties configurationProperties = Mockito.mock(ConfigurationProperties.class);
        Mockito.when(configurationProperties.getOutgoingSmtpServerConnectionTimeOut()).thenReturn(5);
        ConnectionValidationServiceImpl connectionValidationService =
                new ConnectionValidationServiceImpl(dispatch, connectionPermissionChecker, authorizationRunner,
                        springContext, mailreaderConfig, storageService, messages, sslSocketMailConfigurator,
                        configurationProperties);
        connectionValidationService.validateOutgoingMailServerConfig("mailConfig");
        verify(authorizationRunner, times(1)).callAsSuperUserWithTimeout(anyString(), any(), anyInt(), any());
    }
}
