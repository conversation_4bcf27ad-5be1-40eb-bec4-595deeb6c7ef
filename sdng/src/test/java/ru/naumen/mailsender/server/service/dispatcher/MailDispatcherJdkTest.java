package ru.naumen.mailsender.server.service.dispatcher;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import java.util.Date;
import java.util.Map;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import ru.naumen.mailsender.server.service.sender.OutboundMailContainerDao;
import ru.naumen.metainfo.shared.elements.mail.ISendMailParameters;

@RunWith(MockitoJUnitRunner.class)
public class MailDispatcherJdkTest
{
    @Mock
    private ISendMailParameters parameters;
    @Mock
    private OutboundMailContainerDao dao;

    private MailDispatcher mailDispatcher;

    @Before
    public void setUp()
    {
        mailDispatcher = new MailDispatcher(dao, null, null, mock(Map.class), 5);
        when(parameters.getIterationDelay()).thenReturn(1);
        when(dao.getNextIterationTimeFromDB(any())).thenReturn(new Date());
    }

    @Test
    public void testThatErrorDelayMultiplierIsResetAfterSuccess()
    {
        mailDispatcher.prepareForNextIteration(parameters, true);
        mailDispatcher.prepareForNextIteration(parameters, true);
        Assert.assertEquals(mailDispatcher.delayMultiplier, 3);
        mailDispatcher.prepareForNextIteration(parameters, false);
        Assert.assertEquals(mailDispatcher.delayMultiplier, 1);
    }

    @Test
    public void testThatErrorDelayMultiplierIsNotExceedMaxErrorDelayMultiplier()
    {
        mailDispatcher.prepareForNextIteration(parameters, true);
        mailDispatcher.prepareForNextIteration(parameters, true);
        mailDispatcher.prepareForNextIteration(parameters, true);
        mailDispatcher.prepareForNextIteration(parameters, true);
        Assert.assertEquals(mailDispatcher.delayMultiplier, 5);
        mailDispatcher.prepareForNextIteration(parameters, true);
        Assert.assertEquals(mailDispatcher.delayMultiplier, 5);
    }
}
