package ru.naumen.migration.server.once;

import static org.mockito.Mockito.*;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Collections;
import java.util.stream.Collectors;

import org.apache.commons.fileupload2.core.FileItem;
import org.hamcrest.core.IsInstanceOf;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.junit.rules.TemporaryFolder;
import org.junit.runner.RunWith;
import org.mockito.InOrder;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import jakarta.annotation.Nonnull;
import jakarta.xml.bind.UnmarshalException;
import net.customware.gwt.dispatch.server.Dispatch;
import net.customware.gwt.dispatch.shared.Action;
import ru.naumen.core.server.cluster.external.ClusterInfoService;
import ru.naumen.core.server.cluster.external.ClusterServiceManager;
import ru.naumen.commons.server.utils.MessageDigestUtils;
import ru.naumen.commons.server.utils.XmlUtils;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.ConfigurationProperties;
import ru.naumen.core.server.background.BackgroundManagerBean;
import ru.naumen.core.server.embeddedapplication.EmbeddedApplicationService;
import ru.naumen.core.server.jta.TransactionRunnerService;
import ru.naumen.core.server.metastorage.impl.metainfo.MetaStorageService;
import ru.naumen.core.server.script.ScriptExecutionContext;
import ru.naumen.core.server.script.ScriptService;
import ru.naumen.core.server.script.storage.ScriptStorageServiceBean;
import ru.naumen.core.server.upload.NestedFileItem;
import ru.naumen.core.server.upload.UploadService;
import ru.naumen.metainfo.server.spi.MetainfoServiceBean;
import ru.naumen.metainfo.shared.dispatch2.ImportLicenseAction;
import ru.naumen.metainfo.shared.dispatch2.ImportMetainfoAction;
import ru.naumen.metainfo.shared.dispatch2.embeddedapplication.SaveEmbeddedApplicationAction;
import ru.naumen.metainfo.shared.script.Script;
import ru.naumen.migration.server.once.configurable.UpdateSequenceSerializer;
import ru.naumen.migration.server.once.configurable.strategies.ExecScriptStrategy;
import ru.naumen.migration.server.once.configurable.strategies.OnStartApplicationActionsStrategyFactory;
import ru.naumen.migration.server.once.configurable.strategies.UploadEmbeddedAppStrategy;
import ru.naumen.migration.server.once.configurable.strategies.UploadLicenseStrategy;
import ru.naumen.migration.server.once.configurable.strategies.UploadMetainfoStrategy;
import ru.naumen.sec.server.admin.log.SchedulerTaskLogService;
import ru.naumen.sec.server.autorize.AuthorizationRunnerService;

@RunWith(MockitoJUnitRunner.class)
public class ApplyOnceScriptJdkTest
{
    @Mock
    private UploadService uploadService;
    @Mock
    private Dispatch dispatch;
    @Mock
    private ScriptService scriptService;
    @Mock
    private AuthorizationRunnerService authorizationRunnerService;
    @Mock
    private TransactionRunnerService transactionRunner;
    @Mock
    private SchedulerTaskLogService schedulerTaskLogService;
    @Mock
    private MigrationChecksumDao migrationChecksumDao;
    @Mock
    private MetaStorageService metaStorageService;
    @Mock
    private EmbeddedApplicationService embeddedApplicationService;
    @Mock
    private ScriptStorageServiceBean scriptStorageService;
    @Mock
    private MetainfoServiceBean metainfoServiceBean;
    @Mock
    private ClusterInfoService clusterInfoService;
    @Mock
    private ClusterServiceManager clusterServiceManager;
    @Mock
    private BackgroundManagerBean backgroundManagerBean;

    @Rule
    public TemporaryFolder tempFolder = new TemporaryFolder();

    @Before
    public void setup()
    {
        when(uploadService.add(any(FileItem.class), anyBoolean())).thenReturn("some$uuid");
        doAnswer(inv ->
        {
            ((Runnable)inv.getArguments()[1]).run();
            return null;
        }).when(authorizationRunnerService).runAsSuperUser(anyString(), any(Runnable.class));
        lenient().doAnswer(inv ->
        {
            ((Runnable)inv.getArguments()[0]).run();
            return null;
        }).when(transactionRunner).run(any(Runnable.class));
    }

    /**
     * Тестирование того, что скрипт миграции НЕ выбрасывает ошибку, если файл метаинформации не указан/не существует
     */
    @Test
    public void testMigrationWithoutMetainfoFile()
    {
        try
        {
            createApplyOnceScript().onApplicationEvent(null);
        }
        finally
        {
            verifyNoMoreInteractions(uploadService, dispatch, scriptService, authorizationRunnerService);
        }
    }

    /**
     * Тестирование случая, когда импорт падает с ошибкой. Скрипт выполнится не должен
     */
    @Test(expected = Exception.class)
    public void testMigrationMetainfoImportFailure() throws Exception
    {
        final Path metainfoPath = Files.writeString(tempFolder.getRoot().toPath().resolve("metainfo.xml"),
                "Metainfo content", StandardCharsets.UTF_8);

        final Path scriptPath = Files.writeString(tempFolder.getRoot().toPath().resolve("script.groovy"),
                "Script content", StandardCharsets.UTF_8);

        ApplyOnceScript applyOnceScript = createApplyOnceScript();

        try
        {
            applyOnceScript.onApplicationEvent(null);
        }
        finally
        {
            verify(uploadService, times(1)).add(eq(new NestedFileItem("Metainfo content",
                    "metainfo.xml", "application/xml")), false);
            verify(authorizationRunnerService, times(1)).runAsSuperUser(eq("once"), any());
            verify(dispatch, times(1)).execute(any(ImportMetainfoAction.class));
            verifyNoMoreInteractions(scriptService);
        }
    }

    /** Тестирование того, что, когда у нас указана папка path, а там есть все файлы(preImportScript.groovy, license
     * .xml, metainfo.xml, postImportScript.groovy), то всё выполняется строго в таком порядке.
     */
    @Test
    public void testMigrationWithPath() throws Exception
    {
        Path pathToFilesMigration = tempFolder.newFolder().toPath();

        //создаем preImportScript.groovy
        Files.writeString(pathToFilesMigration.resolve("preImportScript.groovy"), "pre import script body",
                StandardCharsets.UTF_8);
        //создаем license.xml
        Files.writeString(pathToFilesMigration.resolve("license.xml"), "license body", StandardCharsets.UTF_8);
        //создаем metainfo.xml
        Files.writeString(pathToFilesMigration.resolve("metainfo.xml"), "Metainfo content", StandardCharsets.UTF_8);
        //создаем license.xml
        Files.writeString(pathToFilesMigration.resolve("postImportScript.groovy"), "post import script body",
                StandardCharsets.UTF_8);

        ApplyOnceScript applyOnceScript = createApplyOnceScript(pathToFilesMigration);
        InOrder inOrder = Mockito.inOrder(authorizationRunnerService, dispatch, scriptService);
        applyOnceScript.onApplicationEvent(null);

        //проверяем последовательность выполнения
        inOrder.verify(scriptService).execute(eq(new Script("pre import script body")),
                eq(Collections.emptyMap()), any(ScriptExecutionContext.class));
        inOrder.verify(dispatch).execute(any(ImportLicenseAction.class));
        inOrder.verify(dispatch).execute(any(ImportMetainfoAction.class));
        inOrder.verify(scriptService).execute(eq(new Script("post import script body")),
                eq(Collections.emptyMap()), any(ScriptExecutionContext.class));

        verify(uploadService, times(2)).add(any(NestedFileItem.class), anyBoolean());
        verify(authorizationRunnerService, times(4)).runAsSuperUser(eq("once"), any());
        verify(dispatch, times(2)).execute(any(Action.class));
        verify(scriptService, times(2)).execute(any(Script.class),
                eq(Collections.emptyMap()), any(ScriptExecutionContext.class));
    }

    /** Тестирование того, что, когда у нас указана папка path, а там есть все файлы(preImportScript.groovy, license
     * .xml, metainfo.xml, postImportScript.groovy) и контрольная сумма совпадает с суммой из БД, и задана java-опция
     * "skipAutoUpdateChecksum", то всё выполняется строго в таком порядке.
     */
    @Test
    public void testMigrationWithPathAndOldCheckSum() throws Exception
    {
        Path pathToFilesMigration = tempFolder.newFolder().toPath();

        //создаем preImportScript.groovy
        Files.writeString(pathToFilesMigration.resolve("preImportScript.groovy"), "pre import script body",
                StandardCharsets.UTF_8);
        //создаем license.xml
        Files.writeString(pathToFilesMigration.resolve("license.xml"), "license body", StandardCharsets.UTF_8);
        //создаем metainfo.xml
        Files.writeString(pathToFilesMigration.resolve("metainfo.xml"), "Metainfo content", StandardCharsets.UTF_8);
        //создаем license.xml
        Files.writeString(pathToFilesMigration.resolve("postImportScript.groovy"), "post import script body",
                StandardCharsets.UTF_8);

        System.setProperty("skipAutoUpdateChecksum", "");
        ApplyOnceScript applyOnceScript = createApplyOnceScript(pathToFilesMigration);
        InOrder inOrder = Mockito.inOrder(authorizationRunnerService, dispatch, scriptService);
        String checkSum =
                MessageDigestUtils.md5HashForFiles(
                        Files.walk(pathToFilesMigration).filter(Files::isRegularFile).collect(
                                Collectors.toList()));
        Mockito.lenient().when(migrationChecksumDao.get(checkSum)).thenReturn(new MigrationChecksum(checkSum));

        applyOnceScript.onApplicationEvent(null);

        //проверяем последовательность выполнения
        inOrder.verify(scriptService).execute(eq(new Script("pre import script body")),
                eq(Collections.emptyMap()), any(ScriptExecutionContext.class));
        inOrder.verify(dispatch).execute(any(ImportLicenseAction.class));
        inOrder.verify(dispatch).execute(any(ImportMetainfoAction.class));
        inOrder.verify(scriptService).execute(eq(new Script("post import script body")),
                eq(Collections.emptyMap()), any(ScriptExecutionContext.class));

        verify(uploadService, times(2)).add(any(NestedFileItem.class), anyBoolean());
        verify(authorizationRunnerService, times(4)).runAsSuperUser(eq("once"), any());
        verify(dispatch, times(2)).execute(any(Action.class));
        verify(scriptService, times(2)).execute(any(Script.class),
                eq(Collections.emptyMap()), any(ScriptExecutionContext.class));
    }

    /**
     * Тестирование, если по пути path лежит xml-файл, в котором задан порядок выполнения скриптов и загрузки
     * метаинфы и лицензий, все загружается в том порядке как указано в файле. Файлы скриптов, лицензий и меты лежат
     * в той же папке, рядом с XML файлом порядка загрузки.
     */
    @Test
    public void testMigrationWithXMLFileInPath() throws Exception
    {
        Path xmlFile = createScriptsMetaInfoLicenseFiles();

        InOrder inOrder = Mockito.inOrder(authorizationRunnerService, dispatch, scriptService);
        ApplyOnceScript applyOnceScript = createApplyOnceScript(xmlFile);
        applyOnceScript.onApplicationEvent(null);

        //проверяем последовательность выполнения
        checkOrdersForImportFromXMLFile(inOrder);
    }

    /**
     * Тестирование, если по пути path лежит xml-файл, в котором задан порядок выполнения скриптов и загрузки
     * метаинфы и лицензий, все загружается в том порядке как указано в файле. Файлы скриптов, лицензий и меты лежат
     * в подпапке, рядом с XML файлом порядка загрузки.
     */
    @Test
    public void testMigrationWithXMLFileInPathAndRelativeToFiles() throws Exception
    {
        File newTmpFolder = tempFolder.newFolder();
        tempFolder.newFolder(newTmpFolder.getName(), "test");
        Path pathToFilesMigration = newTmpFolder.toPath();

        //создаем importScripts.xml
        Path xmlFile = Files.writeString(pathToFilesMigration.resolve("importScripts.xml"),
                "<ns2:updateSequence xmlns:ns2=\"http://naumen.ru/updateSequence\">\n"
                        + "<action type=\"script\">test/startScript.groovy</action>\n"
                        + "<action type=\"upload\" entity=\"license\">test/license.xml</action>\n"
                        + "<action type=\"upload\" entity=\"metainfo\">test/metainfo.xml</action>\n"
                        + "<action type=\"script\">test/finishScript.groovy</action>\n"
                        + "</ns2:updateSequence>",
                StandardCharsets.UTF_8);
        //создаем license.xml
        Files.writeString(pathToFilesMigration.resolve("test/license.xml"), "license body", StandardCharsets.UTF_8);
        //создаем metainfo.xml
        Files.writeString(pathToFilesMigration.resolve("test/metainfo.xml"), "Metainfo content",
                StandardCharsets.UTF_8);
        //создаем startScript.groovy
        Files.writeString(pathToFilesMigration.resolve("test/startScript.groovy"), "pre import script body",
                StandardCharsets.UTF_8);

        //создаем startScript.groovy
        Files.writeString(pathToFilesMigration.resolve("test/finishScript.groovy"), "post import script body",
                StandardCharsets.UTF_8);

        InOrder inOrder = Mockito.inOrder(authorizationRunnerService, dispatch, scriptService);
        ApplyOnceScript applyOnceScript = createApplyOnceScript(xmlFile);
        applyOnceScript.onApplicationEvent(null);

        //проверяем последовательность выполнения
        checkOrdersForImportFromXMLFile(inOrder);
    }

    /**
     * Тестирование, если по пути path лежит xml-файл, в котором задан порядок выполнения скриптов и загрузки
     * метаинфы и лицензий, все загружается в том порядке как указано в файле. Для файлов скриптов, лицензий и меты
     * указан абсолютный путь
     */
    @Test
    public void testMigrationWithXMLFileInPathAndAbsolutePathToFiles() throws Exception
    {
        File newTmpFolder = tempFolder.newFolder();
        File newTmpFolderForFiles = tempFolder.newFolder();
        Path pathToXMLFileMigration = newTmpFolder.toPath();
        Path pathToFilesMigration = newTmpFolderForFiles.toPath();

        //создаем importScripts.xml
        Path xmlFile = Files.writeString(pathToXMLFileMigration.resolve("importScripts.xml"),
                "<ns2:updateSequence xmlns:ns2=\"http://naumen.ru/updateSequence\">\n"
                        + "<action type=\"script\">" + pathToFilesMigration + "/startScript.groovy</action>\n"
                        + "<action type=\"upload\" entity=\"license\">" + pathToFilesMigration
                        + "/license.xml</action>\n"
                        + "<action type=\"upload\" entity=\"metainfo\">" + pathToFilesMigration
                        + "/metainfo.xml</action>\n"
                        + "<action type=\"script\">" + pathToFilesMigration + "/finishScript.groovy</action>\n"
                        + "</ns2:updateSequence>",
                StandardCharsets.UTF_8);
        //создаем license.xml
        Files.writeString(pathToFilesMigration.resolve("license.xml"), "license body", StandardCharsets.UTF_8);
        //создаем metainfo.xml
        Files.writeString(pathToFilesMigration.resolve("metainfo.xml"), "Metainfo content", StandardCharsets.UTF_8);
        //создаем startScript.groovy
        Files.writeString(pathToFilesMigration.resolve("startScript.groovy"), "pre import script body",
                StandardCharsets.UTF_8);

        //создаем startScript.groovy
        Files.writeString(pathToFilesMigration.resolve("finishScript.groovy"), "post import script body",
                StandardCharsets.UTF_8);

        InOrder inOrder = Mockito.inOrder(authorizationRunnerService, dispatch, scriptService);
        ApplyOnceScript applyOnceScript = createApplyOnceScript(xmlFile);
        applyOnceScript.onApplicationEvent(null);

        //проверяем последовательность выполнения
        checkOrdersForImportFromXMLFile(inOrder);
    }

    /**
     * Тестирование, если по пути path лежит xml-файл, в котором задан порядок выполнения скриптов и загрузки
     * метаинфы и лицензий, все загружается в том порядке как указано в файле, при этом в скрипте после загрузки
     * выбрасывается исключение, следовательно последний скрипт не выполняется.
     * Файлы скриптов, лицензий и меты лежат в той же папке, рядом с XML файлом с порядка загрузки.
     */
    @Test
    public void testMigrationWithXMLFileInPathAndErrorInPostImportScript() throws Exception
    {
        String msg = "Not really exception";
        when(scriptService.execute(eq(new Script("post import script body")), any(), any())).thenThrow(
                new FxException(msg));
        Path pathToFilesMigration = tempFolder.newFolder().toPath();

        //создаем importScripts.xml
        Path xmlFile = Files.writeString(pathToFilesMigration.resolve("importScripts.xml"),
                "<ns2:updateSequence xmlns:ns2=\"http://naumen.ru/updateSequence\">\n"
                        + "<action type=\"script\">startScript.groovy</action>\n"
                        + "<action type=\"upload\" entity=\"license\">license.xml</action>\n"
                        + "<action type=\"upload\" entity=\"metainfo\">metainfo.xml</action>\n"
                        + "<action type=\"script\">finishScript.groovy</action>\n"
                        + "<action type=\"script\">finishScript2.groovy</action>\n"
                        + "</ns2:updateSequence>",
                StandardCharsets.UTF_8);
        //создаем license.xml
        Files.writeString(pathToFilesMigration.resolve("license.xml"), "license body", StandardCharsets.UTF_8);
        //создаем metainfo.xml
        Files.writeString(pathToFilesMigration.resolve("metainfo.xml"), "Metainfo content", StandardCharsets.UTF_8);
        //создаем startScript.groovy
        Files.writeString(pathToFilesMigration.resolve("startScript.groovy"), "pre import script body",
                StandardCharsets.UTF_8);
        //создаем finishScript.groovy
        Files.writeString(pathToFilesMigration.resolve("finishScript.groovy"), "post import script body",
                StandardCharsets.UTF_8);
        //создаем finishScript2.groovy
        Files.writeString(pathToFilesMigration.resolve("finishScript2.groovy"), "post import script body 2",
                StandardCharsets.UTF_8);

        InOrder inOrder = Mockito.inOrder(authorizationRunnerService, dispatch, scriptService);

        ApplyOnceScript applyOnceScript = createApplyOnceScript(xmlFile);
        applyOnceScript.onApplicationEvent(null);

        //проверяем последовательность выполнения
        checkOrdersForImportFromXMLFile(inOrder);
    }

    /**
     * Тестирование, если по пути path лежит xml-файл, в котором задан порядок выполнения скриптов и загрузки
     * метаинфы и лицензий, все загружается в том порядке как указано в файле, при этом в скрипте перед загрузкой
     * выбрасывается исключение, следовательно лицензии и метаинфрмация не загружаются и остальные скрипты не
     * выполняются.
     * Файлы скриптов, лицензий и меты лежат в той же папке, рядом с XML файлом с порядка загрузки.
     */
    @Test
    public void testMigrationWithXMLFileInPathAndErrorInPreImportScript() throws Exception
    {
        String msg = "Not really exception";
        when(scriptService.execute(any(), any(), any())).thenThrow(new FxException(msg));
        Path xmlFile = createScriptsMetaInfoLicenseFiles();

        ApplyOnceScript applyOnceScript = createApplyOnceScript(xmlFile);
        applyOnceScript.onApplicationEvent(null);

        verify(uploadService, times(0)).add(any(NestedFileItem.class), anyBoolean());
        verify(authorizationRunnerService, times(1)).runAsSuperUser(eq("once"), any());
        verify(dispatch, times(0)).execute(any(Action.class));
        verify(scriptService, times(1)).execute(any(Script.class),
                eq(Collections.emptyMap()), any(ScriptExecutionContext.class));
    }

    /**
     * Тестирование, если по пути path лежит xml-файл, в котором задан порядок выполнения скриптов и загрузки
     * метаинфы и лицензий, все загружается в том порядке как указано в файле, при этом при загрузке метаинфы
     * выбрасывается исключение, следовательно остальные скрипты не выполняются
     * Файлы скриптов, лицензий и меты лежат в той же папке, рядом с XML файлом с порядка загрузки.
     */
    @Test
    public void testMigrationWithXMLFileInPathAndErrorInUploadMetainfo() throws Exception
    {
        String msg = "Not really exception";

        when(dispatch.execute(isA(ImportMetainfoAction.class))).thenThrow(new FxException(msg));
        Path xmlFile = createScriptsMetaInfoLicenseFiles();

        InOrder inOrder = Mockito.inOrder(authorizationRunnerService, dispatch, scriptService);

        ApplyOnceScript applyOnceScript = createApplyOnceScript(xmlFile);
        applyOnceScript.onApplicationEvent(null);

        inOrder.verify(scriptService).execute(eq(new Script("pre import script body")),
                eq(Collections.emptyMap()), any(ScriptExecutionContext.class));
        inOrder.verify(dispatch).execute(any(ImportLicenseAction.class));
        inOrder.verify(dispatch).execute(any(ImportMetainfoAction.class));

        verify(uploadService, times(2)).add(any(NestedFileItem.class), anyBoolean());
        verify(authorizationRunnerService, times(3)).runAsSuperUser(eq("once"), any());
        verify(dispatch, times(2)).execute(any(Action.class));
        verify(scriptService, times(1)).execute(any(Script.class),
                eq(Collections.emptyMap()), any(ScriptExecutionContext.class));
    }

    /**
     * Тестирование, если по пути path лежит xml-файл, в котором задан порядок выполнения скриптов и загрузки
     * метаинфы и лицензий, все загружается в том порядке как указано в файле, при этом при загрузке лицензий
     * выбрасывается исключение, следовательно метаинфа не загружается и остальные скрипты не выполняются.
     * Файлы скриптов, лицензий и меты лежат в той же папке, рядом с XML файлом с порядка загрузки.
     */
    @Test
    public void testMigrationWithXMLFileInPathAndErrorInUploadLicense() throws Exception
    {
        String msg = "Not really exception";

        when(dispatch.execute(isA(ImportLicenseAction.class))).thenThrow(new FxException(msg));
        Path xmlFile = createScriptsMetaInfoLicenseFiles();

        InOrder inOrder = Mockito.inOrder(authorizationRunnerService, dispatch, scriptService);

        ApplyOnceScript applyOnceScript = createApplyOnceScript(xmlFile);
        applyOnceScript.onApplicationEvent(null);

        inOrder.verify(scriptService).execute(eq(new Script("pre import script body")),
                eq(Collections.emptyMap()), any(ScriptExecutionContext.class));
        inOrder.verify(dispatch).execute(any(ImportLicenseAction.class));

        verify(uploadService, times(1)).add(any(NestedFileItem.class), anyBoolean());
        verify(authorizationRunnerService, times(2)).runAsSuperUser(eq("once"), any());
        verify(dispatch, times(1)).execute(any(Action.class));
        verify(scriptService, times(1)).execute(any(Script.class),
                eq(Collections.emptyMap()), any(ScriptExecutionContext.class));
    }

    /**
     * Тестирование, если по пути path лежит xml-файл, в котором задан порядок выполнения скриптов и загрузки
     * метаинфы и лицензий, но самих файлов скриптов нет по указанному пути.
     */
    @Test
    public void testMigrationWithXMLFileNotFilesForActionExist() throws Exception
    {
        Path pathToFilesMigration = tempFolder.newFolder().toPath();

        //создаем importScripts.xml
        Path xmlFile = Files.writeString(pathToFilesMigration.resolve("importScripts.xml"),
                "<ns2:updateSequence xmlns:ns2=\"http://naumen.ru/updateSequence\">\n"
                        + "<action type=\"script\">startScript.groovy</action>\n"
                        + "<action type=\"upload\" entity=\"license\">license.xml</action>\n"
                        + "<action type=\"upload\" entity=\"metainfo\">metainfo.xml</action>\n"
                        + "<action type=\"script\">finishScript.groovy</action>\n"
                        + "</ns2:updateSequence>",
                StandardCharsets.UTF_8);

        ApplyOnceScript applyOnceScript = createApplyOnceScript(xmlFile);
        applyOnceScript.onApplicationEvent(null);

        verify(uploadService, times(0)).add(any(NestedFileItem.class), anyBoolean());
        verify(authorizationRunnerService, times(0)).runAsSuperUser(eq("once"), any());
        verify(dispatch, times(0)).execute(any(Action.class));
        verify(scriptService, times(0)).execute(any(Script.class),
                eq(Collections.emptyMap()), any(ScriptExecutionContext.class));
    }

    /**
     * Тестирование выполнения действий загрузки внутреннего и внешнего ВП
     */
    @Test
    public void testUploadEmbeddedApp() throws Exception
    {
        File newTmpFolder = tempFolder.newFolder();
        File testFolder = tempFolder.newFolder(newTmpFolder.getName(), "test");
        Path pathToFilesMigration = newTmpFolder.toPath();
        Path testFolderPath = testFolder.toPath();

        //создаем importScripts.xml
        Path xmlFile = Files.writeString(pathToFilesMigration.resolve("importScripts.xml"),
                "<ns2:updateSequence xmlns:ns2=\"http://naumen.ru/updateSequence\">\n"
                        + "<action type=\"upload\" entity=\"ea\">" + testFolderPath.getFileName()
                        + "/testInternalApp.json</action>"
                        + "<action type=\"upload\" entity=\"ea\">" + testFolderPath.getFileName()
                        + "/testExternalApp.json</action>"
                        + "</ns2:updateSequence>",
                StandardCharsets.UTF_8);

        //создаем testInternalApp.json
        Files.writeString(testFolderPath.resolve("testInternalApp.json"), "{\n"
                        + "  \"code\": \"testApp\",\n"
                        + "  \"type\": \"internal\",\n"
                        + "  \"title\": \"testApp\",\n"
                        + "  \"filePath\": \"" + testFolderPath.getFileName() + "/testApp.zip\",\n"
                        + "  \"description\": \"\",\n"
                        + "  \"fullScreenAllowed\" : false, \n"
                        + "  \"enabled\" : false, \n"
                        + "  \"minHeight\" : 400\n"
                        + "}",
                StandardCharsets.UTF_8);

        //создаем testApp.zip
        Files.writeString(testFolderPath.resolve("testApp.zip"), "test.zip )",
                StandardCharsets.UTF_8);

        //создаем testExternalApp.json
        Files.writeString(testFolderPath.resolve("testExternalApp.json"), "{\n"
                        + "  \"code\": \"testExtApp\",\n"
                        + "  \"type\": \"external\",\n"
                        + "  \"title\": \"testExternalApp\",\n"
                        + "  \"description\": \"description\",\n"
                        + "  \"applicationAddress\": \"https://google.com\", \n"
                        + "  \"urlDefinition\" : \"system\", \n "
                        + "  \"minHeight\" : 400\n"
                        + "}",
                StandardCharsets.UTF_8);

        ApplyOnceScript applyOnceScript = createApplyOnceScript(xmlFile);
        applyOnceScript.onApplicationEvent(null);

        verify(authorizationRunnerService, times(4)).runAsSuperUser(eq("once"), any());
        verify(uploadService, times(1)).add(any(NestedFileItem.class), anyBoolean());
        verify(dispatch, times(2)).execute(isA(SaveEmbeddedApplicationAction.class));
    }

    /**
     * Тестирование, если по пути path лежит xml-файл, в котором задан порядок выполнения скриптов и загрузки
     * метаинфы и лицензий, но файла лицензии нет по указанному пути. Проверка, что выбрасывается исключение
     * FileNotFoundException при вызове метода executeMigration()
     */
    @Test(expected = FileNotFoundException.class)
    public void testMigrationWithXMLFileNoOneFileForAction() throws Exception
    {
        Path pathToFilesMigration = tempFolder.newFolder().toPath();

        //создаем importScripts.xml
        Path xmlFile = Files.writeString(pathToFilesMigration.resolve("importScripts.xml"),
                "<ns2:updateSequence xmlns:ns2=\"http://naumen.ru/updateSequence\">\n"
                        + "<action type=\"script\">startScript.groovy</action>\n"
                        + "<action type=\"upload\" entity=\"license\">license1.xml</action>\n"
                        + "<action type=\"upload\" entity=\"metainfo\">metainfo.xml</action>\n"
                        + "</ns2:updateSequence>",
                StandardCharsets.UTF_8);

        //создаем metainfo.xml
        Files.writeString(pathToFilesMigration.resolve("metainfo.xml"), "Metainfo content", StandardCharsets.UTF_8);
        //создаем startScript.groovy
        Files.writeString(pathToFilesMigration.resolve("startScript.groovy"), "pre import script body",
                StandardCharsets.UTF_8);

        ApplyOnceScript applyOnceScript = createApplyOnceScript(xmlFile);
        applyOnceScript.executeMigration();
    }

    /**
     * Тестирование, если по пути path лежит xml-файл, в котором задан порядок выполнения скриптов и загрузки
     * метаинфы и лицензий, но файла лицензии нет по указанному пути. Проверка, что процесс останавливается и
     * метаинформация не загружается
     */
    @Test
    public void testMigrationWithXMLFileNoLicenseFileForActionOtherActionFail() throws Exception
    {
        Path pathToFilesMigration = tempFolder.newFolder().toPath();

        //создаем importScripts.xml
        Path xmlFile = Files.writeString(pathToFilesMigration.resolve("importScripts.xml"),
                "<ns2:updateSequence xmlns:ns2=\"http://naumen.ru/updateSequence\">\n"
                        + "<action type=\"script\">startScript.groovy</action>\n"
                        + "<action type=\"upload\" entity=\"license\">license1.xml</action>\n"
                        + "<action type=\"upload\" entity=\"metainfo\">metainfo.xml</action>\n"
                        + "</ns2:updateSequence>",
                StandardCharsets.UTF_8);

        //создаем metainfo.xml
        Files.writeString(pathToFilesMigration.resolve("metainfo.xml"), "Metainfo content", StandardCharsets.UTF_8);
        //создаем startScript.groovy
        Files.writeString(pathToFilesMigration.resolve("startScript.groovy"), "pre import script body",
                StandardCharsets.UTF_8);

        ApplyOnceScript applyOnceScript = createApplyOnceScript(xmlFile);
        applyOnceScript.onApplicationEvent(null);
        verify(uploadService, times(0)).add(any(NestedFileItem.class), anyBoolean());
        verify(authorizationRunnerService, times(1)).runAsSuperUser(eq("once"), any());
        verify(dispatch, times(0)).execute(any(Action.class));
        verify(scriptService, times(1)).execute(any(Script.class),
                eq(Collections.emptyMap()), any(ScriptExecutionContext.class));
    }

    /**
     * Тестирование опции загрузки метаинформации с режимом полной замены.
     * Проверка, что действие по загрузке метаинформации выполнится.
     */
    @Test
    public void testMigrationUploadMetainfoWithFullReload() throws Exception
    {
        Path pathToFilesMigration = tempFolder.newFolder().toPath();

        //создаем importScripts.xml
        Path xmlFile = Files.writeString(pathToFilesMigration.resolve("importScripts.xml"),
                "<ns2:updateSequence xmlns:ns2=\"http://naumen.ru/updateSequence\">\n"
                        + "<action type=\"upload\" entity=\"metainfo\" options=\"fullReload\">metainfo"
                        + ".xml</action>\n"
                        + "</ns2:updateSequence>",
                StandardCharsets.UTF_8);

        //создаем metainfo.xml
        Files.writeString(pathToFilesMigration.resolve("metainfo.xml"), "Metainfo content", StandardCharsets.UTF_8);

        ApplyOnceScript applyOnceScript = createApplyOnceScript(xmlFile);
        applyOnceScript.onApplicationEvent(null);
        verify(uploadService, times(1)).add(any(NestedFileItem.class), anyBoolean());
        verify(dispatch, times(1)).execute(any(ImportMetainfoAction.class));
    }

    /**
     * Тестирование, если по пути path лежит xml-файл, в котором нет ни одного действия.
     */
    @Rule
    public ExpectedException expectedEx = ExpectedException.none();

    @Test
    public void testMigrationWithXMLFileNoActionsInXML() throws Exception
    {
        expectedEx.expect(FxException.class);
        expectedEx.expectCause(IsInstanceOf.instanceOf(UnmarshalException.class));
        Path pathToFilesMigration = tempFolder.newFolder().toPath();

        //создаем importScripts.xml
        Path xmlFile = Files.writeString(pathToFilesMigration.resolve("importScripts.xml"),
                "<ns2:updateSequence xmlns:ns2=\"http://naumen.ru/updateSequence\">\n"
                        + "</ns2:updateSequence>",
                StandardCharsets.UTF_8);

        ApplyOnceScript applyOnceScript = createApplyOnceScript(xmlFile);
        applyOnceScript.executeMigration();
    }

    /**
     * Тестирование выполнения этапа миграции с указанием кода миграции.
     */
    @Test
    public void testMigrationActionCodes() throws Exception
    {
        Path pathToFilesMigration = tempFolder.newFolder().toPath();

        //создаем importScripts.xml
        Path xmlFile = Files.writeString(pathToFilesMigration.resolve("importScripts.xml"),
                "<ns2:updateSequence xmlns:ns2=\"http://naumen.ru/updateSequence\">\n"
                        + "<action type=\"script\" code=\"script1\">startScript.groovy</action>\n"
                        + "</ns2:updateSequence>",
                StandardCharsets.UTF_8);

        //создаем startScript.groovy
        Files.writeString(pathToFilesMigration.resolve("startScript.groovy"), "script body",
                StandardCharsets.UTF_8);

        ApplyOnceScript applyOnceScript = createApplyOnceScript(xmlFile);
        applyOnceScript.onApplicationEvent(null);
        verify(uploadService, times(0)).add(any(NestedFileItem.class), anyBoolean());
        verify(authorizationRunnerService, times(1)).runAsSuperUser(eq("once"), any());
        verify(dispatch, times(0)).execute(any(Action.class));
        verify(scriptService, times(1)).execute(any(Script.class),
                eq(Collections.emptyMap()), any(ScriptExecutionContext.class));
    }

    private void checkOrdersForImportFromXMLFile(InOrder inOrder)
            throws net.customware.gwt.dispatch.shared.DispatchException
    {
        inOrder.verify(scriptService).execute(eq(new Script("pre import script body")),
                eq(Collections.emptyMap()), any(ScriptExecutionContext.class));
        inOrder.verify(dispatch).execute(any(ImportLicenseAction.class));
        inOrder.verify(dispatch).execute(any(ImportMetainfoAction.class));
        inOrder.verify(scriptService).execute(eq(new Script("post import script body")),
                eq(Collections.emptyMap()), any(ScriptExecutionContext.class));

        verify(uploadService, times(2)).add(any(NestedFileItem.class), anyBoolean());
        verify(authorizationRunnerService, times(4)).runAsSuperUser(eq("once"), any());
        verify(dispatch, times(2)).execute(any(Action.class));
        verify(scriptService, times(2)).execute(any(Script.class),
                eq(Collections.emptyMap()), any(ScriptExecutionContext.class));
    }

    private Path createScriptsMetaInfoLicenseFiles() throws IOException
    {
        Path pathToFilesMigration = tempFolder.newFolder().toPath();

        //создаем importScripts.xml
        Path xmlFile = Files.writeString(pathToFilesMigration.resolve("importScripts.xml"),
                "<ns2:updateSequence xmlns:ns2=\"http://naumen.ru/updateSequence\">\n"
                        + "<action type=\"script\">startScript.groovy</action>\n"
                        + "<action type=\"upload\" entity=\"license\">license.xml</action>\n"
                        + "<action type=\"upload\" entity=\"metainfo\">metainfo.xml</action>\n"
                        + "<action type=\"script\">finishScript.groovy</action>\n"
                        + "</ns2:updateSequence>",
                StandardCharsets.UTF_8);
        //создаем license.xml
        Files.writeString(pathToFilesMigration.resolve("license.xml"), "license body", StandardCharsets.UTF_8);
        //создаем metainfo.xml
        Files.writeString(pathToFilesMigration.resolve("metainfo.xml"), "Metainfo content", StandardCharsets.UTF_8);
        //создаем startScript.groovy
        Files.writeString(pathToFilesMigration.resolve("startScript.groovy"), "pre import script body",
                StandardCharsets.UTF_8);

        //создаем startScript.groovy
        Files.writeString(pathToFilesMigration.resolve("finishScript.groovy"), "post import script body",
                StandardCharsets.UTF_8);
        return xmlFile;
    }

    @Nonnull
    private ApplyOnceScript createApplyOnceScript(final Path pathToFilesMigration)
    {
        XmlUtils xmlUtils = new XmlUtils();
        ConfigurationProperties configurationProperties = new ConfigurationProperties();
        UpdateSequenceSerializer updateSequenceSerializer =
                new UpdateSequenceSerializer(xmlUtils, configurationProperties);
        ExecScriptStrategy execScriptStrategy =
                new ExecScriptStrategy(uploadService, authorizationRunnerService, dispatch, scriptService,
                        schedulerTaskLogService);
        UploadMetainfoStrategy uploadMetainfoStrategy =
                new UploadMetainfoStrategy(uploadService, authorizationRunnerService, dispatch);
        UploadLicenseStrategy uploadLicenseStrategy =
                new UploadLicenseStrategy(uploadService, authorizationRunnerService, dispatch, metaStorageService);
        UploadEmbeddedAppStrategy uploadEmbeddedAppStrategy =
                new UploadEmbeddedAppStrategy(uploadService, authorizationRunnerService, dispatch,
                        embeddedApplicationService, scriptStorageService, metainfoServiceBean);
        OnStartApplicationActionsStrategyFactory onStartApplicationActionsStrategyFactory =
                new OnStartApplicationActionsStrategyFactory(execScriptStrategy, uploadMetainfoStrategy,
                        uploadLicenseStrategy, uploadEmbeddedAppStrategy);
        SDProInstallationValidator installationValidator =
                new SDProInstallationValidator(configurationProperties, migrationChecksumDao,
                        clusterInfoService, clusterServiceManager);
        return new ApplyOnceScript(pathToFilesMigration, migrationChecksumDao, updateSequenceSerializer,
                onStartApplicationActionsStrategyFactory, installationValidator, backgroundManagerBean,
                configurationProperties);
    }

    @Nonnull
    private ApplyOnceScript createApplyOnceScript()
    {
        XmlUtils xmlUtils = new XmlUtils();
        ConfigurationProperties configurationProperties = new ConfigurationProperties();
        UpdateSequenceSerializer updateSequenceSerializer =
                new UpdateSequenceSerializer(xmlUtils, configurationProperties);

        ExecScriptStrategy execScriptStrategy =
                new ExecScriptStrategy(uploadService, authorizationRunnerService, dispatch, scriptService,
                        schedulerTaskLogService);
        UploadMetainfoStrategy uploadMetainfoStrategy =
                new UploadMetainfoStrategy(uploadService, authorizationRunnerService, dispatch);
        UploadLicenseStrategy uploadLicenseStrategy =
                new UploadLicenseStrategy(uploadService, authorizationRunnerService, dispatch, metaStorageService);
        UploadEmbeddedAppStrategy uploadEmbeddedAppStrategy =
                new UploadEmbeddedAppStrategy(uploadService, authorizationRunnerService, dispatch,
                        embeddedApplicationService, scriptStorageService, metainfoServiceBean);
        OnStartApplicationActionsStrategyFactory onStartApplicationActionsStrategyFactory =
                new OnStartApplicationActionsStrategyFactory(execScriptStrategy, uploadMetainfoStrategy,
                        uploadLicenseStrategy, uploadEmbeddedAppStrategy);
        SDProInstallationValidator installationValidator =
                new SDProInstallationValidator(configurationProperties, migrationChecksumDao, clusterInfoService,
                        clusterServiceManager);

        return new ApplyOnceScript(null, migrationChecksumDao, updateSequenceSerializer,
                onStartApplicationActionsStrategyFactory, installationValidator, backgroundManagerBean,
                configurationProperties);
    }
}
