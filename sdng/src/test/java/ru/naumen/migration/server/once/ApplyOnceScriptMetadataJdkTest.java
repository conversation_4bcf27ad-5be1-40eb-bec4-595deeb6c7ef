package ru.naumen.migration.server.once;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Collections;

import org.apache.commons.fileupload2.core.FileItem;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.TemporaryFolder;
import org.junit.runner.RunWith;
import org.mockito.InOrder;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import jakarta.annotation.Nonnull;
import net.customware.gwt.dispatch.server.Dispatch;
import net.customware.gwt.dispatch.shared.Action;
import ru.naumen.core.server.cluster.external.ClusterInfoService;
import ru.naumen.core.server.cluster.external.ClusterServiceManager;
import ru.naumen.commons.server.utils.XmlUtils;
import ru.naumen.core.server.ConfigurationProperties;
import ru.naumen.core.server.background.BackgroundManagerBean;
import ru.naumen.core.server.embeddedapplication.EmbeddedApplicationService;
import ru.naumen.core.server.jta.TransactionRunnerService;
import ru.naumen.core.server.metastorage.impl.metainfo.MetaStorageService;
import ru.naumen.core.server.script.ScriptExecutionContext;
import ru.naumen.core.server.script.ScriptService;
import ru.naumen.core.server.script.storage.ScriptStorageServiceBean;
import ru.naumen.core.server.upload.NestedFileItem;
import ru.naumen.core.server.upload.UploadService;
import ru.naumen.metainfo.server.spi.MetainfoServiceBean;
import ru.naumen.metainfo.shared.dispatch2.ImportLicenseAction;
import ru.naumen.metainfo.shared.dispatch2.ImportMetainfoAction;
import ru.naumen.metainfo.shared.script.Script;
import ru.naumen.migration.server.once.configurable.UpdateSequenceSerializer;
import ru.naumen.migration.server.once.configurable.strategies.ExecScriptStrategy;
import ru.naumen.migration.server.once.configurable.strategies.OnStartApplicationActionsStrategyFactory;
import ru.naumen.migration.server.once.configurable.strategies.UploadEmbeddedAppStrategy;
import ru.naumen.migration.server.once.configurable.strategies.UploadLicenseStrategy;
import ru.naumen.migration.server.once.configurable.strategies.UploadMetainfoStrategy;
import ru.naumen.sec.server.admin.log.SchedulerTaskLogService;
import ru.naumen.sec.server.autorize.AuthorizationRunnerService;

@RunWith(MockitoJUnitRunner.class)
public class ApplyOnceScriptMetadataJdkTest
{
    @Rule
    public TemporaryFolder tempFolder = new TemporaryFolder();
    @Mock
    private UploadService uploadService;
    @Mock
    private Dispatch dispatch;
    @Mock
    private ScriptService scriptService;
    @Mock
    private AuthorizationRunnerService authorizationRunnerService;
    @Mock
    private TransactionRunnerService transactionRunner;
    @Mock
    private SchedulerTaskLogService schedulerTaskLogService;
    @Mock
    private MigrationChecksumDao migrationChecksumDao;
    @Mock
    private MetaStorageService metaStorageService;
    @Mock
    private EmbeddedApplicationService embeddedApplicationService;
    @Mock
    private ScriptStorageServiceBean scriptStorageService;
    @Mock
    private MetainfoServiceBean metainfoServiceBean;
    @Mock
    private ClusterInfoService clusterInfoService;
    @Mock
    private ClusterServiceManager clusterServiceManager;
    @Mock
    private BackgroundManagerBean backgroundManagerBean;

    @Before
    public void setup()
    {
        when(uploadService.add(any(FileItem.class), anyBoolean())).thenReturn("some$uuid");
        doAnswer(inv ->
        {
            ((Runnable)inv.getArguments()[1]).run();
            return null;
        }).when(authorizationRunnerService).runAsSuperUser(anyString(), any(Runnable.class));
        lenient().doAnswer(inv ->
        {
            ((Runnable)inv.getArguments()[0]).run();
            return null;
        }).when(transactionRunner).run(any(Runnable.class));
    }

    /**
     * Тестирование скриптовой миграции, если по пути path лежит xml-файл, в котором задан порядок выполнения
     * скриптов, загрузки метаинфы и лицензий плюс указан пустой тег с метаданными дистрибутива,
     * все загружается в том порядке как указано в файле.
     * Файлы скриптов, лицензий и меты лежат в подпапке, рядом с XML файлом порядка загрузки.
     * Метаданные дистрибутива не оказывают влияния на порядок выполнения этапов.
     */
    @Test
    public void testMigrationWithXMLFileAndEmptyMetadata() throws Exception
    {
        File newTmpFolder = tempFolder.newFolder();
        tempFolder.newFolder(newTmpFolder.getName(), "testWithMetadata");
        Path pathToFilesMigration = newTmpFolder.toPath();

        Path xmlFile = Files.writeString(pathToFilesMigration.resolve("importWithMetadata.xml"),
                "<ns2:updateSequence xmlns:ns2=\"http://naumen.ru/updateSequence\">\n"
                        + "<metadata>"
                        + "</metadata>"
                        + "<action type=\"script\">testWithMetadata/startScript.groovy</action>\n"
                        + "<action type=\"upload\" entity=\"license\">testWithMetadata/license.xml</action>\n"
                        + "<action type=\"upload\" entity=\"metainfo\">testWithMetadata/metainfo.xml</action>\n"
                        + "<action type=\"script\">testWithMetadata/finishScript.groovy</action>\n"
                        + "</ns2:updateSequence>",
                StandardCharsets.UTF_8);
        Files.writeString(pathToFilesMigration.resolve("testWithMetadata/license.xml"), "license body",
                StandardCharsets.UTF_8);
        Files.writeString(pathToFilesMigration.resolve("testWithMetadata/metainfo.xml"), "Metainfo content",
                StandardCharsets.UTF_8);
        Files.writeString(pathToFilesMigration.resolve("testWithMetadata/startScript.groovy"), "pre import script body",
                StandardCharsets.UTF_8);
        Files.writeString(pathToFilesMigration.resolve("testWithMetadata/finishScript.groovy"),
                "post import script body",
                StandardCharsets.UTF_8);

        InOrder inOrder = Mockito.inOrder(authorizationRunnerService, dispatch, scriptService);
        ApplyOnceScript applyOnceScript = createApplyOnceScript(xmlFile);
        applyOnceScript.onApplicationEvent(null);

        //проверяем последовательность выполнения
        checkOrdersForImportFromXMLFile(inOrder);
    }

    /**
     * Тестирование скриптовой миграции, если по пути path лежит xml-файл, в котором задан порядок выполнения
     * скриптов, загрузки метаинфы и лицензий плюс указаны необязательные метаданные дистрибутива, все загружается в
     * том порядке как указано в файле.
     * Файлы скриптов, лицензий и меты лежат в подпапке, рядом с XML файлом порядка загрузки.
     * Метаданные дистрибутива не оказывают влияния на порядок выполнения этапов.
     */
    @Test
    public void testMigrationWithXMLFileAndMetadata() throws Exception
    {
        File newTmpFolder = tempFolder.newFolder();
        tempFolder.newFolder(newTmpFolder.getName(), "testWithMetadata");
        Path pathToFilesMigration = newTmpFolder.toPath();

        Path xmlFile = Files.writeString(pathToFilesMigration.resolve("importWithMetadata.xml"),
                "<ns2:updateSequence xmlns:ns2=\"http://naumen.ru/updateSequence\">\n"
                        + "<metadata>"
                        + "<product>sdpro</product>"
                        + "<version>1.0.0</version>"
                        + "<platformVersion>1.0.0</platformVersion>"
                        + "</metadata>"
                        + "<action type=\"script\">testWithMetadata/startScript.groovy</action>\n"
                        + "<action type=\"upload\" entity=\"license\">testWithMetadata/license.xml</action>\n"
                        + "<action type=\"upload\" entity=\"metainfo\">testWithMetadata/metainfo.xml</action>\n"
                        + "<action type=\"script\">testWithMetadata/finishScript.groovy</action>\n"
                        + "</ns2:updateSequence>",
                StandardCharsets.UTF_8);
        Files.writeString(pathToFilesMigration.resolve("testWithMetadata/license.xml"), "license body",
                StandardCharsets.UTF_8);
        Files.writeString(pathToFilesMigration.resolve("testWithMetadata/metainfo.xml"), "Metainfo content",
                StandardCharsets.UTF_8);
        Files.writeString(pathToFilesMigration.resolve("testWithMetadata/startScript.groovy"), "pre import script body",
                StandardCharsets.UTF_8);
        Files.writeString(pathToFilesMigration.resolve("testWithMetadata/finishScript.groovy"),
                "post import script body",
                StandardCharsets.UTF_8);

        InOrder inOrder = Mockito.inOrder(authorizationRunnerService, dispatch, scriptService);
        ApplyOnceScript applyOnceScript = createApplyOnceScript(xmlFile);
        applyOnceScript.onApplicationEvent(null);

        //проверяем последовательность выполнения
        checkOrdersForImportFromXMLFile(inOrder);
    }

    /**
     * Тестирование скриптовой миграции, если по пути path лежит xml-файл, в котором задан порядок выполнения
     * скриптов, загрузки метаинфы и лицензий плюс указаны необязательные метаданные дистрибутива в неполном составе
     * (только версия продукта и платформы), все загружается в том порядке как указано в файле.
     * Файлы скриптов, лицензий и меты лежат в подпапке, рядом с XML файлом порядка загрузки.
     * Метаданные дистрибутива не оказывают влияния на порядок выполнения этапов.
     */
    @Test
    public void testMigrationWithXMLFileAndMetadataWithoutProduct() throws Exception
    {
        File newTmpFolder = tempFolder.newFolder();
        tempFolder.newFolder(newTmpFolder.getName(), "testWithMetadata");
        Path pathToFilesMigration = newTmpFolder.toPath();

        Path xmlFile = Files.writeString(pathToFilesMigration.resolve("importWithMetadata.xml"),
                "<ns2:updateSequence xmlns:ns2=\"http://naumen.ru/updateSequence\">\n"
                        + "<metadata>"
                        + "<version>1.0.0</version>"
                        + "<platformVersion>1.0.0</platformVersion>"
                        + "</metadata>"
                        + "<action type=\"script\">testWithMetadata/startScript.groovy</action>\n"
                        + "<action type=\"upload\" entity=\"license\">testWithMetadata/license.xml</action>\n"
                        + "<action type=\"upload\" entity=\"metainfo\">testWithMetadata/metainfo.xml</action>\n"
                        + "<action type=\"script\">testWithMetadata/finishScript.groovy</action>\n"
                        + "</ns2:updateSequence>",
                StandardCharsets.UTF_8);
        Files.writeString(pathToFilesMigration.resolve("testWithMetadata/license.xml"), "license body",
                StandardCharsets.UTF_8);
        Files.writeString(pathToFilesMigration.resolve("testWithMetadata/metainfo.xml"), "Metainfo content",
                StandardCharsets.UTF_8);
        Files.writeString(pathToFilesMigration.resolve("testWithMetadata/startScript.groovy"), "pre import script body",
                StandardCharsets.UTF_8);
        Files.writeString(pathToFilesMigration.resolve("testWithMetadata/finishScript.groovy"),
                "post import script body",
                StandardCharsets.UTF_8);

        InOrder inOrder = Mockito.inOrder(authorizationRunnerService, dispatch, scriptService);
        ApplyOnceScript applyOnceScript = createApplyOnceScript(xmlFile);
        applyOnceScript.onApplicationEvent(null);

        //проверяем последовательность выполнения
        checkOrdersForImportFromXMLFile(inOrder);
    }

    /**
     * Тестирование скриптовой миграции, если по пути path лежит xml-файл, в котором задан порядок выполнения
     * скриптов, загрузки метаинфы и лицензий плюс указаны необязательные метаданные дистрибутива в неполном составе
     * (только версия продукта), все загружается в том порядке как указано в файле.
     * Файлы скриптов, лицензий и меты лежат в подпапке, рядом с XML файлом порядка загрузки.
     * Метаданные дистрибутива не оказывают влияния на порядок выполнения этапов.
     */
    @Test
    public void testMigrationWithXMLFileAndMetadataWithoutProductAndPlatform() throws Exception
    {
        File newTmpFolder = tempFolder.newFolder();
        tempFolder.newFolder(newTmpFolder.getName(), "testWithMetadata");
        Path pathToFilesMigration = newTmpFolder.toPath();

        Path xmlFile = Files.writeString(pathToFilesMigration.resolve("importWithMetadata.xml"),
                "<ns2:updateSequence xmlns:ns2=\"http://naumen.ru/updateSequence\">\n"
                        + "<metadata>"
                        + "<version>1.0.0</version>"
                        + "</metadata>"
                        + "<action type=\"script\">testWithMetadata/startScript.groovy</action>\n"
                        + "<action type=\"upload\" entity=\"license\">testWithMetadata/license.xml</action>\n"
                        + "<action type=\"upload\" entity=\"metainfo\">testWithMetadata/metainfo.xml</action>\n"
                        + "<action type=\"script\">testWithMetadata/finishScript.groovy</action>\n"
                        + "</ns2:updateSequence>",
                StandardCharsets.UTF_8);
        Files.writeString(pathToFilesMigration.resolve("testWithMetadata/license.xml"), "license body",
                StandardCharsets.UTF_8);
        Files.writeString(pathToFilesMigration.resolve("testWithMetadata/metainfo.xml"), "Metainfo content",
                StandardCharsets.UTF_8);
        Files.writeString(pathToFilesMigration.resolve("testWithMetadata/startScript.groovy"), "pre import script body",
                StandardCharsets.UTF_8);
        Files.writeString(pathToFilesMigration.resolve("testWithMetadata/finishScript.groovy"),
                "post import script body",
                StandardCharsets.UTF_8);

        InOrder inOrder = Mockito.inOrder(authorizationRunnerService, dispatch, scriptService);
        ApplyOnceScript applyOnceScript = createApplyOnceScript(xmlFile);
        applyOnceScript.onApplicationEvent(null);

        //проверяем последовательность выполнения
        checkOrdersForImportFromXMLFile(inOrder);
    }

    private void checkOrdersForImportFromXMLFile(InOrder inOrder)
            throws net.customware.gwt.dispatch.shared.DispatchException
    {
        inOrder.verify(scriptService).execute(eq(new Script("pre import script body")),
                eq(Collections.emptyMap()), any(ScriptExecutionContext.class));
        inOrder.verify(dispatch).execute(any(ImportLicenseAction.class));
        inOrder.verify(dispatch).execute(any(ImportMetainfoAction.class));
        inOrder.verify(scriptService).execute(eq(new Script("post import script body")),
                eq(Collections.emptyMap()), any(ScriptExecutionContext.class));

        verify(uploadService, times(2)).add(any(NestedFileItem.class), anyBoolean());
        verify(authorizationRunnerService, times(4)).runAsSuperUser(eq("once"), any());
        verify(dispatch, times(2)).execute(any(Action.class));
        verify(scriptService, times(2)).execute(any(Script.class),
                eq(Collections.emptyMap()), any(ScriptExecutionContext.class));
    }

    @Nonnull
    private ApplyOnceScript createApplyOnceScript(final Path pathToFilesMigration)
    {
        XmlUtils xmlUtils = new XmlUtils();
        ConfigurationProperties configurationProperties = new ConfigurationProperties();
        UpdateSequenceSerializer updateSequenceSerializer =
                new UpdateSequenceSerializer(xmlUtils, configurationProperties);
        ExecScriptStrategy execScriptStrategy =
                new ExecScriptStrategy(uploadService, authorizationRunnerService, dispatch, scriptService,
                        schedulerTaskLogService);
        UploadMetainfoStrategy uploadMetainfoStrategy =
                new UploadMetainfoStrategy(uploadService, authorizationRunnerService, dispatch);
        UploadLicenseStrategy uploadLicenseStrategy =
                new UploadLicenseStrategy(uploadService, authorizationRunnerService, dispatch, metaStorageService);
        UploadEmbeddedAppStrategy uploadEmbeddedAppStrategy =
                new UploadEmbeddedAppStrategy(uploadService, authorizationRunnerService, dispatch,
                        embeddedApplicationService, scriptStorageService, metainfoServiceBean);
        OnStartApplicationActionsStrategyFactory onStartApplicationActionsStrategyFactory =
                new OnStartApplicationActionsStrategyFactory(execScriptStrategy, uploadMetainfoStrategy,
                        uploadLicenseStrategy, uploadEmbeddedAppStrategy);
        SDProInstallationValidator installationValidator =
                new SDProInstallationValidator(configurationProperties, migrationChecksumDao, clusterInfoService,
                        clusterServiceManager);
        return new ApplyOnceScript(pathToFilesMigration, migrationChecksumDao, updateSequenceSerializer,
                onStartApplicationActionsStrategyFactory, installationValidator, backgroundManagerBean,
                configurationProperties);
    }
}
