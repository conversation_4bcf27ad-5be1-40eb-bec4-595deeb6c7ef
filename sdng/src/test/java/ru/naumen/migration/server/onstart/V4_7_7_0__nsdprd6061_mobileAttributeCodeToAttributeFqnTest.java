package ru.naumen.migration.server.onstart;

import java.util.Collection;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;

import com.google.common.collect.HashBasedTable;

import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.utils.UUIDGenerator;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.mobile.MobileAttribute;
import ru.naumen.metainfo.shared.mobile.PresentationType;

/**
 * Тесты на миграцию {@link V4_7_7_0__nsdprd6061_mobileAttributeCodeToAttributeFqn}
 * 
 * <AUTHOR>
 * @since May 5, 2017
 */
public class V4_7_7_0__nsdprd6061_mobileAttributeCodeToAttributeFqnTest
{
    private V4_7_7_0__nsdprd6061_mobileAttributeCodeToAttributeFqn migration;

    @Before
    public void beforeTest()
    {
        migration = new V4_7_7_0__nsdprd6061_mobileAttributeCodeToAttributeFqn();
    }

    @Test
    public void testBuildMobileAttribute()
    {
        PresentationType presentation = PresentationType.WITH_TITLE;
        ClassFqn caseFqn = ClassFqn.parse("ou$foo");
        String attrCode = "bar";
        AttributeFqn attributeFqn = AttributeFqn.create(caseFqn, attrCode);
        Attribute attrMock = buildAttrMock(caseFqn, attrCode);

        MobileAttribute mobileAttribute = migration.buildMobileAttribute(attrMock, presentation);
        Assert.assertEquals(attrCode, mobileAttribute.getCode());
        Assert.assertEquals(attributeFqn.toString(), mobileAttribute.getAttributeFqnString());
        Assert.assertNotNull(mobileAttribute.getUuid());
        Assert.assertEquals(presentation, mobileAttribute.getPresentation());
    }

    @Test
    public void testMigrateAttribute_attributesWithEqualCode()
    {
        String attrCode = "foo";
        PresentationType presentation = PresentationType.WITH_TITLE;
        ClassFqn case1 = ClassFqn.parse("ou$bar");
        ClassFqn case2 = ClassFqn.parse("ou$baz");
        Attribute attr1 = buildAttrMock(case1, attrCode);
        Attribute attr2 = buildAttrMock(case2, attrCode);

        MobileAttribute attribute = new MobileAttribute("1234", attrCode, presentation);

        HashBasedTable<String, ClassFqn, Attribute> table = HashBasedTable.create();
        table.put(attrCode, case1, attr1);
        table.put(attrCode, case2, attr2);

        Collection<MobileAttribute> migratedAttributes = migration.migrateAttribute(attribute, table, null);

        Assert.assertTrue(migratedAttributes.size() == 2);
        Assert.assertTrue(migratedAttributes.stream().anyMatch(attr -> attr.getAttributeFqn().equals(attr1.getFqn())));
        Assert.assertTrue(migratedAttributes.stream().anyMatch(attr -> attr.getAttributeFqn().equals(attr2.getFqn())));
        Assert.assertTrue(migratedAttributes.stream().allMatch(attr -> presentation.equals(attr.getPresentation())));
    }

    @Test
    public void testMigrateAttribute_nonexistingCode()
    {
        String attrUuid = UUIDGenerator.get().nextUUID();
        String attrCode = "foo";
        PresentationType presentation = PresentationType.WITH_TITLE;

        ClassFqn defaultFqn = Constants.OU.FQN;
        MobileAttribute attribute = new MobileAttribute(attrUuid, attrCode, presentation);
        Collection<MobileAttribute> migratedAttributes = migration.migrateAttribute(attribute, HashBasedTable.create(),
                defaultFqn);

        Assert.assertTrue(migratedAttributes.size() == 1);
        MobileAttribute migratedAttr = migratedAttributes.stream().findFirst().get();
        Assert.assertEquals(attrUuid, migratedAttr.getUuid());
        Assert.assertEquals(AttributeFqn.create(defaultFqn, attrCode), migratedAttr.getAttributeFqn());
        Assert.assertEquals(presentation, migratedAttr.getPresentation());
    }

    @Test
    public void testMigrateAttribute_thatAlreadyMigrated()
    {
        MobileAttribute attribute = new MobileAttribute(UUIDGenerator.get().nextUUID(), "ou$foo@bar",
                PresentationType.WITH_TITLE);
        Collection<MobileAttribute> migratedAttributes = migration.migrateAttribute(attribute, null, null);

        Assert.assertTrue(migratedAttributes.size() == 1);
        Assert.assertEquals(attribute, migratedAttributes.stream().findFirst().get());
    }

    private Attribute buildAttrMock(ClassFqn caseFqn, String attrCode)
    {
        Attribute attributeMock = Mockito.mock(Attribute.class);
        AttributeFqn attributeFqn = AttributeFqn.create(caseFqn, attrCode);
        Mockito.when(attributeMock.getFqn()).thenReturn(attributeFqn);
        return attributeMock;
    }
}
