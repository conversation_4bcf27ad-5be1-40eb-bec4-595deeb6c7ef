package ru.naumen.scriptlet4docx.util.string;

import static org.junit.Assert.*;

import java.util.Arrays;
import java.util.List;

import ru.naumen.scriptlet4docx.util.test.TestUtils;

import org.junit.Test;

public class StringUtilTest
{
    private static final String TEST_FILES_PATH = "/ru/naumen/scriptlet4docx";

    @Test
    public void testReplaceOneByOne()
    {
        String in = "wswsw RRR d dh gh  gh g RRR d";
        String out = StringUtil.replaceOneByOne(in, "RRR", Arrays.asList("va", "vt"));

        assertEquals("wswsw va d dh gh  gh g vt d", out);

        in = "wswsw RRR d dh gh  gh g RRR d RRR";
        out = StringUtil.replaceOneByOne(in, "RRR", Arrays.asList("va", "vt"));

        assertEquals("wswsw va d dh gh  gh g vt d ", out);
    }

    @Test
    public void testNoBreakingInitialTemplate()
    {
        String in = TestUtils.readResource(TEST_FILES_PATH + "/util/string/StringUtilTest-1.txt");

        String out = StringUtil.replaceOneByOne(in, "4d5f4c1a-b11a-45f0-834c-e716b278e349",
                List.of());

        assertEquals(in, out);
    }

    @Test
    public void testEscapeSimpleSet()
    {
        String in = "markup text <, >> & &amp;";

        assertEquals("markup text &lt;, &gt;&gt; &amp; &amp;amp;", StringUtil.escapeSimpleSet(in));
    }
}
