package ru.naumen.scriptlet4docx.docx;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;

import static org.junit.Assert.*;

import org.junit.BeforeClass;
import org.junit.Test;
import org.mockito.Mockito;

import ru.naumen.scriptlet4docx.util.test.TestUtils;

public class DocxTemplaterTest
{
    private static HashMap<String, Object> params;
    private static final String TEST_FILES_PATH = "/ru/naumen/scriptlet4docx/docx/";
    private File none;

    @BeforeClass
    public static void setUpBeforeClass()
    {
        params = new HashMap<>();
        HashMap<String, String> contract = new HashMap<>();
        contract.put("number", "123#445");
        params.put("value", 1);
        params.put("contract", contract);
        params.put("escapeTest", "This should be escaped: &, <, >.");

        List<String> personList = new ArrayList<>();
        personList.add("vasya");
        personList.add("petya");

        params.put("personList", personList);
        params.put("menList", personList);

        List<HashMap<String, Object>> employeeList = new ArrayList<>();
        HashMap<String, Object> p1 = new HashMap<>();
        p1.put("name", "Tom");
        p1.put("address", "Moscow");
        HashMap<String, Object> p2 = new HashMap<>();
        p2.put("name", "John");
        p2.put("address", "New York");
        employeeList.add(p1);
        employeeList.add(p2);

        params.put("employeeList", employeeList);
        HashMap<String, Object> p3 = new HashMap<>();
        p3.put("nomeCliente", "Bob Smith");

        params.put("crm", p3);
    }

    @Test
    public void testPreProcessTableScripting_multiTr()
    {
        String template = TestUtils.readResource(TEST_FILES_PATH + "DocxTemplaterTest-5.xml");

        String result = TableScriptingProcessor.process(template);

        assertNotNull(result);
        assertFalse(result.contains("$["));

        assertTrue(result.contains("&lt;% def iterStatus=0; for ( wawawa in tour.wawawa ) { iterStatus++; %&gt;"));
        assertTrue(result.contains("${wawawa.myway}"));

        assertTrue(result.contains("&lt;% iterStatus=0; for ( mamama in tour1.mamama ) { iterStatus++; %&gt;"));
        assertTrue(result.contains("${mamama.myway}"));

    }

    @Test
    public void testProcessScriptedTemplate_tableScripting()
    {
        String template = TestUtils.readResource(TEST_FILES_PATH + "DocxTemplaterTest-6.xml");
        DocxTemplater templater = new DocxTemplater(none);
        template = templater.cleanupTemplate(template);
        String result = templater.processCleanedTemplate(template, params);

        assertNotNull(result);
        assertFalse(result.contains("$["));

        assertTrue(result.contains(">vasya<"));
        assertTrue(result.contains(">petya<"));
    }

    @Test
    public void testProcessScriptedTemplate_brokenType2_noProcess()
    {
        String template = TestUtils.readResource(TEST_FILES_PATH + "DocxTemplaterTest-7.xml");
        DocxTemplater templater = new DocxTemplater(none);
        String result = templater.processCleanedTemplate(template, params);

        assertNotNull(result);
        assertEquals(template, result);
    }

    @Test
    public void testProcessScriptedTemplate_tableScripting_iterStatus()
    {
        String template = TestUtils.readResource(TEST_FILES_PATH + "DocxTemplaterTest-8.xml");
        DocxTemplater templater = new DocxTemplater(none);
        template = templater.cleanupTemplate(template);
        String result = templater.processCleanedTemplate(template, params);

        assertNotNull(result);
        assertFalse(result.contains("$["));

        assertTrue(result.contains(">vasya<"));
        assertTrue(result.contains(">petya<"));

        assertFalse(result.contains(">${iterStatus}<"));

        assertTrue(result.contains(">1<"));
        assertTrue(result.contains(">2<"));
    }

    @Test
    public void testProcessScriptedTemplate_tableScripting_iterStatus_multiTable()
    {
        String template = TestUtils.readResource(TEST_FILES_PATH + "DocxTemplaterTest-4.xml");
        DocxTemplater templater = new DocxTemplater(none);
        template = templater.cleanupTemplate(template);
        String result = templater.processCleanedTemplate(template, params);

        assertNotNull(result);
        assertFalse(result.contains("$["));

        assertTrue(result.contains(">vasya<"));
        assertTrue(result.contains(">petya<"));

        assertFalse(result.contains(">${iterStatus}<"));

        assertTrue(result.contains(">1<"));
        assertTrue(result.contains(">2<"));

        assertEquals(2, StringUtils.countMatches(result, ">1<"));
        assertEquals(2, StringUtils.countMatches(result, ">2<"));
    }

    @Test
    public void testProcessScriptedTemplate_logicScriptlets()
    {
        String template = TestUtils.readResource(TEST_FILES_PATH + "DocxTemplaterTest-9.xml");

        HashMap<String, Object> params1 = new HashMap<>();
        params1.put("value", 1);
        DocxTemplater templater = new DocxTemplater(none);
        template = templater.cleanupTemplate(template);
        String result = templater.processCleanedTemplate(template, params1);

        assertNotNull(result);
        assertFalse(result.contains("else"));
        assertFalse(result.contains("if"));

        assertTrue(result.contains("mom and dad"));
        assertTrue(result.contains("like kitties"));

        params1 = new HashMap<>();
        params1.put("value", 0);

        template = templater.cleanupTemplate(template);
        result = templater.processCleanedTemplate(template, params1);

        assertNotNull(result);
        assertFalse(result.contains("else"));
        assertFalse(result.contains("if"));

        assertTrue(result.contains("mom and dad"));
        assertTrue(result.contains("like dogs"));
    }

    @Test
    public void testProcessScriptedTemplate_logicScriptlets_gt()
    {
        String template = TestUtils.readResource(TEST_FILES_PATH + "DocxTemplaterTest-15.xml");

        HashMap<String, Object> params1 = new HashMap<>();
        params1.put("value", 1);
        DocxTemplater templater = new DocxTemplater(none);
        template = templater.cleanupTemplate(template);
        String result = templater.processCleanedTemplate(template, params1);

        assertNotNull(result);
        assertFalse(result.contains("else"));
        assertFalse(result.contains("if"));

        assertTrue(result.contains("mom and dad"));
        assertTrue(result.contains("like dogs"));

        params1 = new HashMap<>();
        params1.put("value", 2);

        template = templater.cleanupTemplate(template);
        result = templater.processCleanedTemplate(template, params1);

        assertNotNull(result);
        assertFalse(result.contains("else"));
        assertFalse(result.contains("if"));

        assertTrue(result.contains("mom and dad"));
        assertTrue(result.contains("like kitties"));
    }

    @Test
    public void testProcessScriptedTemplate_logicScriptlets_lt()
    {
        String template = TestUtils.readResource(TEST_FILES_PATH + "DocxTemplaterTest-16.xml");

        HashMap<String, Object> params1 = new HashMap<>();
        params1.put("value", 1);
        DocxTemplater templater = new DocxTemplater(none);
        template = templater.cleanupTemplate(template);
        String result = templater.processCleanedTemplate(template, params1);

        assertNotNull(result);
        assertFalse(result.contains("else"));
        assertFalse(result.contains("if"));

        assertTrue(result.contains("mom and dad"));
        assertTrue(result.contains("like kitties"));

        params1 = new HashMap<>();
        params1.put("value", 2);

        template = templater.cleanupTemplate(template);
        result = templater.processCleanedTemplate(template, params1);

        assertNotNull(result);
        assertFalse(result.contains("else"));
        assertFalse(result.contains("if"));

        assertTrue(result.contains("mom and dad"));
        assertTrue(result.contains("like dogs"));
    }

    @Test
    public void testProcessScriptedTemplate_logicScriptlets_quote()
    {
        String template = TestUtils.readResource(TEST_FILES_PATH + "DocxTemplaterTest-17.xml");

        HashMap<String, Object> params1 = new HashMap<>();
        params1.put("value", "kitties");
        DocxTemplater templater = new DocxTemplater(none);
        template = templater.cleanupTemplate(template);
        String result = templater.processCleanedTemplate(template, params1);

        assertNotNull(result);
        assertFalse(result.contains("else"));
        assertFalse(result.contains("if"));

        assertTrue(result.contains("mom and dad"));
        assertTrue(result.contains("like kitties"));

        params1 = new HashMap<>();
        params1.put("value", "dogs");

        template = templater.cleanupTemplate(template);
        result = templater.processCleanedTemplate(template, params1);

        assertNotNull(result);
        assertFalse(result.contains("else"));
        assertFalse(result.contains("if"));

        assertTrue(result.contains("mom and dad"));
        assertTrue(result.contains("like dogs"));
    }

    @Test
    public void testProcessScriptedTemplate_logicScriptlets_quoteCurly()
    {
        String template = TestUtils.readResource(TEST_FILES_PATH + "/DocxTemplaterTest-18.xml");

        HashMap<String, Object> params1 = new HashMap<>();
        params1.put("value", "kitties");
        DocxTemplater templater = new DocxTemplater(none);
        template = templater.cleanupTemplate(template);
        String result = templater.processCleanedTemplate(template, params1);

        assertNotNull(result);
        assertFalse(result.contains("else"));
        assertFalse(result.contains("if"));

        assertTrue(result.contains("mom and dad"));
        assertTrue(result.contains("like kitties"));

        params1 = new HashMap<>();
        params1.put("value", "dogs");

        template = templater.cleanupTemplate(template);
        result = templater.processCleanedTemplate(template, params1);

        assertNotNull(result);
        assertFalse(result.contains("else"));
        assertFalse(result.contains("if"));

        assertTrue(result.contains("mom and dad"));
        assertTrue(result.contains("like dogs"));
    }

    @Test
    public void testProcessScriptedTemplate_spacePreserve()
    {
        String template = TestUtils.readResource(TEST_FILES_PATH + "/DocxTemplaterTest-10.xml");
        DocxTemplater templater = new DocxTemplater(none);
        template = templater.cleanupTemplate(template);
        String result = templater.processCleanedTemplate(template, params);

        assertNotNull(result);
        assertFalse(result.contains("print"));

        assertTrue(result.contains("like dogs"));
    }

    @Test
    public void testProcess_withInputStreamAsOutput() throws Exception
    {
        File inFile = new File("src/test/resources" + TEST_FILES_PATH + "DocxTemplaterTest-1.docx");
        File resFile = new File("target/test-files/DocxTemplaterTest-stream-2-result.docx");
        resFile.delete();

        DocxTemplater templater = new DocxTemplater(inFile);

        InputStream resStream = templater.processAndReturnInputStream(params);

        FileUtils.copyInputStreamToFile(resStream, resFile);

        assertTrue(resFile.exists());
        assertTrue(resFile.length() > 0);
    }

    @Test
    public void testProcess_withOutputStream() throws Exception
    {
        File inFile = new File("src/test/resources" + TEST_FILES_PATH + "DocxTemplaterTest-1.docx");
        File resFile = new File("src/test/resources" + TEST_FILES_PATH + "DocxTemplaterTest-stream-3-result.docx");
        resFile.delete();

        DocxTemplater docxTemplater = new DocxTemplater(inFile);

        docxTemplater.process(new FileOutputStream(resFile), params);

        assertTrue(resFile.exists());
        assertTrue(resFile.length() > 0);
    }

    @Test
    public void testProcess_stream() throws Exception
    {
        File inFile = new File("src/test/resources" + TEST_FILES_PATH + "DocxTemplaterTest-1.docx");
        File resFile = new File("target/test-files/DocxTemplaterTest-1-stream-result.docx");
        resFile.delete();

        DocxTemplater docxTemplater = new DocxTemplater(new FileInputStream(inFile), "k1");

        docxTemplater.process(resFile, params);

        assertTrue(resFile.exists());
        assertTrue(resFile.length() > 0);
    }

    @Test
    public void testProcess_header() throws Exception
    {
        File inFile = new File("src/test/resources" + TEST_FILES_PATH + "DocxTemplaterTest-2-header.docx");
        File resFile = new File("target/test-files/DocxTemplaterTest-2-header.docx");
        resFile.delete();

        DocxTemplater docxTemplater = new DocxTemplater(new FileInputStream(inFile), "k1");

        docxTemplater.process(resFile, params);

        assertTrue(resFile.exists());
        assertTrue(resFile.length() > 0);
    }

    @Test
    public void testProcess_streamMultiRun() throws Exception
    {
        File inFile = new File("src/test/resources" + TEST_FILES_PATH + "DocxTemplaterTest-1.docx");
        File resFile = new File("target/test-files/DocxTemplaterTest-1-stream-result1.docx");
        resFile.delete();

        final InputStream stream1 = new FileInputStream(inFile);
        final InputStream stream2 = Mockito.spy(new FileInputStream(inFile));

        final DocxTemplater docxTemplater1 = new DocxTemplater(stream1, "k2");
        final DocxTemplater docxTemplater2 = new DocxTemplater(stream2, "k2");

        docxTemplater1.process(resFile, params);
        docxTemplater2.process(resFile, params);

        // testing that stream2 was not actuall read but was closed
        Mockito.verify(stream2, Mockito.never()).read();
        Mockito.verify(stream2).close();

        assertTrue(resFile.exists());
        assertTrue(resFile.length() > 0);
    }

    @Test
    public void testProcessScriptedTemplate_escapeAmpLtGt()
    {
        String template = TestUtils.readResource(TEST_FILES_PATH + "DocxTemplaterTest-11.xml");

        HashMap<String, Object> params = new HashMap<>();

        params.put("escapeTest", "This should be escaped: &, <, >.");
        DocxTemplater templater = new DocxTemplater(none);
        template = templater.cleanupTemplate(template);
        String result = templater.processCleanedTemplate(template, params);

        assertTrue(result.contains(">This should be escaped: &amp;, &lt;, &gt;.<"));
    }

    @Test
    public void testProcessScriptedTemplate_nullsReplacement()
    {
        String template = TestUtils.readResource(TEST_FILES_PATH + "DocxTemplaterTest-12.xml");

        HashMap<String, Object> params = new HashMap<>();

        params.put("someNullyVar", null);
        DocxTemplater templater = new DocxTemplater(none);
        template = templater.cleanupTemplate(template);
        String result = templater.processCleanedTemplate(template, params);

        assertFalse(result.contains("space=\"preserve\">null<"));
        assertTrue(result.contains("space=\"preserve\"><"));

        assertFalse(result.contains("space=\"arg1\">null<"));
        assertTrue(result.contains("space=\"arg1\"><"));

        templater.setNullReplacement("UNKNOWD");
        result = templater.processCleanedTemplate(template, params);

        assertTrue(result.contains("space=\"preserve\">UNKNOWD<"));
        assertTrue(result.contains("space=\"arg1\">UNKNOWD<"));
    }

    @Test
    public void testProcessScriptedTemplate_noSuchPropertyNullsReplacement()
    {
        String template = TestUtils.readResource(TEST_FILES_PATH + "DocxTemplaterTest-12.xml");

        HashMap<String, Object> params = new HashMap<>();

        DocxTemplater templater = new DocxTemplater(none);
        template = templater.cleanupTemplate(template);
        String result = templater.processCleanedTemplate(template, params);

        assertFalse(result.contains("space=\"preserve\">null<"));
        assertTrue(result.contains("space=\"preserve\"><"));

        assertFalse(result.contains("space=\"arg1\">null<"));
        assertTrue(result.contains("space=\"arg1\"><"));

        templater.setNullReplacement("UNKNOWD");
        result = templater.processCleanedTemplate(template, params);

        assertTrue(result.contains("space=\"preserve\">UNKNOWD<"));
        assertTrue(result.contains("space=\"arg1\">UNKNOWD<"));
    }

    @Test
    public void testProcessScriptedTemplate_booleanAndCond()
    {
        String template = TestUtils.readResource(TEST_FILES_PATH + "DocxTemplaterTest-19.xml");

        HashMap<String, Object> params = new HashMap<>();

        params.put("cond1", "1");
        params.put("cond2", true);
        DocxTemplater templater = new DocxTemplater(none);
        template = templater.cleanupTemplate(template);
        String result = templater.processCleanedTemplate(template, params);
        assertTrue(result.contains("like kitties"));

        params.put("cond1", false);
        result = templater.processCleanedTemplate(template, params);
        assertFalse(result.contains("like kitties"));
    }

    @Test
    public void testProcessScriptedTemplate_newLine()
    {
        String template = TestUtils.readResource(TEST_FILES_PATH + "/DocxTemplaterTest-20.xml");

        DocxTemplater templater = new DocxTemplater(none);
        template = templater.cleanupTemplate(template);
        params.put("hasNewLines", "this is A\n this is B\r\n this is C");
        String result = templater.processCleanedTemplate(template, params);

        assertNotNull(result);
        assertTrue(result.contains("this is A<w:br/>"));
        assertEquals(4, StringUtils.countMatches(result, "this is A<w:br/>"));
    }
}
