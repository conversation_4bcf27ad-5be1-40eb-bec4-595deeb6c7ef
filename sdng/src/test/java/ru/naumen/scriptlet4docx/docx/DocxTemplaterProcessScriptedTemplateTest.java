package ru.naumen.scriptlet4docx.docx;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

import java.util.Collection;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.Parameterized;
import org.junit.runners.Parameterized.Parameters;

import java.io.File;
import java.util.Map;

import ru.naumen.scriptlet4docx.util.test.TestUtils;

/**
 * Вынесенные из {@link DocxTemplaterTest} тесты на {@link DocxTemplater#processCleanedTemplate(String, Map)}.
 * Параметризированные.
 *
 * <AUTHOR>
 * @since 01.08.2024
 */
@RunWith(Parameterized.class)
public class DocxTemplaterProcessScriptedTemplateTest
{
    @Parameters
    public static Collection<Object[]> testFilesPaths()
    {
        return List.of(new Object[][] {
                { "DocxTemplaterTest-1.xml", 4 },
                { "DocxTemplaterTest-2.xml", 1 },
                { "DocxTemplaterTest-3.xml", 1 }
        });
    }

    private static final String TEST_FILES_FOLDER = "/ru/naumen/scriptlet4docx/docx/";
    private final String filePath;
    private final Map<String, Object> params;
    private final int expectedMatchesCount;

    private File none;

    public DocxTemplaterProcessScriptedTemplateTest(String filePath, int expectedMatchesCount)
    {
        this.filePath = filePath;
        this.expectedMatchesCount = expectedMatchesCount;
        this.params = Map.of("contract", Map.of("number", "123#445"));
    }

    @Test
    public void testProcessScriptedTemplate()
    {
        String template = TestUtils.readResource(TEST_FILES_FOLDER + filePath);

        DocxTemplater templater = new DocxTemplater(none);
        template = templater.cleanupTemplate(template);
        String result = templater.processCleanedTemplate(template, params);

        assertNotNull(result);
        assertTrue(result.contains("123#445"));
        assertEquals(expectedMatchesCount, StringUtils.countMatches(result, "123#445"));
    }
}