package ru.naumen.scriptlet4docx.docx;

import groovy.util.Eval;

import java.io.File;
import java.io.FileInputStream;
import java.util.List;
import java.util.Objects;

import static org.junit.Assert.*;

import org.junit.Test;

import ru.naumen.scriptlet4docx.docx.TemplateContent.ContentItem;

public class TemplateFileManagerTest
{
    private static final String TEST_FILES_PATH = "/ru/naumen/scriptlet4docx/docx/";

    @Test
    public void testTemplatesDirOps() throws Exception
    {
        TemplateFileManager mgr = TemplateFileManager.getInstance();
        mgr.cleanup();

        String templateKey = "k1";
        File tempDir = mgr.getTemplatesDir();
        File docxFile = new File("src/test/resources" + TEST_FILES_PATH + "DocxTemplaterTest-1.docx");
        assertFalse(mgr.isPrepared(templateKey));
        mgr.prepare(docxFile, templateKey);
        assertTrue(mgr.isPrepared(templateKey));
        assertFalse(Eval.x(
                mgr.getTemplateContent(templateKey).getItems(),
                "x.find{it.identifier == 'document.xml'}.content").toString().isEmpty());
        assertEquals(mgr.getTemplateUnzipFolder(templateKey), new File(tempDir, templateKey + "/"
                + "/doc-unzip"));
        assertFalse(mgr.isPreProcessedTemplateExists(templateKey));
        mgr.savePreProcessed(templateKey, new TemplateContent(List.of(new ContentItem("document.xml", "1"))));
        assertTrue(mgr.isPreProcessedTemplateExists(templateKey));
        assertEquals("1", Eval.x(mgr.getTemplateContent(templateKey).getItems(),
                "x.find{it.identifier == 'document.xml'}.content"));

        assertFalse(mgr.isTemplateFileFromStreamExists(templateKey));
        mgr.saveTemplateFileFromStream(templateKey, new FileInputStream(docxFile));
        assertTrue(mgr.isTemplateFileFromStreamExists(templateKey));

        mgr.cleanup();
        assertTrue(tempDir.exists());
        assertEquals(0, Objects.requireNonNull(tempDir.listFiles()).length);
    }
}
