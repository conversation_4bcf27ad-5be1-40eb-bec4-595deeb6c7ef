package ru.naumen.scriptlet4docx.docx;

import static org.junit.Assert.assertTrue;

import java.io.File;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.Parameterized;
import org.junit.runners.Parameterized.Parameters;

/**
 * Вынесенные из {@link DocxTemplaterTest} тесты на {@link DocxTemplater#process(File, Map)}.
 *
 * <AUTHOR>
 * @since 02.08.2024
 */
@RunWith(Parameterized.class)
public class DocxTemplaterProcessFilesTest
{
    @Parameters
    public static Collection<Object[]> inputAndOutputFilePaths()
    {
        return List.of(new Object[][] {
                { "DocxTemplaterTest-1.docx", "DocxTemplaterTest-1-file-result-1.docx" },
                { "DocxTemplaterTest-13.docx", "DocxTemplaterTest-1-file-result-2.docx" },
                { "DocxTemplaterTest-14.docx", "DocxTemplaterTest-1-file-result-3.docx" }
        });
    }

    private static final String TEST_INPUT_FILES_FOLDER = "src/test/resources/ru/naumen/scriptlet4docx/docx/";
    private static final String TEST_OUTPUT_FILES_FOLDER = "target/test-files/";
    private final String inputFile;
    private final String outputFile;
    private final Map<String, Object> params;

    public DocxTemplaterProcessFilesTest(String inputFile, String outputFile)
    {
        this.inputFile = inputFile;
        this.outputFile = outputFile;

        params = new HashMap<>();
        Map<String, String> contract = Map.of("number", "123#445");
        params.put("contract", contract);

        List<HashMap<String, Object>> employeeList = new ArrayList<>();
        HashMap<String, Object> p1 = new HashMap<>();
        p1.put("name", "Tom");
        p1.put("address", "Moscow");
        HashMap<String, Object> p2 = new HashMap<>();
        p2.put("name", "John");
        p2.put("address", "New York");
        employeeList.add(p1);
        employeeList.add(p2);

        params.put("employeeList", employeeList);
    }

    @Test
    public void testProcess_file()
    {
        File inFile = new File(TEST_INPUT_FILES_FOLDER + inputFile);
        File resFile = new File(TEST_OUTPUT_FILES_FOLDER + outputFile);
        resFile.delete();

        DocxTemplater docxTemplater = new DocxTemplater(inFile);

        docxTemplater.process(resFile, params);

        assertTrue(resFile.exists());
        assertTrue(resFile.length() > 0);
    }
}