package ru.naumen.scriptlet4docx.docx;

import static org.junit.Assert.*;

import org.junit.Test;

import ru.naumen.scriptlet4docx.util.test.TestUtils;

public class DividedScriptWrapsProcessorTest
{
    private static final String TEST_FILES_PATH = "/ru/naumen/scriptlet4docx/docx/";

    @Test
    public void testProcess_dollar()
    {
        String template = TestUtils.readResource(TEST_FILES_PATH + "DividedScriptWrapsProcessor-1.xml");

        String result = DividedScriptWrapsProcessor.process(template);

        assertNotNull(result);
        assertTrue(result.contains("${"));
        assertFalse(result.contains("${contract"));
    }

    @Test
    public void testProcess_ltGt()
    {
        String template = TestUtils.readResource(TEST_FILES_PATH + "DividedScriptWrapsProcessor-2.xml");

        String result = DividedScriptWrapsProcessor.process(template);

        assertNotNull(result);
        assertTrue(result.contains("&lt;%"));
        assertTrue(result.contains("%&gt;"));
    }

    @Test
    public void testProcess_ltGt_out()
    {
        String template = TestUtils.readResource(TEST_FILES_PATH + "DividedScriptWrapsProcessor-3.xml");

        String result = DividedScriptWrapsProcessor.process(template);

        assertNotNull(result);
        assertTrue(result.contains("&lt;%="));
        assertTrue(result.contains("%&gt;"));
    }
}
