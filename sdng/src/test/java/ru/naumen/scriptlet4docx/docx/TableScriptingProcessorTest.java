package ru.naumen.scriptlet4docx.docx;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

import ru.naumen.commons.shared.FxException;

import org.junit.Test;

import ru.naumen.scriptlet4docx.util.test.TestUtils;

public class TableScriptingProcessorTest
{
    private static final String TEST_FILES_PATH = "/ru/naumen/scriptlet4docx/docx/";

    @Test
    public void testProccess1()
    {
        String template = TestUtils.readResource(
                TEST_FILES_PATH + "TableScriptingProcessorTest-1.xml");

        String result = TableScriptingProcessor.process(template);

        assertNotNull(result);
        assertFalse(result.contains("$["));
        assertTrue(result
                .contains("&lt;% def iterStatus=0; for ( wawawa in tour.wawawa ) { iterStatus++; %&gt;"));
        assertTrue(result.contains("${wawawa.myway}"));
    }

    @Test(expected = FxException.class)
    public void testProccess_multiVarEx1()
    {
        String template = TestUtils.readResource(
                TEST_FILES_PATH + "TableScriptingProcessorTest-2.xml");

        TableScriptingProcessor.process(template);
    }

    @Test(expected = FxException.class)
    public void testProccess_multiVarEx2()
    {
        String template = TestUtils.readResource(
                TEST_FILES_PATH + "TableScriptingProcessorTest-3.xml");

        TableScriptingProcessor.process(template);
    }

    @Test
    public void testProccess_rootVar()
    {
        String cleanTr = TestUtils.readResource(
                TEST_FILES_PATH + "TableScriptingProcessorTest-4.xml");

        String result = TableScriptingProcessor.process(cleanTr);

        assertNotNull(result);
        assertFalse(result.contains("$["));
        assertTrue(result
                .contains("&lt;% def iterStatus=0; for ( wawawa in wawawaList ) { iterStatus++; %&gt;"));
        assertTrue(result.contains("${wawawa.id}"));
    }

    @Test
    public void testProccess_iterStatus_MultiTables()
    {
        String cleanTr = TestUtils.readResource(
                TEST_FILES_PATH + "TableScriptingProcessorTest-5.xml");

        String result = TableScriptingProcessor.process(cleanTr);

        assertNotNull(result);
        assertFalse(result.contains("$["));
        assertTrue(result
                .contains("&lt;% def iterStatus=0; for ( wawawa in wawawaList ) { iterStatus++; %&gt;"));
        assertTrue(result.contains("${wawawa.id}"));

        assertTrue(result
                .contains("&lt;% iterStatus=0; for ( bababa in bababaList ) { iterStatus++; %&gt;"));
        assertTrue(result.contains("${bababa.id}"));
    }

    @Test
    public void testProccess2()
    {
        String template = TestUtils.readResource(
                TEST_FILES_PATH + "TableScriptingProcessorTest-6.xml");

        String result = TableScriptingProcessor.process(template);

        assertNotNull(result);
        assertFalse(result.contains("$["));
        assertTrue(result
                .contains("&lt;% def iterStatus=0; for ( employee in employeeList ) { iterStatus++; %&gt;"));
        assertTrue(result.contains("${employee.address}"));
    }

    @Test
    public void testProccess_internalTr()
    {
        String template = TestUtils.readResource(
                TEST_FILES_PATH + "TableScriptingProcessorTest-7.xml");

        String result = TableScriptingProcessor.process(template);

        assertNotNull(result);
        assertFalse(result.contains("trPr>&lt;"));
        assertFalse(result.contains("&gt;<w:trHeigh"));
        assertTrue(result
                .contains("&lt;% def iterStatus=0; for ( employee in employeeList ) { iterStatus++; %&gt;"));
        assertTrue(result.contains("${employee.address}"));
    }
}
