package ru.naumen.scriptlet4docx.docx;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

import java.util.List;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.Parameterized;
import org.junit.runners.Parameterized.Parameters;

import ru.naumen.scriptlet4docx.util.test.TestUtils;

@RunWith(Parameterized.class)
public class TableScriptingRowProcessorTest
{
    @Parameters
    public static List<String[]> fileNamesExpectedContents()
    {
        return List.of(new String[][] {
                { "TableScriptingRowProcessorTest-1.xml",
                        "&lt;% def iterStatus=0; for ( wawawa in tour.wawawa ) { iterStatus++; %&gt;",
                        "${wawawa.myway}" },
                { "TableScriptingRowProcessorTest-2.xml",
                        "&lt;% def iterStatus=0; for ( person in personList ) { iterStatus++; %&gt;",
                        "${person}" },
                { "TableScriptingRowProcessorTest-3.xml",
                        "&lt;% def iterStatus=0; for ( wawawa in tour.wawawa ) { iterStatus++; %&gt;",
                        "${repSrv.applyFunc(wawawa.myway)}" },
                { "TableScriptingRowProcessorTest-4.xml",
                        "&lt;% def iterStatus=0; for ( wawawa in tourist.tour.wawawa ) { iterStatus++; %&gt;",
                        "${wawawa.myway}" },
                { "TableScriptingRowProcessorTest-5.xml",
                        "&lt;% def iterStatus=0; for ( wawawa in tourist().tour.wawawa ) { iterStatus++; %&gt;",
                        "${wawawa.myway}" },
                { "TableScriptingRowProcessorTest-6.xml",
                        "&lt;% def iterStatus=0; for ( wawawa in tour.wawawa ) { iterStatus++; %&gt;",
                        "${tour.applyFunc(wawawa.myway)}" },
                { "TableScriptingRowProcessorTest-7.xml",
                        "&lt;% def iterStatus=0; for ( wawawa in wawawaList ) { iterStatus++; %&gt;",
                        "${wawawa.myway}" },
        });
    }

    private static final String TEST_FILES_FOLDER = "/ru/naumen/scriptlet4docx/docx/";

    private final String fileName;
    private final String expectedCodeSubstitution;
    private final String expectedVariableSubstitution;

    public TableScriptingRowProcessorTest(
            String fileName, String expectedCodeSubstitution, String expectedVariableSubstitution)
    {
        this.fileName = fileName;
        this.expectedCodeSubstitution = expectedCodeSubstitution;
        this.expectedVariableSubstitution = expectedVariableSubstitution;
    }

    @Test
    public void testProcess()
    {
        String template = TestUtils.readResource(TEST_FILES_FOLDER + fileName);

        TableScriptingRowProcessor tableScriptingRowProcessor = new TableScriptingRowProcessor();

        String result = tableScriptingRowProcessor.process(template);

        assertNotNull(result);
        assertFalse(result.contains("$["));
        assertTrue(result.contains(expectedCodeSubstitution));
        assertTrue(result.contains(expectedVariableSubstitution));
    }
}
