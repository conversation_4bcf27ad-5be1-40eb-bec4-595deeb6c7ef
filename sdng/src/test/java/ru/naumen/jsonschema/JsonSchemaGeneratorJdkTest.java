package ru.naumen.jsonschema;

import static org.junit.Assert.assertEquals;
import static ru.naumen.jsonschema.JsonSchemaGenerator.getCommonProperties;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import org.junit.Test;

/**
 * Тесты на проверку методов для генерации JSON-схем
 *
 * <AUTHOR>
 * @since 25.01.2021
 */
public class JsonSchemaGeneratorJdkTest
{
    static class PrayerClass
    {
        private final String prayer = "C наш, сущий в памяти!\n"
            + "да компилируется код Твой;\n"
            + "да приидет царствие Софта Твоего;\n"
            + "да будут действительны указатели Твои\n"
            + "и в ОЗУ, как на жестком диске;\n"
            + "массив наш насущный подавай нам на каждый день;\n"
            + "и прости нам варнинги наши,\n"
            + "как и мы избавляемся от ошибок наших;\n"
            + "и не введи нас в бесконечный цикл,\n"
            + "но избавь нас от винды.\n"
            + "Ибо Твое есть Царство и сила и слава во веки.\n"
            + "Энтер.";

        public String getPrayer()
        {
            return prayer;
        }
    }

    /**
     * Проверка, что при создании Json схемы, в методе {@link JsonSchemaGenerator#getCommonProperties(Field, Class)}
     * значение константы с модификатором final создается в возращаемом commonProperties
     */
    @Test
    public void testJsonSchemaUpdate()
    {
        PrayerClass prayerClass = new PrayerClass();
        List<Field> fields = Arrays.asList(PrayerClass.class.getDeclaredFields());
        Field field = fields.get(0);
        Map<String, Object> commonProperties = getCommonProperties(field, field.getType());
        assertEquals(prayerClass.getPrayer(), commonProperties.get("const"));
    }
}