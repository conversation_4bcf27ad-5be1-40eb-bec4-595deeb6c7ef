package ru.naumen.jsonschema;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

import java.util.List;
import java.util.Map;
import java.util.Set;

import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;

import com.google.gson.JsonParser;

import ru.naumen.core.server.script.api.IJsonSchemaApi;
import ru.naumen.core.server.script.api.JsonSchemaApi;
import ru.naumen.core.server.script.conf.ScriptModule;
import ru.naumen.core.server.script.spi.modules.MetaDataScriptModulesService;
import ru.naumen.core.server.script.spi.modules.ScriptModulesService;
import ru.naumen.core.server.script.spi.modules.events.ModulesClassLoaderChangingEvent;
import ru.naumen.core.server.script.spi.modules.strategy.all.BaseAllScriptModulesCompilationServiceTest;
import ru.naumen.jsonschema.annotations.MechanismSettings;

/**
 * Тестирование генерации json схемы
 *
 * <AUTHOR>
 * @since Aug 3, 2020
 */
public class JsonSchemaJdkTest extends BaseAllScriptModulesCompilationServiceTest
{
    private static final String PORTAL_SETTINGS_JSON_SCHEMA = "{\"moduleCode\":\"\",\"$schema\":\"http://json-schema"
            + ".org/draft-07/schema#\",\"$title\":\"Настройки портала\",\"type\":\"object\","
            + "\"properties\":{\"abstractClassSettings\":{\"widget\":\"widget-widget-widget\",\"nullable\":true,"
            + "\"description\":\"Параметры локализации\",\"disabled\":true,\"title\":\"Локализация\","
            + "\"type\":\"array\",\"items\":{\"editable\":\"editable_val\",\"key\":\"portalFqn\","
            + "\"oneOf\":[{\"description\":\"Настройки, применимые для всех сущностей\","
            + "\"nesting\":{\"rootSettings\":\"includeNested=false\",\"childSettings\":\"includeNested=true\","
            + "\"parentField\":\"parentField - AllowedLanguage\",\"idField\":\"idField - "
            + "AllowedLanguage\"},\"additionalProperties\":false,\"title\":\"Любой класс объектов\","
            + "\"type\":\"object\",\"properties\":{\"locale\":{\"nullable\":true,\"description\":\"Определяет связь "
            + "языка на портале и локали SMP\",\"titles\":[\"ruru\",\"enen\",\"clientclient\"],\"title\":\"Связанная "
            + "локаль SMP\",\"type\":\"string\",\"enum\":[\"ru\",\"en\",\"client\"]},"
            + "\"shareAlias\":{\"widget\":\"widget-widget-widget\",\"nullable\":true,\"description\":\"Должно "
            + "содержать только имя файла\",\"disabled\":true,\"title\":\"title for JsonSchemaMeta field "
            + "shareAlias\",\"type\":\"string\"},\"isDefault\":{\"nullable\":true,\"description\":\"На портале может "
            + "быть настроено\",\"title\":\"Язык по умолчанию\",\"type\":\"boolean\","
            + "\"customProperty\":\"customProperty - isDefault\"},\"title\":{\"nullable\":true,"
            + "\"description\":\"Отображается пользователю\",\"title\":\"Название языка\",\"type\":\"string\"}},"
            + "\"customProperty\":\"customProperty - AllowedLanguage\"},{\"description\":\"Настройки, применимые для "
            + "всех сущностей\",\"nesting\":{\"rootSettings\":\"includeNested=false\","
            + "\"childSettings\":\"includeNested=true\",\"parentField\":\"parentField - AbstractClassSettings\","
            + "\"idField\":\"idField - AbstractClassSettings\"},\"additionalProperties\":false,\"title\":\"Любой "
            + "класс объектов\",\"type\":\"object\",\"properties\":{\"locale\":{\"nullable\":true,"
            + "\"description\":\"Определяет TestClassForOneOf\",\"titles\":[\"ruru\",\"enen\",\"clientclient\"],"
            + "\"title\":\"TestClassForOneOf\",\"type\":\"string\",\"customProperty\":\"customProperty - "
            + "TestClassForOneOf Locale\",\"enum\":[\"ru\",\"en\",\"client\"]},"
            + "\"shareAlias\":{\"widget\":\"widget-widget-widget\",\"nullable\":true,\"description\":\"Должно "
            + "содержать только имя файла\",\"disabled\":true,\"title\":\"title for JsonSchemaMeta field "
            + "shareAlias\",\"type\":\"string\"},\"isDefault\":{\"nullable\":true,\"description\":\"На портале может "
            + "быть настроено\",\"title\":\"Язык по умолчанию\",\"type\":\"boolean\","
            + "\"customProperty\":\"customProperty - isDefault\"},\"title\":{\"nullable\":true,"
            + "\"description\":\"Отображается пользователю\",\"title\":\"Название языка\",\"type\":\"string\"}}}]},"
            + "\"customProperty\":\"customProperty - abstractClassSettings\"},"
            + "\"TestClassForUi\":{\"widget\":\"widget-widget-widget\",\"nullable\":true,\"description\":\"Определяет"
            + " TestClassForUi\",\"disabled\":true,\"nesting\":{\"rootSettings\":\"includeNested=false\","
            + "\"childSettings\":\"includeNested=true\",\"parentField\":\"parentField - TestClassForUi\","
            + "\"idField\":\"idField - TestClassForUi\"},\"title\":\"TestClassForUi\",\"type\":\"object\","
            + "\"properties\":{\"ttttt\":{\"nullable\":true,\"description\":\"Отображается пользователю\","
            + "\"title\":\"Название языка\",\"type\":\"string\"}}}},\"$id\":\"PortalSettings_schema\"}";

    private static final String PORTAL_SETTINGS = "import groovy.transform.Canonical\n"
            + "\n"
            + "import ru.naumen.jsonschema.annotations.*\n"
            + "\n"
            + "@Canonical\n"
            + "@MechanismSettings(name = \"PortalSettings\")\n"
            + "@JsonSchemaMeta(title = \"Настройки портала\")\n"
            + "class PortalSettings\n"
            + "{\n"
            + "    @JsonSchemaMeta(title = \"Локализация\", description = \"Параметры локализации\")\n"
            + "    @UiSchemaMeta(disabled = true, widget = \"widget-widget-widget\")\n"
            + "    @CustomProperty(name = \"customProperty\", value = \"customProperty - abstractClassSettings\")\n"
            + "    List<AbstractClassSettings> abstractClassSettings = Collections.emptyList();\n"
            + "\n"
            + "    @JsonSchemaMeta(title = 'TestClassForUi', description = 'Определяет TestClassForUi')\n"
            + "    @UiSchemaMeta(disabled = true, widget = \"widget-widget-widget\")\n"
            + "    TestClassForUi TestClassForUi = new TestClassForUi()\n"
            + "}\n"
            + "\n"
            + "@Canonical\n"
            + "@CustomProperty(name = \"customProperty\", value = \"customProperty - AllowedLanguage\")\n"
            + "@JsonSchemaMeta(title = 'Любой класс объектов', description = 'Настройки, применимые для всех "
            + "сущностей', additional = false)\n"
            + "@UiHierarchy(id = \"idField - AllowedLanguage\", parent = \"parentField - AllowedLanguage\")\n"
            + "class AllowedLanguage\n"
            + "{\n"
            + "    @JsonSchemaMeta(title = 'Связанная локаль SMP', description = 'Определяет связь языка на портале и"
            + " локали SMP')\n"
            + "    Locale locale = Locale.ru;\n"
            + "}\n"
            + "\n"
            + "@Canonical\n"
            + "@JsonSchemaMeta(title = 'Любой класс объектов', description = 'Настройки, применимые для всех "
            + "сущностей', additional = false)\n"
            + "class TestClassForOneOf\n"
            + "{\n"
            + "    @JsonSchemaMeta(title = 'TestClassForOneOf', description = 'Определяет TestClassForOneOf')\n"
            + "    @CustomProperty(name = \"customProperty\", value = \"customProperty - TestClassForOneOf Locale\")\n"
            + "    Locale locale = Locale.ru;\n"
            + "}\n"
            + "\n"
            + "@Canonical\n"
            + "@JsonSchemaMeta(title = \"Локаль портала\")\n"
            + "@JsonSchemaOneOf(key = \"portalFqn\", editable = \"editable_val\", classes=[ AllowedLanguage.class, "
            + "TestClassForOneOf ])\n"
            + "@UiHierarchy(id = \"idField - AbstractClassSettings\", parent = \"parentField - "
            + "AbstractClassSettings\")\n"
            + "abstract class AbstractClassSettings\n"
            + "{\n"
            + "    @JsonSchemaMeta(title = \"Название языка\", description = \"Отображается пользователю\")\n"
            + "    String title = \"\";\n"
            + "\n"
            + "    @JsonSchemaMeta(title = \"title for JsonSchemaMeta field shareAlias\", description   = \"Должно "
            + "содержать только имя файла\")\n"
            + "    @UiSchemaMeta(disabled = true, widget = \"widget-widget-widget\")\n"
            + "    String shareAlias = \"\";\n"
            + "\n"
            + "    @JsonSchemaMeta(title = \"Язык по умолчанию\", description = \"На портале может быть настроено\")\n"
            + "    @CustomProperty(name = \"customProperty\", value = \"customProperty - isDefault\")\n"
            + "    Boolean isDefault = false;\n"
            + "}\n"
            + "\n"
            + "@Canonical\n"
            + "@JsonSchemaMeta(title = \"Локаль портала\")\n"
            + "@UiHierarchy(id = \"idField - TestClassForUi\", parent = \"parentField - TestClassForUi\")\n"
            + "class TestClassForUi\n"
            + "{\n"
            + "    @JsonSchemaMeta(title = \"Название языка\", description = \"Отображается пользователю\")\n"
            + "    String ttttt = \"\";\n"
            + "}\n"
            + "\n"
            + "enum Locale\n"
            + "{\n"
            + "    @UiTitle(title = \"ruru\")\n"
            + "    ru,\n"
            + "    @UiTitle(title = \"enen\")\n"
            + "    en,\n"
            + "    @UiTitle(title = \"clientclient\")\n"
            + "    client\n"
            + "}";

    private static final String SIMPLE_MECHANISM = "import ru.naumen.jsonschema.annotations.*\n"
            + "import groovy.transform.Canonical\n"
            + "\n"
            + "@Canonical\n"
            + "@MechanismSettings(name = \"SimpleMechanism\")\n"
            + "class SimpleMechanism\n"
            + "{\n"
            + "    @JsonSchemaMeta\n"
            + "    Integer i = 0;\n"
            + "}";

    private static final String SIMPLE_MECHANISM_JSON_SCHEMA =
            "{\"moduleCode\":\"\",\"$schema\":\"http://json-schema.org/draft-07/schema#\","
                    + "\"$title\":\"SimpleMechanism\",\"type\":\"object\",\"properties\":{\"i\":{\"nullable\":true,"
                    + "\"title\":\"i\",\"type\":\"integer\"}},\"$id\":\"SimpleMechanism_schema\"}";

    private static final String MECHANISM_NAME_WITH_DIFFERENT_CLASS_NAME = "import groovy.transform.Canonical\n"
            + "import ru.naumen.jsonschema.annotations.JsonSchemaMeta\n"
            + "import ru.naumen.jsonschema.annotations.MechanismSettings\n"
            + "import ru.naumen.jsonschema.annotations.UiSchemaMeta\n"
            + "import ru.naumen.jsonschema.annotations.UiTitle\n"
            + "\n"
            + "\n"
            + "enum Schema\n"
            + "{\n"
            + "    @UiTitle(title = 'SD Pro')\n"
            + "    Corp,\n"
            + "    @UiTitle(title = 'Гибкая настройка')\n"
            + "    Unique\n"
            + "\n"
            + "\n"
            + "    Schema()\n"
            + "    {}\n"
            + "}\n"
            + "\n"
            + "\n"
            + "@MechanismSettings(name = 'app-initial')\n"
            + "@Canonical\n"
            + "@JsonSchemaMeta(title = 'Параметры для инициализации настроек портала')\n"
            + "class InitSettings\n"
            + "{\n"
            + "    @JsonSchemaMeta(title = 'Схема данных', description = 'Выбранное значение влияет на основную "
            + "логику взаимодействия с SMP')\n"
            + "    Schema schema = Schema.Unique\n"
            + "}\n"
            + "\n"
            + "\n"
            + "@MechanismSettings(name = 'app')\n"
            + "@Canonical\n"
            + "@JsonSchemaMeta(title = 'Параметры для инициализации настроек портала')\n"
            + "class Settings\n"
            + "{\n"
            + "    @JsonSchemaMeta(title = 'Схема данных', description = 'Выбранное значение влияет на основную "
            + "логику взаимодействия с SMP')\n"
            + "    Schema schema = Schema.Unique\n"
            + "\n"
            + "\n"
            + "    @UiSchemaMeta(widget = 'textarea')\n"
            + "    @JsonSchemaMeta(title = 'Поля для ввода текста')\n"
            + "    String textarea\n"
            + "\n"
            + "\n"
            + "    @UiSchemaMeta(widget = 'status-select', paramsValues = ['serviceCall'])\n"
            + "    @JsonSchemaMeta(title = 'Статус запроса')\n"
            + "    String scState\n"
            + "}\n"
            + "return\n";

    private static final String MECHANISM_NAME_WITH_DIFFERENT_CLASS_NAME_APP_JSON_SCHEMA =
            "{\"moduleCode\":\"\",\"$schema\":\"http"
            + "://json-schema.org/draft-07/schema#\",\"$title\":\"Параметры для инициализации настроек портала\","
            + "\"type\":\"object\",\"properties\":{\"schema\":{\"nullable\":true,\"description\":\"Выбранное значение"
            + " влияет на основную логику взаимодействия с SMP\",\"titles\":[\"SD Pro\",\"Гибкая настройка\"],"
            + "\"title\":\"Схема данных\",\"type\":\"string\",\"enum\":[\"Corp\",\"Unique\"]},"
            + "\"scState\":{\"paramsValues\":[\"serviceCall\"],\"widget\":\"status-select\",\"nullable\":true,"
            + "\"title\":\"Статус запроса\",\"type\":\"string\"},\"textarea\":{\"widget\":\"textarea\","
            + "\"nullable\":true,\"title\":\"Поля для ввода текста\",\"type\":\"string\"}},\"$id\":\"Settings_schema\"}";

    private static final String MECHANISM_NAME_WITH_DIFFERENT_CLASS_NAME_APP_INITIAL_JSON_SCHEMA =
            "{\"moduleCode\":\"\",\"$schema\":\"http://json"
            + "-schema.org/draft-07/schema#\",\"$title\":\"Параметры для инициализации настроек портала\","
            + "\"type\":\"object\",\"properties\":{\"schema\":{\"nullable\":true,\"description\":\"Выбранное значение"
            + " влияет на основную логику взаимодействия с SMP\",\"titles\":[\"SD Pro\",\"Гибкая настройка\"],"
            + "\"title\":\"Схема данных\",\"type\":\"string\",\"enum\":[\"Corp\",\"Unique\"]}},"
            + "\"$id\":\"InitSettings_schema\"}";

    private static final String ROOT_OBJECT_ANNOTATION_MECHANISM = "import ru.naumen.jsonschema.annotations.*\n"
            + "\n"
            + "@MechanismSettings(name = 'RootObjectAnnotation')\n"
            + "@JsonSchemaMeta(title = 'RootTest', requiredFields = ['attribute2'], additional=false)\n"
            + "@UiHierarchy(id = \"rootId\", parent = \"rootParentId\")\n"
            + "@CustomProperty(name = \"customProperty\", value = \"customProperty RootTest\")\n"
            + "class RootTest\n"
            + "{\n"
            + "  Collection<SecondClass> coll\n"
            + "  @CustomProperty(name = \"customProperty\", value = \"customProperty attribute2\")\n"
            + "  String attribute2\n"
            + "  @CustomProperty(name = \"customProperty\", value = \"customProperty attribute3\")\n"
            + "  SecondClass attribute3\n"
            + "}\n"
            + "\n"
            + "@UiHierarchy(id = \"idField\", parent = \"parentFieldId\")\n"
            + "@JsonSchemaMeta(title = 'SecondClass', requiredFields = ['attribute'], additional=false)\n"
            + "@CustomProperty(name = \"customProperty\", value = \"customProperty SecondClass\")\n"
            + "class SecondClass\n"
            + "{\n"
            + "  String attribute\n"
            + "}";

    private static final String ROOT_OBJECT_ANNOTATION_JSON_SCHEMA = "{\"moduleCode\":\"\",\"$schema\":\"http://json-schema"
            + ".org/draft-07/schema#\",\"$title\":\"RootTest\",\"nesting\":{\"rootSettings\":\"includeNested=false\","
            + "\"childSettings\":\"includeNested=true\",\"parentField\":\"rootParentId\","
            + "\"idField\":\"rootId\"},\"additionalProperties\":false,\"type\":\"object\","
            + "\"properties\":{\"coll\":{\"nullable\":true,\"title\":\"coll\",\"type\":\"array\","
            + "\"items\":{\"nesting\":{\"rootSettings\":\"includeNested=false\","
            + "\"childSettings\":\"includeNested=true\",\"parentField\":\"parentFieldId\",\"idField\":\"idField\"},"
            + "\"additionalProperties\":false,\"type\":\"object\",\"title\":\"SecondClass\","
            + "\"properties\":{\"attribute\":{\"nullable\":true,\"title\":\"attribute\",\"type\":\"string\"}},"
            + "\"required\":[\"attribute\"],\"customProperty\":\"customProperty SecondClass\"}},"
            + "\"attribute2\":{\"nullable\":true,\"title\":\"attribute2\",\"type\":\"string\","
            + "\"customProperty\":\"customProperty attribute2\"},\"attribute3\":{\"nullable\":true,"
            + "\"additionalProperties\":false,\"nesting\":{\"rootSettings\":\"includeNested=false\","
            + "\"childSettings\":\"includeNested=true\",\"parentField\":\"parentFieldId\","
            + "\"idField\":\"idField\"},\"title\":\"SecondClass\",\"type\":\"object\",\"required\":[\"attribute\"],"
            + "\"customProperty\":\"customProperty attribute3\",\"properties\":{\"attribute\":{\"nullable\":true,"
            + "\"title\":\"attribute\",\"type\":\"string\"}}}},\"required\":[\"attribute2\"],"
            + "\"customProperty\":\"customProperty RootTest\",\"$id\":\"RootTest_schema\"}";

    private static final JsonParser parser = new JsonParser();

    private static final List<ScriptModule> SCRIPT_MODULES = List.of(
            BaseAllScriptModulesCompilationServiceTest.createScriptModule("PortalSettings", PORTAL_SETTINGS, "app1"),
            BaseAllScriptModulesCompilationServiceTest.createScriptModule("SimpleMechanism", SIMPLE_MECHANISM, "app1"),
            BaseAllScriptModulesCompilationServiceTest.createScriptModule("RootObjectAnnotation",ROOT_OBJECT_ANNOTATION_MECHANISM,"app1")
    );

    private static JsonSchemaServiceImpl jsonSchemaService;
    private static IJsonSchemaApi jsonSchemaApi;

    @BeforeClass
    public static void setup()
    {
        MetaDataScriptModulesService modulesServiceMock = mock(MetaDataScriptModulesService.class);
        ScriptModulesService scriptModulesServiceMock = mock(ScriptModulesService.class);
        JsonSchemaGenerator realJsonSchemaGenerator =
                new JsonSchemaGenerator(modulesServiceMock, scriptModulesServiceMock);
        JsonSchemaGenerator jsonSchemaGenerator = spy(realJsonSchemaGenerator);
        doReturn("").when(jsonSchemaGenerator).getCurrentModuleCode(any());
        jsonSchemaService = new JsonSchemaServiceImpl(false, jsonSchemaGenerator);
        jsonSchemaApi = new JsonSchemaApi(jsonSchemaService);
        doAnswer(invocation ->
        {
            var modulesRecreatedEvent = (ModulesClassLoaderChangingEvent)invocation.getArguments()[0];
            jsonSchemaService.onModulesClassLoaderRecreated(modulesRecreatedEvent);
            return "OK";
        }).when(eventPublisher).publishEvent(any(ModulesClassLoaderChangingEvent.class));
    }

    @Before
    public void before()
    {
        BaseAllScriptModulesCompilationServiceTest.dropAllModules();
        BaseAllScriptModulesCompilationServiceTest.recompileModules(SCRIPT_MODULES);
    }

    /**
     * Зарегистрировано верное количество JSON схем и что их содержимое совпадает с ожидаемым
     */
    @Test
    public void testJsonSchemaGenerator()
    {
        assertEquals(3, jsonSchemaService.getMechanismCodes().size());
        assertEquals(parser.parse(PORTAL_SETTINGS_JSON_SCHEMA),
                parser.parse(jsonSchemaApi.getJsonSchema("app1_PortalSettings")));
        assertEquals(parser.parse(SIMPLE_MECHANISM_JSON_SCHEMA),
                parser.parse(jsonSchemaApi.getJsonSchema("app1_SimpleMechanism")));
        assertEquals(parser.parse(ROOT_OBJECT_ANNOTATION_JSON_SCHEMA),
                parser.parse(jsonSchemaApi.getJsonSchema("app1_RootObjectAnnotation")));
    }

    /**
     * Удаление аннотации фиксируется и json схема удаляется
     */
    @Test
    public void testJsonSchemaUpdate()
    {
        String classWithoutMechanismAnnotation = SIMPLE_MECHANISM.replace(
                "@MechanismSettings(name = \"SimpleMechanism\")", "");
        BaseAllScriptModulesCompilationServiceTest.recompileModules(List.of(
                BaseAllScriptModulesCompilationServiceTest.createScriptModule(
                        "SimpleMechanism", classWithoutMechanismAnnotation, "app1")));
        assertEquals(Set.of("app1_PortalSettings", "app1_RootObjectAnnotation"),
                jsonSchemaService.getMechanismCodes());
    }

    /**
     * Проверка, что Json схема, доступна по имени механизма, указанному поле аннотации
     * {@link MechanismSettings#name()}, а не по имени класса над которым навешана аннотация.
     */
    @Test
    public void testJsonSchemaWithDifferentClassName()
    {
        BaseAllScriptModulesCompilationServiceTest.dropAllModules();
        BaseAllScriptModulesCompilationServiceTest.createModules(Map.of("stng", MECHANISM_NAME_WITH_DIFFERENT_CLASS_NAME));
        assertEquals(2, jsonSchemaService.getMechanismCodes().size());
        assertEquals(Set.of("app-initial", "app"), jsonSchemaService.getMechanismCodes());
        assertEquals(parser.parse(MECHANISM_NAME_WITH_DIFFERENT_CLASS_NAME_APP_JSON_SCHEMA),
                parser.parse(jsonSchemaApi.getJsonSchema("app")));
        assertEquals(parser.parse(MECHANISM_NAME_WITH_DIFFERENT_CLASS_NAME_APP_INITIAL_JSON_SCHEMA),
                parser.parse(jsonSchemaApi.getJsonSchema("app-initial")));
    }
}
