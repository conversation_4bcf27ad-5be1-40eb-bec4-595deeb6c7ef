package ru.naumen.objectlist.server;

import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import org.apache.commons.lang3.RandomStringUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import ru.naumen.core.server.actioncontext.ActionContextHolder;
import ru.naumen.core.server.bo.query.distinct.DistinctConfiguration;
import ru.naumen.core.server.bo.query.distinct.ThreadDistinctMode;
import ru.naumen.core.server.jta.ds.DataSourceType;
import ru.naumen.core.server.jta.ds.context.manager.DataSourceBoundInvocationManager;
import ru.naumen.core.server.jta.ds.context.manager.DataSourceBoundInvocationManager.InvocationRequestMode;
import ru.naumen.core.server.jta.readonly.ReadOnlyContentsService;

@RunWith(MockitoJUnitRunner.class)
public class ObjectListQueryListenerJdkTest
{
    @Mock
    private ReadOnlyContentsService readOnlyContentsService;

    @Mock
    private DataSourceBoundInvocationManager dataSourceBoundInvocationManager;

    @Mock
    private DistinctConfiguration distinctConfiguration;

    @Mock
    private ActionContextHolder actionContextHolder;

    @Test
    public void testDisableDistinct()
    {
        final String contentUUID = RandomStringUtils.randomAlphabetic(10);
        when(distinctConfiguration.isDistinctDisabledForContent(eq(contentUUID))).thenReturn(true);
        final ObjectListQueryListener objectListQueryListener = new ObjectListQueryListener(readOnlyContentsService,
                dataSourceBoundInvocationManager, distinctConfiguration, actionContextHolder);
        objectListQueryListener.beforeObjectListQueried(contentUUID);
        Assert.assertEquals("Unexpected thread distinct mode", ThreadDistinctMode.OFF,
                ThreadDistinctMode.CURRENT.get());

        objectListQueryListener.afterObjectListQueried();
        Assert.assertEquals("Unexpected thread distinct mode", ThreadDistinctMode.ON,
                ThreadDistinctMode.CURRENT.get());
    }

    @Test
    public void testSwitchToReadOnly()
    {
        final String contentUUID = RandomStringUtils.randomAlphabetic(10);
        when(readOnlyContentsService.isReadOnlyContent(eq(contentUUID))).thenReturn(true);
        final ObjectListQueryListener objectListQueryListener = new ObjectListQueryListener(readOnlyContentsService,
                dataSourceBoundInvocationManager, distinctConfiguration, actionContextHolder);
        objectListQueryListener.beforeObjectListQueried(contentUUID);
        verify(dataSourceBoundInvocationManager).changeCurrentInvocation(eq(DataSourceType.READ_ONLY), eq(
                InvocationRequestMode.FORCE), eq(null));
    }
}