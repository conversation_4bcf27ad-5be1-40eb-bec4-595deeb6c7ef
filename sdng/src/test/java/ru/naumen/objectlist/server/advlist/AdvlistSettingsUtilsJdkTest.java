package ru.naumen.objectlist.server.advlist;

import java.util.HashSet;

import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import com.google.common.collect.Sets;

import ru.naumen.core.server.mapper.MappingService;
import ru.naumen.core.server.objectloader.IPrefixObjectLoaderService;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.TestUtils;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.objectlist.shared.advlist.AdvlistSettings;

/**
 * <AUTHOR>
 * @since 28.11.2011
 */
public class AdvlistSettingsUtilsJdkTest
{
    @Mock
    IPrefixObjectLoaderService objectLoaderService;
    @Mock
    MappingService mappingService;
    @Mock
    MessageFacade messages;

    AdvlistSettingsUtils utils;

    @Test
    public void getDtObjects()
    {
        //настройка
        String uuid1 = TestUtils.randomString();
        String uuid2 = TestUtils.randomString();
        DtObject obj1 = Mockito.mock(DtObject.class);
        DtObject obj2 = Mockito.mock(DtObject.class);
        IUUIDIdentifiable object1 = Mockito.mock(IUUIDIdentifiable.class);
        IUUIDIdentifiable object2 = Mockito.mock(IUUIDIdentifiable.class);
        Mockito.when(objectLoaderService.get(uuid1)).thenReturn(object1);
        Mockito.when(objectLoaderService.get(uuid2)).thenReturn(object2);
        //вызов
        HashSet<DtObject> result = utils.getDtObjects(Sets.newHashSet(uuid1, uuid2, AdvlistSettings.OWNER_EVERYBODY));
        //проверка
    }

    @Before
    public void setUp()
    {
        MockitoAnnotations.initMocks(this);

        utils = new AdvlistSettingsUtils();
        utils.objectLoaderService = objectLoaderService;
        utils.mappingService = mappingService;
        utils.messages = messages;
    }
}
