package ru.naumen.objectlist.server;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

import org.junit.Test;

import ru.naumen.core.shared.ui.element.PermittedCasesContainer;
import ru.naumen.metainfo.shared.ClassFqn;

/**
 * Тестирование утилитарных методов для работы со списками.
 * <AUTHOR>
 * @since Nov 26, 2020
 */
public class ObjectListUtilsJdkTest
{
    @Test
    public void hasCompatibleClasses()
    {
        ClassFqn fqnOfClass1 = ClassFqn.parse("employee");
        ClassFqn caseFqn11 = ClassFqn.parse("employee$internal");
        ClassFqn fqnOfClass2 = ClassFqn.parse("serviceCall");
        ClassFqn caseFqn21 = ClassFqn.parse("serviceCall$request");
        ClassFqn caseFqn22 = ClassFqn.parse("serviceCall$incident");

        PermittedCasesContainer templateClass = new PermittedCasesContainer(fqnOfClass2);
        PermittedCasesContainer templateCases = new PermittedCasesContainer(caseFqn21);

        assertTrue(ObjectListUtils.hasCompatibleClasses(new PermittedCasesContainer(fqnOfClass2), templateClass));
        assertFalse(ObjectListUtils.hasCompatibleClasses(new PermittedCasesContainer(fqnOfClass2), templateCases));

        assertFalse(ObjectListUtils.hasCompatibleClasses(new PermittedCasesContainer(fqnOfClass1), templateClass));
        assertFalse(ObjectListUtils.hasCompatibleClasses(new PermittedCasesContainer(fqnOfClass1), templateCases));

        assertFalse(ObjectListUtils.hasCompatibleClasses(new PermittedCasesContainer(caseFqn11), templateClass));
        assertFalse(ObjectListUtils.hasCompatibleClasses(new PermittedCasesContainer(caseFqn11), templateCases));

        assertTrue(ObjectListUtils.hasCompatibleClasses(new PermittedCasesContainer(caseFqn21), templateClass));
        assertTrue(ObjectListUtils.hasCompatibleClasses(new PermittedCasesContainer(caseFqn21), templateCases));

        assertTrue(ObjectListUtils.hasCompatibleClasses(new PermittedCasesContainer(caseFqn22), templateClass));
        assertFalse(ObjectListUtils.hasCompatibleClasses(new PermittedCasesContainer(caseFqn22), templateCases));
    }
}
