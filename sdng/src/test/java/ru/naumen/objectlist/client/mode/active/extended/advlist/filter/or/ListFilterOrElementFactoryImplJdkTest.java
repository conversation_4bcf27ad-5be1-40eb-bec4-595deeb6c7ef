package ru.naumen.objectlist.client.mode.active.extended.advlist.filter.or;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import org.apache.commons.lang.ArrayUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;

import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.Constants.AggregateAttributeType;
import ru.naumen.metainfo.shared.Constants.BOLinksAttributeType;
import ru.naumen.metainfo.shared.Constants.CatalogItemAttributeType;
import ru.naumen.metainfo.shared.Constants.ObjectAttributeType;
import ru.naumen.metainfo.shared.Constants.RichTextAttributeType;
import ru.naumen.metainfo.shared.Constants.TextAttributeType;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.objectlist.shared.advlist.filtration.LinkAttributePredicate;
import ru.naumen.objectlist.shared.advlist.filtration.OUOrEmployeeLinkAttributePredicate;

/**
 *
 * <AUTHOR>
 * @since 22 марта 2016 г.
 */
public class ListFilterOrElementFactoryImplJdkTest
{
    private static final String TEXT_ATTR_FQN = "ou@textAttr";
    private static final String CATALOG_ATTR_FQN = "ou@catalogAttr";
    private static final String LINKS_ATTR_FQN = "ou@linksAttr";
    private static final String OU_LINK_ATTR_FQN = "ou@ouLinkAttr";
    private static final String AGGREGATE_OU_LINK_FQN = "ou@aggregateOuLinkAttr";
    private static final String RICH_TEXT_FQN = "ou@richTextAttr";

    private static final Attribute TEXT_ATTR = Mockito.mock(Attribute.class);
    private static final Attribute CATALOG_ATTR = Mockito.mock(Attribute.class);
    private static final Attribute LINKS_ATTR = Mockito.mock(Attribute.class);
    private static final Attribute OU_LINK_ATTR = Mockito.mock(Attribute.class);
    private static final Attribute AGGREGATE_OU_LINK = Mockito.mock(Attribute.class);
    private static final Attribute RICH_TEXT = Mockito.mock(Attribute.class);

    private ListFilterOrElementConstants constants;
    private ListFilterOrElementConditionsFactoryImpl factory;
    private boolean isLicensed;

    @Test
    public void getCondidtionsFormRichTextAttrType()
    {
        String[] conditions = factory.conditions(RICH_TEXT, RichTextAttributeType.CODE, false, false, false);
        Assert.assertArrayEquals(constants.setConditionWithNullWithIncludeEmpty(), conditions);
    }

    @Before
    public void setUp()
    {
        isLicensed = true;

        factory = new ListFilterOrElementConditionsFactoryImpl()
        {
            @Override
            protected boolean isCurrentUserLicensed()
            {
                return isLicensed;
            }
        };

        when(TEXT_ATTR.getFqn()).thenReturn(AttributeFqn.parse(TEXT_ATTR_FQN));
        when(CATALOG_ATTR.getFqn()).thenReturn(AttributeFqn.parse(CATALOG_ATTR_FQN));
        when(LINKS_ATTR.getFqn()).thenReturn(AttributeFqn.parse(LINKS_ATTR_FQN));
        when(OU_LINK_ATTR.getFqn()).thenReturn(AttributeFqn.parse(OU_LINK_ATTR_FQN));
        when(AGGREGATE_OU_LINK.getFqn()).thenReturn(AttributeFqn.parse(AGGREGATE_OU_LINK_FQN));
        when(RICH_TEXT.getFqn()).thenReturn(AttributeFqn.parse(RICH_TEXT_FQN));

        LinkAttributePredicate linkAttributePredicate = mock(LinkAttributePredicate.class);
        when(linkAttributePredicate.apply(TEXT_ATTR)).thenReturn(false);
        when(linkAttributePredicate.contains(TextAttributeType.CODE)).thenReturn(false);
        when(linkAttributePredicate.apply(CATALOG_ATTR)).thenReturn(false);
        when(linkAttributePredicate.contains(CatalogItemAttributeType.CODE)).thenReturn(false);
        when(linkAttributePredicate.apply(LINKS_ATTR)).thenReturn(true);
        when(linkAttributePredicate.contains(BOLinksAttributeType.CODE)).thenReturn(true);
        when(linkAttributePredicate.apply(OU_LINK_ATTR)).thenReturn(true);
        when(linkAttributePredicate.contains(ObjectAttributeType.CODE)).thenReturn(true);
        when(linkAttributePredicate.apply(AGGREGATE_OU_LINK)).thenReturn(true);
        when(linkAttributePredicate.contains(AggregateAttributeType.CODE)).thenReturn(true);
        factory.attributeLinkPredicate = linkAttributePredicate;

        when(linkAttributePredicate.apply(TEXT_ATTR)).thenReturn(false);
        when(linkAttributePredicate.contains(TextAttributeType.CODE)).thenReturn(false);
        when(linkAttributePredicate.apply(CATALOG_ATTR)).thenReturn(false);
        when(linkAttributePredicate.contains(CatalogItemAttributeType.CODE)).thenReturn(false);
        when(linkAttributePredicate.apply(LINKS_ATTR)).thenReturn(true);
        when(linkAttributePredicate.contains(BOLinksAttributeType.CODE)).thenReturn(true);
        when(linkAttributePredicate.apply(OU_LINK_ATTR)).thenReturn(true);
        when(linkAttributePredicate.contains(ObjectAttributeType.CODE)).thenReturn(true);
        when(linkAttributePredicate.apply(AGGREGATE_OU_LINK)).thenReturn(true);
        when(linkAttributePredicate.contains(AggregateAttributeType.CODE)).thenReturn(true);

        constants = new ListFilterOrElementConstants();
        factory.constants = constants;

        OUOrEmployeeLinkAttributePredicate ouOrEmployeeLinkAttributePredicate = mock(
                OUOrEmployeeLinkAttributePredicate.class);
        when(ouOrEmployeeLinkAttributePredicate.apply(CATALOG_ATTR)).thenReturn(false);
        when(ouOrEmployeeLinkAttributePredicate.apply(LINKS_ATTR)).thenReturn(false);
        when(ouOrEmployeeLinkAttributePredicate.apply(OU_LINK_ATTR)).thenReturn(true);
        when(ouOrEmployeeLinkAttributePredicate.apply(AGGREGATE_OU_LINK)).thenReturn(true);
        factory.attributeLinkToOUOrEmployeePredicate = ouOrEmployeeLinkAttributePredicate;
    }

    @Test
    public void testGetConditionsForAggrenateAndUnlicensed()
    {
        isLicensed = false;
        String[] conditions = factory.conditions(AGGREGATE_OU_LINK, AggregateAttributeType.CODE, false, false,
                false);
        String[] expected = constants.setConditionWithNullForNotLicensed();
        Assert.assertArrayEquals(expected, conditions);
    }

    @Test
    public void testGetConditionsForBOLinks()
    {
        String[] conditions = factory.conditions(LINKS_ATTR, BOLinksAttributeType.CODE, false, false, false);
        String[] expected = (String[])ArrayUtils.addAll(constants.setConditionWithNullWithContains(),
                constants.removedConditions());
        Assert.assertArrayEquals(expected, conditions);
    }

    @Test
    public void testGetConditionsForCatalogAttrtype()
    {
        String[] conditions = factory.conditions(CATALOG_ATTR, CatalogItemAttributeType.CODE, false, false, false);
        Assert.assertArrayEquals(constants.setConditionWithNullWithContainsAndInSet(), conditions);
    }

    @Test
    public void testGetConditionsForOuLinkForUnlicensed()
    {
        isLicensed = false;
        String[] conditions = factory.conditions(OU_LINK_ATTR, ObjectAttributeType.CODE, false, false, false);
        String[] expected = constants.setConditionWithNullForNotLicensed();
        Assert.assertArrayEquals(expected, conditions);
    }

    @Test
    public void testGetConditionsForTextAttrType()
    {
        String[] conditions = factory.conditions(TEXT_ATTR, TextAttributeType.CODE, false, false, false);
        Assert.assertArrayEquals(constants.setConditionWithNullWithIncludeEmpty(), conditions);
    }
}
