package ru.naumen.metainfo;


import static org.junit.Assert.assertEquals;

import org.junit.Test;

import ru.naumen.metainfo.shared.AttributeFqn;

/**
 * <AUTHOR>
 *
 */
public class AttributeFqnTest
{
    @Test
    public void testParse()
    {
        String fqn = "class@code";

        AttributeFqn parsedValue = AttributeFqn.parse(fqn);

        assertEquals("code", parsedValue.getCode());
        assertEquals("class", parsedValue.getClassFqn().toString());
    }
}
