package ru.naumen.metainfo.shared.relations;

import org.junit.Assert;
import org.junit.Test;

import ru.naumen.core.shared.MockTestUtils;
import ru.naumen.core.shared.TestUtils;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.Relation;

/**
 * Юнит тесты на RelationKey 
 * <AUTHOR>
 * @since 22.12.2010
 */
public class RelationBaseKeyJdkTest
{

    @Test
    public void test_equals()
    {
        // настройка системы
        ClassFqn leftFqn = TestUtils.createClassFqn();
        ClassFqn rightFqn = TestUtils.createClassFqn();
        Relation rb = MockTestUtils.relation(leftFqn, rightFqn);
        RelationBaseKey rbk1 = new RelationBaseKey(rb);
        RelationBaseKey rbk2 = new RelationBaseKey(rb);

        // вызов системы
        // проверка утверждений
        Assert.assertEquals("Должны быть равны", rbk1, rbk2);
        Assert.assertFalse("Не равно null", rbk1.equals(null));
        Assert.assertFalse("Не равно объекту другого класса", rbk1.equals(""));
        Assert.assertEquals("Хэш коды также должны быть равны", rbk1.hashCode(), rbk2.hashCode());
        // очистка        
    }

    /**
     * Проверяем, что отрабатывает конструктор
     */
    @Test
    public void test_RelationKey()
    {
        // настройка системы        
        // вызов системы
        new RelationBaseKey();
        // проверка утверждений
        // очистка        
    }
}
