package ru.naumen.metainfo.shared;

import ru.naumen.core.shared.CommonL10nUtils;
import ru.naumen.core.shared.utils.ILocaleInfo;

/**
 * 
 * <AUTHOR>
 * @since 14.10.2010
 */
public class MetainfoUtilsStub extends MetainfoUtils
{
    public MetainfoUtilsStub()
    {
        setLocaleInfo(new ILocaleInfo()
        {
            @Override
            public String getCurrentLang()
            {
                return ILocaleInfo.RUSSIAN;
            }

            @Override
            public String getDefaultInterfaceLang()
            {
                return ILocaleInfo.RUSSIAN;
            }
        });
    }

    public ILocaleInfo getLocaleInfo()
    {
        return this.localeInfo;
    }

    public MetainfoUtilsStub setLocaleInfo(ILocaleInfo localeInfo)
    {
        this.localeInfo = localeInfo;
        this.l10nUtils = new CommonL10nUtils(localeInfo);
        return this;
    }
}
