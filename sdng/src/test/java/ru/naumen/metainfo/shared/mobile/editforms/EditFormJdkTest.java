package ru.naumen.metainfo.shared.mobile.editforms;

import static java.util.Arrays.asList;

import org.junit.Assert;
import org.junit.Test;

import ru.naumen.core.shared.Constants.AbstractBO;
import ru.naumen.core.shared.Constants.ServiceCall;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.mobile.MobileAttribute;

/**
 * Тесты на настройку формы редактирования объекта в мобильном клиенте {@link EditForm}
 *
 * <AUTHOR>
 * @since 08 11 2016
 */
public class EditFormJdkTest
{
    private static MobileAttribute createAttribute(String code)
    {
        MobileAttribute mobileAttribute = new MobileAttribute();
        AttributeFqn attributeFqn = AttributeFqn.create(ServiceCall.FQN, code);
        mobileAttribute.setAttributeFqn(attributeFqn);
        return mobileAttribute;
    }

    @Test
    public void testCantAddDuplecatedByCodeAttr()
    {
        EditForm editForm = new EditForm();
        editForm.addAttribute(createAttribute(AbstractBO.TITLE));
        editForm.addAttribute(createAttribute(AbstractBO.TITLE));

        Assert.assertEquals(1, editForm.getAttributes().size());
    }

    @Test
    public void testCantSetDuplecatedByCodeAttrs()
    {
        EditForm editForm = new EditForm();
        editForm.setAttributes(asList(createAttribute(AbstractBO.TITLE), createAttribute(AbstractBO.TITLE)));

        Assert.assertEquals(1, editForm.getAttributes().size());
    }
}
