package ru.naumen.metainfo.shared.elements;

import jakarta.annotation.Nullable;
import ru.naumen.metainfo.shared.elements.adapters.HasCodeEqualsAdapter;

/**
 * <AUTHOR>
 */
public class CatalogDecorator implements Catalog
{
    private Catalog catalog;

    /**
     * 
     */
    public CatalogDecorator(Catalog wrapped)
    {
        this.catalog = wrapped;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean equals(Object obj)
    {
        return HasCodeEqualsAdapter.equals(this, obj);
    }

    @Override
    public String getAdminPermissionCategory()
    {
        return catalog.getAdminPermissionCategory();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String getCode()
    {
        return catalog.getCode();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String getDescription()
    {
        return catalog.getDescription();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MetaClassLite getItemMetaClass()
    {
        return catalog.getItemMetaClass();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String getTitle()
    {
        return catalog.getTitle();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public int hashCode()
    {
        return HasCodeEqualsAdapter.hashCode(this);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean isFlat()
    {
        return catalog.isFlat();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean isHardcoded()
    {
        return catalog.isHardcoded();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean isWithFolders()
    {
        return catalog.isWithFolders();
    }

    @Override
    public String getTitle(String lang)
    {
        return catalog.getTitle(lang);
    }

    @Override
    public @Nullable String getSettingsSet()
    {
        return catalog.getSettingsSet();
    }

    @Override
    public void setSettingsSet(@Nullable String settingsSet)
    {
        catalog.setSettingsSet(settingsSet);
    }

    @Override
    public String getElementType()
    {
        return catalog.getElementType();
    }

    @Override
    public String getElementCode()
    {
        return catalog.getElementCode();
    }
}
