package ru.naumen.metainfo.shared;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.nullable;
import static org.mockito.Mockito.anyCollection;
import static org.mockito.Mockito.atLeast;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static ru.naumen.metainfo.shared.Constants.Role.SCRIPT_ACCESS_DTO_KEY;
import static ru.naumen.metainfo.shared.Constants.Role.SCRIPT_FAST_LINK_RIGHTS_DTO_KEY;
import static ru.naumen.metainfo.shared.Constants.Role.SCRIPT_LIST_FILTER_DTO_KEY;
import static ru.naumen.metainfo.shared.Constants.Role.SCRIPT_OWNERS_DTO_KEY;

import java.io.IOException;
import java.io.InputStream;
import java.util.Collection;
import java.util.Map;
import java.util.Optional;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.springframework.core.io.ResourceLoader;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.common.io.ByteStreams;

import jakarta.inject.Inject;
import net.customware.gwt.dispatch.server.Dispatch;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.advimport.AdvImportTestHelper;
import ru.naumen.advimport.server.Constants;
import ru.naumen.advimport.shared.config.mcresolver.ScriptMetaClassResolver;
import ru.naumen.advimport.shared.dispatch.AddConfigurationAction;
import ru.naumen.advimport.shared.dispatch.GetConfigurationResponse;
import ru.naumen.common.CreatedListener;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.dispatch.ObjectTestUtils;
import ru.naumen.core.server.dispatch.SecurityTestHelper;
import ru.naumen.core.server.eventaction.EventActionServiceBean;
import ru.naumen.core.server.hibernate.DataBaseInfo;
import ru.naumen.core.server.metastorage.impl.metainfo.MetaStorageService;
import ru.naumen.core.server.script.ScriptModulesStorageService;
import ru.naumen.core.server.script.conf.SystemScriptModule;
import ru.naumen.core.server.script.crawler.ScriptsCrawler;
import ru.naumen.core.server.script.crawler.ScriptsVisitor;
import ru.naumen.core.server.script.storage.ScriptStorageService;
import ru.naumen.core.server.settings.SettingsStorage;
import ru.naumen.core.shared.TestUtils;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.file.XmlFile;
import ru.naumen.core.shared.settings.SCParameters;
import ru.naumen.core.shared.settings.Settings;
import ru.naumen.core.shared.timer.DeleteTimerDefinitionAction;
import ru.naumen.core.shared.timer.definition.ScriptTimerCondition;
import ru.naumen.core.shared.timer.definition.TimerCondition;
import ru.naumen.core.shared.timer.definition.TimerDefinition;
import ru.naumen.core.shared.utils.UUIDGenerator;
import ru.naumen.mailreader.shared.dispatch.DeleteMailProcessorRuleAction;
import ru.naumen.mailreader.shared.dispatch.SaveMailProcessorRuleAction;
import ru.naumen.mailreader.shared.receiver.MailProcessorRule;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.server.spi.MetainfoServiceBean;
import ru.naumen.metainfo.server.spi.SecurityServiceBean;
import ru.naumen.metainfo.server.spi.elements.sec.AccessMatrixImpl;
import ru.naumen.metainfo.server.spi.elements.sec.SecDomainImpl;
import ru.naumen.metainfo.shared.Constants.Presentations;
import ru.naumen.metainfo.shared.Constants.StringAttributeType;
import ru.naumen.metainfo.shared.dispatch2.AddAttributeAction;
import ru.naumen.metainfo.shared.dispatch2.AddSecurityRoleAction;
import ru.naumen.metainfo.shared.dispatch2.DelAttributeAction;
import ru.naumen.metainfo.shared.dispatch2.DelSecurityRoleAction;
import ru.naumen.metainfo.shared.dispatch2.SaveTimerDefinitionAction;
import ru.naumen.metainfo.shared.dispatch2.scheduler.DeleteSchTaskAction;
import ru.naumen.metainfo.shared.dispatch2.scheduler.SaveSchedulerTaskAction;
import ru.naumen.metainfo.shared.dispatch2.scheduler.SchedulerTaskResponse;
import ru.naumen.metainfo.shared.dispatch2.script.SimpleScriptedResult;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfo.shared.elements.sec.AccessMatrix.Key;
import ru.naumen.metainfo.shared.elements.sec.Role.Type;
import ru.naumen.metainfo.shared.elements.wf.Action;
import ru.naumen.metainfo.shared.elements.wf.Condition;
import ru.naumen.metainfo.shared.elements.wf.State;
import ru.naumen.metainfo.shared.elements.wf.Workflow;
import ru.naumen.metainfo.shared.eventaction.ActionCondition;
import ru.naumen.metainfo.shared.eventaction.ActionConditionType;
import ru.naumen.metainfo.shared.eventaction.Event;
import ru.naumen.metainfo.shared.eventaction.EventAction;
import ru.naumen.metainfo.shared.eventaction.EventType;
import ru.naumen.metainfo.shared.eventaction.NotificationEventAction;
import ru.naumen.metainfo.shared.eventaction.ScriptActionCondition;
import ru.naumen.metainfo.shared.eventaction.ScriptEventAction;
import ru.naumen.metainfo.shared.scheduler.ExecuteScriptTask;
import ru.naumen.metainfo.shared.scheduler.ExecuteScriptTaskDtoFactory;
import ru.naumen.metainfo.shared.script.Script;
import ru.naumen.metainfo.shared.script.ScriptDto;
import ru.naumen.metainfo.shared.script.ScriptDtoFactory;
import ru.naumen.reports.shared.ReportTemplate;
import ru.naumen.reports.shared.dispatch.DeleteReportTemplateAction;
import ru.naumen.reports.shared.dispatch.SaveReportTemplateAction;
import ru.naumen.uniquerandom.UniqueNumbersGenerator;

/**
 * Проверка корректности извлечения скриптов с помощью {@link ScriptsCrawler}
 */
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class ScriptsCrawlerDbTest
{
    private static final String SCRIPT_BODY_ASSERTION_ERROR_MESSAGE = "Тело скрипта не соответствует ожидаемому.";
    @Inject
    private ResourceLoader resourceLoader;
    @Inject
    private CreatedListener createdListener;
    @Inject
    private Dispatch dispatch;
    @Inject
    private MetaStorageService metaStorageService;
    @Inject
    private ScriptsCrawler crawler;
    @Inject
    private MetainfoService metainfoService;
    @Inject
    private AdvImportTestHelper helper;
    @Inject
    private ObjectTestUtils utils;
    @Inject
    private PlatformTransactionManager txManager;
    @Inject
    private DataBaseInfo dataBaseInfo;

    private final ClassFqn ouFqn = ClassFqn.parse("ou$ouImpScrCrwlr");

    @Before
    public void setUp() throws DispatchException
    {
        SecurityTestHelper.autenticateAsSuperUser();
        TransactionTemplate tt = new TransactionTemplate(txManager);
        tt.execute(new TransactionCallback<Void>()
        {
            @Override
            public Void doInTransaction(TransactionStatus status)
            {
                try
                {
                    helper.createCase(ouFqn);
                    return null;
                }
                catch (Exception e)
                {
                    throw new FxException(e);
                }
            }
        });
    }

    /**
     * Проверка извлечения after-import скрипта из advimport'а.
     * @throws Exception
     */
    @Test
    public void testShouldVisitAdvImportAfterImportCustomizer() throws Exception
    {
        //@formatter:off
        final String config = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>"
                + "<config xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" save-log=\"false\" threads-number=\"1\" >"
                + "<mode>CREATE</mode>"
                + "<gui-parameter name=\"idHolder\" type=\"STRING\" title=\"File for import\" />"
                + "<class name=\"ou\" threads-number=\"4\">"
                + "<csv-data-source with-header=\"true\" file-name=\"file\">" + "</csv-data-source>"
                + "<constant-metaclass-resolver metaclass=\"ou\"/>"
                + "<object-searcher attr=\"${idHolder}\" metaclass=\"ou\"/>"
                + "<attr name=\"idHolder\" column=\"id\" />"
                + "<script-customizer><after-import>return true;</after-import></script-customizer>"
                + "</class></config>";
        //@formatter:on
        ScriptsVisitor visitor = mock(ScriptsVisitor.class);
        String uuid = visitAdvImportScript(config, visitor);
        // проверка утверждений
        ArgumentCaptor<Script> scriptCaptor = ArgumentCaptor.forClass(Script.class);
        verify(visitor).visitAdvImportAfterImportScriptCustomizerScript(scriptCaptor.capture(), eq(uuid + "@ou"));
        assertEquals(SCRIPT_BODY_ASSERTION_ERROR_MESSAGE, "return true;", scriptCaptor.getValue().getBody());
    }

    /**
     * Проверка извлечения after-process скрипта из advimport'а.
     * @throws Exception
     */
    @Test
    public void testShouldVisitAdvImportAfterProcessCustomizer() throws Exception
    {
        //@formatter:off
        final String config = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>"
                + "<config xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" save-log=\"false\" threads-number=\"1\" >"
                + "<mode>CREATE</mode>"
                + "<gui-parameter name=\"idHolder\" type=\"STRING\" title=\"File for import\" />"
                + "<class name=\"ou\" threads-number=\"4\">"
                + "<csv-data-source with-header=\"true\" file-name=\"file\">" + "</csv-data-source>"
                + "<constant-metaclass-resolver metaclass=\"ou\"/>"
                + "<object-searcher attr=\"${idHolder}\" metaclass=\"ou\"/>"
                + "<attr name=\"idHolder\" column=\"id\" />"
                + "<script-customizer><after-process>return true;</after-process></script-customizer>"
                + "</class></config>";
        //@formatter:on
        ScriptsVisitor visitor = mock(ScriptsVisitor.class);
        String uuid = visitAdvImportScript(config, visitor);
        // проверка утверждений
        ArgumentCaptor<Script> scriptCaptor = ArgumentCaptor.forClass(Script.class);
        verify(visitor).visitAdvImportAfterProcessScriptCustomizerScript(scriptCaptor.capture(), eq(uuid + "@ou"));
        assertEquals(SCRIPT_BODY_ASSERTION_ERROR_MESSAGE, "return true;", scriptCaptor.getValue().getBody());
    }

    /**
     * Проверка извлечения after-import скрипта из advimport'а.
     * @throws Exception
     */
    @Test
    public void testShouldVisitAdvImportBeforeProcessCustomizer() throws Exception
    {
        //@formatter:off
        final String config = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>"
                + "<config xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" save-log=\"false\" threads-number=\"1\" >"
                + "<mode>CREATE</mode>"
                + "<gui-parameter name=\"idHolder\" type=\"STRING\" title=\"File for import\" />"
                + "<class name=\"ou\" threads-number=\"4\">"
                + "<csv-data-source with-header=\"true\" file-name=\"file\">" + "</csv-data-source>"
                + "<constant-metaclass-resolver metaclass=\"ou\"/>"
                + "<object-searcher attr=\"${idHolder}\" metaclass=\"ou\"/>"
                + "<attr name=\"idHolder\" column=\"id\" />"
                + "<script-customizer><before-process>return true;</before-process></script-customizer>"
                + "</class></config>";
        //@formatter:on
        ScriptsVisitor visitor = mock(ScriptsVisitor.class);
        String uuid = visitAdvImportScript(config, visitor);
        // проверка утверждений
        ArgumentCaptor<Script> scriptCaptor = ArgumentCaptor.forClass(Script.class);
        verify(visitor).visitAdvImportBeforeProcessScriptCustomizerScript(scriptCaptor.capture(), eq(uuid + "@ou"));
        assertEquals(SCRIPT_BODY_ASSERTION_ERROR_MESSAGE, "return true;", scriptCaptor.getValue().getBody());
    }

    /**
     * Проверка извлечения after-import скрипта из advimport'а.
     * @throws Exception
     */
    @Test
    public void testShouldVisitAdvImportBeforeProcessItemCustomizer() throws Exception
    {
        //@formatter:off
        final String config = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>"
                + "<config xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" save-log=\"false\" threads-number=\"1\" >"
                + "<mode>CREATE</mode>"
                + "<gui-parameter name=\"idHolder\" type=\"STRING\" title=\"File for import\" />"
                + "<class name=\"ou\" threads-number=\"4\">"
                + "<csv-data-source with-header=\"true\" file-name=\"file\">" + "</csv-data-source>"
                + "<constant-metaclass-resolver metaclass=\"ou\"/>"
                + "<object-searcher attr=\"${idHolder}\" metaclass=\"ou\"/>"
                + "<attr name=\"idHolder\" column=\"id\" />"
                + "<script-customizer><before-process-item>return true;</before-process-item></script-customizer>"
                + "</class></config>";
        //@formatter:on
        ScriptsVisitor visitor = mock(ScriptsVisitor.class);
        String uuid = visitAdvImportScript(config, visitor);
        // проверка утверждений
        ArgumentCaptor<Script> scriptCaptor = ArgumentCaptor.forClass(Script.class);
        verify(visitor).visitAdvImportBeforeProcessItemScriptCustomizerScript(scriptCaptor.capture(), eq(uuid + "@ou"));
        assertEquals(SCRIPT_BODY_ASSERTION_ERROR_MESSAGE, "return true;", scriptCaptor.getValue().getBody());
    }

    /**
     * Проверка извлечения after-import скрипта из advimport'а.
     * @throws Exception
     */
    @Test
    public void testShouldVisitAdvImportConverterScript() throws Exception
    {
        //@formatter:off
        final String config = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>"
                + "<config xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" save-log=\"false\" threads-number=\"1\" >"
                + "<mode>CREATE</mode>"
                + "<gui-parameter name=\"idHolder\" type=\"STRING\" title=\"File for import\" />"
                + "<class name=\"ou\" threads-number=\"4\">"
                + "<csv-data-source with-header=\"true\" file-name=\"file\">" + "</csv-data-source>"
                + "<constant-metaclass-resolver metaclass=\"ou\"/>"
                + "<object-searcher attr=\"${idHolder}\" metaclass=\"ou\"/>"
                + "<attr name=\"attr\"><script-converter>return true;</script-converter></attr>"
                + "</class></config>";
        //@formatter:on
        ScriptsVisitor visitor = mock(ScriptsVisitor.class);
        String uuid = visitAdvImportScript(config, visitor);
        // проверка утверждений
        ArgumentCaptor<Script> scriptCaptor = ArgumentCaptor.forClass(Script.class);
        verify(visitor).visitAdvImportConverterScript(scriptCaptor.capture(), eq(uuid + "@ou:attr"));
        assertEquals(SCRIPT_BODY_ASSERTION_ERROR_MESSAGE, "return true;", scriptCaptor.getValue().getBody());
    }

    /**
     * Проверка извлечения скрипта фильтра из advimport'а.
     * @throws Exception
     */
    @Test
    public void testShouldVisitAdvImportFilterScript() throws Exception
    {
        //@formatter:off
        final String config = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>"
                + "<config xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" save-log=\"false\" threads-number=\"1\" >"
                + "<mode>CREATE</mode>"
                + "<gui-parameter name=\"idHolder\" type=\"STRING\" title=\"File for import\" />"
                + "<class name=\"ou\" threads-number=\"4\">"
                + "<csv-data-source with-header=\"true\" file-name=\"file\">"
                + "</csv-data-source>"
                + "<script-filter>return true;</script-filter>"
                + "<constant-metaclass-resolver metaclass=\"ou\"/>"
                + "<object-searcher attr=\"${idHolder}\" metaclass=\"ou\"/>"
                + "<attr name=\"idHolder\" column=\"id\" />"
                + "</class></config>";
        //@formatter:on
        ScriptsVisitor visitor = mock(ScriptsVisitor.class);
        String uuid = visitAdvImportScript(config, visitor);
        // проверка утверждений
        ArgumentCaptor<Script> scriptCaptor = ArgumentCaptor.forClass(Script.class);
        verify(visitor).visitAdvImportFilterScript(scriptCaptor.capture(), eq(uuid + "@ou"));
        assertEquals(SCRIPT_BODY_ASSERTION_ERROR_MESSAGE, "return true;", scriptCaptor.getValue().getBody());
    }

    /**
     * Проверка извлечения скрипта {@link ScriptMetaClassResolver} из advimport'а.
     * @throws Exception
     */
    @Test
    public void testShouldVisitAdvImportScriptMetaclassScript() throws Exception
    {
        //@formatter:off
        final String config = "<?xml version=\"1.0\" encoding=\"UTF-8\"?> <config xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" save-log=\"false\" threads-number=\"1\" > <mode>CREATE</mode> <gui-parameter name=\"idHolder\" type=\"STRING\" title=\"File for import\" /> <class name=\"ou\" threads-number=\"4\"> <csv-data-source with-header=\"true\" file-name=\"file\"></csv-data-source> <script-metaclass-resolver>return true</script-metaclass-resolver> <object-searcher attr=\"${idHolder}\" metaclass=\"ou\"/> <attr name=\"idHolder\" column=\"id\" /> </class></config>";
        //@formatter:on
        ScriptsVisitor visitor = mock(ScriptsVisitor.class);
        String uuid = visitAdvImportScript(config, visitor);
        // проверка утверждений
        ArgumentCaptor<Script> scriptCaptor = ArgumentCaptor.forClass(Script.class);
        verify(visitor).visitAdvImportScriptMetaclassResolverScript(scriptCaptor.capture(), eq(uuid + "@ou"));
        assertEquals(SCRIPT_BODY_ASSERTION_ERROR_MESSAGE, "return true", scriptCaptor.getValue().getBody());
    }

    /**
     * Проверка извлечения скрипта вычисления атрибута на форме.
     *
     * @throws DispatchException
     */
    @Test
    public void testShouldVisitAttributeComputableOnFormScript() throws DispatchException
    {
        ScriptsVisitor visitor = mock(ScriptsVisitor.class);
        // Параметры представления отображения
        IProperties viewPresentation = new MapProperties();
        viewPresentation.setProperty(Presentations.ATTR_CODE, Presentations.STRING_VIEW);
        // Параметры представления редактирования
        IProperties editPresentation = new MapProperties();
        editPresentation.setProperty(Presentations.ATTR_CODE, Presentations.STRING_EDIT);
        String attributeCode = "test";
        //Создание атрибута.
        String scriptBody = "return [];";
        ScriptDto scriptDto = utils.createScriptDto(scriptBody);

        //@formatter:off
        AddAttributeAction action = AddAttributeAction.create()
                .setFqn(ouFqn)
                .setCode(attributeCode)
                .setTypeCode(StringAttributeType.CODE)
                .setTitle(attributeCode)
                .setEditable(true)
                .setViewPresentation(viewPresentation)
                .setEditPresentation(editPresentation)
                .setComputableOnForm(true)
                .setComputableOnFormScript(scriptDto);
        //@formatter:on

        visitAttributeScript(visitor, action);
        ArgumentCaptor<Script> scriptCaptor = ArgumentCaptor.forClass(Script.class);
        verify(visitor, atLeast(1)).visitAttributeComputableOnFormScript(scriptCaptor.capture(),
                eq(AttributeFqn.create(ouFqn, attributeCode)));
        assertEquals(SCRIPT_BODY_ASSERTION_ERROR_MESSAGE, scriptBody, scriptCaptor.getValue().getBody());
    }

    /**
     * Проверка извлечения скрипта вычисления значения атрибута.
     *
     * @throws DispatchException
     */
    @Test
    public void testShouldVisitAttributeComputeScript() throws DispatchException
    {
        // Параметры представления отображения
        IProperties viewPresentation = new MapProperties();
        viewPresentation.setProperty(Presentations.ATTR_CODE, Presentations.STRING_VIEW);
        // Параметры представления редактирования
        IProperties editPresentation = new MapProperties();
        editPresentation.setProperty(Presentations.ATTR_CODE, Presentations.STRING_EDIT);
        String attributeCode = "test";
        //Создание атрибута
        String scriptBody = "return true;";
        ScriptDto scriptDto = utils.createScriptDto(scriptBody);

        //@formatter:off
        AddAttributeAction action = AddAttributeAction.create()
                .setFqn(ouFqn)
                .setCode(attributeCode)
                .setTypeCode(StringAttributeType.CODE)
                .setTitle(attributeCode)
                .setComputable(true)
                .setEditable(true)
                .setScript(scriptDto)
                .setViewPresentation(viewPresentation)
                .setEditPresentation(editPresentation);
        //@formatter:on

        ScriptsVisitor visitor = mock(ScriptsVisitor.class);
        visitAttributeScript(visitor, action);
        ArgumentCaptor<Script> scriptCaptor = ArgumentCaptor.forClass(Script.class);
        verify(visitor).visitAttributeComputationScript(scriptCaptor.capture(),
                eq(AttributeFqn.parse(ouFqn + "@" + attributeCode)));
        assertEquals(SCRIPT_BODY_ASSERTION_ERROR_MESSAGE, scriptBody, scriptCaptor.getValue().getBody());
    }

    /**
     * Проверка извлечения скрипта вычисления значения по-умолчанию атрибута.
     *
     * @throws DispatchException
     */
    @Test
    public void testShouldVisitAttributeDefaultScript() throws DispatchException
    {
        ScriptsVisitor visitor = mock(ScriptsVisitor.class);
        // Параметры представления отображения
        IProperties viewPresentation = new MapProperties();
        viewPresentation.setProperty(Presentations.ATTR_CODE, Presentations.STRING_VIEW);
        // Параметры представления редактирования
        IProperties editPresentation = new MapProperties();
        editPresentation.setProperty(Presentations.ATTR_CODE, Presentations.STRING_EDIT);
        String attributeCode = "test";
        //Создание атрибута.
        String scriptBody = "return true;";
        ScriptDto scriptDto = utils.createScriptDto(scriptBody);

        //@formatter:off
        AddAttributeAction action = AddAttributeAction.create()
                .setFqn(ouFqn)
                .setCode(attributeCode)
                .setTypeCode(StringAttributeType.CODE)
                .setTitle(attributeCode)
                .setEditable(true)
                .setViewPresentation(viewPresentation)
                .setEditPresentation(editPresentation)
                .setDefaultByScript(true)
                .setScriptForDefault(scriptDto);
        //@formatter:on

        visitAttributeScript(visitor, action);
        ArgumentCaptor<Script> scriptCaptor = ArgumentCaptor.forClass(Script.class);
        verify(visitor).visitAttributeDefaultValueScript(scriptCaptor.capture(),
                eq(AttributeFqn.parse(ouFqn + "@" + attributeCode)));
        assertEquals(SCRIPT_BODY_ASSERTION_ERROR_MESSAGE, scriptBody, scriptCaptor.getValue().getBody());
    }

    /**
     * Проверка извлечения скрипта фильтрации атрибута.
     *
     * @throws DispatchException
     */
    @Test
    public void testShouldVisitAttributeFiltrationScript() throws DispatchException
    {
        ScriptsVisitor visitor = mock(ScriptsVisitor.class);
        // Параметры представления отображения
        IProperties viewPresentation = new MapProperties();
        viewPresentation.setProperty(Presentations.ATTR_CODE, Presentations.STRING_VIEW);
        // Параметры представления редактирования
        IProperties editPresentation = new MapProperties();
        editPresentation.setProperty(Presentations.ATTR_CODE, Presentations.STRING_EDIT);
        String attributeCode = "test";
        String scriptBody = "return [];";
        //Создание атрибута.
        ScriptDto scriptDto = utils.createScriptDto(scriptBody);

        //@formatter:off
        AddAttributeAction action = AddAttributeAction.create()
                .setFqn(ouFqn)
                .setCode(attributeCode)
                .setTypeCode(StringAttributeType.CODE)
                .setTitle(attributeCode)
                .setEditable(true)
                .setScript(scriptDto)
                .setViewPresentation(viewPresentation)
                .setEditPresentation(editPresentation)
                .setFilteredByScript(true)
                .setScriptForFiltration(scriptDto);
        //@formatter:on

        visitAttributeScript(visitor, action);
        ArgumentCaptor<Script> scriptCaptor = ArgumentCaptor.forClass(Script.class);
        verify(visitor).visitAttributeFiltrationScript(scriptCaptor.capture(),
                eq(AttributeFqn.parse(ouFqn + "@" + attributeCode)));
        assertEquals(SCRIPT_BODY_ASSERTION_ERROR_MESSAGE, scriptBody, scriptCaptor.getValue().getBody());
    }

    /**
     * Проверка извлечения скрипта обработки звонков CTI.
     */
    @Test
    public void testShouldVisitCtiScript()
    {
        String scriptStorageServiceFieldName = "scriptStorageService";
        ScriptStorageService oldScriptStorageService = (ScriptStorageService)ReflectionTestUtils.getField(crawler,
                scriptStorageServiceFieldName);
        ScriptStorageService mockedScriptStorageService = mock(ScriptStorageService.class);
        ReflectionTestUtils.setField(crawler, scriptStorageServiceFieldName, mockedScriptStorageService);

        String scriptBody = "return true";
        String scriptCode = "script-1";
        Script script = mock(Script.class);
        when(script.getBody()).thenReturn(scriptBody);
        when(script.getCode()).thenReturn(scriptCode);
        when(mockedScriptStorageService.getScript(scriptCode)).thenReturn(script);

        String modServiceFieldName = "scriptModulesStorageService";
        ScriptModulesStorageService oldModService = (ScriptModulesStorageService)ReflectionTestUtils.getField(crawler,
                modServiceFieldName);
        ScriptModulesStorageService mockedModService = mock(ScriptModulesStorageService.class);
        ReflectionTestUtils.setField(crawler, modServiceFieldName, mockedModService);
        SystemScriptModule module = mock(SystemScriptModule.class);
        when(module.getCode()).thenReturn("cti");
        when(module.getScriptCode()).thenReturn(scriptCode);
        when(mockedModService.getSystemModule("cti")).thenReturn(Optional.of(module));
        ArgumentCaptor<Script> scriptCaptor = ArgumentCaptor.forClass(Script.class);

        ScriptsVisitor visitor = mock(ScriptsVisitor.class);
        crawler.visit(visitor);
        verify(visitor).visitCtiScript(scriptCaptor.capture());
        assertEquals(SCRIPT_BODY_ASSERTION_ERROR_MESSAGE, scriptBody, scriptCaptor.getValue().getBody());

        ReflectionTestUtils.setField(crawler, modServiceFieldName, oldModService);
        ReflectionTestUtils.setField(crawler, scriptStorageServiceFieldName, oldScriptStorageService);
    }

    /**
     * Проверка извлечения скрипта условия выполнения действия по событию эскалации.
     */
    @Test
    public void testShouldVisitEscalationConditionScript()
    {
        String eventActionServiceFieldName = "eventActionService";
        EventActionServiceBean oldEventActionService = (EventActionServiceBean)ReflectionTestUtils.getField(crawler,
                eventActionServiceFieldName);
        EventActionServiceBean mockedEvenActionService = mock(EventActionServiceBean.class);
        ReflectionTestUtils.setField(crawler, eventActionServiceFieldName, mockedEvenActionService);

        String scriptStorageServiceFieldName = "scriptStorageService";
        ScriptStorageService oldScriptStorageService = (ScriptStorageService)ReflectionTestUtils.getField(crawler,
                scriptStorageServiceFieldName);
        ScriptStorageService mockedScriptStorageService = mock(ScriptStorageService.class);
        ReflectionTestUtils.setField(crawler, scriptStorageServiceFieldName, mockedScriptStorageService);

        String scriptBody = "return true";
        String scriptCode = "script-1";
        Script script = mock(Script.class);
        when(script.getBody()).thenReturn(scriptBody);
        when(script.getCode()).thenReturn(scriptCode);
        when(mockedScriptStorageService.getScript(scriptCode)).thenReturn(script);

        EventAction escalation = mock(EventAction.class);
        ScriptActionCondition actionCondition = mock(ScriptActionCondition.class);
        when(actionCondition.getScript()).thenReturn(scriptCode);
        when(actionCondition.getType()).thenReturn(ActionConditionType.SCRIPT);
        String conditionCode = "condition";
        when(actionCondition.getCode()).thenReturn(conditionCode);
        when(escalation.getConditions()).thenReturn(Lists.newArrayList((ActionCondition)actionCondition));
        Event event = mock(Event.class);
        when(event.getEventType()).thenReturn(EventType.escalation);
        when(escalation.getEvent()).thenReturn(event);
        String escalationCode = "code";
        when(escalation.getCode()).thenReturn(escalationCode);
        when(mockedEvenActionService.getEventActions()).thenReturn(Lists.<EventAction> newArrayList(escalation));

        ScriptsVisitor visitor = mock(ScriptsVisitor.class);
        crawler.visit(visitor);
        ArgumentCaptor<Script> scriptCaptor = ArgumentCaptor.forClass(Script.class);
        verify(visitor).visitEscalationConditionScript(scriptCaptor.capture(), eq(escalationCode + '@' + conditionCode),
                anyCollection());
        assertEquals(SCRIPT_BODY_ASSERTION_ERROR_MESSAGE, scriptBody, scriptCaptor.getValue().getBody());

        ReflectionTestUtils.setField(crawler, eventActionServiceFieldName, oldEventActionService);
        ReflectionTestUtils.setField(crawler, scriptStorageServiceFieldName, oldScriptStorageService);
    }

    /**
     * Проверка извлечения скрипта кастомизации оповещения действия по событию эскалации.
     */
    @Test
    public void testShouldVisitEscalationNotificationCustomizationScript()
    {
        String eventActionServiceFieldName = "eventActionService";
        EventActionServiceBean oldEventActionService = (EventActionServiceBean)ReflectionTestUtils.getField(crawler,
                eventActionServiceFieldName);
        EventActionServiceBean mockedEvenActionService = mock(EventActionServiceBean.class);
        ReflectionTestUtils.setField(crawler, eventActionServiceFieldName, mockedEvenActionService);

        String scriptStorageServiceFieldName = "scriptStorageService";
        ScriptStorageService oldScriptStorageService = (ScriptStorageService)ReflectionTestUtils.getField(crawler,
                scriptStorageServiceFieldName);
        ScriptStorageService mockedScriptStorageService = mock(ScriptStorageService.class);
        ReflectionTestUtils.setField(crawler, scriptStorageServiceFieldName, mockedScriptStorageService);

        String scriptBody = "return true";
        String scriptCode = "script-1";
        Script script = mock(Script.class);
        when(script.getBody()).thenReturn(scriptBody);
        when(script.getCode()).thenReturn(scriptCode);
        when(mockedScriptStorageService.getScript(scriptCode)).thenReturn(script);

        EventAction escalation = mock(EventAction.class);
        NotificationEventAction notificationEventAction = mock(NotificationEventAction.class);
        when(notificationEventAction.getScript()).thenReturn(scriptCode);
        when(escalation.getAction()).thenReturn(notificationEventAction);
        Event event = mock(Event.class);
        when(event.getEventType()).thenReturn(EventType.escalation);
        when(escalation.getEvent()).thenReturn(event);
        String escalationCode = "code";
        when(escalation.getCode()).thenReturn(escalationCode);
        when(mockedEvenActionService.getEventActions()).thenReturn(Lists.<EventAction> newArrayList(escalation));

        ScriptsVisitor visitor = mock(ScriptsVisitor.class);
        crawler.visit(visitor);
        ArgumentCaptor<Script> scriptCaptor = ArgumentCaptor.forClass(Script.class);
        verify(visitor).visitEscalationNotificationCustomizationScript(scriptCaptor.capture(), eq(escalationCode),
                anyCollection());
        assertEquals(SCRIPT_BODY_ASSERTION_ERROR_MESSAGE, scriptBody, scriptCaptor.getValue().getBody());

        ReflectionTestUtils.setField(crawler, eventActionServiceFieldName, oldEventActionService);
        ReflectionTestUtils.setField(crawler, scriptStorageServiceFieldName, oldScriptStorageService);
    }

    /**
     * Проверка извлечения скрипта скриптового действия по событию эскалации.
     */
    @Test
    public void testShouldVisitEscalationScript()
    {
        String eventActionServiceFieldName = "eventActionService";
        EventActionServiceBean oldEventActionService = (EventActionServiceBean)ReflectionTestUtils.getField(crawler,
                eventActionServiceFieldName);
        EventActionServiceBean mockedEvenActionService = mock(EventActionServiceBean.class);
        ReflectionTestUtils.setField(crawler, eventActionServiceFieldName, mockedEvenActionService);

        String scriptStorageServiceFieldName = "scriptStorageService";
        ScriptStorageService oldScriptStorageService = (ScriptStorageService)ReflectionTestUtils.getField(crawler,
                scriptStorageServiceFieldName);
        ScriptStorageService mockedScriptStorageService = mock(ScriptStorageService.class);
        ReflectionTestUtils.setField(crawler, scriptStorageServiceFieldName, mockedScriptStorageService);

        String scriptBody = "return true";
        String scriptCode = "script-1";
        Script script = mock(Script.class);
        when(script.getBody()).thenReturn(scriptBody);
        when(script.getCode()).thenReturn(scriptCode);
        when(mockedScriptStorageService.getScript(scriptCode)).thenReturn(script);

        EventAction escalation = mock(EventAction.class);
        ScriptEventAction scriptEventAction = mock(ScriptEventAction.class);
        when(scriptEventAction.getScript()).thenReturn(scriptCode);
        when(escalation.getAction()).thenReturn(scriptEventAction);
        Event event = mock(Event.class);
        when(event.getEventType()).thenReturn(EventType.escalation);
        when(escalation.getEvent()).thenReturn(event);
        String escalationCode = "code";
        when(escalation.getCode()).thenReturn(escalationCode);
        when(mockedEvenActionService.getEventActions()).thenReturn(Lists.<EventAction> newArrayList(escalation));

        ScriptsVisitor visitor = mock(ScriptsVisitor.class);
        crawler.visit(visitor);
        ArgumentCaptor<Script> scriptCaptor = ArgumentCaptor.forClass(Script.class);
        verify(visitor).visitEscalationScript(scriptCaptor.capture(), eq(escalationCode),
                anyCollection());
        assertEquals(SCRIPT_BODY_ASSERTION_ERROR_MESSAGE, scriptBody, scriptCaptor.getValue().getBody());

        ReflectionTestUtils.setField(crawler, eventActionServiceFieldName, oldEventActionService);
        ReflectionTestUtils.setField(crawler, scriptStorageServiceFieldName, oldScriptStorageService);
    }

    /**
     * Проверка извлечения скрипта условия выполнения действия по событию.
     *
     */
    @Test
    public void testShouldVisitEventActionConditionScript()
    {
        String eventActionServiceFieldName = "eventActionService";
        EventActionServiceBean oldEventActionService = (EventActionServiceBean)ReflectionTestUtils.getField(crawler,
                eventActionServiceFieldName);
        EventActionServiceBean mockedEvenActionService = mock(EventActionServiceBean.class);
        ReflectionTestUtils.setField(crawler, eventActionServiceFieldName, mockedEvenActionService);

        String scriptStorageServiceFieldName = "scriptStorageService";
        ScriptStorageService oldScriptStorageService = (ScriptStorageService)ReflectionTestUtils.getField(crawler,
                scriptStorageServiceFieldName);
        ScriptStorageService mockedScriptStorageService = mock(ScriptStorageService.class);
        ReflectionTestUtils.setField(crawler, scriptStorageServiceFieldName, mockedScriptStorageService);

        String scriptBody = "return true";
        String scriptCode = "script-1";
        Script script = mock(Script.class);
        when(script.getBody()).thenReturn(scriptBody);
        when(script.getCode()).thenReturn(scriptCode);
        when(mockedScriptStorageService.getScript(scriptCode)).thenReturn(script);

        EventAction eventAction = mock(EventAction.class);
        ScriptActionCondition actionCondition = mock(ScriptActionCondition.class);
        when(actionCondition.getScript()).thenReturn(scriptCode);
        when(actionCondition.getType()).thenReturn(ActionConditionType.SCRIPT);
        String conditionCode = "condition";
        when(actionCondition.getCode()).thenReturn(conditionCode);
        Event event = mock(Event.class);
        when(event.getEventType()).thenReturn(EventType.add);
        when(eventAction.getEvent()).thenReturn(event);
        when(eventAction.getConditions()).thenReturn(Lists.newArrayList((ActionCondition)actionCondition));
        String eventActionCode = "code";
        when(eventAction.getCode()).thenReturn(eventActionCode);
        when(mockedEvenActionService.getEventActions()).thenReturn(Lists.<EventAction> newArrayList(eventAction));

        ScriptsVisitor visitor = mock(ScriptsVisitor.class);
        crawler.visit(visitor);
        ArgumentCaptor<Script> scriptCaptor = ArgumentCaptor.forClass(Script.class);
        verify(visitor).visitEventActionConditionScript(scriptCaptor.capture(),
                eq(eventActionCode + '@' + conditionCode), anyCollection());
        assertEquals(SCRIPT_BODY_ASSERTION_ERROR_MESSAGE, scriptBody, scriptCaptor.getValue().getBody());

        ReflectionTestUtils.setField(crawler, eventActionServiceFieldName, oldEventActionService);
        ReflectionTestUtils.setField(crawler, scriptStorageServiceFieldName, oldScriptStorageService);
    }

    /**
     * Проверка извлечения скрипта кастомизации оповещения из действия по событию.
     *
     */
    @Test
    public void testShouldVisitEventActionNotificationCustomizationScript()
    {
        String scriptStorageServiceFieldName = "scriptStorageService";
        ScriptStorageService oldScriptStorageService = (ScriptStorageService)ReflectionTestUtils.getField(crawler,
                scriptStorageServiceFieldName);
        ScriptStorageService mockedScriptStorageService = mock(ScriptStorageService.class);
        ReflectionTestUtils.setField(crawler, scriptStorageServiceFieldName, mockedScriptStorageService);

        String scriptBody = "return true";
        String scriptCode = "script-1";
        Script script = mock(Script.class);
        when(script.getBody()).thenReturn(scriptBody);
        when(script.getCode()).thenReturn(scriptCode);
        when(mockedScriptStorageService.getScript(scriptCode)).thenReturn(script);

        String eventActionServiceFieldName = "eventActionService";
        EventActionServiceBean oldEventActionService = (EventActionServiceBean)ReflectionTestUtils.getField(crawler,
                eventActionServiceFieldName);
        EventActionServiceBean mockedEvenActionService = mock(EventActionServiceBean.class);
        ReflectionTestUtils.setField(crawler, eventActionServiceFieldName, mockedEvenActionService);

        EventAction eventAction = mock(EventAction.class);
        NotificationEventAction notificationEventAction = mock(NotificationEventAction.class);
        when(notificationEventAction.getScript()).thenReturn(scriptCode);
        when(eventAction.getAction()).thenReturn(notificationEventAction);
        Event event = mock(Event.class);
        when(event.getEventType()).thenReturn(EventType.add);
        when(eventAction.getEvent()).thenReturn(event);
        String eventActionCode = "code";
        when(eventAction.getCode()).thenReturn(eventActionCode);
        when(mockedEvenActionService.getEventActions()).thenReturn(Lists.<EventAction> newArrayList(eventAction));

        ScriptsVisitor visitor = mock(ScriptsVisitor.class);
        crawler.visit(visitor);
        ArgumentCaptor<Script> scriptCaptor = ArgumentCaptor.forClass(Script.class);
        verify(visitor).visitEventActionNotificationCustomizationScript(scriptCaptor.capture(), eq(eventActionCode),
                anyCollection());
        assertEquals(SCRIPT_BODY_ASSERTION_ERROR_MESSAGE, scriptBody, scriptCaptor.getValue().getBody());

        ReflectionTestUtils.setField(crawler, eventActionServiceFieldName, oldEventActionService);
        ReflectionTestUtils.setField(crawler, scriptStorageServiceFieldName, oldScriptStorageService);
    }

    /**
     * Проверка извлечения скрипта из действия по событию.
     *
     */
    @Test
    public void testShouldVisitEventActionScript()
    {
        String scriptStorageServiceFieldName = "scriptStorageService";
        ScriptStorageService oldScriptStorageService = (ScriptStorageService)ReflectionTestUtils.getField(crawler,
                scriptStorageServiceFieldName);
        ScriptStorageService mockedScriptStorageService = mock(ScriptStorageService.class);
        ReflectionTestUtils.setField(crawler, scriptStorageServiceFieldName, mockedScriptStorageService);

        String scriptBody = "return true";
        String scriptCode = "script-1";
        Script script = mock(Script.class);
        when(script.getBody()).thenReturn(scriptBody);
        when(script.getCode()).thenReturn(scriptCode);
        when(mockedScriptStorageService.getScript(scriptCode)).thenReturn(script);

        String eventActionServiceFieldName = "eventActionService";
        EventActionServiceBean oldEventActionService = (EventActionServiceBean)ReflectionTestUtils.getField(crawler,
                eventActionServiceFieldName);
        EventActionServiceBean mockedEvenActionService = mock(EventActionServiceBean.class);
        ReflectionTestUtils.setField(crawler, eventActionServiceFieldName, mockedEvenActionService);

        EventAction eventAction = mock(EventAction.class);
        ScriptEventAction scriptEventAction = mock(ScriptEventAction.class);
        when(scriptEventAction.getScript()).thenReturn(scriptCode);
        when(eventAction.getAction()).thenReturn(scriptEventAction);
        Event event = mock(Event.class);
        when(event.getEventType()).thenReturn(EventType.add);
        when(eventAction.getEvent()).thenReturn(event);
        String eventActionCode = "code";
        when(eventAction.getCode()).thenReturn(eventActionCode);
        when(mockedEvenActionService.getEventActions()).thenReturn(Lists.<EventAction> newArrayList(eventAction));

        ScriptsVisitor visitor = mock(ScriptsVisitor.class);
        crawler.visit(visitor);
        ArgumentCaptor<Script> scriptCaptor = ArgumentCaptor.forClass(Script.class);
        verify(visitor).visitEventActionScript(scriptCaptor.capture(), eq(eventActionCode),
                anyCollection());
        assertEquals(SCRIPT_BODY_ASSERTION_ERROR_MESSAGE, scriptBody, scriptCaptor.getValue().getBody());

        ReflectionTestUtils.setField(crawler, eventActionServiceFieldName, oldEventActionService);
        ReflectionTestUtils.setField(crawler, scriptStorageServiceFieldName, oldScriptStorageService);
    }

    /**
     * Проверка извлечения скрипта из правила обработки почты.
     *
     * @throws DispatchException
     */
    @Test
    public void testShouldVisitMailProcessingScript() throws DispatchException
    {
        MailProcessorRule mailProcessorRule = new MailProcessorRule();
        ScriptDto script = ScriptDtoFactory.createNew();
        script.setCode("script" + UniqueNumbersGenerator.nextInt(10000));
        script.setBody("return true;");

        SaveMailProcessorRuleAction saveAction = new SaveMailProcessorRuleAction(mailProcessorRule, script);
        saveAction.setWithScripts(true);

        ScriptsVisitor visitor = mock(ScriptsVisitor.class);
        try
        {
            SimpleScriptedResult<MailProcessorRule> result = dispatch.execute(saveAction);
            mailProcessorRule = result.get();
            crawler.visit(visitor);
        }
        finally
        {
            try
            {
                DeleteMailProcessorRuleAction deleteAction = new DeleteMailProcessorRuleAction(mailProcessorRule);
                dispatch.execute(deleteAction);
            }
            catch (Exception ex)
            {
            }
        }
        ArgumentCaptor<Script> scriptCaptor = ArgumentCaptor.forClass(Script.class);
        verify(visitor).visitMailProcessorRuleScript(scriptCaptor.capture(), eq(mailProcessorRule.getCode()));
        assertEquals(SCRIPT_BODY_ASSERTION_ERROR_MESSAGE, script.getBody(), scriptCaptor.getValue().getBody());
    }

    @Test
    public void testShouldVisitPermissionScript()
    {
        String scriptStorageServiceFieldName = "scriptStorageService";
        ScriptStorageService oldScriptStorageService = (ScriptStorageService)ReflectionTestUtils.getField(crawler,
                scriptStorageServiceFieldName);
        ScriptStorageService mockedScriptStorageService = mock(ScriptStorageService.class);
        ReflectionTestUtils.setField(crawler, scriptStorageServiceFieldName, mockedScriptStorageService);

        String scriptBody = "return true";
        String scriptCode = "script-1";
        Script script = mock(Script.class);
        when(script.getBody()).thenReturn(scriptBody);
        when(script.getCode()).thenReturn(scriptCode);
        when(mockedScriptStorageService.getScript(scriptCode)).thenReturn(script);

        String secServiceFieldName = "securityService";
        SecurityServiceBean oldSecService = (SecurityServiceBean)ReflectionTestUtils.getField(crawler,
                secServiceFieldName);
        SecurityServiceBean mockedSecService = mock(SecurityServiceBean.class);

        ReflectionTestUtils.setField(crawler, secServiceFieldName, mockedSecService);
        SecDomainImpl domain = mock(SecDomainImpl.class);
        when(mockedSecService.getDomains()).thenReturn(Lists.newArrayList(domain));
        when(domain.getCode()).thenReturn("code");
        AccessMatrixImpl accessMatrix = mock(AccessMatrixImpl.class);
        when(domain.getAccessMatrix()).thenReturn(accessMatrix);
        Map<Key, String> map = Maps.newHashMap();
        Key key = mock(Key.class);
        key.columnCode = "profile";
        key.rowCode = "marker";
        map.put(key, scriptCode);
        when(accessMatrix.getDeclaredScripts()).thenReturn(map);
        ScriptsVisitor visitor = mock(ScriptsVisitor.class);
        crawler.visit(visitor);
        ArgumentCaptor<Script> scriptCaptor = ArgumentCaptor.forClass(Script.class);
        verify(visitor).visitPermissionScript(scriptCaptor.capture(), eq("code@profile:marker"));
        assertEquals(SCRIPT_BODY_ASSERTION_ERROR_MESSAGE, scriptBody, scriptCaptor.getValue().getBody());

        ReflectionTestUtils.setField(crawler, secServiceFieldName, oldSecService);
        ReflectionTestUtils.setField(crawler, scriptStorageServiceFieldName, oldScriptStorageService);
    }

    /**
     * Проверка извлечения скрипта из шаблона отчёта.
     *
     * @throws DispatchException
     * @throws IOException
     */
    @Test
    public void testShouldVisitReportTemplateScript() throws DispatchException, IOException
    {
        String reportCode = "testReport";
        InputStream reportTemplateInputStream = resourceLoader
                .getResource("classpath:/ru/naumen/reports/server/script/rootTitle.prpt").getInputStream();
        byte[] content = ByteStreams.toByteArray(reportTemplateInputStream);

        XmlFile xmlTemplateFile = new XmlFile();
        xmlTemplateFile.setContent(content);
        if (dataBaseInfo.isOracle())
        {
            xmlTemplateFile.setFileName("rootTitle_oracle.prpt");
        }
        else
        {
            xmlTemplateFile.setFileName("rootTitle.prpt");
        }
        xmlTemplateFile.setMimeType("application/zip");

        ReportTemplate template = new ReportTemplate(reportCode);
        template.setXmlTemplateFile(xmlTemplateFile);
        ScriptDto script = ScriptDtoFactory.createNew();
        script.setBody("return true;");
        script.setCode("script" + UniqueNumbersGenerator.nextInt(10000));
        template.setScript(script.getCode());
        ScriptsVisitor visitor = mock(ScriptsVisitor.class);
        SaveReportTemplateAction saveAction = new SaveReportTemplateAction(template, true, script);
        saveAction.setWithScripts(true);
        try
        {
            dispatch.execute(saveAction);
            crawler.visit(visitor);
        }
        finally
        {
            DeleteReportTemplateAction deleteAction = new DeleteReportTemplateAction(Lists.newArrayList(reportCode));
            dispatch.execute(deleteAction);
        }
        ArgumentCaptor<Script> scriptCaptor = ArgumentCaptor.forClass(Script.class);
        verify(visitor).visitReportTemplateScript(scriptCaptor.capture(), eq(reportCode));
        assertEquals(SCRIPT_BODY_ASSERTION_ERROR_MESSAGE, script.getBody(), scriptCaptor.getValue().getBody());
    }

    /**
     * Проверка извлечения тела скрипта определения прав доступа из свойств роли.
     *
     * @throws DispatchException
     */
    @Test
    public void testShouldVisitRoleAccessScript() throws DispatchException
    {
        IProperties properties = new MapProperties();
        ScriptDto withoutScript = ScriptDtoFactory.createWithout();
        String scriptBody = "return true;";
        ScriptDto createScriptDto = utils.createScriptDto(scriptBody);

        properties.setProperty(ru.naumen.metainfo.shared.Constants.Role.ASSIGNED_KEY, "");
        properties.setProperty(SCRIPT_ACCESS_DTO_KEY, createScriptDto);
        properties.setProperty(SCRIPT_OWNERS_DTO_KEY, withoutScript);
        properties.setProperty(SCRIPT_LIST_FILTER_DTO_KEY, withoutScript);
        properties.setProperty(SCRIPT_FAST_LINK_RIGHTS_DTO_KEY, withoutScript);
        String roleCode = "testRole";
        ScriptsVisitor visitor = mock(ScriptsVisitor.class);
        visitRoleScript(properties, roleCode, visitor);
        ArgumentCaptor<Script> scriptCaptor = ArgumentCaptor.forClass(Script.class);
        verify(visitor).visitRoleAccessKeyScript(scriptCaptor.capture(), eq(roleCode), nullable(Collection.class));
        assertEquals(SCRIPT_BODY_ASSERTION_ERROR_MESSAGE, scriptBody, scriptCaptor.getValue().getBody());
    }

    /**
     * Проверка извлечения тела скрипта определения прав доступа из свойств роли.
     *
     * @throws DispatchException
     */
    @Test
    public void testShouldVisitRoleOwnerScript() throws DispatchException
    {
        IProperties properties = new MapProperties();
        String scriptBody = "return true;";
        ScriptDto createScriptDto = utils.createScriptDto(scriptBody);
        ScriptDto withoutScript = ScriptDtoFactory.createWithout();

        properties.setProperty(ru.naumen.metainfo.shared.Constants.Role.ASSIGNED_KEY, "");
        properties.setProperty(SCRIPT_ACCESS_DTO_KEY, withoutScript);
        properties.setProperty(SCRIPT_OWNERS_DTO_KEY, createScriptDto);
        properties.setProperty(SCRIPT_LIST_FILTER_DTO_KEY, withoutScript);
        properties.setProperty(SCRIPT_FAST_LINK_RIGHTS_DTO_KEY, withoutScript);
        String roleCode = "testRole";
        ScriptsVisitor visitor = mock(ScriptsVisitor.class);
        visitRoleScript(properties, roleCode, visitor);
        ArgumentCaptor<Script> scriptCaptor = ArgumentCaptor.forClass(Script.class);
        verify(visitor).visitRoleOwnersKeyScript(scriptCaptor.capture(), eq(roleCode), nullable(Collection.class));
        assertEquals(SCRIPT_BODY_ASSERTION_ERROR_MESSAGE, scriptBody, scriptCaptor.getValue().getBody());
    }

    /**
     * Проверка извлечения тела скрипта определения условия отбора объектов доступных обладателю роли.
     *
     * @throws DispatchException
     */
    @Test
    public void testShouldVisitRoleScriptedListFilterScript() throws DispatchException
    {
        IProperties properties = new MapProperties();
        String scriptBody = "return [];";
        ScriptDto createScriptDto = utils.createScriptDto(scriptBody);
        ScriptDto withoutScript = ScriptDtoFactory.createWithout();

        properties.setProperty(ru.naumen.metainfo.shared.Constants.Role.ASSIGNED_KEY, "");
        properties.setProperty(SCRIPT_ACCESS_DTO_KEY, withoutScript);
        properties.setProperty(SCRIPT_OWNERS_DTO_KEY, withoutScript);
        properties.setProperty(SCRIPT_LIST_FILTER_DTO_KEY, createScriptDto);
        properties.setProperty(SCRIPT_FAST_LINK_RIGHTS_DTO_KEY, createScriptDto);
        String roleCode = "testRole";
        ScriptsVisitor visitor = mock(ScriptsVisitor.class);
        visitRoleScript(properties, roleCode, visitor);
        ArgumentCaptor<Script> scriptCaptor = ArgumentCaptor.forClass(Script.class);
        verify(visitor).visitRoleScriptedListFilterScript(scriptCaptor.capture(), eq(roleCode),
                nullable(Collection.class));
        assertEquals(SCRIPT_BODY_ASSERTION_ERROR_MESSAGE, scriptBody, scriptCaptor.getValue().getBody());
    }

    /**
     * Проверка извлечения скрипта из задачи планировщика.
     *
     * @throws DispatchException
     */
    @Test
    public void testShouldVisitSchedulerScript() throws DispatchException
    {
        ExecuteScriptTask schedulerTask = new ExecuteScriptTask();
        schedulerTask.setType(ExecuteScriptTask.NAME);
        schedulerTask.setCode(TestUtils.randomString());
        ScriptDto script = ScriptDtoFactory.createNew();
        script.setBody("return true;");
        script.setCode("script" + UniqueNumbersGenerator.nextInt(10000));
        schedulerTask.setScript(script.getCode());
        SaveSchedulerTaskAction saveAction = new SaveSchedulerTaskAction(
                ExecuteScriptTaskDtoFactory.create(schedulerTask, script), true);
        ScriptsVisitor visitor = mock(ScriptsVisitor.class);
        try
        {
            SchedulerTaskResponse response = dispatch.execute(saveAction);
            schedulerTask = (ExecuteScriptTask)response.get().get();
            crawler.visit(visitor);
        }
        finally
        {
            try
            {
                DeleteSchTaskAction deleteAction = new DeleteSchTaskAction(Lists.newArrayList(schedulerTask.getCode()));
                dispatch.execute(deleteAction);
            }
            catch (Exception e)
            {
            }
        }
        ArgumentCaptor<Script> scriptCaptor = ArgumentCaptor.forClass(Script.class);
        verify(visitor).visitSchedulerTaskScript(scriptCaptor.capture(), eq(schedulerTask.getCode()));
        assertEquals(SCRIPT_BODY_ASSERTION_ERROR_MESSAGE, script.getBody(), scriptCaptor.getValue().getBody());
    }

    @Test
    public void testShouldVisitSCParamAgreementsScript()
    {
        String scriptStorageServiceFieldName = "scriptStorageService";
        ScriptStorageService oldScriptStorageService = (ScriptStorageService)ReflectionTestUtils.getField(crawler,
                scriptStorageServiceFieldName);
        ScriptStorageService mockedScriptStorageService = mock(ScriptStorageService.class);
        ReflectionTestUtils.setField(crawler, scriptStorageServiceFieldName, mockedScriptStorageService);

        String scriptBody = "return true";
        String scriptCode = "script-1";
        Script script = mock(Script.class);
        when(script.getBody()).thenReturn(scriptBody);
        when(script.getCode()).thenReturn(scriptCode);
        when(mockedScriptStorageService.getScript(scriptCode)).thenReturn(script);

        String settingsStorageFieldName = "settingsStorage";
        SettingsStorage oldSettingsStorage = (SettingsStorage)ReflectionTestUtils.getField(crawler,
                settingsStorageFieldName);
        SCParameters scParams = prepareScParams(settingsStorageFieldName);
        when(scParams.getAgreementsFiltrationScript()).thenReturn(scriptCode);
        ScriptsVisitor visitor = mock(ScriptsVisitor.class);
        crawler.visit(visitor);
        ArgumentCaptor<Script> scriptCaptor = ArgumentCaptor.forClass(Script.class);
        verify(visitor).visitSCParamAgreementScript(scriptCaptor.capture());
        assertEquals(SCRIPT_BODY_ASSERTION_ERROR_MESSAGE, scriptBody, scriptCaptor.getValue().getBody());

        ReflectionTestUtils.setField(crawler, settingsStorageFieldName, oldSettingsStorage);
        ReflectionTestUtils.setField(crawler, scriptStorageServiceFieldName, oldScriptStorageService);
    }

    @Test
    public void testShouldVisitSCParamServicesScript()
    {
        String scriptStorageServiceFieldName = "scriptStorageService";
        ScriptStorageService oldScriptStorageService = (ScriptStorageService)ReflectionTestUtils.getField(crawler,
                scriptStorageServiceFieldName);
        ScriptStorageService mockedScriptStorageService = mock(ScriptStorageService.class);
        ReflectionTestUtils.setField(crawler, scriptStorageServiceFieldName, mockedScriptStorageService);

        String scriptBody = "return true";
        String scriptCode = "script-1";
        Script script = mock(Script.class);
        when(script.getBody()).thenReturn(scriptBody);
        when(script.getCode()).thenReturn(scriptCode);
        when(mockedScriptStorageService.getScript(scriptCode)).thenReturn(script);

        String settingsStorageFieldName = "settingsStorage";
        SettingsStorage oldSettingsStorage = (SettingsStorage)ReflectionTestUtils.getField(crawler,
                settingsStorageFieldName);
        SCParameters scParams = prepareScParams(settingsStorageFieldName);
        when(scParams.getServicesFiltrationScript()).thenReturn(scriptCode);
        ScriptsVisitor visitor = mock(ScriptsVisitor.class);
        crawler.visit(visitor);
        ArgumentCaptor<Script> scriptCaptor = ArgumentCaptor.forClass(Script.class);
        verify(visitor).visitSCParamServicesScript(scriptCaptor.capture());
        assertEquals(SCRIPT_BODY_ASSERTION_ERROR_MESSAGE, scriptBody, scriptCaptor.getValue().getBody());

        ReflectionTestUtils.setField(crawler, settingsStorageFieldName, oldSettingsStorage);
        ReflectionTestUtils.setField(crawler, scriptStorageServiceFieldName, oldScriptStorageService);
    }

    /**
     * Проверка извлечения скрипта из условия приостановки счётчика времени.
     *
     * @throws DispatchException
     */
    @Test
    public void testShouldVisitTimerPauseConditionScript() throws DispatchException
    {
        String code = UUIDGenerator.get().nextUUID();
        ScriptTimerCondition timerCondition = new ScriptTimerCondition();
        ScriptsVisitor visitor = mock(ScriptsVisitor.class);
        TimerDefinition timerDefinition = visitTimerScript(timerCondition, code, visitor);
        ArgumentCaptor<Script> scriptCaptor = ArgumentCaptor.forClass(Script.class);
        verify(visitor).visitTimerPauseConditionScript(scriptCaptor.capture(), eq(timerDefinition.getCode()),
                anyCollection());
        assertEquals(SCRIPT_BODY_ASSERTION_ERROR_MESSAGE, "true", scriptCaptor.getValue().getBody());
    }

    /**
     * Проверка извлечения скрипта из условия возобновления счётчика времени.
     *
     * @throws DispatchException
     */
    @Test
    public void testShouldVisitTimerResumeConditionScript() throws DispatchException
    {
        String code = UUIDGenerator.get().nextUUID();
        ScriptTimerCondition timerCondition = new ScriptTimerCondition();
        ScriptsVisitor visitor = mock(ScriptsVisitor.class);
        TimerDefinition timerDefinition = visitTimerScript(timerCondition, code, visitor);
        ArgumentCaptor<Script> scriptCaptor = ArgumentCaptor.forClass(Script.class);
        verify(visitor).visitTimerResumeCondition(scriptCaptor.capture(), eq(timerDefinition.getCode()),
                anyCollection());
        assertEquals(SCRIPT_BODY_ASSERTION_ERROR_MESSAGE, "true", scriptCaptor.getValue().getBody());
    }

    /**
     * Проверка извлечения скрипта из условия запуска счётчика времени.
     *
     * @throws DispatchException
     */
    @Test
    public void testShouldVisitTimerStartConditionScript() throws DispatchException
    {
        String code = UUIDGenerator.get().nextUUID();
        ScriptsVisitor visitor = mock(ScriptsVisitor.class);
        ScriptTimerCondition timerCondition = new ScriptTimerCondition();
        TimerDefinition timerDefinition = visitTimerScript(timerCondition, code, visitor);
        ArgumentCaptor<Script> scriptCaptor = ArgumentCaptor.forClass(Script.class);
        verify(visitor).visitTimerStartConditionScript(scriptCaptor.capture(), eq(timerDefinition.getCode()),
                anyCollection());
        assertEquals(SCRIPT_BODY_ASSERTION_ERROR_MESSAGE, "true", scriptCaptor.getValue().getBody());
    }

    /**
     * Проверка извлечения скрипта из условия остановки счётчика времени.
     *
     * @throws DispatchException
     */
    @Test
    public void testShouldVisitTimerStopConditionScript() throws DispatchException
    {
        ScriptTimerCondition timerCondition = new ScriptTimerCondition();
        ScriptsVisitor visitor = mock(ScriptsVisitor.class);
        String code = UUIDGenerator.get().nextUUID();
        TimerDefinition timerDefinition = visitTimerScript(timerCondition, code, visitor);
        ArgumentCaptor<Script> scriptCaptor = ArgumentCaptor.forClass(Script.class);
        verify(visitor).visitTimerStopConditionScript(scriptCaptor.capture(), eq(timerDefinition.getCode()),
                anyCollection());
        assertEquals(SCRIPT_BODY_ASSERTION_ERROR_MESSAGE, "true", scriptCaptor.getValue().getBody());
    }

    @Test
    public void testShouldVisitWorkflowPostActionScript()
    {
        String scriptStorageServiceFieldName = "scriptStorageService";
        ScriptStorageService oldScriptStorageService = (ScriptStorageService)ReflectionTestUtils.getField(crawler,
                scriptStorageServiceFieldName);
        ScriptStorageService mockedScriptStorageService = mock(ScriptStorageService.class);
        ReflectionTestUtils.setField(crawler, scriptStorageServiceFieldName, mockedScriptStorageService);

        String scriptBody = "return true";
        String scriptCode = "script-1";
        Script script = mock(Script.class);
        when(script.getBody()).thenReturn(scriptBody);
        when(script.getCode()).thenReturn(scriptCode);
        when(mockedScriptStorageService.getScript(scriptCode)).thenReturn(script);

        MetainfoService oldMetinfoService = (MetainfoService)ReflectionTestUtils.getField(crawler, "metainfoService");
        State state = prepareWorkflowState();
        Action action = mock(Action.class);
        when(state.getDeclaredActions(false)).thenReturn(Sets.newHashSet(action));
        prepareWorkflowAction(action);
        ScriptsVisitor visitor = mock(ScriptsVisitor.class);
        crawler.visit(visitor);
        ArgumentCaptor<Script> scriptCaptor = ArgumentCaptor.forClass(Script.class);
        verify(visitor).visitWorkflowPostActionScript(scriptCaptor.capture(), eq("fqn@code:condition"));
        assertEquals(SCRIPT_BODY_ASSERTION_ERROR_MESSAGE, scriptBody, scriptCaptor.getValue().getBody());

        ReflectionTestUtils.setField(crawler, "metainfoService", oldMetinfoService);
        ReflectionTestUtils.setField(crawler, scriptStorageServiceFieldName, oldScriptStorageService);
    }

    @Test
    public void testShouldVisitWorkflowPostConditionScript()
    {
        String scriptStorageServiceFieldName = "scriptStorageService";
        ScriptStorageService oldScriptStorageService = (ScriptStorageService)ReflectionTestUtils.getField(crawler,
                scriptStorageServiceFieldName);
        ScriptStorageService mockedScriptStorageService = mock(ScriptStorageService.class);
        ReflectionTestUtils.setField(crawler, scriptStorageServiceFieldName, mockedScriptStorageService);

        String scriptBody = "return true";
        String scriptCode = "script-1";
        Script script = mock(Script.class);
        when(script.getBody()).thenReturn(scriptBody);
        when(script.getCode()).thenReturn(scriptCode);
        when(mockedScriptStorageService.getScript(scriptCode)).thenReturn(script);

        MetainfoService oldMetinfoService = (MetainfoService)ReflectionTestUtils.getField(crawler, "metainfoService");
        State state = prepareWorkflowState();
        Condition condition = mock(Condition.class);
        when(state.getDeclaredConditions(false)).thenReturn(Sets.newHashSet(condition));
        prepareWorkflowCondition(condition);
        ScriptsVisitor visitor = mock(ScriptsVisitor.class);
        crawler.visit(visitor);
        ArgumentCaptor<Script> scriptCaptor = ArgumentCaptor.forClass(Script.class);
        verify(visitor).visitWorkflowPostConditionScript(scriptCaptor.capture(), eq("fqn@code:condition"));
        assertEquals(SCRIPT_BODY_ASSERTION_ERROR_MESSAGE, scriptBody, scriptCaptor.getValue().getBody());

        ReflectionTestUtils.setField(crawler, "metainfoService", oldMetinfoService);
        ReflectionTestUtils.setField(crawler, scriptStorageServiceFieldName, oldScriptStorageService);
    }

    @Test
    public void testShouldVisitWorkflowPreActionScript()
    {
        String scriptStorageServiceFieldName = "scriptStorageService";
        ScriptStorageService oldScriptStorageService = (ScriptStorageService)ReflectionTestUtils.getField(crawler,
                scriptStorageServiceFieldName);
        ScriptStorageService mockedScriptStorageService = mock(ScriptStorageService.class);
        ReflectionTestUtils.setField(crawler, scriptStorageServiceFieldName, mockedScriptStorageService);

        String scriptBody = "return true";
        String scriptCode = "script-1";
        Script script = mock(Script.class);
        when(script.getBody()).thenReturn(scriptBody);
        when(script.getCode()).thenReturn(scriptCode);
        when(mockedScriptStorageService.getScript(scriptCode)).thenReturn(script);

        MetainfoService oldMetinfoService = (MetainfoService)ReflectionTestUtils.getField(crawler, "metainfoService");
        State state = prepareWorkflowState();
        Action action = mock(Action.class);
        when(state.getDeclaredActions(true)).thenReturn(Sets.newHashSet(action));
        prepareWorkflowAction(action);
        ScriptsVisitor visitor = mock(ScriptsVisitor.class);
        crawler.visit(visitor);
        ArgumentCaptor<Script> scriptCaptor = ArgumentCaptor.forClass(Script.class);
        verify(visitor).visitWorkflowPreActionScript(scriptCaptor.capture(), eq("fqn@code:condition"));
        assertEquals(SCRIPT_BODY_ASSERTION_ERROR_MESSAGE, scriptBody, scriptCaptor.getValue().getBody());

        ReflectionTestUtils.setField(crawler, "metainfoService", oldMetinfoService);
        ReflectionTestUtils.setField(crawler, scriptStorageServiceFieldName, oldScriptStorageService);
    }

    @Test
    public void testShouldVisitWorkflowPreConditionScript()
    {
        String scriptStorageServiceFieldName = "scriptStorageService";
        ScriptStorageService oldScriptStorageService = (ScriptStorageService)ReflectionTestUtils.getField(crawler,
                scriptStorageServiceFieldName);
        ScriptStorageService mockedScriptStorageService = mock(ScriptStorageService.class);
        ReflectionTestUtils.setField(crawler, scriptStorageServiceFieldName, mockedScriptStorageService);

        String scriptBody = "return true";
        String scriptCode = "script-1";
        Script script = mock(Script.class);
        when(script.getBody()).thenReturn(scriptBody);
        when(script.getCode()).thenReturn(scriptCode);
        when(mockedScriptStorageService.getScript(scriptCode)).thenReturn(script);

        MetainfoService oldMetinfoService = (MetainfoService)ReflectionTestUtils.getField(crawler, "metainfoService");
        State state = prepareWorkflowState();
        Condition condition = mock(Condition.class);
        when(state.getDeclaredConditions(true)).thenReturn(Sets.newHashSet(condition));
        prepareWorkflowCondition(condition);
        ScriptsVisitor visitor = mock(ScriptsVisitor.class);
        crawler.visit(visitor);
        ArgumentCaptor<Script> scriptCaptor = ArgumentCaptor.forClass(Script.class);
        verify(visitor).visitWorkflowPreConditionScript(scriptCaptor.capture(), eq("fqn@code:condition"));
        assertEquals(SCRIPT_BODY_ASSERTION_ERROR_MESSAGE, "return true", scriptCaptor.getValue().getBody());

        ReflectionTestUtils.setField(crawler, "metainfoService", oldMetinfoService);
        ReflectionTestUtils.setField(crawler, scriptStorageServiceFieldName, oldScriptStorageService);
    }

    /**
     * Предварительная настройка для проверки скриптов из advImport'ов.
     *
     * @throws Exception
     */
    private void advImportSetUp() throws Exception
    {
        // настройка системы
        createdListener.setUp();
    }

    /**
     * Действия после проверки скрипта advImport'а.
     */
    private void advImportTearDown()
    {
        createdListener.tearDown();
    }

    /**
     * @param handlerUtilsFieldName
     * @return
     */
    private SCParameters prepareScParams(String settingsStorageFieldName)
    {
        SettingsStorage mockedSettingsStorage = mock(SettingsStorage.class);
        ReflectionTestUtils.setField(crawler, settingsStorageFieldName, mockedSettingsStorage);
        Settings settings = mock(Settings.class);
        when(mockedSettingsStorage.getSettings()).thenReturn(settings);
        SCParameters scParams = mock(SCParameters.class);
        when(settings.getScParameters()).thenReturn(scParams);
        return scParams;
    }

    /**
     * @param condition
     */
    private void prepareWorkflowAction(Action action)
    {
        when(action.getCode()).thenReturn("condition");
        when(action.getType()).thenReturn(Action.ActionType.SCRIPT);
        IProperties properties = mock(IProperties.class);
        when(action.getProperties()).thenReturn(properties);
        when(properties.getProperty("script")).thenReturn("script-1");
    }

    /**
     * @param condition
     */
    private void prepareWorkflowCondition(Condition condition)
    {
        when(condition.getCode()).thenReturn("condition");
        when(condition.getType()).thenReturn(Condition.ConditionType.SCRIPT);
        IProperties properties = mock(IProperties.class);
        when(condition.getProperties()).thenReturn(properties);
        when(properties.getProperty("script")).thenReturn("script-1");
    }

    /**
     * @return
     */
    private State prepareWorkflowState()
    {
        MetainfoServiceBean mockedMetainfoService = mock(MetainfoServiceBean.class);
        ReflectionTestUtils.setField(crawler, "metainfoService", mockedMetainfoService);
        MetaClass metaClass = mock(MetaClass.class);
        Workflow workflow = mock(Workflow.class);
        when(mockedMetainfoService.getMetaClasses()).thenReturn(Lists.newArrayList(metaClass));
        when(metaClass.isHasWorkflow()).thenReturn(true);
        when(metaClass.getWorkflow()).thenReturn(workflow);
        ClassFqn fqn = ClassFqn.parse("fqn");
        when(metaClass.getFqn()).thenReturn(fqn);
        State state = mock(State.class);
        when(workflow.getStates()).thenReturn(Lists.newArrayList(state));
        when(state.getCode()).thenReturn("code");
        return state;
    }

    /**
     * Выполняет действия по созданию конфигурации advImport'а и запуску crawler'а с моком visitor'а.
     *
     * @param config конфигурация advImport'а;
     * @param visitor мок;
     * @return uuid созданной конфигурации advImport'а для проверки корректности вызовов на моке.
     * @throws Exception
     * @throws DispatchException
     */
    private String visitAdvImportScript(final String config, ScriptsVisitor visitor) throws Exception, DispatchException
    {
        String uuid = null;
        try
        {
            advImportSetUp();
            String code = UUIDGenerator.get().nextUUID();
            String title = UUIDGenerator.get().nextUUID();
            // вызов системы
            AddConfigurationAction a = new AddConfigurationAction(code, title, config);
            GetConfigurationResponse response = dispatch.execute(a);
            crawler.visit(visitor);
            uuid = response.get().get().getUUID();
        }
        finally
        {
            // очистка
            metaStorageService.remove(Constants.ADVIMPORT_METASTORAGE_TYPE, uuid);
            advImportTearDown();
        }
        return uuid;
    }

    /**
     * Выполняет действия по созданию атрибута и запуску crawler'а с моком visitor'а.
     *
     * @param visitor мок;
     * @param action действие по созданию атрибута.
     * @throws DispatchException
     */
    private void visitAttributeScript(ScriptsVisitor visitor, AddAttributeAction action) throws DispatchException
    {
        try
        {
            dispatch.execute(action);
            //Запуск визитора скриптов.
            crawler.visit(visitor);
        }
        finally
        {
            try
            {
                DelAttributeAction delAction = new DelAttributeAction(ouFqn, action.getCode());
                dispatch.execute(delAction);
            }
            catch (Exception ex)
            {
            }
        }
    }

    /**
     * Выполняет действия по созданию роли и запуску crawler'а с моком visitor'а.
     *
     * @param properties
     * @param roleCode
     * @param visitor
     * @throws DispatchException
     */
    private void visitRoleScript(IProperties properties, String roleCode, ScriptsVisitor visitor)
            throws DispatchException
    {
        AddSecurityRoleAction addSecurityRoleAction = new AddSecurityRoleAction(roleCode, roleCode, null, Type.SCRIPT,
                properties);
        try
        {
            dispatch.execute(addSecurityRoleAction);
            crawler.visit(visitor);
        }
        finally
        {
            try
            {
                DelSecurityRoleAction delSecurityRoleAction = new DelSecurityRoleAction(Lists.newArrayList(roleCode));
                dispatch.execute(delSecurityRoleAction);
            }
            catch (Exception e)
            {
            }
        }
    }

    /**
     * Выполняет действия по созданию счётчика времени и запуску crawler'а с моком visitor'а.
     *
     * @param timerDefinition
     * @param code
     * @param visitor
     * @return
     * @throws DispatchException
     */
    private TimerDefinition visitTimerScript(TimerCondition timerCondition, String code, ScriptsVisitor visitor)
            throws DispatchException
    {
        ScriptDto script1 = utils.createScriptDto("true");
        ScriptDto script2 = utils.createScriptDto("true");
        ScriptDto script3 = utils.createScriptDto("true");
        ScriptDto script4 = utils.createScriptDto("true");

        TimerDefinition timerDefinition = new TimerDefinition();
        timerDefinition.setCode(code);
        timerDefinition.setTimerCondition(timerCondition);
        MetaClassLite ouMC = metainfoService.getMetaClass(ouFqn);
        timerDefinition.setTargetMetaClasses(Lists.newArrayList(ouMC));
        timerDefinition.setClassOfTargets(ouMC);
        timerDefinition.getTargetTypes().add(ouFqn);
        try
        {
            SaveTimerDefinitionAction saveAction = new SaveTimerDefinitionAction(timerDefinition, true);
            saveAction.setWithScripts(true);
            saveAction.setStartConditionScript(script1);
            saveAction.setStopConditionScript(script2);
            saveAction.setPauseConditionScript(script3);
            saveAction.setResumeConditionScript(script4);
            //Выполнение
            SimpleScriptedResult<DtoContainer<TimerDefinition>> response = dispatch.execute(saveAction);
            timerDefinition = response.get().get();
            crawler.visit(visitor);
        }
        finally
        {
            try
            {
                DeleteTimerDefinitionAction deleteAction = new DeleteTimerDefinitionAction(code);
                dispatch.execute(deleteAction);
            }
            catch (Exception e)
            {
            }
        }
        return timerDefinition;
    }
}
