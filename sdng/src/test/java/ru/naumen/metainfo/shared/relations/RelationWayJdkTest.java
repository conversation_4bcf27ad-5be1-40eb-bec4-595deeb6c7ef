package ru.naumen.metainfo.shared.relations;

import org.junit.Assert;
import org.junit.Test;

import ru.naumen.core.shared.MockTestUtils;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.Relation;

/**
 * Юнит тесты на RelationWay
 * <AUTHOR>
 * @since 21.12.2010
 */
public class RelationWayJdkTest
{
    @Test
    public void test_equals()
    {
        // настройка системы
        Relation rb1 = MockTestUtils.relation();
        RelationWay rw1 = new RelationWay(rb1, true);
        RelationWay rw2 = new RelationWay(rb1, true);
        RelationWay rw3 = new RelationWay(rb1, false);
        // вызов системы
        // проверка утверждений
        Assert.assertEquals("Должны быть равны", rw1, rw2);
        Assert.assertFalse("Не равно null", rw1.equals(null));
        Assert.assertFalse("Не равно объекту другого класса", rw1.equals(""));
        Assert.assertFalse("Должны быть не равны", rw1.equals(rw3));
        Assert.assertEquals("Хэш коды также должны быть равны", rw1.hashCode(), rw2.hashCode());
        // очистка
    }

    @Test
    public void test_getFrom()
    {
        // настройка системы
        ClassFqn leftFqn = MockTestUtils.classFqn();
        ClassFqn rightFqn = MockTestUtils.classFqn();
        Relation rb1 = MockTestUtils.relation(leftFqn, rightFqn);
        RelationWay rw1 = new RelationWay(rb1, true);
        RelationWay rw2 = new RelationWay(rb1, false);
        // вызов системы
        // проверка утверждений
        // проверка утверждений
        Assert.assertEquals("Должен вернуться левый fqn", rw1.getFrom(), leftFqn);
        Assert.assertEquals("Должны вернуться правый fqn", rw2.getFrom(), rightFqn);
        // очистка
    }

    @Test
    public void test_getRelation()
    {
        // настройка системы
    	ClassFqn leftFqn = MockTestUtils.classFqn();
        ClassFqn rightFqn = MockTestUtils.classFqn();
        Relation rb1 = MockTestUtils.relation(leftFqn, rightFqn);
        boolean isOutgoing1 = true;
        RelationWay rw1 = new RelationWay(rb1, isOutgoing1);
        // вызов системы
        // проверка утверждений
        // проверка утверждений
        Assert.assertEquals("Связь та, что задали при создании направления", rw1.getRelation(), rb1);
        // очистка
    }

    @Test
    public void test_isOutgoing()
    {
        // настройка системы
    	ClassFqn leftFqn = MockTestUtils.classFqn();
        ClassFqn rightFqn = MockTestUtils.classFqn();
        Relation rb1 = MockTestUtils.relation(leftFqn, rightFqn);
        boolean isOutgoing1 = true;
        RelationWay rw1 = new RelationWay(rb1, isOutgoing1);
        boolean isOutgoing2 = false;
        RelationWay rw2 = new RelationWay(rb1, isOutgoing2);
        // вызов системы
        // проверка утверждений
        // проверка утверждений
        Assert.assertEquals("Исходящее направление", rw1.isOutgoing(), isOutgoing1);
        Assert.assertEquals("Входящее направление", rw2.isOutgoing(), isOutgoing2);
        // очистка
    }

    /**
     * Проверяем, что отрабатывает конструктор
     */
    @Test
    public void test_RelationWay()
    {
        // настройка системы        
        // вызов системы
        new RelationWay();
        // проверка утверждений
        // очистка        
    }
}
