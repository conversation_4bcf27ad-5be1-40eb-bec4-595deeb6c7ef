package ru.naumen.metainfo.shared.relations;

import java.util.Arrays;
import java.util.List;

import org.junit.Test;

import ru.naumen.Assert;
import ru.naumen.core.shared.MockTestUtils;
import ru.naumen.core.shared.TestUtils;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.Relation;

/**
 * Юнит тесты на RelationIndexes
 * <AUTHOR>
 * @since 22.12.2010
 */
public class RelationIndexesJdkTest
{
    @Test
    public void test_getRelationsFor()
    {
        // настройка системы
        ClassFqn leftFqn = TestUtils.createClassFqn();
        ClassFqn rightFqn = TestUtils.createClassFqn();
        Relation relation = MockTestUtils.relation(leftFqn, rightFqn);
        List<Relation> relations = Arrays.asList(relation);
        RelationIndexes ri = new RelationIndexes(relations);
        // вызов системы
        // проверка утверждений
        Assert.assertContentEquals(relations, ri.getRelationsFor(leftFqn, true));
        Assert.assertContentEquals(relations, ri.getRelationsFor(rightFqn, false));

        Assert.assertContentEquals(relations, ri.getRelationsFor(leftFqn, true, relation.getType()));
        Assert.assertContentEquals(relations, ri.getRelationsFor(rightFqn, false, relation.getType()));

        Assert.assertContentEquals(relations, ri.getRelationsFor(leftFqn, relation.getType(), rightFqn));
        // очистка
    }

}
