package ru.naumen.metainfo.shared;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import org.junit.Assert;
import org.junit.Test;
import org.mockito.Mockito;

import ru.naumen.core.server.i18n.LocaleInfoImpl;
import ru.naumen.core.server.i18n.LocaleUtils;
import ru.naumen.core.shared.MockTestUtils;
import ru.naumen.metainfo.shared.Constants.ManyToOneRelation;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfo.shared.elements.Relation;

/**
 * <AUTHOR>
 */
public class MetainfoUtilsJdkTest
{
    @Test
    public void coverConstantClasses()
    {
        Object o = new Constants();
        o = new Constants.BackLinkAttributeType();
        o = new Constants.BOLinksAttributeType();
        o = new Constants.BooleanAttributeType();
        o = new Constants.CaseListAttributeType();
        o = new Constants.CatalogItemAttributeType();
        o = new Constants.CatalogItemsAttributeType();
        o = new Constants.ColorAttributeType();
        o = new Constants.DateAttributeType();
        o = new Constants.DateTimeAttributeType();
        o = new Constants.DateTimeIntervalAttributeType();
        o = new Constants.Dependence();
        o = new Constants.DoubleAttributeType();
        o = new Constants.FileAttributeType();
        o = new Constants.HyperlinkAttributeType();
        o = new Constants.IntegerAttributeType();
        o = new Constants.ManyToManyRelation();
        o = new Constants.ManyToOneRelation();
        o = new Constants.NullRelation();
        o = new Constants.ObjectAttributeType();
        o = new Constants.Presentations();
        o = new Constants.RichTextAttributeType();
        o = new Constants.Role();
        o = new Constants.StringAttributeType();
        o = new Constants.TextAttributeType();
        o = new Constants.UI();
        o = new Constants.UnknownAttributeType();
        o = new Constants.UUIDAttributeType();
        o = new Constants.UUIDRelation();
        o = o.toString();
    }

    @Test
    public void getClassHierarchy()
    {
        // настройка системы
        ClassFqn fqn1 = MockTestUtils.classFqn();
        ClassFqn fqn2 = MockTestUtils.classFqn();
        MetaClass clz1 = MockTestUtils.metaClass(fqn1);
        MetaClass clz2 = MockTestUtils.metaClass(fqn2, fqn1);

        List<MetaClassLite> expected = new ArrayList<MetaClassLite>();
        expected.add(clz2);
        expected.add(clz1);

        MetainfoUtils utils = new MetainfoUtils();
        // вызов системы
        List<MetaClassLite> actual = utils.getClassHierarchy(clz2.getFqn(), expected);
        // проверка утверждений
        Assert.assertEquals(expected, actual);
        // очистка
    }

    @Test
    public void getRelations()
    {
        // настройка системы
        Relation r1 = MockTestUtils.parentRelation();
        Relation r2 = MockTestUtils.parentRelation();
        List<Relation> relations = Arrays.asList(r1, r2);

        LocaleUtils localeUtils = Mockito.mock(LocaleUtils.class);

        MetainfoUtils utils = new MetainfoUtils();
        utils.localeInfo = new LocaleInfoImpl(localeUtils);
        // вызов системы
        Collection<Relation> result = utils.getRelations(relations, ManyToOneRelation.CODE, r1.getLeft().getId(), r1
                .getRight().getId());
        // проверка утверждений
        Assert.assertEquals(1, result.size());
        Assert.assertEquals(r1, result.iterator().next());
        // очистка
    }
}
