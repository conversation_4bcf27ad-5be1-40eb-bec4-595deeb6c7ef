package ru.naumen.metainfo.server.spi.serialization;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.List;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;

import com.google.common.collect.Sets;

import ru.naumen.metainfo.server.spi.elements.AttributeTypeImpl;
import ru.naumen.metainfo.server.spi.elements.AttributeTypeOverrideImpl;
import ru.naumen.metainfo.server.spi.store.AttributeType;
import ru.naumen.metainfo.server.spi.store.Property;

/**
 * 
 * <AUTHOR>
 * @since 9 марта 2016 г.
 */
public class DefaultAttrTypeSerializationStrategyJdkTest
{
    private DefaultAttrTypeSerializationStrategy strategy;

    @Before
    public void setUp()
    {
        strategy = new DefaultAttrTypeSerializationStrategy();
    }

    @Test
    public void testDeserialize()
    {
        AttributeType from = new AttributeType();
        from.setCode("typeCode");
        from.getProperty().add(new Property("property1", "property1-value"));
        from.getProperty().add(new Property("property2", "property2-value"));

        AttributeTypeImpl to = mock(AttributeTypeImpl.class);

        strategy.deserialize(from, to);

        verify(to).setCode("typeCode");
        verify(to).setProperty("property1", "property1-value");
        verify(to).setProperty("property2", "property2-value");

        Mockito.verifyNoMoreInteractions(to);
    }

    @Test
    public void testDeserializeOverride()
    {
        AttributeType from = new AttributeType();
        from.setCode("typeCode");
        from.getProperty().add(new Property("property1", "property1-value"));
        from.getProperty().add(new Property("property2", "property2-value"));

        AttributeTypeOverrideImpl to = mock(AttributeTypeOverrideImpl.class);

        strategy.deserialize(from, to);

        verify(to).setProperty("property1", "property1-value");
        verify(to).setProperty("property2", "property2-value");

        Mockito.verifyNoMoreInteractions(to);
    }

    @Test
    public void testSerialize()
    {
        AttributeTypeImpl from = mock(AttributeTypeImpl.class);
        when(from.getCode()).thenReturn("typeCode");
        when(from.propertyNames()).thenReturn(Sets.newHashSet("property1", "property2"));
        when(from.getProperty("property1")).thenReturn("property1-value");
        when(from.getProperty("property2")).thenReturn("property2-value");

        AttributeType to = new AttributeType();

        strategy.serialize(from, to);

        assertEquals(2, to.getProperty().size());
        assertEquals("property1-value", getValue(to.getProperty(), "property1"));
        assertEquals("property2-value", getValue(to.getProperty(), "property2"));
        assertEquals("typeCode", to.getCode());
    }

    @Test
    public void testSerializeOverride()
    {
        AttributeTypeOverrideImpl from = mock(AttributeTypeOverrideImpl.class);
        when(from.getCode()).thenReturn("typeCode");
        when(from.propertyNames()).thenReturn(Sets.newHashSet("property1", "property2"));
        when(from.getProperty("property1")).thenReturn("property1-value");
        when(from.getProperty("property2")).thenReturn("property2-value");

        AttributeType to = new AttributeType();

        strategy.serialize(from, to);

        assertEquals(2, to.getProperty().size());
        assertEquals("property1-value", getValue(to.getProperty(), "property1"));
        assertEquals("property2-value", getValue(to.getProperty(), "property2"));
        Assert.assertNull(to.getCode());
    }

    private Object getValue(List<Property> properties, String key)
    {
        for (Property property : properties)
        {
            if (property.getCode().equals(key))
            {
                return property.getValue();
            }
        }
        return null;
    }
}
