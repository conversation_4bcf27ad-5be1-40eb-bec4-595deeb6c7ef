package ru.naumen.metainfo.server.spi.elements.wf;

/**
 * 
 * <AUTHOR>
 *
 */
public interface TransactionalWorkflow
{
    ActionImpl addDeclaredPostAction(WorkflowImpl workflow, String state, String code);

    void addDeclaredPostCondition(WorkflowImpl workflow, String state, String code);

    void addDeclaredPreAction(WorkflowImpl workflow, String state, String code);

    void addDeclaredPreCondition(WorkflowImpl workflow, String state, String code);

    StateDeclarationImpl addStateDeclaration(WorkflowImpl workflow, String stateCode);

    StateDeclarationImpl addStateOverride(WorkflowImpl workflow, String stateCode);

    void addStateSetting(WorkflowImpl workflow, String stateCode, String code);

    TransitionImpl addTransition(WorkflowImpl workflow, String stateCodeFrom, String stateCodeTo);

    void setEndStateCode(WorkflowImpl workflow, String stateCode);

    void setOriginalStateCode(WorkflowImpl workflow, String stateCode);
}
