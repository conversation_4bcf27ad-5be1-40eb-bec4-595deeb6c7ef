package ru.naumen.metainfo.server.spi.dispatch;

import java.util.Collections;

import jakarta.inject.Inject;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import net.customware.gwt.dispatch.server.Dispatch;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.dispatch.ObjectTestUtils;
import ru.naumen.core.server.dispatch.SecurityTestHelper;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.utils.UUIDGenerator;
import ru.naumen.metainfo.server.ClassMetainfoServiceException;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.dispatch2.DelAttributeGroupAction;
import ru.naumen.metainfo.shared.elements.AttributeGroup;

/**
 * <AUTHOR>
 * @since 17.02.2011
 *
 */
@jakarta.transaction.Transactional
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class DelAttributeGroupActionHandlerDbTest
{
    ClassFqn fqn, parentFqn;

    @Inject
    Dispatch dispatch;

    @Inject
    MetainfoService metainfoService;

    @Inject
    ObjectTestUtils utils;

    @Test(expected = ClassMetainfoServiceException.class)
    public void delDeclaredInParentClass() throws Exception
    {
        //настройка системы
        utils.createAttributeGroup(parentFqn, Collections.<String> emptyList());
        //вызов системы
        dispatch.execute(new DelAttributeGroupAction(fqn, UUIDGenerator.get().nextUUID()));
        //проверка утверждений
        //очистка
    }

    @Test
    public void delExists() throws Exception
    {
        //настройка системы
        AttributeGroup grp = utils.createAttributeGroup(parentFqn, Collections.<String> emptyList());
        //вызов системы
        String code = grp.getCode();
        dispatch.execute(new DelAttributeGroupAction(parentFqn, code));
        //проверка утверждений
        try
        {
            metainfoService.getMetaClass(parentFqn).getAttributeGroup(code);
            metainfoService.getMetaClass(fqn).getAttributeGroup(code);
        }
        catch (ClassMetainfoServiceException e)
        {
            return;
        }
        throw new FxException("Группа не удалена из метакласса или его дочернего метакласса");
        //очистка
    }

    @Test(expected = ClassMetainfoServiceException.class)
    public void delNotExists() throws Exception
    {
        //настройка системы
        ClassFqn fqn = ClassFqn.parse(Constants.Employee.CLASS_ID);
        //вызов системы
        dispatch.execute(new DelAttributeGroupAction(fqn, UUIDGenerator.get().nextUUID()));
        //проверка утверждений
        //очистка
    }

    @Before
    public void setUp() throws Exception
    {
        SecurityTestHelper.autenticateAsSuperUser();

        if (fqn == null)
        {
            parentFqn = ClassFqn.parse(Constants.Employee.CLASS_ID);
            fqn = utils.getDefaultEmployeeCase();
        }
    }

}
