package ru.naumen.metainfo.server.spi;

import jakarta.transaction.Transactional;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import com.google.common.collect.Lists;

import jakarta.inject.Inject;
import ru.naumen.core.server.dispatch.ObjectTestUtils;
import ru.naumen.core.server.dispatch.SecurityTestHelper;
import ru.naumen.core.server.jta.TransactionInvocation;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.MockTestUtils;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.server.spi.elements.DependenceImpl;
import ru.naumen.metainfo.server.spi.elements.MetaClassImpl;
import ru.naumen.metainfo.shared.ClassFqn;

/**
 * <AUTHOR>
 */
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class RelationsInitializerTest
{
    @Inject
    RelationsInitializer relationsInitializer;
    @Inject
    ObjectTestUtils utils;

    private MetainfoService transactionalMetainfoService;

    @Transactional
    @Test
    public void addRelation() throws Exception
    {
        int size = transactionalMetainfoService.getRelations().size();

        ClassFqn fqn = utils.createCase(ClassFqn.parse(Constants.Employee.CLASS_ID));
        MetaClassImpl metaClass = (MetaClassImpl)transactionalMetainfoService.getMetaClass(fqn);

        relationsInitializer.createManyToManyRelation(metaClass, MockTestUtils.objectAttribute("manyToMany", fqn),
                Lists.<DependenceImpl> newArrayList());
        size++;
        relationsInitializer.createManyToOneRelation(metaClass, MockTestUtils.objectAttribute("manyToOne", fqn),
                Lists.<DependenceImpl> newArrayList());
        size++;
        relationsInitializer.createNullRelation(metaClass);
        size++;
        relationsInitializer.createUUIDLinkRelation(metaClass, MockTestUtils.objectAttribute("uuid", fqn),
                Lists.<DependenceImpl> newArrayList());
        size += 2;

        org.junit.Assert.assertEquals(transactionalMetainfoService.getRelations().size(), size);
    }

    @Before
    public void setUp()
    {
        SecurityTestHelper.autenticateAsSuperUser();
        transactionalMetainfoService = TransactionInvocation.build(MetainfoService.class);
    }
}