package ru.naumen.metainfo.server.vcs.git;

import static org.apache.commons.lang3.RandomStringUtils.randomAlphabetic;
import static org.junit.Assert.assertEquals;

import java.io.ByteArrayOutputStream;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashSet;
import java.util.Set;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.Parameterized;
import org.junit.runners.Parameterized.Parameters;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.ui.ChildObjectList;
import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.metainfo.shared.ui.FileList;
import ru.naumen.metainfo.shared.ui.HasObjectActionsMenu;
import ru.naumen.metainfo.shared.ui.ObjectActionsMenuHolder;
import ru.naumen.metainfo.shared.ui.ObjectList;
import ru.naumen.metainfo.shared.ui.RelObjectList;
import ru.naumen.metainfo.shared.ui.ToolPanel;
import ru.naumen.metainfo.shared.ui.UIContainer;

@RunWith(Parameterized.class)
public class GitMetainfoContentObjectActionsTest<T extends Content & HasObjectActionsMenu> extends BaseGitMetainfoServiceXmlTest
{
    @Parameters(name = "Content {0}, systemSettings = {1}, expectedTags = {2}")
    public static Collection<Object[]> data()
    {
        Set<String> systemSettingsTags = Set.of("useSystemSettings");
        Set<String> nonSystemSettingsTags = Set.of("useSystemSettings", "include", "objectActionsMenuPosition",
                "menuIconCatalogCode");
        return Arrays.asList(new Object[][] {
                { new RelObjectList(), true, systemSettingsTags },
                { new RelObjectList(), false, nonSystemSettingsTags },
                { new FileList(), true, systemSettingsTags },
                { new FileList(), false, nonSystemSettingsTags },
                { new ChildObjectList(), true, systemSettingsTags },
                { new ChildObjectList(), false, nonSystemSettingsTags },
                { new ObjectList(), true, systemSettingsTags },
                { new ObjectList(), false, nonSystemSettingsTags }
        });
    }

    private final T content;
    private final ObjectActionsMenuHolder objectActionsMenu;
    private final Set<String> expectedTags;
    private final UIContainer uiContainer;

    /**
     *
     * @param content тестируемый контент
     * @param useSystemSettings исползьовать ли системную настройку панели действий
     * @param expectedTags ожидаемый набор тегов в настройке панелий действий после экспорта
     */
    public GitMetainfoContentObjectActionsTest(T content, boolean useSystemSettings, Set<String> expectedTags)
    {
        super();
        this.content = content;
        this.objectActionsMenu = createObjectActionsMenu(useSystemSettings);
        this.expectedTags = expectedTags;

        uiContainer = new UIContainer();
        final String str = randomAlphabetic(8);
        uiContainer.setFqn(ClassFqn.parse(str));
        uiContainer.setCode(str);
    }

    @Test
    public void testContentObjectActions() throws Exception
    {
        content.setUuid(randomAlphabetic(8));
        content.setObjectActionsMenu(objectActionsMenu);
        uiContainer.setContent(content);
        container.getUi().add(uiContainer);

        final ByteArrayOutputStream stream = new ByteArrayOutputStream();
        setupSegmentOutputStream(stream, content.getSegmentID());

        gitMetainfoService.exportToVCS(branch, branch, "full");
        final NodeList nodes = getNodes(stream.toByteArray(), "//objectActionsMenu");
        assertEquals(1, nodes.getLength());
        final Node item = nodes.item(0);
        final NodeList childNodes = item.getChildNodes();
        final int length = childNodes.getLength();
        Set<String> childTags = new HashSet<>();

        for (int i = 0; i < length; i++)
        {
            final String localName = childNodes.item(i).getLocalName();
            if (localName != null)
            {
                childTags.add(localName);
            }
        }
        assertEquals(expectedTags, childTags);
    }

    private ObjectActionsMenuHolder createObjectActionsMenu(boolean useSystemSettings)
    {
        final ObjectActionsMenuHolder objectActionsMenuHolder = new ObjectActionsMenuHolder();
        objectActionsMenuHolder.setUseSystemSettings(useSystemSettings);
        objectActionsMenuHolder.setMenuIconCatalogCode(randomAlphabetic(8));
        objectActionsMenuHolder.setObjectActionsMenuPosition(randomAlphabetic(8));
        objectActionsMenuHolder.setObjectActionsToolPanel(new ToolPanel());
        return objectActionsMenuHolder;
    }
}
