package ru.naumen.metainfo.server.spi.dispatch;

import jakarta.inject.Inject;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.transaction.annotation.Transactional;

import net.customware.gwt.dispatch.server.Dispatch;
import ru.naumen.core.server.dispatch.ObjectTestUtils;
import ru.naumen.core.server.dispatch.SecurityTestHelper;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.TestUtils;
import ru.naumen.core.shared.utils.UUIDGenerator;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.server.spi.MetainfoServiceBean;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.UI;
import ru.naumen.metainfo.shared.dispatch2.ResetUIAction;
import ru.naumen.metainfo.shared.ui.Layout;
import ru.naumen.metainfo.shared.ui.LocalizedString;
import ru.naumen.metainfo.shared.ui.Tab;
import ru.naumen.metainfo.shared.ui.TabBar;

/**
 * <AUTHOR>
 * @since 18.02.2011
 *
 */
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class ResetUIActionHandlerDbTest
{
    @Inject
    Dispatch dispatch;
    @Inject
    MetainfoService metainfoService;
    @Inject
    ObjectTestUtils utils;
    @Inject
    MetainfoServiceBean metainfoServiceBean;

    @Test
    @Transactional
    public void reset() throws Exception
    {
        //настройка системы
        String code = TestUtils.randomString();
        TabBar ui = new TabBar();
        ui.setUuid(TabBar.PREFIX + code);
        Tab tab = new Tab();
        tab.setUuid(code);
        ui.getTab().add(tab);
        tab.getCaption().add(new LocalizedString("ru", UUIDGenerator.get().nextUUID()));
        tab.setLayout(new Layout());

        ClassFqn parentFqn = ClassFqn.parse(Constants.Employee.CLASS_ID);
        metainfoServiceBean.setUIForm(parentFqn, UI.Form.EDIT, ui, true, true);
        ClassFqn fqn = utils.createCase(parentFqn);
        metainfoServiceBean.setUIForm(fqn, UI.Form.EDIT, new Layout(), true, false);
        Assert.assertEquals(fqn, metainfoService.getUiForm(fqn, UI.Form.EDIT).getDeclaredMetaclass());
        //вызов системы
        dispatch.execute(new ResetUIAction(fqn, UI.Form.EDIT));
        //проверка утверждений
        Assert.assertEquals(parentFqn, metainfoService.getUiForm(fqn, UI.Form.EDIT).getDeclaredMetaclass());
        //очистка

    }

    @Before
    public void setUp()
    {
        SecurityTestHelper.autenticateAsSuperUser();
    }
}
