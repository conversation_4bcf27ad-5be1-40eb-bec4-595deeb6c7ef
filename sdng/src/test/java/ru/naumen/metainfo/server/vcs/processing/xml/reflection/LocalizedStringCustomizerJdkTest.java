package ru.naumen.metainfo.server.vcs.processing.xml.reflection;

import java.lang.reflect.Method;
import java.util.List;

import org.junit.Test;

import ru.naumen.metainfo.shared.ui.LocalizedString;

public class LocalizedStringCustomizerJdkTest
{
    @Test
    public void testNullInput() throws NoSuchMethodException
    {
        final LocalizedStringCustomizerFactory localizedStringCustomizerFactory =
                new LocalizedStringCustomizerFactory();
        localizedStringCustomizerFactory.getReturnValueConsumer().accept(null);
    }
}