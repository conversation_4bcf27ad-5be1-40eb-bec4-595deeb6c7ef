package ru.naumen.metainfo.server.vcs.git;

import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import java.io.ByteArrayOutputStream;
import java.io.OutputStream;
import java.nio.file.Paths;
import java.util.Comparator;

import javax.xml.xpath.XPath;
import javax.xml.xpath.XPathConstants;
import javax.xml.xpath.XPathFactory;

import org.apache.commons.collections4.iterators.NodeListIterator;
import org.junit.Assert;
import org.junit.Before;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import com.google.common.collect.Comparators;

import ru.naumen.commons.server.utils.XmlUtils;
import ru.naumen.core.server.ConfigurationProperties;
import ru.naumen.core.server.git.GitRepositoryManager;
import ru.naumen.core.server.git.GitRepositoryManager.RepositoryControl;
import ru.naumen.core.server.git.RepositoryFileManager;
import ru.naumen.core.server.git.RepositoryOperations;
import ru.naumen.metainfo.server.spi.MetainfoContainer;
import ru.naumen.metainfo.server.spi.MetainfoContainer.Header;
import ru.naumen.metainfo.server.spi.MetainfoExportSource;
import ru.naumen.metainfo.server.spi.importing.MetainfoImportService;

/**
 * Базовый класс для тестов сортировки XML элементов при экспорте в гит
 *
 * <AUTHOR>
 * @since 18.03.2020
 */
public class BaseGitMetainfoServiceXmlTest
{
    private static final XPath X_PATH = XPathFactory.newInstance().newXPath();

    protected GitMetainfoService gitMetainfoService;

    protected MetainfoContainer container;
    protected String branch = "branch";
    private final MetainfoExportSource exportSource = mock(MetainfoExportSource.class);
    private final MetainfoImportService metainfoImportService = mock(MetainfoImportService.class);
    private final GitRepositoryManager gitRepositoryManager = mock(GitRepositoryManager.class);
    private final RepositoryControl repo = mock(RepositoryControl.class);
    private final RepositoryOperations repositoryOperations = mock(RepositoryOperations.class);
    private final RepositoryFileManager repositoryFileManager = mock(RepositoryFileManager.class);
    private final ConfigurationProperties configurationProperties = mock(ConfigurationProperties.class);

    @Before
    public void setUpGitManager()
    {
        container = new MetainfoContainer();
        container.setHead(new Header());

        when(exportSource.exportObject(anyString())).thenReturn(container);
        when(gitRepositoryManager.acquireRepositoryControl()).thenReturn(repo);
        when(repo.operations()).thenReturn(repositoryOperations);
        when(repo.fileManager()).thenReturn(repositoryFileManager);

        when(repositoryOperations.getCurrentBranchName()).thenReturn(branch);
        when(repositoryOperations.isPullPossibleForCurrentBranch()).thenReturn(false);
        when(configurationProperties.isProcessingExternalEntityInXML()).thenReturn(false);

        gitMetainfoService = new GitMetainfoService(gitRepositoryManager, exportSource,
                new XmlUtils(), metainfoImportService, configurationProperties);
    }

    protected void setupSegmentOutputStream(ByteArrayOutputStream stream, String segmentID)
    {
        when(repositoryFileManager.createOutput(anyString())).thenAnswer(invocation ->
        {
            String argument = (String)invocation.getArguments()[0];
            if (!Paths.get(argument).getFileName().toString().contains(segmentID))
            {
                return OutputStream.nullOutputStream();
            }
            return stream;
        });
    }

    /**
     * Проверить сортировку XML элементов по заданному XPath
     * @param xmlBytes данные XML, из которых будет создан документ
     * @param expression xpath выражение для поиска узлов. Из найденных узлов будет получено текстовое содержимое
     */
    protected static void assertElementsAscendingSort(byte[] xmlBytes, String... expression) throws Exception
    {
        assertElementSort(xmlBytes, Comparator.comparing(Node::getTextContent), expression);
    }

    protected static void assertElementSort(byte[] xmlBytes,
            Comparator<Node> comparator, String... expressions) throws Exception
    {
        Document doc = XmlUtils.getDocument(xmlBytes, false);
        for (String expression : expressions)
        {
            final NodeList elements = (NodeList)X_PATH.compile(expression).evaluate(doc, XPathConstants.NODESET);
            Iterable<Node> nodes = () -> new NodeListIterator(elements);
            boolean inOrder = Comparators.isInOrder(nodes, comparator);
            Assert.assertTrue("Elements for expression " + expression + " are not sorted properly.", inOrder);
        }
    }

    protected static NodeList getNodes(byte[] xmlBytes, String expression) throws Exception
    {
        final Document doc = XmlUtils.getDocument(xmlBytes, false);
        return (NodeList)X_PATH.compile(expression).evaluate(doc, XPathConstants.NODESET);
    }

}
