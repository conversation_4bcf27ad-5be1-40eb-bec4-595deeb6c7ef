package ru.naumen.metainfo.server.vcs.processing.container;

import static org.junit.Assert.assertTrue;

import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.xml.xpath.XPath;
import javax.xml.xpath.XPathConstants;
import javax.xml.xpath.XPathExpressionException;
import javax.xml.xpath.XPathFactory;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.Parameterized;
import org.junit.runners.Parameterized.Parameters;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import ru.naumen.commons.server.utils.XmlUtils;
import ru.naumen.commons.shared.utils.Pair;
import ru.naumen.core.server.customforms.storage.CustomFormInfo;
import ru.naumen.core.server.customforms.storage.FormParameterInfo;
import ru.naumen.metainfo.server.spi.MetainfoContainer;
import ru.naumen.metainfo.shared.AdaptedClassFqn;

@RunWith(Parameterized.class)
public class FormParametersProcessorMapSortJdkTest
{
    @Parameters(name = "Pair list {0}")
    public static Collection<Object[]> parameters()
    {   //Простой случай
        List<Pair<String, ?>> simpleData = List.of(Pair.create("ou", "ouCase"), Pair.create("ou", null));
        //Ручное перечисление часто используемых FQN для агрегирующих атрибутов
        List<Pair<String, ?>> typicalAggregateData = List.of(
                Pair.create("ou", null),
                Pair.create("employee", "emplCase"),
                Pair.create("ou", "ouCase"),
                Pair.create("team", null),
                Pair.create("employee", null),
                Pair.create("team", "teamCase")
        );
        return Arrays.asList(new Object[][] { { simpleData }, { typicalAggregateData } });
    }

    private final List<Pair<String, String>> idCasePairs;

    public FormParametersProcessorMapSortJdkTest(List<Pair<String, String>> idCasePairs)
    {
        this.idCasePairs = idCasePairs;
    }

    @Test
    public void processBeforeExport() throws XPathExpressionException
    {
        CustomFormInfo customFormInfo = new CustomFormInfo();
        FormParameterInfo formParameterInfo = new FormParameterInfo();
        Map<AdaptedClassFqn, String> complexRelationAggrAttrGroups = new HashMap<>();
        for (Pair<String, String> p : idCasePairs)
        {
            AdaptedClassFqn adaptedClassFqn = new AdaptedClassFqn();
            adaptedClassFqn.setId(p.left);
            adaptedClassFqn.setCase(p.right);
            complexRelationAggrAttrGroups.put(adaptedClassFqn, adaptedClassFqn.toString());
        }
        formParameterInfo.setComplexRelationAggrAttrGroups(complexRelationAggrAttrGroups);
        customFormInfo.getParameters().add(formParameterInfo);

        MetainfoContainer container = new MetainfoContainer();
        container.getCustomForms().add(customFormInfo);

        FormParametersContainerCustomizer formParametersProcessor = new FormParametersContainerCustomizer();
        formParametersProcessor.customize(container);

        Document document = new XmlUtils().toDocument(container, false);
        XPath xPath = XPathFactory.newInstance().newXPath();
        NodeList evaluate = (NodeList)xPath.compile("//complexRelationAggrAttrGroups//key").evaluate(document,
                XPathConstants.NODESET);
        for (int i = 0; i < evaluate.getLength() - 1; i++)
        {
            int j = i;
            String textContent = getTextContent(evaluate.item(i));
            String nextTextContent = getTextContent(evaluate.item(++j));
            String msg =
                    "Text content {" + textContent + "} must be lexicographically less than {" + nextTextContent + '}';
            assertTrue(msg, textContent.compareTo(nextTextContent) < 0);
        }
    }

    private String getTextContent(Node item)
    {
        NodeList childNodes = item.getChildNodes();
        int length = childNodes.getLength();
        assertTrue(length > 0 && length <= 2);
        String idTextContent = childNodes.item(0).getTextContent();
        return length == 2 ? idTextContent + childNodes.item(1).getTextContent() : idTextContent;
    }

}