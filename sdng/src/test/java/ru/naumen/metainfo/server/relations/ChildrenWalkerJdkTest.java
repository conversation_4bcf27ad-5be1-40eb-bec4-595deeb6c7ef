package ru.naumen.metainfo.server.relations;

import java.util.Arrays;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import ru.naumen.common.shared.utils.IClosure;
import ru.naumen.core.server.bo.DaoFactory;
import ru.naumen.core.server.bo.IChildDao;
import ru.naumen.core.shared.IChild;
import ru.naumen.core.shared.IHasMetaInfo;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.elements.Relation;

public class ChildrenWalkerJdkTest
{
    private interface Helper extends IUUIDIdentifiable, IHasMetaInfo, IChild<Helper>
    {

    }

    @Mock
    MetainfoService service;
    @Mock
    MetainfoUtils utils;
    @Mock
    DaoFactory factory;
    @Mock
    Helper parent;
    @Mock
    Helper child1;
    @Mock
    Helper child2;

    ChildrenWalker<Helper> walker;

    @Before
    public void setUp() throws Exception
    {
        MockitoAnnotations.initMocks(this);
        ClassFqn pFqn = Mockito.mock(ClassFqn.class);
        Mockito.when(service.getClassFqn(Mockito.same(parent))).thenReturn(pFqn);
        ClassFqn cFqn = Mockito.mock(ClassFqn.class);

        Relation rel = Mockito.mock(Relation.class);
        Mockito.when(rel.getLeft()).thenReturn(cFqn);

        Mockito.when(utils.getIncomingParentRelations(Mockito.anyCollection(), Mockito.eq(pFqn)))
            .thenReturn(Arrays.asList(rel));
        
        IChildDao dao = Mockito.mock(IChildDao.class);
        Mockito.when(dao.list(Mockito.<IUUIDIdentifiable> any())).thenReturn(Arrays.asList(child1, child2));
        Mockito.when(factory.<IChildDao> get(Mockito.eq(cFqn))).thenReturn(dao);

        setUpWalker();
    }

    @After
    public void tearDown() throws Exception
    {
        walker = null;
    }

    @Test
    public void walk()
    {
        //настройка
        IClosure<Helper> closure = Mockito.mock(IClosure.class);
        //вызов
        walker.walk(closure, parent);
        //проверка
        Mockito.verify(closure).execute(Mockito.eq(child1));
        Mockito.verify(closure).execute(Mockito.eq(child2));
        Mockito.verifyNoMoreInteractions(closure);
    }

    private void setUpWalker()
    {
        walker = new ChildrenWalker<Helper>();
        walker.factory = factory;
        walker.metainfoService = service;
        walker.metainfoUtils = utils;
    }
}
