package ru.naumen.metainfo.server.spi.dispatch;

import java.util.Arrays;
import java.util.List;

import jakarta.inject.Inject;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.transaction.annotation.Transactional;

import net.customware.gwt.dispatch.server.Dispatch;
import ru.naumen.core.server.dispatch.ObjectTestUtils;
import ru.naumen.core.server.dispatch.SecurityTestHelper;
import ru.naumen.core.shared.Constants;
import ru.naumen.metainfo.server.ClassMetainfoServiceException;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.dispatch2.RestoreClassMetainfoAction;
import ru.naumen.metainfo.shared.elements.MetaClassLite.Status;

/**
 * <AUTHOR>
 * @since 21.02.2011
 *
 */
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class RestoreClassMetainfoActionHandlerDbTest
{
    @Inject
    Dispatch dispatch;
    @Inject
    MetainfoService metainfoService;
    @Inject
    ObjectTestUtils utils;

    @Test
    @Transactional
    public void restoreArchived() throws Exception
    {
        //настройка системы
        ClassFqn fqn = utils.createCase(ClassFqn.parse(Constants.Employee.CLASS_ID));
        utils.archiveCase(fqn);
        //вызов системы
        dispatch.execute(new RestoreClassMetainfoAction(fqn));
        //проверка утверждений
        Assert.assertEquals(Status.DEFAULT, metainfoService.getMetaClass(fqn).getStatus());
        //очистка
    }

    @Test
    @Transactional
    public void restoreNotArchived() throws Exception
    {
        //настройка системы
        ClassFqn fqn = utils.createCase(ClassFqn.parse(Constants.Employee.CLASS_ID));
        //вызов системы
        dispatch.execute(new RestoreClassMetainfoAction(fqn));
        //проверка утверждений
        Assert.assertEquals(Status.DEFAULT, metainfoService.getMetaClass(fqn).getStatus());
        //очистка
    }

    @Test(expected = ClassMetainfoServiceException.class)
    @Transactional
    public void restoreToArchivedParent() throws Exception
    {
        //настройка системы
        ClassFqn parentFqn = utils.createCase(ClassFqn.parse(Constants.Employee.CLASS_ID));
        ClassFqn fqn = utils.createCase(parentFqn);
        utils.archiveCase(parentFqn);
        //вызов системы
        dispatch.execute(new RestoreClassMetainfoAction(fqn));
        //проверка утверждений
        //очистка
    }

    @Test
    @Transactional
    public void restoreWithChildren() throws Exception
    {
        //настройка системы
        ClassFqn parentFqn = utils.createCase(ClassFqn.parse(Constants.Employee.CLASS_ID));
        ClassFqn fqn1 = utils.createCase(parentFqn);
        ClassFqn fqn2 = utils.createCase(parentFqn);
        ClassFqn fqn11 = utils.createCase(fqn1);
        utils.archiveCase(parentFqn);
        //вызов системы
        dispatch.execute(new RestoreClassMetainfoAction(parentFqn));
        //проверка утверждений
        List<ClassFqn> fqns = Arrays.asList(parentFqn, fqn1, fqn2, fqn11);
        for (ClassFqn fqn : fqns)
        {
            Assert.assertEquals(Status.DEFAULT, metainfoService.getMetaClass(fqn).getStatus());
        }
        //очистка
    }

    @Before
    public void setUp()
    {
        SecurityTestHelper.autenticateAsSuperUser();
    }
}
