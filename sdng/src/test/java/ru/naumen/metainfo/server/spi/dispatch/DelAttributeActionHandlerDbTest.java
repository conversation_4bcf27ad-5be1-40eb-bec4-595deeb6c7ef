package ru.naumen.metainfo.server.spi.dispatch;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

import java.util.Arrays;

import jakarta.inject.Inject;

import org.junit.AfterClass;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.transaction.annotation.Transactional;

import net.customware.gwt.dispatch.server.Dispatch;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.server.dispatch.ObjectTestUtils;
import ru.naumen.core.server.dispatch.SecurityTestHelper;
import ru.naumen.core.server.dispatch.SecurityTestUtils;
import ru.naumen.core.server.jta.TransactionRunner;
import ru.naumen.core.server.jta.TransactionRunner.TransactionType;
import ru.naumen.core.server.script.storage.ScriptStorageService;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.utils.UUIDGenerator;
import ru.naumen.metainfo.server.ClassMetainfoServiceException;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.dispatch2.DelAttributeAction;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.script.Script;
import ru.naumen.metainfo.shared.script.ScriptDto;

/**
 * <AUTHOR>
 * @since 17.02.2011
 *
 */
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class DelAttributeActionHandlerDbTest
{
    private static final ClassFqn EMPLOYEE_FQN = ClassFqn.parse(Constants.Employee.CLASS_ID);

    private static ClassFqn emplCase;

    @AfterClass
    public static void afterClass()
    {
        emplCase = null;
    }

    @Inject
    private Dispatch dispatch;
    @Inject
    private MetainfoService metainfoService;
    @Inject
    private ObjectTestUtils utils;

    @Inject
    private SecurityTestUtils securityTestUtils;

    @Inject
    private ScriptStorageService scriptStorageService;

    @Transactional
    @Test(expected = ClassMetainfoServiceException.class)
    public void delExists() throws Exception
    {
        //настройка системы
        ClassFqn fqn = EMPLOYEE_FQN;
        String attr = utils.createAttribute(fqn);
        //вызов системы
        dispatch.execute(new DelAttributeAction(fqn, attr));
        //проверка утверждений
        metainfoService.getMetaClass(fqn).getAttribute(attr);

        //очистка
    }

    @Test(expected = ClassMetainfoServiceException.class)
    @Transactional
    public void delHardCoded() throws Exception
    {
        //настройка системы
        ClassFqn fqn = EMPLOYEE_FQN;
        //вызов системы
        dispatch.execute(new DelAttributeAction(fqn, Constants.Employee.FIRST_NAME));
        //проверка утверждений
        //очистка
    }

    @Test(expected = ClassMetainfoServiceException.class)
    @Transactional
    public void delInGroup() throws Exception
    {
        //настройка системы
        ClassFqn fqn = EMPLOYEE_FQN;
        String attr = utils.createAttribute(fqn);
        utils.createAttributeGroup(fqn, Arrays.asList(attr));
        //вызов системы
        dispatch.execute(new DelAttributeAction(fqn, attr));
        //проверка утверждений
        //очистка
    }

    @Test(expected = ClassMetainfoServiceException.class)
    @Transactional
    public void delNotExists() throws Exception
    {
        //настройка системы
        ClassFqn fqn = EMPLOYEE_FQN;
        //вызов системы
        dispatch.execute(new DelAttributeAction(fqn, UUIDGenerator.get().nextUUID()));
        //проверка утверждений
        //очистка
    }

    @Before
    public void setUp() throws Exception
    {
        SecurityTestHelper.autenticateAsSuperUser();

        if (emplCase == null)
        {
            TransactionRunner.call(TransactionType.NEW, () -> {
                emplCase = utils.createCase(EMPLOYEE_FQN);
                return null;
            });
        }
    }

    /**
     * Проверка на то, что после удаления атрибута, содержащего скрипт вычисления значения атрибута при редактировании,
     * информация о скрипте не удаляется из кэша информации о скриптах, а информация об атрибуте
     * удаляется из мест использования скрипта.
     *  
     * @throws Exception
     */
    @Test
    @Transactional
    public void testDeclaredAttributeComputableOnFormScriptInfoShouldBeObliteratedOnDelete() throws Exception
    {
        //настройка системы
        ClassFqn fqn = emplCase;
        String title = UUIDGenerator.get().nextUUID();
        String description = UUIDGenerator.get().nextUUID();
        ScriptDto scriptDto = utils.createScriptDto("return []");
        String code = utils.createAttribute(fqn, true, false, false, title, description, false, null, true, scriptDto,
                false, null);
        MetaClass metaClass = metainfoService.getMetaClass(fqn);
        Attribute attribute = metaClass.getAttribute(code);
        String scriptCode = attribute.getComputableOnFormScript();

        assertInfoDeletion(fqn, attribute, scriptCode);
    }

    /**
     * Проверка на то, что после удаления атрибута, содержащего скрипт вычисления значения атрибута,
     * информация о скрипте не удаляется из кэша информации о скриптах, а информация об атрибуте
     * удаляется из мест использования скрипта.
     * 
     * @throws Exception
     */
    @Test
    @Transactional
    public void testDeclaredAttributeComputableScriptShouldBeObliteratedOnDelete() throws Exception
    {
        //настройка системы
        ClassFqn fqn = emplCase;
        String title = UUIDGenerator.get().nextUUID();
        String description = UUIDGenerator.get().nextUUID();
        ScriptDto scriptDto = utils.createScriptDto("'asd'");
        String code = utils.createAttribute(fqn, true, false, false, title, description, true, scriptDto, false, null,
                false, null);
        MetaClass metaClass = metainfoService.getMetaClass(fqn);
        Attribute attribute = metaClass.getAttribute(code);
        String scriptCode = attribute.getScript();

        assertInfoDeletion(fqn, attribute, scriptCode);
    }

    /**
     * Проверка на то, что после удаления атрибута, содержащего скрипт вычисления значения атрибута по-умолчанию,
     * информация о скрипте не удаляется из кэша информации о скриптах, а информация об атрибуте
     * удаляется из мест использования скрипта.
     * 
     * @throws Exception
     */
    @Test
    @Transactional
    public void testDeclaredAttributeScriptForDefaultShouldBeObliteratedOnDelete() throws Exception
    {
        //настройка системы
        ClassFqn fqn = emplCase;
        String title = UUIDGenerator.get().nextUUID();
        String description = UUIDGenerator.get().nextUUID();
        ScriptDto scriptDto = utils.createScriptDto("return 'asd'");
        String code = utils.createAttribute(fqn, true, false, false, title, description, false, null, false, null,
                true, scriptDto);
        MetaClass metaClass = metainfoService.getMetaClass(fqn);
        Attribute attribute = metaClass.getAttribute(code);
        String scriptCode = attribute.getScriptForDefault();

        assertInfoDeletion(fqn, attribute, scriptCode);
    }

    /**
     * Проверка на то, что после удаления атрибута, содержащего скрипт фильтрации значений атрибута,
     * информация о скрипте не удаляется из кэша информации о скриптах, а информация об атрибуте
     * удаляется из мест использования скрипта.
     * 
     * @throws Exception
     */
    @Test
    @Transactional
    public void testDeclaredAttributeScriptForFiltrationShouldBeObliteratedOnDelete() throws Exception
    {
        securityTestUtils.initLicensing();
        ClassFqn fqn = emplCase;
        ScriptDto scriptDto = utils.createScriptDto("return []");
        String code = utils.createAggregateOUAttribute(fqn, null, true, scriptDto);
        MetaClass metaClass = metainfoService.getMetaClass(fqn);
        Attribute attribute = metaClass.getAttribute(code);
        String scriptCode = attribute.getScriptForFiltration();

        assertInfoDeletion(fqn, attribute, scriptCode);

    }

    private void assertInfoDeletion(ClassFqn fqn, Attribute attribute, String scriptCode) throws DispatchException
    {
        dispatch.execute(new DelAttributeAction(fqn, attribute.getCode()));
        Script script = scriptStorageService.getScript(scriptCode);
        assertNotNull("Информация о скрипте не должна быть удалена из кэша информации о скриптах.", script);
        assertTrue("Информация об атрибуте должна быть удалена из мест использования скрипта.",
                CollectionUtils.isEmpty(script.getUsagePoints()));

        //Очистка
        utils.deleteUnusedScript(scriptCode);
    }
}
