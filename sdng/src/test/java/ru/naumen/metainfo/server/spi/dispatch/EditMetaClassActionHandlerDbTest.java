package ru.naumen.metainfo.server.spi.dispatch;

import java.util.ArrayList;
import java.util.Collections;

import jakarta.inject.Inject;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.transaction.annotation.Transactional;

import net.customware.gwt.dispatch.server.Dispatch;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.commons.server.utils.RandomTools;
import ru.naumen.core.server.dispatch.ObjectTestUtils;
import ru.naumen.core.server.dispatch.SecurityTestHelper;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.utils.UUIDGenerator;
import ru.naumen.metainfo.server.ClassMetainfoServiceException;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.dispatch2.EditMetaClassAction;
import ru.naumen.metainfo.shared.elements.MetaClass;

/**
 * <AUTHOR>
 * @since 18.02.2011
 *
 */
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class EditMetaClassActionHandlerDbTest
{
    @Inject
    Dispatch dispatch;
    @Inject
    MetainfoService metainfoService;
    @Inject
    ObjectTestUtils utils;

    @Test
    @Transactional
    public void editExists() throws Exception
    {
        //настройка системы
        ClassFqn fqn = ClassFqn.parse(Constants.Employee.CLASS_ID);
        String title = UUIDGenerator.get().nextUUID();
        String description = UUIDGenerator.get().nextUUID();
        //вызов системы       
        dispatch.execute(new EditMetaClassAction(fqn, title, description, Collections.<String, String> emptyMap(),
                IProperties.EMPTY, new ArrayList<>()));
        //проверка утверждений
        MetaClass metaclass = metainfoService.getMetaClass(fqn);
        Assert.assertEquals(title, metaclass.getTitle());
        Assert.assertEquals(description, metaclass.getDescription());
        //очистка
    }

    @Test(expected = ClassMetainfoServiceException.class)
    @Transactional
    public void editNotExists() throws Exception
    {
        //настройка системы
        //вызов системы
        dispatch.execute(new EditMetaClassAction(ClassFqn.parse("n" + RandomTools.getIntRandom()), UUIDGenerator.get()
                .nextUUID(), UUIDGenerator.get().nextUUID(), Collections.<String, String> emptyMap(), IProperties.EMPTY,
                new ArrayList<>()));
        //проверка утверждений
        //очистка
    }

    @Before
    public void setUp()
    {
        SecurityTestHelper.autenticateAsSuperUser();
    }
}
