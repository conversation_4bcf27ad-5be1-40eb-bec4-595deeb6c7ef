package ru.naumen.metainfo.server.spi;

import static java.util.concurrent.CompletableFuture.runAsync;

import java.util.concurrent.CompletableFuture;

import jakarta.inject.Inject;

import com.google.common.collect.Lists;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import net.customware.gwt.dispatch.server.Dispatch;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.commons.shared.FxException;
import ru.naumen.sec.server.autorize.AuthorizationRunnerService;
import ru.naumen.core.server.bo.AbstractBO;
import ru.naumen.core.server.bo.ou.OU;
import ru.naumen.core.server.dispatch.ObjectTestUtils;
import ru.naumen.core.server.dispatch.SecurityTestUtils;
import ru.naumen.core.server.jta.TransactionRunner;
import ru.naumen.core.server.jta.TransactionRunner.TransactionType;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.dispatch.GetDtObjectAction;
import ru.naumen.core.shared.dispatch.GetDtObjectResponse;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.StringAttributeType;

/**
 * Тестирование конкурентного редактирования атрибута типа обратная ссылка в параллельных транзакциях без появления
 * ошибок типа StaleObjectStateException
 * после корректировки OptimisticLock.
 *
 * <AUTHOR>
 * @since Sep 01, 2017
 */
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class ParallelEditBackBoDbTest
{
    @Inject
    private ObjectTestUtils objectTestUtils;

    @Inject
    private AuthorizationRunnerService authorizeRunner;

    @Inject
    private Dispatch dispatch;

    @Inject
    private SecurityTestUtils securityTestUtils;
    private String code1;
    private String code2;
    private String common;
    private String commonBoLinks;
    private String commonBackBoLinks;

    private AbstractBO bo;
    private OU ou;

    /**
     * Тестирование конкурентного редактирования в двух параллельных транзакциях.
     * !!! Тест не проходит, при слишком маленьком значение ru.naumen.schema.ddl.timeout.
     *
     * Выполнение действий:
     * Запускаем две параллельные транзакции на редактирование: транзакция1 редактирует первую группу атрибутов
     * (attr1, common, commonBoLinks),
     * транзакция2 редактирует вторую группу атрибутов (attr2, common, commonBoLinks). Группы атрибутов пересекаются.
     *
     * Проверки:
     * Проверяем, что значения attr1 и attr2 соответствуют отредактированным. Значение атрибута common не гарантируется.
     * @throws Throwable
     */
    @Test
    public void parallelEditObjectTest() throws Throwable
    {

        TransactionRunner.call(TransactionType.NEW, () -> authorizeRunner.callAsSuperUser("naumen", () ->
        {
            IProperties properties = new MapProperties();
            properties.setProperty(commonBoLinks, Lists.newArrayList());
            objectTestUtils.edit(bo, properties);
            return bo;
        }));

        CompletableFuture.allOf(
                runAsync(() -> editObject(bo, code1, common, commonBackBoLinks, 1)),
                runAsync(() -> editObject(bo, code2, common, commonBackBoLinks, 2))
        ).join();

        GetDtObjectResponse resp = dispatch.execute(new GetDtObjectAction(bo.getUUID()));

        Assert.assertEquals("CHANGED1", resp.getObj().getProperty(code1));
        Assert.assertEquals("CHANGED2", resp.getObj().getProperty(code2));
    }

    @Before
    public void setUp() throws Exception
    {
        securityTestUtils.autenticateAsSuperUser();
        securityTestUtils.initLicensing();

        ou = objectTestUtils.createOU();
        final ClassFqn f = TransactionRunner.call(TransactionType.NEW, () ->
        {
            try
            {
                ClassFqn fqn = objectTestUtils.createMetaClass(false, false);
                ClassFqn caseFqn = objectTestUtils.createCase(fqn);

                code1 = objectTestUtils.createAttribute(caseFqn, StringAttributeType.CODE, "attr1");
                code2 = objectTestUtils.createAttribute(caseFqn, StringAttributeType.CODE, "attr2");
                common = objectTestUtils.createAttribute(caseFqn, StringAttributeType.CODE, "commonAttr");
                commonBoLinks = objectTestUtils.createBOLinksTypeAttribute(ClassFqn.parse(OU.CLASS_ID), caseFqn);
                commonBackBoLinks = objectTestUtils.createBackLinksTypeAttribute(caseFqn,
                        AttributeFqn.create(Constants.OU.FQN, commonBoLinks));
                return caseFqn;
            }
            catch (Exception e)
            {
                throw new FxException(e);
            }
        });
        bo = objectTestUtils.createBO(f);
    }

    private AbstractBO editObject(AbstractBO bo, String code1, String code2, String codeBoLinks, int value)
    {
        return TransactionRunner.call(TransactionType.NEW, () -> authorizeRunner.callAsSuperUser("naumen", () ->
        {
            IProperties properties = new MapProperties();
            properties.setProperty(code1, "CHANGED" + value);
            properties.setProperty(code2, "CHANGED" + value);
            properties.setProperty(codeBoLinks, Lists.newArrayList(ou.getUUID()));

            objectTestUtils.edit(bo, properties);
            return bo;
        }));
    }
}
