package ru.naumen.metainfo.server.spi.dispatch.catalog;

import java.util.Set;

import jakarta.inject.Inject;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.transaction.annotation.Transactional;

import net.customware.gwt.dispatch.server.Dispatch;
import ru.naumen.Assert;
import ru.naumen.core.server.dispatch.ObjectTestUtils;
import ru.naumen.core.server.dispatch.SecurityTestHelper;
import ru.naumen.core.shared.Constants;
import ru.naumen.metainfo.shared.dispatch2.catalog.GetCatalogCodesAction;
import ru.naumen.metainfo.shared.dispatch2.catalog.GetCatalogCodesResponse;
import ru.naumen.metainfo.shared.elements.Catalog;

/**
 * <AUTHOR>
 * @since 21.02.2011
 *
 */
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class GetCatalogCodesActionHandlerDbTest
{
    @Inject
    Dispatch dispatch;
    @Inject
    ObjectTestUtils utils;

    @Test
    @Transactional
    public void getCatalogCodes() throws Exception
    {
        //настройка системы
        Catalog catalog1 = utils.createCatalog();
        Catalog catalog2 = utils.createCatalog();
        //вызов системы
        GetCatalogCodesResponse result = dispatch.execute(new GetCatalogCodesAction());
        //проверка утверждений
        Set<String> codes = result.getCodes();
        Assert.assertContains(codes, Constants.ImpactCatalog.CODE);
        Assert.assertContains(codes, catalog1.getCode());
        Assert.assertContains(codes, catalog2.getCode());
        //очистка
    }

    @Before
    public void setUp()
    {
        SecurityTestHelper.autenticateAsSuperUser();
    }
}
