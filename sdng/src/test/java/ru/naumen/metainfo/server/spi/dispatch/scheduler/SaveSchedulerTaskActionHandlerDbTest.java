package ru.naumen.metainfo.server.spi.dispatch.scheduler;

import jakarta.inject.Inject;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.transaction.annotation.Transactional;

import net.customware.gwt.dispatch.server.Dispatch;
import ru.naumen.core.server.dispatch.ObjectTestUtils;
import ru.naumen.core.server.dispatch.SecurityTestHelper;
import ru.naumen.core.server.metastorage.impl.metainfo.MetaStorageService;
import ru.naumen.core.server.script.storage.ScriptStorageService;
import ru.naumen.core.shared.TestUtils;
import ru.naumen.core.shared.common.I18nUtil;
import ru.naumen.core.shared.utils.UUIDGenerator;
import ru.naumen.metainfo.shared.dispatch2.scheduler.SaveSchedulerTaskAction;
import ru.naumen.metainfo.shared.dispatch2.scheduler.SchedulerTaskResponse;
import ru.naumen.metainfo.shared.scheduler.ExecuteScriptTask;
import ru.naumen.metainfo.shared.scheduler.ExecuteScriptTaskDtoFactory;
import ru.naumen.metainfo.shared.scheduler.SchedulerTaskContainer;
import ru.naumen.metainfo.shared.script.ScriptDto;

/**
 * <AUTHOR>
 * @since 08.06.2011
 *
 */
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class SaveSchedulerTaskActionHandlerDbTest
{
    @Inject
    private Dispatch dispatch;
    @Inject
    private MetaStorageService metaStorage;
    @Inject
    private I18nUtil i18nUtil;
    @Inject
    private ScriptStorageService scriptStorageService;
    @Inject
    private ObjectTestUtils utils;

    @Test
    @Transactional
    public void addNew() throws Exception
    {
        //настройка системы
        ExecuteScriptTask schTask = new ExecuteScriptTask();
        String title = UUIDGenerator.get().nextUUID();
        String description = UUIDGenerator.get().nextUUID();
        ScriptDto scriptDto = utils.createScriptDto("script");
        i18nUtil.updateI18nObjectTitle(schTask, title);
        schTask.setType(ExecuteScriptTask.NAME);
        schTask.setCode(TestUtils.randomString());
        i18nUtil.updateI18nObjectDescription(schTask, description);
        schTask.setScript(scriptDto.getCode());
        SecurityTestHelper.autenticateAsSuperUser();
        //вызов системы
        SchedulerTaskResponse result = dispatch
                .execute(new SaveSchedulerTaskAction(ExecuteScriptTaskDtoFactory.create(schTask, scriptDto), true));
        //проверка утверждений
        SchedulerTaskContainer container = metaStorage.get(ru.naumen.core.server.scheduler.Constants.SCHEDULER_TASK,
                result.get().get().getCode());
        ExecuteScriptTask savedSchedulerTask = (ExecuteScriptTask)container.getSchedulerTask();
        String taskScriptBody = scriptStorageService.getScriptBody(savedSchedulerTask.getScript());
        Assert.assertEquals(result.get().get().getCode(), savedSchedulerTask.getCode());
        Assert.assertEquals(title, i18nUtil.getLocalizedTitle(savedSchedulerTask));
        Assert.assertEquals(ExecuteScriptTask.NAME, savedSchedulerTask.getType());
        Assert.assertEquals(description, i18nUtil.getLocalizedDescription(savedSchedulerTask));
        Assert.assertEquals(scriptDto.getBody(), taskScriptBody);
        //очистка
    }

    @Test
    @Transactional
    public void edit() throws Exception
    {
        //настройка системы
        ExecuteScriptTask schTask = new ExecuteScriptTask();
        String title = UUIDGenerator.get().nextUUID();
        String description = UUIDGenerator.get().nextUUID();
        ScriptDto scriptDto = utils.createScriptDto("script");

        i18nUtil.updateI18nObjectTitle(schTask, title);
        schTask.setType(ExecuteScriptTask.NAME);
        schTask.setCode(TestUtils.randomString());
        i18nUtil.updateI18nObjectDescription(schTask, description);
        schTask.setScript(scriptDto.getCode());
        SecurityTestHelper.autenticateAsSuperUser();
        ExecuteScriptTask savedTask = (ExecuteScriptTask)dispatch
                .execute(new SaveSchedulerTaskAction(ExecuteScriptTaskDtoFactory.create(schTask, scriptDto), true))
                .get().get();
        String code = savedTask.getCode();

        ExecuteScriptTask editSchTask = new ExecuteScriptTask();
        String newTitle = UUIDGenerator.get().nextUUID();
        String newDescription = UUIDGenerator.get().nextUUID();
        scriptDto = utils.createScriptDto("script");
        editSchTask.setCode(code);
        i18nUtil.updateI18nObjectTitle(editSchTask, newTitle);
        editSchTask.setType(ExecuteScriptTask.NAME);
        i18nUtil.updateI18nObjectDescription(editSchTask, newDescription);
        editSchTask.setScript(scriptDto.getCode());
        //вызов системы
        dispatch.execute(new SaveSchedulerTaskAction(ExecuteScriptTaskDtoFactory.create(editSchTask, scriptDto)));
        //проверка утверждений
        SchedulerTaskContainer savedContainer = metaStorage
                .get(ru.naumen.core.server.scheduler.Constants.SCHEDULER_TASK, code);
        ExecuteScriptTask savedSchedulerTask = (ExecuteScriptTask)savedContainer.getSchedulerTask();
        String savedScriptBody = scriptStorageService.getScriptBody(savedSchedulerTask.getScript());
        Assert.assertEquals(code, savedSchedulerTask.getCode());
        Assert.assertEquals(newTitle, i18nUtil.getLocalizedTitle(savedSchedulerTask));
        Assert.assertEquals(ExecuteScriptTask.NAME, savedSchedulerTask.getType());
        Assert.assertEquals(newDescription, i18nUtil.getLocalizedDescription(savedSchedulerTask));
        Assert.assertEquals(scriptDto.getBody(), savedScriptBody);
        //очистка
    }
}
