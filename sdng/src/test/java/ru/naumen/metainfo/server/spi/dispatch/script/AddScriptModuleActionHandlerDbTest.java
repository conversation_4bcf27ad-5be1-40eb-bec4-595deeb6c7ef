package ru.naumen.metainfo.server.spi.dispatch.script;

import jakarta.inject.Inject;
import jakarta.transaction.Transactional;

import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.dispatch.Dispatch;
import ru.naumen.metainfo.shared.dispatch2.script.AddScriptModuleAction;
import ru.naumen.metainfoadmin.shared.Constants;

@Transactional
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class AddScriptModuleActionHandlerDbTest
{
    @Inject
    private Dispatch dispatch;

    @Rule
    public ExpectedException expected = ExpectedException.none();

    @Test
    public void testNotInstatiableScriptModule() throws DispatchException
    {
        expected.expect(FxException.class);
        expected.expectMessage("Возникла ошибка компиляции");
        IProperties props = new MapProperties();
        props.setProperty(Constants.ScriptModule.CODE, "testModule");
        props.setProperty(Constants.ScriptModule.SCRIPT, "import groovy.transform.Field; @Field FakeClass a = 'ac'");
        AddScriptModuleAction action = new AddScriptModuleAction(props);
        dispatch.execute(action);
    }
}
