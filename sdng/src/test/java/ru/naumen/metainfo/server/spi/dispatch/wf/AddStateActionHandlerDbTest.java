package ru.naumen.metainfo.server.spi.dispatch.wf;

import java.util.ArrayList;

import jakarta.inject.Inject;
import jakarta.transaction.Transactional;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import net.customware.gwt.dispatch.server.Dispatch;
import ru.naumen.core.server.dispatch.ObjectTestUtils;
import ru.naumen.core.server.dispatch.SecurityTestHelper;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.utils.UUIDGenerator;
import ru.naumen.metainfo.server.ClassMetainfoServiceException;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.dispatch2.wf.AddStateAction;
import ru.naumen.metainfo.shared.dispatch2.wf.GetStateResult;
import ru.naumen.metainfo.shared.elements.wf.State;
import ru.naumen.metainfo.shared.elements.wf.State.ResponsibleType;
import ru.naumen.uniquerandom.RandomUtilsImpl;

/**
 * 
 * <AUTHOR>
 *
 */
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class AddStateActionHandlerDbTest
{
    @Inject
    Dispatch dispatch;
    @Inject
    MetainfoService metainfoService;
    @Inject
    ObjectTestUtils testUtils;

    @Transactional
    @Test(expected = ClassMetainfoServiceException.class)
    public void addExists() throws Exception
    {
        // настройка системы
        ClassFqn fqn = Constants.ServiceCall.FQN;
        String code = RandomUtilsImpl.nextCode64();
        testUtils.createState(fqn, code);
        // вызов системы
        testUtils.createState(fqn, code);
        // проверка утверждений
        // очистка
    }

    @Transactional
    @Test
    public void addNotExists() throws Exception
    {
        // настройка системы
        ClassFqn fqn = Constants.ServiceCall.FQN;
        // вызов системы
        AddStateAction a = new AddStateAction(fqn, RandomUtilsImpl.nextCode64(), UUIDGenerator.get().nextUUID(),
                UUIDGenerator.get().nextUUID(), null, null, ResponsibleType.EMPLOYEE_AND_TEAM, new ArrayList<>());
        GetStateResult result = dispatch.execute(a);
        // проверка утверждений
        Assert.assertEquals(a.getState(), result.get().getCode());
        Assert.assertEquals(a.getTitle(), result.get().getTitle());

        State state = metainfoService.getMetaClass(fqn).getWorkflow().getState(a.getState());
        Assert.assertEquals(a.getState(), state.getCode());
        Assert.assertEquals(a.getTitle(), state.getTitle());
        Assert.assertEquals(a.getDescription(), state.getDescription());
        // очистка
    }

    @Before
    public void setUp()
    {
        SecurityTestHelper.autenticateAsSuperUser();
    }
}
