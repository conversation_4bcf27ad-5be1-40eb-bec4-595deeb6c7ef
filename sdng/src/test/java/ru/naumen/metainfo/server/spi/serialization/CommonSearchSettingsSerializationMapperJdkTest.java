package ru.naumen.metainfo.server.spi.serialization;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

import java.util.ArrayList;
import java.util.List;

import org.junit.Test;
import org.mockito.Mockito;

import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.criteria.DtoProperties;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.CommonSearchSetting;
import ru.naumen.metainfo.shared.search.CommonSearchSettings;
import ru.naumen.metainfo.shared.search.IndexedFileTypesConstants.Codes;
import ru.naumen.metainfo.shared.search.IndexedFileTypesConstants.Codes.SpecialCodes;

public class CommonSearchSettingsSerializationMapperJdkTest
{
    private final CommonSearchSettingsSerializationMapper serializationMapper =
            new CommonSearchSettingsSerializationMapper(Mockito.mock(MessageFacade.class));

    /**
     * Тестирование сериализации если в индексируемых типах файлов указано {@link SpecialCodes#USER_DEFINED_EMPTY}
     * В таком случае в результате трансформации должен быть пустой список индексируемых типов файлов
     */
    @Test
    public void testTransformUserDefinedEmpty()
    {
        final CommonSearchSettingsSerializationMapper serializationMapper =
                new CommonSearchSettingsSerializationMapper(Mockito.mock(MessageFacade.class));
        final CommonSearchSettings commonSearchSettings = new CommonSearchSettings();
        commonSearchSettings.setIndexedFormats(List.of(SpecialCodes.USER_DEFINED_EMPTY));

        final ArrayList<DtObject> to = new ArrayList<>();
        serializationMapper.transform(commonSearchSettings, to, new DtoProperties());
        assertEquals(5, to.size());
        final DtObject dtObject = to.get(4);
        List<DtObject> indexedFormatsDto = dtObject.getProperty(CommonSearchSetting.VALUE);
        assertTrue(indexedFormatsDto.isEmpty());
    }

    @Test
    public void testIndexedFormatsMapping()
    {
        final CommonSearchSettings commonSearchSettings = new CommonSearchSettings();
        commonSearchSettings.setIndexedFormats(List.of(Codes.MS_OFFICE));

        final ArrayList<DtObject> to = new ArrayList<>();
        serializationMapper.transform(commonSearchSettings, to, new DtoProperties());
        assertEquals(5, to.size());
        final DtObject dtObject = to.get(4);
        List<DtObject> indexedFormatsDto = dtObject.getProperty(CommonSearchSetting.VALUE);
        assertEquals(1, indexedFormatsDto.size());
        final String code = indexedFormatsDto.get(0).getProperty(FakeMetaClassesConstants.CODE);
        assertEquals(Codes.MS_OFFICE, code);
    }
}