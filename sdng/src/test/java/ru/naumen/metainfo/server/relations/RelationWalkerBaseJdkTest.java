package ru.naumen.metainfo.server.relations;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;

import ru.naumen.Assert;
import ru.naumen.common.shared.utils.IClosure;
import ru.naumen.core.server.bo.ICoreBO;
import ru.naumen.core.shared.MockTestUtils;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.Relation;

/**
 * 
 * <AUTHOR>
 * @since 06.12.2010
 */
public class RelationWalkerBaseJdkTest
{
    static class Closure<T> implements IClosure<T>
    {
        List<T> results = new ArrayList<T>();
        /**
         * {@inheritDoc}
         */
        @Override
        public void execute(T object)
        {
            results.add(object);
        }
    }

    @Mock
    MetainfoService metainfoService;
    @Mock
    RelationService relationService;

    RelationWalkerBase<ICoreBO> walker;

    Collection<Relation> relations;

    ClassFqn fqn1, fqn2, fqn3, fqn4;

    Relation rel12, rel13, rel41, rel32, rel34;

    @Mock
    ICoreBO o1, o2, o3;
    @Mock
    IRelationHandler relationHandler;
    @Mock
    MetaClass metaClass;
    
    @Before
    public void setUp()
    {
        MockitoAnnotations.initMocks(this);

        walker = new RelationWalkerBase<ICoreBO>();
        walker.metainfoService = metainfoService;
        walker.relationService = relationService;

        fqn1 = MockTestUtils.classFqn();
        fqn2 = MockTestUtils.classFqn();
        fqn3 = MockTestUtils.classCaseFqn();
        fqn4 = MockTestUtils.classCaseFqn();
        rel12 = MockTestUtils.relation(fqn1, fqn2);
        rel13 = MockTestUtils.relation(fqn1, fqn3);
        rel32 = MockTestUtils.relation(fqn3, fqn2);
        rel34 = MockTestUtils.relation(fqn3, fqn4);
        rel41 = MockTestUtils.relation(fqn4, fqn1);
        relations = Arrays.asList(rel12, rel13, rel32, rel34, rel41);
        Mockito.when(metainfoService.getRelations()).thenReturn(relations);
        walker.init();
        
        Mockito.when(metainfoService.getMetaClass(Mockito.<ClassFqn>any())).thenReturn(metaClass);
        Mockito.when(metainfoService.getClassFqn(Mockito.any()))
        .thenAnswer(new Answer<ClassFqn>()
        {
            @Override
            public ClassFqn answer(InvocationOnMock invocation) throws Throwable
            {
                ICoreBO bo = (ICoreBO)invocation.getArguments()[0];
                return o1 == bo ? fqn1 : fqn2;
            }
        });
        Mockito.when(relationService.getRelationHandler(Mockito.<Relation>any()))
        .thenAnswer(new Answer<IRelationHandler>()
        {
            @Override
            public IRelationHandler answer(InvocationOnMock invocation) throws Throwable
            {
                Relation r = (Relation)invocation.getArguments()[0];
                return relationHandler;
            }
        });
        
    }

    @After
    public void tearDown()
    {
        walker = null;
        metainfoService = null;
        relationService = null;
        relations = null;
        fqn1 = fqn2 = fqn3 = fqn4 = null;
        rel12 = rel13 = rel32 = rel34 = rel41 = null;
    }

    @Test
    public void walk()
    {
        // настройка системы
        Closure<ICoreBO> closure = new Closure<ICoreBO>();
        // вызов системы
        walker.walk(closure, o1);
        // проверка утверждений
        Assert.assertContentSame(Arrays.asList(o1), closure.results);
        //Assert.assertContentSame(walker.getRelationsFor(fqn1, false), Arrays.asList(rel41));
        // очистка
    }
}
