package ru.naumen.metainfo.server.spi.dispatch;

import java.util.Collections;
import java.util.Map;
import java.util.Map.Entry;

import jakarta.transaction.Transactional;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import com.google.common.collect.Collections2;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;

import jakarta.inject.Inject;
import net.customware.gwt.dispatch.server.Dispatch;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.server.dispatch.ObjectTestUtils;
import ru.naumen.core.server.dispatch.SecurityTestHelper;
import ru.naumen.core.shared.Constants.Employee;
import ru.naumen.core.shared.Constants.OU;
import ru.naumen.core.shared.SecConstants.MarkerGroups;
import ru.naumen.core.shared.TestUtils;
import ru.naumen.core.shared.utils.UUIDGenerator;
import ru.naumen.metainfo.server.ClassMetainfoServiceException;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.AttrGroup;
import ru.naumen.metainfo.shared.Constants.IntegerAttributeType;
import ru.naumen.metainfo.shared.Constants.Presentations;
import ru.naumen.metainfo.shared.Constants.UI;
import ru.naumen.metainfo.shared.dispatch2.AddAttributeAction;
import ru.naumen.metainfo.shared.dispatch2.AddAttributeMarkerAction;
import ru.naumen.metainfo.shared.dispatch2.CopyMetaClassAction;
import ru.naumen.metainfo.shared.dispatch2.GetFormAction;
import ru.naumen.metainfo.shared.dispatch2.GetFormActionResponse;
import ru.naumen.metainfo.shared.dispatch2.GetSecurityDomainAction;
import ru.naumen.metainfo.shared.dispatch2.SaveUIAction;
import ru.naumen.metainfo.shared.dispatch2.UpdateAccessMatrixAction;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.sec.AccessMatrix;
import ru.naumen.metainfo.shared.elements.sec.AccessMatrix.Key;
import ru.naumen.metainfo.shared.elements.sec.Marker;
import ru.naumen.metainfo.shared.elements.sec.SecDomain;
import ru.naumen.metainfo.shared.script.ScriptDto;
import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.metainfo.shared.ui.Form;
import ru.naumen.metainfo.shared.ui.Layout;
import ru.naumen.metainfo.shared.ui.PropertyList;
import ru.naumen.uniquerandom.UniqueNumbersGenerator;

/**
 * <AUTHOR>
 * @since 17.02.2011
 *
 */
@Transactional
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class CopyMetaClassActionHandlerDbTest
{
    @Inject
    Dispatch dispatch;
    @Inject
    MetainfoService metainfoService;
    @Inject
    ObjectTestUtils utils;

    @Test
    public void copy() throws Exception
    {
        //настройка системы
        ClassFqn fromFqn = utils.createCase(ClassFqn.parse(Employee.CLASS_ID));
        ClassFqn toFqn = ClassFqn.parse(Employee.CLASS_ID, "Case" + UniqueNumbersGenerator.nextInt(1000000));
        //Изменение формы добавления
        Form form = new Form();
        Layout layout = new Layout();
        PropertyList newContent = new PropertyList();
        newContent.setUuid(UUIDGenerator.get().nextUUID());
        newContent.setAttributeGroup(AttrGroup.SYSTEM);
        newContent.setParent(layout);
        layout.getChilds().add(newContent);
        form.setLayout(layout);
        layout.setParent(form);
        dispatch.execute(new SaveUIAction(fromFqn, form, null, UI.Form.NEW));
        //Изменение прав - инвертируем все унаследованые права
        SecDomain domain = dispatch.execute(new GetSecurityDomainAction(fromFqn)).get();
        Map<Key, Boolean> data = domain.getAccessMatrix().getData();
        Map<Key, Boolean> newData = Maps.newHashMap();
        for (Entry<Key, Boolean> key : data.entrySet())
        {
            newData.put(key.getKey(), Boolean.FALSE.equals(key.getValue()));
        }
        Map<Key, String> scripts = domain.getAccessMatrix().getScripts();
        Map<Key, ScriptDto> scriptsDtos = utils.convertToMapScriptDtos(scripts);
        dispatch.execute(new UpdateAccessMatrixAction(fromFqn, newData, scriptsDtos, Sets.<ScriptDto> newHashSet()));
        //Добавляем новый атрибут в fromFqn
        String attrCode = "attrCode1";
        String attrTitle = TestUtils.randomString();
        MapProperties typeProperties = new MapProperties();
        IProperties viewPresentation = new MapProperties();
        viewPresentation.setProperty(Presentations.ATTR_CODE, Presentations.INTEGER_VIEW);
        IProperties editPresentation = new MapProperties();
        editPresentation.setProperty(Presentations.ATTR_CODE, Presentations.INTEGER_EDIT);

        //@formatter:off
        AddAttributeAction action = AddAttributeAction.create()
                .setFqn(fromFqn)
                .setCode(attrCode)
                .setTypeCode(IntegerAttributeType.CODE)
                .setTitle(attrTitle)
                .setEditable(true)
                .setTypeProperties(typeProperties)
                .setViewPresentation(viewPresentation)
                .setEditPresentation(editPresentation);
        //@formatter:on
        dispatch.execute(action);

        //Добавляем маркер на созданный атрибут
        String markerTitle = TestUtils.randomString();
        Marker marker = dispatch.execute(new AddAttributeMarkerAction(fromFqn, markerTitle, Sets.newHashSet(attrCode),
                MarkerGroups.VIEW_ATTRS, null, null)).get();
        //вызов системы
        String title = UUIDGenerator.get().nextUUID();
        String description = UUIDGenerator.get().nextUUID();
        dispatch.execute(new CopyMetaClassAction(fromFqn, toFqn, title, description,
                Collections.<String, String> emptyMap(), IProperties.EMPTY));
        //проверка утверждений
        MetaClass metaclass = metainfoService.getMetaClass(toFqn);
        Assert.assertEquals(title, metaclass.getTitle());
        Assert.assertEquals(description, metaclass.getDescription());
        //проверяем форму
        GetFormActionResponse formResponse = dispatch.execute(new GetFormAction(false, toFqn, UI.Form.NEW));
        form = (Form)formResponse.getContentInfo().getContent();
        Assert.assertTrue(Collections2.transform(form.getLayout().getChilds(), Content.UUID_EXTRACTOR::apply)
                .contains(newContent.getUuid()));
        //проверяем права
        domain = dispatch.execute(new GetSecurityDomainAction(toFqn)).get();
        AccessMatrix matrix = domain.getAccessMatrix();
        data = matrix.getDeclaredData();
        Assert.assertEquals(newData.size(), data.size());
        for (Entry<Key, Boolean> key : newData.entrySet())
        {
            Assert.assertEquals(key.getValue(), data.get(key.getKey()));
        }
        //Проверяем наличие маркера на созданныйатрибут 
        Assert.assertTrue(domain.getMarkerCodes().contains(marker.getCode()));
        //очистка
    }

    @Test(expected = ClassMetainfoServiceException.class)
    public void copyFromWithEmptyCase() throws Exception
    {
        //настройка системы
        ClassFqn fromFqn = ClassFqn.parse(Employee.CLASS_ID);
        ClassFqn toFqn = utils.createCase(fromFqn);
        //вызов системы
        dispatch.execute(new CopyMetaClassAction(fromFqn, toFqn, UUIDGenerator.get().nextUUID(),
                UUIDGenerator.get().nextUUID(), Collections.<String, String> emptyMap(), IProperties.EMPTY));
        //проверка утверждений
        //очистка
    }

    @Test(expected = ClassMetainfoServiceException.class)
    public void copyIdNotEquals() throws Exception
    {
        //настройка системы
        ClassFqn fromFqn = utils.getDefaultEmployeeCase();
        ClassFqn toFqn = utils.createCase(ClassFqn.parse(OU.CLASS_ID));
        //вызов системы
        dispatch.execute(new CopyMetaClassAction(fromFqn, toFqn, UUIDGenerator.get().nextUUID(),
                UUIDGenerator.get().nextUUID(), Collections.<String, String> emptyMap(), IProperties.EMPTY));
        //проверка утверждений
        //очистка
    }

    @Test(expected = ClassMetainfoServiceException.class)
    public void copyToExists() throws Exception
    {
        //настройка системы
        ClassFqn fromFqn = utils.getDefaultEmployeeCase();
        ClassFqn toFqn = utils.createCase(ClassFqn.parse(Employee.CLASS_ID));
        //вызов системы
        dispatch.execute(new CopyMetaClassAction(fromFqn, toFqn, UUIDGenerator.get().nextUUID(),
                UUIDGenerator.get().nextUUID(), Collections.<String, String> emptyMap(), IProperties.EMPTY));
        //проверка утверждений
        //очистка
    }

    @Test(expected = ClassMetainfoServiceException.class)
    public void copyToWithEmptyCase() throws Exception
    {
        //настройка системы
        ClassFqn toFqn = ClassFqn.parse(Employee.CLASS_ID);
        ClassFqn fromFqn = utils.getDefaultEmployeeCase();
        //вызов системы
        dispatch.execute(new CopyMetaClassAction(fromFqn, toFqn, UUIDGenerator.get().nextUUID(),
                UUIDGenerator.get().nextUUID(), Collections.<String, String> emptyMap(), IProperties.EMPTY));
        //проверка утверждений
        //очистка
    }

    @Before
    public void setUp()
    {
        SecurityTestHelper.autenticateAsSuperUser();
    }

}
