package ru.naumen.metainfo.server.spi.dispatch;

import static org.junit.Assert.assertNull;
import static ru.naumen.Assert.assertContentEquals;

import java.util.Objects;
import java.util.Set;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import com.google.common.collect.Sets;

import ru.naumen.common.server.config.LoadObjectsConfiguration;
import ru.naumen.core.server.CommonUtils;
import ru.naumen.core.server.mapper.MappingService;
import ru.naumen.sec.server.users.CurrentEmployeeContext;
import ru.naumen.core.server.actioncontext.ActionContextHolder;
import ru.naumen.sec.server.autorize.cache.AuthorizeUserCacheService;
import ru.naumen.core.server.common.AccessorHelper;
import ru.naumen.core.server.dispatch.Dispatch;
import ru.naumen.core.server.license.LicensingServiceImpl;
import ru.naumen.core.server.objectloader.IPrefixObjectLoaderService;
import ru.naumen.core.server.settings.SettingsStorage;
import ru.naumen.core.server.util.FormQueryParametersResolver;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.Constants.AbstractBO;
import ru.naumen.core.shared.Constants.Employee;
import ru.naumen.core.shared.Constants.OU;
import ru.naumen.core.shared.Constants.ServiceCall;
import ru.naumen.core.shared.script.places.OriginService;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.server.spi.ui.AddBOPermissionChecker;
import ru.naumen.metainfo.server.spi.ui.UIContentProcessorService;
import ru.naumen.metainfo.server.utils.AddFormHelper;

/**
 * Тестирование логики обработчика открытия формы добавления объекта.
 * <AUTHOR>
 * @since Oct 08, 2021
 */
@RunWith(MockitoJUnitRunner.class)
public class GetAddFormContextDataActionHandlerJdkTest
{
    @Mock
    private AddBOPermissionChecker permissionChecker;
    @Mock
    private IPrefixObjectLoaderService loader;
    @Mock
    private AccessorHelper accessorHelper;
    @Mock
    private SettingsStorage settingsStorage;
    @Mock
    private AuthorizeUserCacheService authorizeUserCache;
    @Mock
    private CurrentEmployeeContext currentEmployeeContext;
    @Mock
    private MetainfoService metainfoService;
    @Mock
    private Dispatch dispatch;
    @Mock
    private AddFormHelper addFormHelper;
    @Mock
    private LicensingServiceImpl licensingService;
    @Mock
    private OriginService originService;
    @Mock
    private FormQueryParametersResolver formQueryParametersResolver;
    @Mock
    private CommonUtils commonUtils;
    @Mock
    private MessageFacade messages;
    @Mock
    private UIContentProcessorService contentProcessorService;
    @Mock
    private ActionContextHolder actionContextHolder;

    @Mock
    MappingService mappingService;

    private final LoadObjectsConfiguration loadObjectsConfiguration = new LoadObjectsConfiguration();

    private GetAddFormContextDataActionHandler handler;

    @Before
    public void setUp() throws Exception
    {
        handler = new GetAddFormContextDataActionHandler(
                permissionChecker,
                loader,
                accessorHelper,
                settingsStorage,
                authorizeUserCache,
                currentEmployeeContext,
                metainfoService,
                dispatch,
                addFormHelper,
                licensingService,
                originService,
                formQueryParametersResolver,
                commonUtils,
                messages,
                contentProcessorService,
                loadObjectsConfiguration,
                actionContextHolder);
    }

    @Test
    public void testGetParentProperties()
    {
        final Set<String> minimalProperties = Sets.newHashSet(AbstractBO.UUID, AbstractBO.TITLE, AbstractBO.METACLASS);
        final Set<String> scAddProperties = Sets.newHashSet(AbstractBO.UUID, AbstractBO.TITLE, AbstractBO.METACLASS,
                Employee.PHONES_INDEX, Employee.EMAIL);

        loadObjectsConfiguration.setLoadAllPropertiesForNewObjectParent(false);
        assertContentEquals(scAddProperties, Objects.requireNonNull(
                handler.getParentProperties("employee$12345", ServiceCall.FQN)).getProperties());
        assertContentEquals(minimalProperties, Objects.requireNonNull(
                handler.getParentProperties("ou$12345", ServiceCall.FQN)).getProperties());
        assertContentEquals(minimalProperties, Objects.requireNonNull(
                handler.getParentProperties("employee$12345", OU.FQN)).getProperties());
        assertContentEquals(minimalProperties, Objects.requireNonNull(
                handler.getParentProperties("ou$12345", OU.FQN)).getProperties());
        assertNull(handler.getParentProperties(null, ServiceCall.FQN));

        loadObjectsConfiguration.setLoadAllPropertiesForNewObjectParent(true);
        assertNull(handler.getParentProperties("employee$12345", ServiceCall.FQN));
        assertNull(handler.getParentProperties("ou$12345", ServiceCall.FQN));
        assertNull(handler.getParentProperties("employee$12345", OU.FQN));
        assertNull(handler.getParentProperties("ou$12345", OU.FQN));
        assertNull(handler.getParentProperties(null, ServiceCall.FQN));
    }
}
