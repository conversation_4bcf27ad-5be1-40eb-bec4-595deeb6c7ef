package ru.naumen.metainfo.server.spi;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.Set;
import java.util.function.Function;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import ru.naumen.commons.shared.FxException;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.server.dispatch.ObjectTestUtils;
import ru.naumen.core.server.dispatch.SecurityTestHelper;
import ru.naumen.core.server.escalation.EscalationSchemeValue;
import ru.naumen.core.server.jta.TransactionInvocation;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.ServiceCall;
import ru.naumen.core.shared.interfacesettings.InterfaceSettings;
import ru.naumen.core.shared.personalsettings.Theme;
import ru.naumen.core.shared.timer.definition.TimerDefinition;
import ru.naumen.core.shared.utils.UUIDGenerator;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.server.spi.elements.CatalogImpl;
import ru.naumen.metainfo.server.spi.elements.MetaClassImpl;
import ru.naumen.metainfo.server.spi.elements.TransactionalMetaClass;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.Catalog;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.Relation;
import ru.naumen.metainfo.shared.elements.RelationImpl;
import ru.naumen.metainfo.shared.ui.LocalizedString;

/**
 * Тест проверяет работу класса {@link MetainfoServiceBean} с учётом того, что состояние экземпляра данного класса
 * хранится в кэше.
 * Таким образом, проверяются, в основном, getter'ы и setter'ы - что атрибуты и дочерние элементы хранятся в
 * соответстующих узлах кэша.
 *
 * <AUTHOR>
 */
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class MetainfoServiceBeanDbTest
{
    @Inject
    ObjectTestUtils utils;
    @Inject
    RelationsInitializer relationsInitializer;
    private MetainfoService transactionalMetainfoService;
    @Inject
    TransactionalMetaClass transactionalMetaClass;

    /**
     * Создание и загрузка каталога по коду, проверяется работа кэша.
     * Каталог должен быть помещён в кэш по переданному коду, а потом по этому же коду загрузиться из кэша.
     * При этом у самого объекта {@link CatalogImpl} должен быть выставлен атрибут {@code code}, который также
     * хранится в кэше.
     *
     * @throws Exception
     */
    @Test
    public void addAndLoadCatalogByCodeFromCache()
    {
        String code = UUIDGenerator.get().nextUUID();
        //
        MetaClassImpl metaClass = (MetaClassImpl)transactionalMetainfoService.getMetaClass(
                ClassFqn.parse(ServiceCall.CLASS_ID));
        CatalogImpl createdCatalog = transactionalMetaClass.addCatalog(metaClass, code);
        Catalog loadedCatalog = transactionalMetainfoService.getCatalog(code);
        //
        Assert.assertEquals(code, createdCatalog.getCode());
        Assert.assertEquals(code, loadedCatalog.getCode());
    }

    @Test
    public void addAndLoadMetaClass()
    {
        ClassFqn fqn = ClassFqn.parse(ServiceCall.CLASS_ID, UUIDGenerator.get().nextUUID());
        MetaClassImpl createdMetaclass = transactionalMetaClass.addMetaClass(fqn, ClassFqn.parse(ServiceCall.CLASS_ID));
        MetaClass loadedMetaClass = transactionalMetainfoService.getMetaClass(fqn);
        //
        Assert.assertEquals(fqn, createdMetaclass.getFqn());
        Assert.assertEquals(fqn, loadedMetaClass.getFqn());

        transactionalMetaClass.deleteMetaClass(createdMetaclass);
    }

    @Test
    public void addAndLoadTheme()
    {
        Theme theme1 = new Theme(UUIDGenerator.get().nextUUID(), "blue", Collections.<LocalizedString>emptyList(), "");
        transactionalMetainfoService.addTheme(theme1);
        Theme theme2 = new Theme(UUIDGenerator.get().nextUUID(), "blue", Collections.<LocalizedString>emptyList(), "");
        //
        transactionalMetainfoService.addTheme(theme2);
        Collection<Theme> themes = transactionalMetainfoService.getThemes();
        //
        Assert.assertTrue(themes.containsAll(Arrays.asList(theme1, theme1)));
    }

    @Test
    public void addAndLoadTimerDefinition()
    {
        TimerDefinition timeDef = new TimerDefinition();
        String code = UUIDGenerator.get().nextUUID();
        timeDef.setCode(code);
        //
        transactionalMetainfoService.addTimerDefinition(timeDef);
        TimerDefinition loadedTimeDef = transactionalMetainfoService.getTimerDefinition(code);
        //
        Assert.assertEquals(timeDef, loadedTimeDef);
    }

    @Test
    public void deleteCatalog()
    {
        String code = UUIDGenerator.get().nextUUID();
        //
        MetaClassImpl metaClass = (MetaClassImpl)transactionalMetainfoService.getMetaClass(
                ClassFqn.parse(ServiceCall.CLASS_ID));
        transactionalMetaClass.addCatalog(metaClass, code);
        //
        boolean removed = transactionalMetaClass.deleteCatalog(code);
        Catalog loadedCatalog = transactionalMetainfoService.getCatalog(code);
        //
        Assert.assertTrue(removed);
        Assert.assertNull(loadedCatalog);
    }

    @Test(expected = FxException.class)
    public void deleteEscalationScheme()
    {
        EscalationSchemeValue value = new EscalationSchemeValue(UUIDGenerator.get().nextUUID());
        value.setTargetTypes(new ArrayList<ClassFqn>());
        transactionalMetainfoService.addEscalationScheme(value);
        //
        transactionalMetaClass.deleteEscalationSchemeFromCache(value.getCode());
        transactionalMetainfoService.getEscalationScheme(value.getCode());
    }

    @Transactional
    @Test
    public void deleteRelation() throws Exception
    {
        //настройка системы
        ClassFqn fqn = utils.createCase(ClassFqn.parse(Constants.Employee.CLASS_ID));
        MetaClassImpl metaClass = (MetaClassImpl)transactionalMetainfoService.getMetaClass(fqn);
        RelationImpl rel = relationsInitializer.createNullRelation(metaClass);
        String relationCode = rel.getCode();
        //
        boolean deleted = transactionalMetaClass.deleteRelation(rel);
        boolean inList = transactionalMetainfoService.getRelations()
                .stream()
                .anyMatch(relation -> relationCode.equals(relation.getCode()));
        //
        Assert.assertTrue(deleted);
        Assert.assertFalse(inList);
    }

    @Test
    public void deleteTimerDefinition()
    {
        TimerDefinition timeDef = new TimerDefinition();
        String code = UUIDGenerator.get().nextUUID();
        timeDef.setCode(code);
        transactionalMetainfoService.addTimerDefinition(timeDef);
        //
        transactionalMetaClass.deleteTimerDefinitionFromCache(code);
        TimerDefinition loadedTimeDef = transactionalMetainfoService.getTimerDefinition(code);
        //
        Assert.assertNull(loadedTimeDef);
    }

    /**
     * Метод {@link MetainfoServiceBean#getCreatedCatalogCodes} возвращает колллекцию, которая содержит
     * коды только что созданных каталогов.
     *
     * @throws Exception
     */
    @Test
    public void getCreatedCatalogCodes()
    {
        String code1 = UUIDGenerator.get().nextUUID();
        String code2 = UUIDGenerator.get().nextUUID();
        //
        MetaClassImpl metaClass = (MetaClassImpl)transactionalMetainfoService.getMetaClass(
                ClassFqn.parse(ServiceCall.CLASS_ID));
        transactionalMetaClass.addCatalog(metaClass, code1);
        transactionalMetaClass.addCatalog(metaClass, code2);
        //
        Set<String> codes = transactionalMetainfoService.getCatalogCodes();
        //
        Assert.assertTrue(codes.containsAll(Arrays.asList(code1, code2)));
    }

    @Test
    public void getCreatedEscalationScheme()
    {
        EscalationSchemeValue value = new EscalationSchemeValue(UUIDGenerator.get().nextUUID());
        value.setTargetTypes(new ArrayList<ClassFqn>());
        transactionalMetainfoService.addEscalationScheme(value);
        //
        EscalationSchemeValue loadedScheme = transactionalMetainfoService.getEscalationScheme(value.getCode());
        //
        Assert.assertEquals(value, loadedScheme);
    }

    @Test
    public void getCreatedEscalationSchemes()
    {
        EscalationSchemeValue value1 = new EscalationSchemeValue(UUIDGenerator.get().nextUUID());
        value1.setTargetTypes(new ArrayList<ClassFqn>());
        transactionalMetainfoService.addEscalationScheme(value1);
        EscalationSchemeValue value2 = new EscalationSchemeValue(UUIDGenerator.get().nextUUID());
        value2.setTargetTypes(new ArrayList<ClassFqn>());
        transactionalMetainfoService.addEscalationScheme(value2);
        //
        Collection<EscalationSchemeValue> schemes = transactionalMetainfoService.getEscalationSchemes();
        //
        Assert.assertTrue(schemes.containsAll(Arrays.asList(value1, value2)));
    }

    @Test
    public void getCreatedRelations() throws Exception
    {
        String code1 = UUIDGenerator.get().nextUUID();
        RelationImpl relationOne = null;
        RelationImpl relationTwo = null;
        String code2 = UUIDGenerator.get().nextUUID();
        try
        {
            relationOne = transactionalMetaClass.addRelation(code1);
            relationTwo = transactionalMetaClass.addRelation(code2);
            //
            Collection<Relation> relations = transactionalMetainfoService.getRelations();
            //
            Function<Relation, String> extractRelationCode = new Function<Relation, String>()
            {

                @Override
                @Nullable
                public String apply(@Nullable Relation input)
                {
                    return ((RelationImpl)input).getCode();
                }

            };
            HashSet<String> relationCodes = new HashSet<>();
            CollectionUtils.transform(relations, extractRelationCode, relationCodes);
            Assert.assertTrue(relationCodes.containsAll(Arrays.asList(code1, code2)));
        }
        finally
        {
            if (relationTwo != null)
            {
                transactionalMetaClass.deleteRelation(relationOne);
            }
            if (relationOne != null)
            {
                transactionalMetaClass.deleteRelation(relationTwo);
            }
        }
    }

    @Test
    public void getMetaClasses()
    {
        ClassFqn fqn1 = ClassFqn.parse(ServiceCall.CLASS_ID, UUIDGenerator.get().nextUUID());
        MetaClassImpl mc1 = transactionalMetaClass.addMetaClass(fqn1, ClassFqn.parse(ServiceCall.CLASS_ID));
        ClassFqn fqn2 = ClassFqn.parse(ServiceCall.CLASS_ID, UUIDGenerator.get().nextUUID());
        MetaClassImpl mc2 = transactionalMetaClass.addMetaClass(fqn2, ClassFqn.parse(ServiceCall.CLASS_ID));
        //
        Collection<MetaClass> metaclasses = transactionalMetainfoService.getMetaClasses();
        //
        Function<MetaClass, ClassFqn> extractFqn = new Function<MetaClass, ClassFqn>()
        {

            @Override
            @Nullable
            public ClassFqn apply(@Nullable MetaClass input)
            {
                return input.getFqn();
            }
        };
        HashSet<ClassFqn> fqns = new HashSet<>();
        CollectionUtils.transform(metaclasses, extractFqn, fqns);
        Assert.assertTrue(fqns.containsAll(Arrays.asList(fqn1, fqn2)));

        transactionalMetaClass.deleteMetaClass(mc2);
        transactionalMetaClass.deleteMetaClass(mc1);
    }

    @Test
    public void saveAndLoadInterfaceSettings()
    {
        UUIDGenerator uuidGenerator = UUIDGenerator.get();
        InterfaceSettings settings = new InterfaceSettings(uuidGenerator.nextUUID(), uuidGenerator.nextUUID(),
                uuidGenerator.nextUUID(), uuidGenerator.nextUUID());
        transactionalMetainfoService.saveInterfaceSettings(settings);

        InterfaceSettings loadedSettings = transactionalMetainfoService.getInterfaceSettings();

        Assert.assertEquals(settings.getLocale(), loadedSettings.getLocale());
        Assert.assertEquals(settings.getThemeAdmin(), loadedSettings.getThemeAdmin());
        Assert.assertEquals(settings.getThemeOperator(), loadedSettings.getThemeOperator());
        Assert.assertEquals(settings.getEventLocale(), loadedSettings.getEventLocale());
    }

    @Before
    public void setUp()
    {
        SecurityTestHelper.autenticateAsSuperUser();
        transactionalMetainfoService = TransactionInvocation.build(MetainfoService.class);
    }
}