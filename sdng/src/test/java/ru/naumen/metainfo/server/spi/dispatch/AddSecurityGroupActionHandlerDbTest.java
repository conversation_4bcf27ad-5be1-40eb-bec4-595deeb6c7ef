package ru.naumen.metainfo.server.spi.dispatch;

import jakarta.inject.Inject;
import jakarta.transaction.Transactional;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import net.customware.gwt.dispatch.server.Dispatch;
import ru.naumen.commons.server.utils.StringUtilities;
import ru.naumen.core.server.dispatch.SecurityTestHelper;
import ru.naumen.core.shared.utils.UUIDGenerator;
import ru.naumen.metainfo.server.SecurityService;
import ru.naumen.metainfo.shared.dispatch2.AddSecurityGroupAction;
import ru.naumen.metainfo.shared.dispatch2.GetSecurityGroupResponse;
import ru.naumen.metainfo.shared.elements.sec.Group;

/**
 *
 * <AUTHOR>
 *
 */
@Transactional
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class AddSecurityGroupActionHandlerDbTest
{
    @Inject
    Dispatch dispatch;
    @Inject
    SecurityService securityService;

    @Test
    public void addNotExists() throws Exception
    {
        // настройка системы
        // вызов системы
        String title = UUIDGenerator.get().nextUUID();
        String code = StringUtilities.transliterate(title);

        AddSecurityGroupAction a = new AddSecurityGroupAction(code, title);
        GetSecurityGroupResponse result = dispatch.execute(a);
        // проверка утверждений
        Assert.assertEquals(title, result.get().getUserGroup().getTitle());
        Group grp = securityService.getGroup(result.get().getUserGroup().getCode());
        Assert.assertEquals(title, grp.getTitle());
        // очистка
    }

    @Before
    public void setUp()
    {
        SecurityTestHelper.autenticateAsSuperUser();
    }
}
