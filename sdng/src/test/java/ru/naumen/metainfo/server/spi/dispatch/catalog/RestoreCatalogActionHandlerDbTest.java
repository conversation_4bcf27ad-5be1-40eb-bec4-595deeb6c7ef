package ru.naumen.metainfo.server.spi.dispatch.catalog;

import jakarta.inject.Inject;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.transaction.annotation.Transactional;

import net.customware.gwt.dispatch.server.Dispatch;
import ru.naumen.core.server.dispatch.ObjectTestUtils;
import ru.naumen.core.server.dispatch.SecurityTestHelper;
import ru.naumen.core.shared.utils.UUIDGenerator;
import ru.naumen.metainfo.server.ClassMetainfoServiceException;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.dispatch2.catalog.RestoreCatalogAction;
import ru.naumen.metainfo.shared.elements.Catalog;
import ru.naumen.metainfo.shared.elements.MetaClassLite.Status;

/**
 * <AUTHOR>
 * @since 21.02.2011
 *
 */
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class RestoreCatalogActionHandlerDbTest
{
    @Inject
    Dispatch dispatch;
    @Inject
    MetainfoService metainfoService;
    @Inject
    ObjectTestUtils utils;

    @Test
    @Transactional
    public void restoreActive() throws Exception
    {
        //настройка системы
        Catalog catalog = utils.createCatalog();
        //вызов системы
        dispatch.execute(new RestoreCatalogAction(catalog.getCode()));
        //проверка утверждений
        Assert.assertEquals(Status.DEFAULT, catalog.getItemMetaClass().getStatus());
        //очистка
    }

    @Test
    @Transactional
    public void restoreExists() throws Exception
    {
        //настройка системы
        Catalog catalog = utils.createCatalog();
        utils.archiveCatalog(catalog.getCode());
        //вызов системы
        dispatch.execute(new RestoreCatalogAction(catalog.getCode()));
        //проверка утверждений
        Assert.assertEquals(Status.DEFAULT, catalog.getItemMetaClass().getStatus());
        //очистка
    }

    @Test(expected = ClassMetainfoServiceException.class)
    @Transactional
    public void restoreNotExists() throws Exception
    {
        //настройка системы
        //вызов системы
        dispatch.execute(new RestoreCatalogAction(UUIDGenerator.get().nextUUID()));
        //проверка утверждений
        //очистка
    }

    @Before
    public void setUp()
    {
        SecurityTestHelper.autenticateAsSuperUser();
    }
}
