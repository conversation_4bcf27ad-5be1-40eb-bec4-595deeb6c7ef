package ru.naumen.metainfo.server.spi.dispatch;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static ru.naumen.core.shared.Constants.ScriptsComponentTree.EXISTING_SCRIPT;

import jakarta.inject.Inject;
import jakarta.transaction.Transactional;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import com.google.common.collect.ImmutableMap;

import net.customware.gwt.dispatch.server.Dispatch;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.bo.employee.Employee;
import ru.naumen.core.server.bo.ou.OU;
import ru.naumen.core.server.dispatch.ObjectTestUtils;
import ru.naumen.core.server.dispatch.SecurityTestHelper;
import ru.naumen.core.server.dispatch.SecurityTestUtils;
import ru.naumen.core.server.jta.TransactionRunner;
import ru.naumen.core.server.jta.TransactionRunner.TransactionType;
import ru.naumen.core.server.script.ScriptHelper;
import ru.naumen.core.server.script.storage.ScriptStorageService;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.core.shared.script.places.AttributeCategories;
import ru.naumen.core.shared.script.places.ScriptCategory;
import ru.naumen.core.shared.script.places.ScriptHolders;
import ru.naumen.core.shared.utils.UUIDGenerator;
import ru.naumen.metainfo.server.ClassMetainfoServiceException;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.AggregateAttributeType;
import ru.naumen.metainfo.shared.Constants.Presentations;
import ru.naumen.metainfo.shared.dispatch2.EditAttributeAction;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.script.Script;
import ru.naumen.metainfo.shared.script.ScriptDto;

/**
 * <AUTHOR>
 * @since 17.02.2011
 *
 */
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class EditAttributeActionHandlerDbTest
{
    @Inject
    private Dispatch dispatch;
    @Inject
    private MetainfoService metainfoService;
    @Inject
    private ObjectTestUtils utils;
    @Inject
    private SecurityTestUtils securityTestUtils;
    @Inject
    private ScriptStorageService scriptStorageService;

    @Transactional
    @Test
    public void badEditPresentation() throws Exception
    {
        //настройка системы
        ClassFqn fqn = ClassFqn.parse(Constants.Employee.CLASS_ID);
        String code = Constants.Employee.FIRST_NAME;
        String title = UUIDGenerator.get().nextUUID();
        String description = UUIDGenerator.get().nextUUID();

        MapProperties edit = new MapProperties();
        edit.setProperty(Presentations.ATTR_CODE, UUIDGenerator.get().nextUUID());
        MapProperties view = new MapProperties();
        view.setProperty(Presentations.ATTR_CODE, Presentations.STRING_VIEW);
        //вызов системы

        //@formatter:off
        EditAttributeAction action = EditAttributeAction.create()
                .setFqn(fqn)
                .setCode(code)
                .setTitle(title)
                .setDescription(description)
                .setEditable(true)
                .setRequired(true)
                .setViewPresentation(view)
                .setEditPresentation(edit);
        //@formatter:on
        dispatch.execute(action);

        //проверка утверждений
        String newCode = metainfoService.getMetaClass(fqn).getAttribute(code).getEditPresentation().getCode();
        Assert.assertEquals(Presentations.STRING_EDIT, newCode);
        //Очистка
    }

    @Transactional
    @Test
    public void badViewPresentation() throws Exception
    {
        //настройка системы
        ClassFqn fqn = ClassFqn.parse(Constants.Employee.CLASS_ID);
        String code = Constants.Employee.FIRST_NAME;
        String title = UUIDGenerator.get().nextUUID();
        String description = UUIDGenerator.get().nextUUID();

        MapProperties edit = new MapProperties();
        edit.setProperty(Presentations.ATTR_CODE, Presentations.STRING_EDIT);
        MapProperties view = new MapProperties();
        view.setProperty(Presentations.ATTR_CODE, UUIDGenerator.get().nextUUID());
        //вызов системы

        //@formatter:off
        EditAttributeAction action = EditAttributeAction.create()
                .setFqn(fqn)
                .setCode(code)
                .setTitle(title)
                .setDescription(description)
                .setEditable(true)
                .setRequired(true)
                .setViewPresentation(view)
                .setEditPresentation(edit);
        //@formatter:on
        dispatch.execute(action);

        //проверка утверждений
        String newCode = metainfoService.getMetaClass(fqn).getAttribute(code).getViewPresentation().getCode();
        Assert.assertEquals(Presentations.STRING_VIEW, newCode);
        //очистка
    }

    @Transactional
    @Test
    public void editDeclared() throws Exception
    {
        //настройка системы
        ClassFqn parentFqn = ClassFqn.parse(Constants.Employee.CLASS_ID);
        ClassFqn fqn = utils.createCase(parentFqn);
        String code = utils.createAttribute(fqn);
        String title = UUIDGenerator.get().nextUUID();
        String description = UUIDGenerator.get().nextUUID();

        MapProperties edit = new MapProperties();
        edit.setProperty(Presentations.ATTR_CODE, Presentations.BOOL_RADIOBUTTON);
        MapProperties view = new MapProperties();
        view.setProperty(Presentations.ATTR_CODE, Presentations.BOOL_ONE_ZERO);

        //вызов системы
        //@formatter:off
        EditAttributeAction action = EditAttributeAction.create()
                .setFqn(fqn)
                .setCode(code)
                .setTitle(title)
                .setDescription(description)
                .setEditable(true)
                .setRequired(true)
                .setViewPresentation(view)
                .setEditPresentation(edit);
        //@formatter:on
        dispatch.execute(action);

        //проверка утверждений
        MetaClass metaClass = metainfoService.getMetaClass(fqn);
        Attribute attribute = metaClass.getAttribute(code);
        Assert.assertEquals(title, attribute.getTitle());
        Assert.assertEquals(description, attribute.getDescription());
        Assert.assertTrue(attribute.isRequired());
        Assert.assertFalse("Должен быть признак пользовательского атрибута", attribute.isHardcoded());
        //очистка
    }

    @Transactional
    @Test
    public void editDeclaredNotUnique() throws Exception
    {
        //настройка системы
        ClassFqn parentFqn = ClassFqn.parse(Constants.Employee.CLASS_ID);
        ClassFqn fqn = utils.createCase(parentFqn);
        String code = utils.createAttribute(fqn);
        String title = UUIDGenerator.get().nextUUID();
        String description = UUIDGenerator.get().nextUUID();

        MapProperties edit = new MapProperties();
        edit.setProperty(Presentations.ATTR_CODE, Presentations.BOOL_RADIOBUTTON);
        MapProperties view = new MapProperties();
        view.setProperty(Presentations.ATTR_CODE, Presentations.BOOL_ONE_ZERO);
        //вызов системы
        //        dispatch.execute(new EditAttributeAction(fqn, code, title, description, true, true, true, null, view, edit,
        //                null));

        //вызов системы
        //@formatter:off
        EditAttributeAction action = EditAttributeAction.create()
                .setFqn(fqn)
                .setCode(code)
                .setTitle(title)
                .setDescription(description)
                .setEditable(true)
                .setRequired(true)
                .setUnique(true)
                .setViewPresentation(view)
                .setEditPresentation(edit);
        //@formatter:on
        dispatch.execute(action);

        //проверка утверждений
        MetaClass metaClass = metainfoService.getMetaClass(fqn);
        Attribute attribute = metaClass.getAttribute(code);
        Assert.assertEquals(title, attribute.getTitle());
        Assert.assertEquals(description, attribute.getDescription());
        Assert.assertTrue(attribute.isRequired());
        Assert.assertTrue(attribute.isUnique());
        Assert.assertFalse("Должен быть признак пользовательского атрибута", attribute.isHardcoded());
        //очистка
    }

    @Transactional
    @Test
    public void editDefaultToNull() throws Exception
    {
        //настройка системы
        ClassFqn parentFqn = ClassFqn.parse(Constants.Employee.CLASS_ID);
        Object[] objs = TransactionRunner.call(TransactionType.NEW, () -> {
            ClassFqn fqn = utils.createCase(parentFqn);
            String code = utils.createObjectTypeAttribute(parentFqn, Constants.OU.FQN);
            ClassFqn ouCase = utils.getDefaultOUCase();
            return new Object[] { fqn, code, ouCase };
        });
        ClassFqn fqn = (ClassFqn)objs[0];
        String code = (String)objs[1];
        ClassFqn ouCase = (ClassFqn)objs[2];
        String title = UUIDGenerator.get().nextUUID();
        String description = UUIDGenerator.get().nextUUID();
        OU ou = utils.createOU(ouCase);
        SimpleDtObject defaultOU = new SimpleDtObject(ou.getUUID(), ou.getTitle(), ou.getMetaClass());

        MapProperties edit = new MapProperties();
        edit.setProperty(Presentations.ATTR_CODE, Presentations.BO_SELECT);
        MapProperties view = new MapProperties();
        view.setProperty(Presentations.ATTR_CODE, Presentations.BO_REFERENCE);

        //проверяем, что можно установить дефолтное значение

        //@formatter:off
        EditAttributeAction action = EditAttributeAction.create()
                .setFqn(fqn)
                .setCode(code)
                .setTitle(title)
                .setDescription(description)
                .setEditable(true)
                .setRequired(true)
                .setViewPresentation(view)
                .setEditPresentation(edit)
                .setDefaultValue(defaultOU);
        //@formatter:on
        dispatch.execute(action);

        Assert.assertEquals(defaultOU, metainfoService.getMetaClass(fqn).getAttribute(code).getDefaultValue());

        //проверяем, что можно сбросить дефолтное значение
        //@formatter:off
        action = EditAttributeAction.create()
                .setFqn(fqn)
                .setCode(code)
                .setTitle(title)
                .setDescription(description)
                .setEditable(true)
                .setRequired(true)
                .setViewPresentation(view)
                .setEditPresentation(edit);
        //@formatter:on
        dispatch.execute(action);

        Assert.assertNull("Значение атрибута по умолчанию не изменилось на null",
                metainfoService.getMetaClass(fqn).getAttribute(code).getDefaultValue());
        //очистка
    }

    @Transactional
    @Test
    public void editHardCoded() throws Exception
    {
        //настройка системы
        ClassFqn fqn = ClassFqn.parse(Constants.Employee.CLASS_ID);
        String code = Constants.Employee.FIRST_NAME;
        String title = UUIDGenerator.get().nextUUID();
        String description = UUIDGenerator.get().nextUUID();

        Attribute attribute = editAttribute(fqn, title, description, code);
        //проверка утверждений
        Assert.assertEquals(title, attribute.getTitle());
        Assert.assertEquals(description, attribute.getDescription());
        Assert.assertTrue(attribute.isRequired());
        Assert.assertFalse(attribute.isUnique());
        Assert.assertTrue("Должен быть признак системного атрибута", attribute.isHardcoded());
        //очистка
    }

    @Transactional
    @Test(expected = ClassMetainfoServiceException.class)
    public void editNotExists() throws Exception
    {
        //настройка системы
        ClassFqn fqn = ClassFqn.parse(Constants.Employee.CLASS_ID);
        //вызов системы

        //@formatter:off
        EditAttributeAction action = EditAttributeAction.create()
                .setFqn(fqn)
                .setCode( UUIDGenerator.get().nextUUID())
                .setTitle( UUIDGenerator.get().nextUUID())
                .setDescription( UUIDGenerator.get().nextUUID())
                .setEditable(true)
                .setRequired(true)
                .setViewPresentation(IProperties.EMPTY)
                .setEditPresentation(IProperties.EMPTY);
        //@formatter:on
        dispatch.execute(action);

        //проверка утверждений
        //очистка
    }

    @Transactional
    @Test
    public void editOverride() throws Exception
    {
        //настройка системы
        ClassFqn parentFqn = ClassFqn.parse(Constants.Employee.CLASS_ID);
        ClassFqn fqn = utils.createCase(parentFqn);
        String code = utils.createAttribute(parentFqn);
        String title = UUIDGenerator.get().nextUUID();
        String description = UUIDGenerator.get().nextUUID();

        MapProperties edit = new MapProperties();
        edit.setProperty(Presentations.ATTR_CODE, Presentations.BOOL_RADIOBUTTON);
        MapProperties view = new MapProperties();
        view.setProperty(Presentations.ATTR_CODE, Presentations.BOOL_ONE_ZERO);
        //вызов системы

        //@formatter:off
        EditAttributeAction action = EditAttributeAction.create()
                .setFqn(fqn)
                .setCode(code)
                .setTitle(title)
                .setDescription(description)
                .setEditable(true)
                .setRequired(true)
                .setViewPresentation(view)
                .setEditPresentation(edit);
        //@formatter:on
        dispatch.execute(action);

        //проверка утверждений
        MetaClass metaClass = metainfoService.getMetaClass(fqn);
        Attribute attribute = metaClass.getAttribute(code);
        Assert.assertEquals(title, attribute.getTitle());
        Assert.assertEquals(description, attribute.getDescription());
        Assert.assertTrue(attribute.isRequired());
        Assert.assertFalse("Должен быть признак пользовательского атрибута", attribute.isHardcoded());
        //очистка
    }

    @Before
    public void setUp()
    {
        SecurityTestHelper.autenticateAsSuperUser();
    }

    /**
     * Проверка на то, что после удаления признака вычисления значения атрибута при редактировании, информация
     * о скрипте пропадает из атрибута и мест использования скрипта.
     * 
     * @throws Exception
     */
    @Transactional
    @Test
    public void testDeclaredAttributeComputableOnFormScriptShouldBeObliterated() throws Exception
    {
        //настройка системы
        ClassFqn parentFqn = ClassFqn.parse(Constants.Employee.CLASS_ID);
        ClassFqn fqn = utils.createCase(parentFqn);
        String title = UUIDGenerator.get().nextUUID();
        String description = UUIDGenerator.get().nextUUID();
        ScriptDto scriptDto = utils.createScriptDto("return []");
        String code = utils.createAttribute(fqn, true, false, false, title, description, false, null, true, scriptDto,
                false, null);

        MetaClass oldMetaClass = metainfoService.getMetaClass(fqn);
        Attribute oldAttribute = oldMetaClass.getAttribute(code);
        String oldScriptCode = oldAttribute.getComputableOnFormScript();

        Attribute attribute = utils.editAttribute(fqn, title, description, code, false, null, false, null, false, null,
                false, null);
        String scriptCode = attribute.getComputableOnFormScript();

        //проверка утверждений
        assertAttributeScriptInfoObliterated(oldScriptCode, scriptCode);
        //очистка
    }

    /**
     * Проверка на то, что после редактирования скрипта вычисления значения атрибута при редактировании, номер 
     * скрипта не изменяется.
     * 
     * @throws Exception
     */
    @Transactional
    @Test
    public void testDeclaredAttributeComputableOnFormScriptShouldStayWithSameNumberAfterEdit() throws Exception
    {
        //настройка системы
        ClassFqn parentFqn = ClassFqn.parse(Constants.Employee.CLASS_ID);
        ClassFqn fqn = utils.createCase(parentFqn);
        String title = UUIDGenerator.get().nextUUID();
        String description = UUIDGenerator.get().nextUUID();
        ScriptDto scriptDto = utils.createScriptDto("return []");
        String code = utils.createAttribute(fqn, true, false, false, title, description, false, null, true, scriptDto,
                false, null);

        MetaClass oldMetaClass = metainfoService.getMetaClass(fqn);
        Attribute oldAttribute = oldMetaClass.getAttribute(code);
        String oldScriptCode = oldAttribute.getComputableOnFormScript();
        scriptDto.setCode(oldScriptCode);

        String newScript = "[]";
        scriptDto.setSelectStrategy(EXISTING_SCRIPT);
        scriptDto.setBody(newScript);
        scriptDto.setLoaded(true);
        Attribute attribute = utils.editAttribute(fqn, title, description, code, false, null, true, scriptDto, false,
                null, false, null);
        String scriptCode = attribute.getComputableOnFormScript();

        //проверка утверждений
        assertEditedAttributeScriptInfo(oldScriptCode, newScript, scriptCode, AttributeCategories.COMPUTABLE_ON_FORM,
                ScriptHolders.ATTRIBUTE, attribute.getFqn().toString(), oldMetaClass.getFqn());
        //очистка
    }

    /**
     * Проверка на то, что после редактирования скрипта вычисления значения атрибута, номер 
     * скрипта не изменяется.
     * 
     * @throws Exception
     */
    @Transactional
    @Test
    public void testDeclaredAttributeComputableScriptShouldStayWithSameNumberAfterEdit() throws Exception
    {
        //настройка системы
        ClassFqn parentFqn = ClassFqn.parse(Constants.Employee.CLASS_ID);
        ClassFqn fqn = utils.createCase(parentFqn);
        String title = UUIDGenerator.get().nextUUID();
        String description = UUIDGenerator.get().nextUUID();
        ScriptDto scriptDto = utils.createScriptDto("'asd'");
        String code = utils.createAttribute(fqn, true, false, false, title, description, true, scriptDto, false, null,
                false, null);

        MetaClass oldMetaClass = metainfoService.getMetaClass(fqn);
        Attribute oldAttribute = oldMetaClass.getAttribute(code);
        String oldScriptCode = oldAttribute.getScript();
        scriptDto.setCode(oldScriptCode);

        String newScript = "'asdf'";
        scriptDto.setSelectStrategy(EXISTING_SCRIPT);
        scriptDto.setBody(newScript);
        scriptDto.setLoaded(true);
        Attribute attribute = utils.editAttribute(fqn, title, description, code, true, scriptDto, false, null, false,
                null, false, null);
        String scriptCode = attribute.getScript();

        //проверка утверждений
        assertEditedAttributeScriptInfo(oldScriptCode, newScript, scriptCode, AttributeCategories.COMPUTABLE,
                ScriptHolders.ATTRIBUTE, attribute.getFqn().toString(), oldMetaClass.getFqn());
        //очистка
    }

    /**
     * Проверка на то, что после удаления признака вычисления значения атрибута по-умолчанию, информация
     * о скрипте пропадает из атрибута и информация об атрибуте из мест использования скрипта.
     * 
     * @throws Exception
     */
    @Transactional
    @Test
    public void testDeclaredAttributeScriptForDefaultShouldBeObliterated() throws Exception
    {
        //настройка системы
        ClassFqn parentFqn = ClassFqn.parse(Constants.Employee.CLASS_ID);
        ClassFqn fqn = utils.createCase(parentFqn);
        String title = UUIDGenerator.get().nextUUID();
        String description = UUIDGenerator.get().nextUUID();
        ScriptDto scriptDto = utils.createScriptDto("return 'asd'");
        String code = utils.createAttribute(fqn, true, false, false, title, description, false, null, false, null,
                true, scriptDto);

        MetaClass oldMetaClass = metainfoService.getMetaClass(fqn);
        Attribute oldAttribute = oldMetaClass.getAttribute(code);
        String oldScriptCode = oldAttribute.getScriptForDefault();

        Attribute attribute = utils.editAttribute(fqn, title, description, code, false, null, false, null, false, null,
                false, null);
        String scriptCode = attribute.getScriptForDefault();

        //проверка утверждений
        assertAttributeScriptInfoObliterated(oldScriptCode, scriptCode);
        //очистка
    }

    /**
     * Проверка на то, что после редактирования скрипта вычисления значения атрибута по-умолчанию, номер 
     * скрипта не изменяется.
     * 
     * @throws Exception
     */
    @Transactional
    @Test
    public void testDeclaredAttributeScriptForDefaultShouldStayWithSameNumberAfterEdit() throws Exception
    {
        //настройка системы
        ClassFqn parentFqn = ClassFqn.parse(Constants.Employee.CLASS_ID);
        ClassFqn fqn = utils.createCase(parentFqn);
        String title = UUIDGenerator.get().nextUUID();
        String description = UUIDGenerator.get().nextUUID();
        ScriptDto scriptDto = utils.createScriptDto("return 'asd'");
        String code = utils.createAttribute(fqn, true, false, false, title, description, false, null, false, null,
                true, scriptDto);

        MetaClass oldMetaClass = metainfoService.getMetaClass(fqn);
        Attribute oldAttribute = oldMetaClass.getAttribute(code);
        String oldScriptCode = oldAttribute.getScriptForDefault();
        scriptDto.setCode(oldScriptCode);

        String newScript = "'asd'";
        scriptDto.setSelectStrategy(EXISTING_SCRIPT);
        scriptDto.setBody(newScript);
        scriptDto.setLoaded(true);
        Attribute attribute = utils.editAttribute(fqn, title, description, code, false, null, false, null, true,
                scriptDto, false, null);
        String scriptCode = attribute.getScriptForDefault();

        //проверка утверждений
        assertEditedAttributeScriptInfo(oldScriptCode, newScript, scriptCode, AttributeCategories.DEFAULT_VALUE,
                ScriptHolders.ATTRIBUTE, attribute.getFqn().toString(), oldMetaClass.getFqn());
        //очистка
    }

    /**
     * Проверка на то, что после после удаления признака фильтрации значений атрибута, информация
     * о скрипте пропадает из атрибута и информация об атрибуте из мест использования скрипта.
     * 
     * @throws Exception
     */
    @Transactional
    @Test
    public void testDeclaredAttributeScriptForFiltrationShouldBeObliterated() throws Exception
    {
        securityTestUtils.initLicensing();
        ClassFqn fqn = utils.createCase(Constants.Employee.FQN);

        ScriptDto scriptDto = utils.createScriptDto("return []");
        String code = utils.createAggregateOUAttribute(fqn, null, true, scriptDto);
        MetaClass oldMetaClass = metainfoService.getMetaClass(fqn);
        Attribute oldAttribute = oldMetaClass.getAttribute(code);
        String oldScriptCode = oldAttribute.getScriptForFiltration();

        Attribute attribute = utils.editAttribute(fqn, oldAttribute.getTitle(), oldAttribute.getDescription(), code,
                false, null, false, null, false, null, false, null);
        String scriptCode = attribute.getScriptForFiltration();

        //проверка утверждений
        assertAttributeScriptInfoObliterated(oldScriptCode, scriptCode);
        //очистка
    }

    /**
     * Проверка на то, что после редактирования скрипта фильтрации значений атрибута, номер 
     * скрипта не изменяется.
     * 
     * @throws Exception
     */
    @Transactional
    @Test
    public void testDeclaredAttributeScriptForFiltrationShouldStayWithSameNumberAfterEdit() throws Exception
    {
        securityTestUtils.initLicensing();
        ClassFqn fqn = utils.createCase(Constants.Employee.FQN);

        ScriptDto scriptDto = utils.createScriptDto("return []");
        String code = utils.createAggregateOUAttribute(fqn, null, true, scriptDto);
        MetaClass oldMetaClass = metainfoService.getMetaClass(fqn);
        Attribute oldAttribute = oldMetaClass.getAttribute(code);
        String oldScriptCode = oldAttribute.getScriptForFiltration();
        scriptDto.setCode(oldScriptCode);

        String newScript = "[]";
        scriptDto.setSelectStrategy(EXISTING_SCRIPT);
        scriptDto.setBody(newScript);
        scriptDto.setLoaded(true);
        Attribute attribute = utils.editAttribute(fqn, oldAttribute.getTitle(), oldAttribute.getDescription(), code,
                false, null, false, null, false, null, true, scriptDto);
        String scriptCode = attribute.getScriptForFiltration();

        //проверка утверждений
        assertEditedAttributeScriptInfo(oldScriptCode, newScript, scriptCode, AttributeCategories.FILTRATION,
                ScriptHolders.ATTRIBUTE, attribute.getFqn().toString(), oldMetaClass.getFqn());
        //очистка
    }

    /**
     * По мотивам http://sd-jira.naumen.ru/browse/NSDPRD-518
     */
    @Test
    public void testEditAggregate() throws Exception
    {
        securityTestUtils.initLicensing();
        ClassFqn fqn = TransactionRunner.call(TransactionType.NEW, () -> {
            return utils.createCase(Constants.Employee.FQN);
        });
        MetaClass metaClass = metainfoService.getMetaClass(fqn);
        Employee employee = utils.createEmployee();
        Object defValue = utils.getTreeDtObject(employee, employee.getParent());

        String code = utils.createAggregateOUAttribute(fqn, defValue);
        Assert.assertNotNull(metaClass.getAttribute(code).getDefaultValue());
        Assert.assertNotNull(metaClass.getAttribute(code + AggregateAttributeType.OU_POSTFIX).getDefaultValue());

        utils.editAttribute(fqn, code, null);
        metaClass = metainfoService.getMetaClass(fqn);
        Assert.assertNull(metaClass.getAttribute(code).getDefaultValue());
        Assert.assertNull(metaClass.getAttribute(code + AggregateAttributeType.OU_POSTFIX).getDefaultValue());
    }

    /**
     * Проверка на то, что у скрипта вычисления значения при редактировании переопределённого атрибута старый номер,
     * а у скрипта добавлено место использования.
     * 
     * @throws Exception
     */
    @Transactional
    @Test
    public void testOverrideAttributeComputableOnFormScriptShouldBeWithNewNumber() throws Exception
    {
        //настройка системы
        ClassFqn parentFqn = ClassFqn.parse(Constants.Employee.CLASS_ID);
        ClassFqn fqn = utils.createCase(parentFqn);
        String title = UUIDGenerator.get().nextUUID();
        String description = UUIDGenerator.get().nextUUID();
        ScriptDto scriptDto = utils.createScriptDto("return []");
        String code = utils.createAttribute(parentFqn, true, false, false, title, description, false, null, true,
                scriptDto, false, null);

        MetaClass oldMetaClass = metainfoService.getMetaClass(parentFqn);
        Attribute oldAttribute = oldMetaClass.getAttribute(code);
        String oldScriptCode = oldAttribute.getComputableOnFormScript();
        scriptDto.setCode(oldScriptCode);

        String newScript = "[]";
        scriptDto.setSelectStrategy(EXISTING_SCRIPT);
        scriptDto.setBody(newScript);
        scriptDto.setLoaded(true);
        Attribute attribute = utils.editAttribute(fqn, title, description, code, false, null, true, scriptDto, false,
                null, false, null);
        String scriptCode = attribute.getComputableOnFormScript();

        oldScriptCode = oldAttribute.getComputableOnFormScript();
        AttributeFqn attrFqn = new AttributeFqn(fqn, attribute.getCode());
        //проверка утверждений
        assertEditedOverrideAttributeScriptInfo(oldScriptCode, newScript, scriptCode,
                AttributeCategories.COMPUTABLE_ON_FORM, ScriptHolders.ATTRIBUTE, attrFqn.toString(), attribute
                        .getMetaClass().getFqn());
        //очистка
    }

    /**
     * Проверка на то, что после удаления признака вычисления значения атрибута при редактировании, информация
     * о скрипте переопределяемого атрибута не пропадает из атрибута и кэша информации о скриптах.
     * 
     * @throws Exception
     */
    @Transactional
    @Test
    public void testOverrideAttributeComputableOnFormScriptShouldNotBeObliterated() throws Exception
    {
        //настройка системы
        ClassFqn parentFqn = ClassFqn.parse(Constants.Employee.CLASS_ID);
        ClassFqn fqn = utils.createCase(parentFqn);
        String title = UUIDGenerator.get().nextUUID();
        String description = UUIDGenerator.get().nextUUID();
        ScriptDto scriptDto = utils.createScriptDto("return []");
        String code = utils.createAttribute(parentFqn, true, false, false, title, description, false, null, true,
                scriptDto, false, null);

        Attribute attribute = utils.editAttribute(fqn, title, description, code, false, null, false, null, false, null,
                false, null);
        String scriptCode = attribute.getComputableOnFormScript();

        MetaClass oldMetaClass = metainfoService.getMetaClass(parentFqn);
        Attribute oldAttribute = oldMetaClass.getAttribute(code);
        String oldScriptCode = oldAttribute.getComputableOnFormScript();

        //проверка утверждений
        assertOverridenAttributeScriptInfoNotObliterated(oldScriptCode, scriptCode);
        //очистка
    }

    /**
     * Проверка на то, что у скрипта вычисления значения по-умолчанию переопределённого атрибута старый номер,
     * а у скрипта добавлено место использования.
     * 
     * @throws Exception
     */
    @Transactional
    @Test
    public void testOverrideAttributeScriptForDefaultShouldBeWithNewNumber() throws Exception
    {
        //настройка системы
        ClassFqn parentFqn = ClassFqn.parse(Constants.Employee.CLASS_ID);
        ClassFqn fqn = utils.createCase(parentFqn);
        String title = UUIDGenerator.get().nextUUID();
        String description = UUIDGenerator.get().nextUUID();
        ScriptDto scriptDto = utils.createScriptDto("return 'asd'");
        String code = utils.createAttribute(parentFqn, true, false, false, title, description, false, null, false,
                null, true, scriptDto);

        MetaClass oldMetaClass = metainfoService.getMetaClass(parentFqn);
        Attribute oldAttribute = oldMetaClass.getAttribute(code);
        String oldScriptCode = oldAttribute.getScriptForDefault();
        scriptDto.setCode(oldScriptCode);

        String newScript = "'asd'";
        scriptDto.setSelectStrategy(EXISTING_SCRIPT);
        scriptDto.setBody(newScript);
        scriptDto.setLoaded(true);
        Attribute attribute = utils.editAttribute(fqn, title, description, code, false, null, false, null, true,
                scriptDto, false, null);
        String scriptCode = attribute.getScriptForDefault();

        oldScriptCode = oldAttribute.getScriptForDefault();
        AttributeFqn attrFqn = new AttributeFqn(fqn, attribute.getCode());
        //проверка утверждений
        assertEditedOverrideAttributeScriptInfo(oldScriptCode, newScript, scriptCode,
                AttributeCategories.DEFAULT_VALUE, ScriptHolders.ATTRIBUTE, attrFqn.toString(), attribute
                        .getMetaClass().getFqn());
        //очистка
    }

    /**
     * Проверка на то, что после удаления признака вычисления значения по-умолчанию атрибута при редактировании, информация
     * о скрипте переопределяемого атрибута не пропадает из атрибута и кэша информации о скриптах.
     * 
     * @throws Exception
     */
    @Transactional
    @Test
    public void testOverrideAttributeScriptForDefaultShouldNotBeObliterated() throws Exception
    {
        //настройка системы
        ClassFqn parentFqn = ClassFqn.parse(Constants.Employee.CLASS_ID);
        ClassFqn fqn = utils.createCase(parentFqn);
        String title = UUIDGenerator.get().nextUUID();
        String description = UUIDGenerator.get().nextUUID();
        ScriptDto scriptDto = utils.createScriptDto("return 'asd'");
        String code = utils.createAttribute(parentFqn, true, false, false, title, description, false, null, false,
                null, true, scriptDto);

        Attribute attribute = utils.editAttribute(fqn, title, description, code, false, null, false, null, false, null,
                false, null);
        String scriptCode = attribute.getScriptForDefault();

        MetaClass oldMetaClass = metainfoService.getMetaClass(parentFqn);
        Attribute oldAttribute = oldMetaClass.getAttribute(code);
        String oldScriptCode = oldAttribute.getScriptForDefault();

        //проверка утверждений
        assertOverridenAttributeScriptInfoNotObliterated(oldScriptCode, scriptCode);
        //очистка
    }

    /**
     * Проверка на то, что после после удаления признака фильтрации значений атрибута, информация
     * о скрипте переопределяемого атрибута не пропадает из атрибута и кэша информации о скриптах.
     * 
     * @throws Exception
     */
    @Transactional
    @Test
    public void testOverrideAttributeScriptForFiltrationShouldNotBeObliterated() throws Exception
    {
        securityTestUtils.initLicensing();
        ClassFqn parentFqn = Constants.Employee.FQN;
        ClassFqn fqn = utils.createCase(parentFqn);
        ScriptDto scriptDto = utils.createScriptDto("return []");
        String code = utils.createAggregateOUAttribute(parentFqn, null, true, scriptDto);

        MetaClass oldMetaClass = metainfoService.getMetaClass(parentFqn);
        Attribute oldAttribute = oldMetaClass.getAttribute(code);

        Attribute attribute = utils.editAttribute(fqn, oldAttribute.getTitle(), oldAttribute.getDescription(), code,
                false, null, false, null, false, null, false, null);
        String scriptCode = attribute.getScriptForFiltration();

        oldMetaClass = metainfoService.getMetaClass(parentFqn);
        oldAttribute = oldMetaClass.getAttribute(code);
        String oldScriptCode = oldAttribute.getScriptForFiltration();

        //проверка утверждений
        assertOverridenAttributeScriptInfoNotObliterated(oldScriptCode, scriptCode);
        //очистка
    }

    /**
     * Проверка на то, что после редактирования скрипта фильтрации значений атрибута, у переопределённого
     * атрибута старый номер, а у скрипта добавлено место использования.
     * 
     * @throws Exception
     */
    @Transactional
    @Test
    public void testOverrideAttributeScriptForFiltrationShouldStayWithSameNumberAfterEdit() throws Exception
    {
        securityTestUtils.initLicensing();
        ClassFqn parentFqn = Constants.Employee.FQN;
        ClassFqn fqn = utils.createCase(parentFqn);
        ScriptDto scriptDto = utils.createScriptDto("return []");
        String code = utils.createAggregateOUAttribute(parentFqn, null, true, scriptDto);
        MetaClass oldMetaClass = metainfoService.getMetaClass(parentFqn);
        Attribute oldAttribute = oldMetaClass.getAttribute(code);
        String oldScriptCode = oldAttribute.getScriptForFiltration();
        scriptDto.setCode(oldScriptCode);

        String newScript = "[]";
        scriptDto.setSelectStrategy(EXISTING_SCRIPT);
        scriptDto.setBody(newScript);
        scriptDto.setLoaded(true);
        Attribute attribute = utils.editAttribute(fqn, oldAttribute.getTitle(), oldAttribute.getDescription(), code,
                false, null, false, null, false, null, true, scriptDto);
        String scriptCode = attribute.getScriptForFiltration();

        oldAttribute = oldMetaClass.getAttribute(code);
        oldScriptCode = oldAttribute.getScriptForFiltration();
        AttributeFqn attrFqn = new AttributeFqn(fqn, attribute.getCode());
        //проверка утверждений
        assertEditedOverrideAttributeScriptInfo(oldScriptCode, newScript, scriptCode, AttributeCategories.FILTRATION,
                ScriptHolders.ATTRIBUTE, attrFqn.toString(), fqn);
        //очистка
    }

    /**
     * Проверка на то, что у скрипта вычисления значения переопределённого атрибута старый номер,
     * а у скрипта добавлено место использования.
     * 
     * @throws Exception
     */
    @Transactional
    @Test
    public void testOverrideAttributeScriptShouldBeWithNewNumber() throws Exception
    {
        //настройка системы
        ClassFqn parentFqn = ClassFqn.parse(Constants.Employee.CLASS_ID);
        ClassFqn fqn = utils.createCase(parentFqn);
        String title = UUIDGenerator.get().nextUUID();
        String description = UUIDGenerator.get().nextUUID();
        ScriptDto scriptDto = utils.createScriptDto("return 'asd'");
        String code = utils.createAttribute(parentFqn, true, false, false, title, description, true, scriptDto, false,
                null, false, null);

        MetaClass oldMetaClass = metainfoService.getMetaClass(parentFqn);
        Attribute oldAttribute = oldMetaClass.getAttribute(code);
        String oldScriptCode = oldAttribute.getScript();
        scriptDto.setCode(oldScriptCode);

        String newScript = "'asdf'";
        scriptDto.setSelectStrategy(EXISTING_SCRIPT);
        scriptDto.setBody(newScript);
        scriptDto.setLoaded(true);
        Attribute attribute = utils.editAttribute(fqn, title, description, code, true, scriptDto, false, null, false,
                null, false, null);
        String scriptCode = attribute.getScript();

        oldScriptCode = oldAttribute.getScript();
        AttributeFqn attrFqn = new AttributeFqn(fqn, attribute.getCode());
        //проверка утверждений
        assertEditedOverrideAttributeScriptInfo(oldScriptCode, newScript, scriptCode, AttributeCategories.COMPUTABLE,
                ScriptHolders.ATTRIBUTE, attrFqn.toString(), attribute.getMetaClass().getFqn());
        //очистка
    }

    /**
     * По мотивам http://sd-jira.naumen.ru/browse/NSDPRD-840
     * Сотрудник с переопределенным системным атрибутом должен добавляться
     */
    @Transactional
    @Test
    public void testOverrideTitle() throws Exception
    {
        securityTestUtils.initLicensing();
        ClassFqn parentFqn = ClassFqn.parse(Constants.Employee.CLASS_ID);
        ClassFqn[] fqns = TransactionRunner.call(TransactionType.NEW, () -> {
            ClassFqn ouCase = utils.getDefaultOUCase();
            ClassFqn employeeCaseFqn = utils.createCase(parentFqn);
            return new ClassFqn[] { ouCase, employeeCaseFqn };
        });
        ClassFqn ouCase = fqns[0];
        ClassFqn employeeCaseFqn = fqns[1];

        String code = Constants.AbstractBO.TITLE;
        String title = UUIDGenerator.get().nextUUID();
        String description = UUIDGenerator.get().nextUUID();

        MapProperties edit = new MapProperties(ImmutableMap.of(Presentations.ATTR_CODE, Presentations.STRING_EDIT));
        MapProperties view = new MapProperties(ImmutableMap.of(Presentations.ATTR_CODE, Presentations.STRING_VIEW));

        //вызов системы

        //@formatter:off
        EditAttributeAction action = EditAttributeAction.create()
                .setFqn(employeeCaseFqn)
                .setCode(code)
                .setTitle(title)
                .setDescription(description)
                .setViewPresentation(view)
                .setEditPresentation(edit);
        //@formatter:on
        dispatch.execute(action);

        OU ou = utils.createOU(ouCase);

        //проверка
        utils.createEmployee(employeeCaseFqn, ou);
    }

    private void assertAttributeScriptInfoObliterated(String oldScriptCode, String newScriptCode)
    {
        assertTrue("Информация о скрипте должна быть удалена из атрибута", StringUtilities.isEmpty(newScriptCode));
        Script script = scriptStorageService.getScript(oldScriptCode);
        assertNotNull("Информация о скрипте не должна быть удалена из кэша информации о скриптах.", script);
        assertTrue("Информация об атрибуте должна быть удалена из мест использования скрипта",
                CollectionUtils.isEmpty(script.getUsagePoints()));
    }

    private void assertEditedAttributeScriptInfo(String oldScriptCode, String newScript, String scriptCode,
            ScriptCategory category, ScriptHolders holderType, String location, ClassFqn holderObject)
    {
        Script script = scriptStorageService.getScript(scriptCode);
        assertEquals("После редактирования скрипт не соответствует ожидаемому.", newScript, script.getBody());
        assertTrue("В атрибуте на сервере должен быть код скрипта.", ScriptHelper.isScriptCodeValid(scriptCode));
        assertEquals("Номер скрипта после редактирования должен был остаться прежним.", oldScriptCode, scriptCode);
        assertNotNull("Информация о скрипте должна содержаться в кэше информации о скриптах.", script);
        assertEquals(scriptCode, script.getCode());
        assertEquals("Категория скрипта не соответствует ожидаемой.", category, script.getUsagePoints().get(0)
                .getCategory());
        assertEquals("Тип объекта, содержащего скрипт не соответствует ожидаемому.", holderType, script
                .getUsagePoints().get(0).getHolderType());
        assertEquals("Ожидалось иное расположение скрипта.", location, script.getUsagePoints().get(0).getLocation());
        assertEquals("Ожидался иной объект, содержащий скрипт.", holderObject, script.getUsagePoints().get(0)
                .getRelatedMetaClassFqns().iterator().next());
        assertEquals("Ожидалось иное тело скрипта", newScript, script.getBody());
    }

    private void assertEditedOverrideAttributeScriptInfo(String oldScriptCode, String newScript, String scriptCode,
            ScriptCategory category, ScriptHolders holderType, String location, ClassFqn holderObject)
    {
        Script script = scriptStorageService.getScript(scriptCode);
        assertEquals("После редактирования скрипт не соответствует ожидаемому.", newScript, script.getBody());
        assertTrue("В атрибуте на сервере должен быть код скрипта.", ScriptHelper.isScriptCodeValid(scriptCode));
        assertEquals("Номер скрипта после редактирования должен был остаться прежним.", oldScriptCode, scriptCode);
        assertNotNull("Информация о скрипте атрибута должна содержаться в кэше информации о скриптах.", script);

        assertEquals(scriptCode, script.getCode());
        assertTrue("Количество мест использования скрипта не увеличилось", script.getUsagePoints().size() == 2);
        assertEquals("Категория скрипта не соответствует ожидаемой.", category, script.getUsagePoints().get(1)
                .getCategory());
        assertEquals("Тип объекта, содержащего скрипт не соответствует ожидаемому.", holderType, script
                .getUsagePoints().get(1).getHolderType());
        assertEquals("Ожидалось иное расположение скрипта.", location, script.getUsagePoints().get(1).getLocation());
        assertEquals("Ожидался иной объект, содержащий скрипт.", holderObject, script.getUsagePoints().get(1)
                .getRelatedMetaClassFqns().iterator().next());
    }

    private void assertOverridenAttributeScriptInfoNotObliterated(String oldScriptCode, String scriptCode)
    {
        assertTrue("Информация о скрипте должна быть удалена из атрибута", StringUtilities.isEmpty(scriptCode));
        assertNotNull("Информация о скрипте не должна быть удалена из кэша информации о скриптах.",
                scriptStorageService.getScript(oldScriptCode));

        assertTrue("Информация о скрипте должна быть только в переопределяемом атрибуте.", oldScriptCode != scriptCode);
    }

    private Attribute editAttribute(ClassFqn fqn, String title, String description, String code)
            throws DispatchException
    {
        return utils.editAttribute(fqn, title, description, code, false, null, false, null, false, null, false, null);
    }
}
