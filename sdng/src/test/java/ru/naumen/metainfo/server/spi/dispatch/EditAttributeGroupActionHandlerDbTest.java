package ru.naumen.metainfo.server.spi.dispatch;

import java.util.Arrays;
import java.util.List;

import jakarta.inject.Inject;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.transaction.annotation.Transactional;

import net.customware.gwt.dispatch.server.Dispatch;
import ru.naumen.core.server.dispatch.ObjectTestUtils;
import ru.naumen.core.server.dispatch.SecurityTestHelper;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.utils.UUIDGenerator;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.dispatch2.EditAttributeGroupAction;
import ru.naumen.metainfo.shared.elements.AttributeGroup;

/**
 * <AUTHOR>
 * @since 18.02.2011
 *
 */
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class EditAttributeGroupActionHandlerDbTest
{
    @Inject
    Dispatch dispatch;
    @Inject
    MetainfoService metainfoService;
    @Inject
    ObjectTestUtils utils;

    @Test
    @Transactional
    public void editExists() throws Exception
    {
        //настройка системы
        ClassFqn fqn = ClassFqn.parse(Constants.Employee.CLASS_ID);
        String attr1 = utils.createAttribute(fqn);
        AttributeGroup grp = utils.createAttributeGroup(fqn, Arrays.asList(attr1));
        String attr2 = utils.createAttribute(fqn);
        String title = UUIDGenerator.get().nextUUID();
        List<String> editAtributes = Arrays.asList(attr2);
        //вызов системы       
        dispatch.execute(new EditAttributeGroupAction(fqn, grp.getCode(), title, editAtributes, false));
        //проверка утверждений
        AttributeGroup editGrp = metainfoService.getMetaClass(fqn).getAttributeGroup(grp.getCode());
        Assert.assertEquals(title, editGrp.getTitle());
        Assert.assertEquals(editAtributes, editGrp.getAttributeCodes());
        //очистка
    }

    @Before
    public void setUp()
    {
        SecurityTestHelper.autenticateAsSuperUser();
    }
}
