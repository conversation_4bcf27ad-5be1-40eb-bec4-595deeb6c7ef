
package ru.naumen.metainfo.server.spi.dispatch;

import static org.junit.Assert.assertFalse;

import jakarta.inject.Inject;

import com.googlecode.functionalcollections.FunctionalIterables;

import org.junit.AfterClass;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import ru.naumen.bcp.server.operations.OperationException;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.server.dispatch.ObjectTestUtils;
import ru.naumen.core.server.dispatch.SecurityTestUtils;
import ru.naumen.core.server.jta.TransactionRunner;
import ru.naumen.core.server.metastorage.impl.metainfo.MetaStorageService;
import ru.naumen.core.server.objectloader.IPrefixObjectLoaderService;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.HasCode;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.utils.UUIDGenerator;
import ru.naumen.metainfo.server.spi.store.MetainfoClass;
import ru.naumen.metainfo.server.spi.store.UserAttribute;
import ru.naumen.metainfo.shared.ClassFqn;

/**
 * Тест проверяет ограничения на уникальность пользовательских атрибутов
 *
 * <AUTHOR>
 * @since 13.06.2012
 */
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class UniqueAttributeDbTest
{
    private static ClassFqn caseFqn;
    private static String attrCode;

    @AfterClass
    public static void afterClass()
    {
        caseFqn = null;
        attrCode = null;
    }

    @Inject
    ObjectTestUtils objectTestUtils;
    @Inject
    SecurityTestUtils securityTestUtils;
    @Inject
    IPrefixObjectLoaderService loaderService;

    @Inject
    MetaStorageService metaStorage;

    private final UUIDGenerator generator = UUIDGenerator.get();

    @Before
    public void setUp() throws Exception
    {
        securityTestUtils.autenticateAsSuperUser();
        securityTestUtils.initLicensing();

        if (caseFqn == null)
        {
            TransactionRunner.call(() -> {
                caseFqn = objectTestUtils.createCase(Constants.OU.FQN);
                attrCode = objectTestUtils.createStringAttribute(caseFqn, true);
                return null;
            });
        }
    }

    /**
     * Два одинаковых значения нельзя
     */
    @Test(expected = OperationException.class)
    public void test2Equals()
    {
        String value1 = generator.nextUUID();
        create(value1);
        create(value1);
    }

    /**
     * Значения архивных объектов не учитываются в проверке на уникальность
     */
    @Test
    public void test2EqualsOneRemoved() throws Exception
    {
        String value1 = generator.nextUUID();
        IUUIDIdentifiable obj1 = loaderService.get(create(value1).getUUID());
        objectTestUtils.edit(obj1, CollectionUtils.<String, Object> map(Constants.AbstractBO.REMOVED, true));

        create(value1); //можно добавить второй объеект, так как первый архивен
    }

    /**
     * 1. Создаем пользовательский атрибут в классе отдел
     * 2. Создаем два объекта, у которых значение атрибута совпадает
     * 3. Пытаемся сделать атрибут уникальный (должно быть проброшено исключение)
     * 4. Смотрим какое значение атрибута осталось в БД (для оракла было true <- баг)
     *
     */
    @SuppressWarnings({ "unchecked", "rawtypes" })
    @Test
    public void testBugNSDPRD2296() throws Exception
    {
        //подготовка
        String attrCodeNew = objectTestUtils.createStringAttribute(Constants.OU.FQN, false);
        String value = generator.nextUUID();
        create(value, caseFqn, attrCodeNew);
        create(value, caseFqn, attrCodeNew);

        //вызов системы
        try
        {
            objectTestUtils.editAttribute(Constants.OU.FQN, attrCodeNew, null, true);
        }
        catch (Exception ex)
        {
        }

        //проверка
        MetainfoClass dbClass = metaStorage.get(ru.naumen.metainfo.server.Constants.METACLASS,
                Constants.OU.FQN.toString());
        //@formatter:off
        UserAttribute attr = (UserAttribute)FunctionalIterables
                                .make(dbClass.getClazz().getAttribute())
                                .find(new HasCode.HasCodeFilter(attrCodeNew));
        //@formatter:off

        assertFalse("Изменения в базе данных не должны были произойти", attr.isUnique());
    }

    /**
     * Два разных значения можно
     */
    @Test
    public void testDifferentValues()
    {
        create(generator.nextUUID());
        create(generator.nextUUID());
    }

    /**
     * Два nulla можно
     */
    @Test
    public void testNulls()
    {
        create(null);
        create(null);
    }

    private IUUIDIdentifiable create(String value)
    {
        return create(value, caseFqn, attrCode);
    }

    private IUUIDIdentifiable create(String value, ClassFqn classFqn, String attributeCode)
    {
        return objectTestUtils.create(classFqn,
                CollectionUtils.<String, Object> map("title", generator.nextUUID(), attributeCode, value));
    }

}
