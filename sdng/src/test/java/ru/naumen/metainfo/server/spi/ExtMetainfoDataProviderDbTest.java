package ru.naumen.metainfo.server.spi;

import static org.junit.Assert.assertEquals;

import java.util.Collections;
import java.util.List;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import org.hibernate.query.Query;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.type.StandardBasicTypes;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import net.customware.gwt.dispatch.server.Dispatch;
import ru.naumen.common.CreatedListener;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.core.server.dispatch.ObjectTestUtils;
import ru.naumen.core.server.dispatch.SecurityTestHelper;
import ru.naumen.core.server.jta.TransactionRunner;
import ru.naumen.core.shared.utils.UUIDGenerator;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.dispatch2.CopyMetaClassAction;

/**
 * Тестирование записи информации о метаклассах во внешние по отношению к системе таблицы,
 * для построения отчетов без использования NSD(скриптов)
 *
 * <AUTHOR>
 * @since Dec 23, 2016
 *
 */
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class ExtMetainfoDataProviderDbTest
{
    @Inject
    private ObjectTestUtils testUtils;
    @Inject
    private Dispatch dispatch;
    @Inject
    @Named("sessionFactory")
    private SessionFactory sessionFactory;
    @Inject
    private CreatedListener createdListener;
    @Inject
    private ExtMetainfoDataProvider extMetainfoDataProvider;

    /**
     * Проверяет что у записи с указанным {@code fqn} в БД заполнено поле {@code parent_fqn} со значением {@code parentFqn}.
     */
    @SuppressWarnings({ "unchecked" })
    private void checkFqnParent(final ClassFqn fqn, final ClassFqn parentFqn)
    {
        TransactionRunner.run(() -> {
            final Session session = sessionFactory.getCurrentSession();

            final Query<String> query = session.createNativeQuery("select parent_fqn from tbl_sys_metainfo_titles "
                    + "where fqn = ?");
            query.setParameter(1, fqn.toString(), StandardBasicTypes.STRING);

            final List<String> results = query.list();
            assertEquals(1, results.size());
            assertEquals(parentFqn.toString(), results.get(0));
        });
    }

    /**
     * Проверяем, что в таблице tbl_sys_metainfo_titles отсутствует запись с переданным fqn
     */
    private void checkAbsence(final ClassFqn fqn)
    {
        TransactionRunner.run(() -> {
            final Session session = sessionFactory.getCurrentSession();

            final Query query = session.createNativeQuery("select count(*) from tbl_sys_metainfo_titles "
                    + "where fqn = ?");
            query.setParameter(1, fqn.toString(), StandardBasicTypes.STRING);
            assertEquals(0,  ((Number)query.uniqueResult()).intValue());
        });
    }

    @Before
    public void setUp() throws Exception
    {
        SecurityTestHelper.autenticateAsSuperUser();
        createdListener.setUp();
    }

    /**
     * Тестирование заполнения таблицы {@link ExtMetainfoDataProvider#TITLES_TABLE_NAME} при копировании метакласса
     */
    @Test
    public void shouldCopyTblSysMetainfoTitlesFqnParent() throws Exception
    {
        //настройка системы
        final ClassFqn defaultOuCase = testUtils.getDefaultOUCase();
        final ClassFqn ouType = testUtils.createCase(defaultOuCase);
        final ClassFqn copiedOuType = ClassFqn.parse(
                ouType.toString().substring(0, ouType.toString().length()-4) + "copy");

        //вызов системы
        dispatch.execute(new CopyMetaClassAction(ouType, copiedOuType, UUIDGenerator.get().nextUUID(),
                UUIDGenerator.get().nextUUID(), Collections.emptyMap(), IProperties.EMPTY));
        //проверка утверждений
        checkFqnParent(copiedOuType, defaultOuCase);
        //очистка
    }

    /**
     * Тестирование игнорирования обновления {@link ExtMetainfoDataProvider#TITLES_TABLE_NAME} при копировании
     * метакласса
     */
    @Test
    public void shouldNotCopyTblSysMetainfoTitlesFqnParent() throws Exception
    {
        extMetainfoDataProvider.setNoUpdateClassTitlesOnMetainfoLoad(true);
        final ClassFqn defaultOuCase = testUtils.getDefaultOUCase();
        final ClassFqn ouType = testUtils.createCase(defaultOuCase);
        final ClassFqn copiedOuType = ClassFqn.parse(
                ouType.toString().substring(0, ouType.toString().length()-4) + "copy");

        //вызов системы
        dispatch.execute(new CopyMetaClassAction(ouType, copiedOuType, UUIDGenerator.get().nextUUID(),
                UUIDGenerator.get().nextUUID(), Collections.emptyMap(), IProperties.EMPTY));
        //проверка утверждений
        checkAbsence(copiedOuType);
    }

    /**
     * Тестирование заполнения поле parent_fqn при создании типа и подтипа
     */
    @Test
    public void shouldFillTblSysMetainfoTitlesFqnParent() throws Exception
    {
        final ClassFqn defaultOuCase = testUtils.getDefaultOUCase();
        final ClassFqn ouType = testUtils.createCase(defaultOuCase);
        final ClassFqn ouSubType = testUtils.createCase(ouType);

        checkFqnParent(ouType, defaultOuCase);
        checkFqnParent(ouSubType, ouType);
    }

    /**
     * Тестирование удаления записи из {@link ExtMetainfoDataProvider#TITLES_TABLE_NAME}
     */
    @SuppressWarnings({ "unchecked" })
    @Test
    public void shouldRemoveTblSysMetainfoTitlesRow() throws Exception
    {
        final ClassFqn defaultOuCase = testUtils.getDefaultOUCase();
        final ClassFqn ouType = testUtils.createCase(defaultOuCase);

        checkFqnParent(ouType, defaultOuCase);

        testUtils.deleteMetaClass(ouType);

        TransactionRunner.run(() -> {
            final Session session = sessionFactory.getCurrentSession();

            final Query<String> query = session.createNativeQuery("select parent_fqn from tbl_sys_metainfo_titles "
                    + "where fqn = ?");
            query.setParameter(1, ouType.toString(), StandardBasicTypes.STRING);

            final List<?> results = query.list();
            assertEquals(0, results.size());
        });
    }

    @After
    public void tearDown() throws Exception
    {
        createdListener.tearDown();
    }
}
