package ru.naumen.metainfo.server.spi;

import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import ru.naumen.admin.server.adminprofile.AdminProfilesStorageServiceImpl;
import ru.naumen.core.server.advlist.templates.ListTemplateStorageServiceImpl;
import ru.naumen.core.server.customforms.storage.CustomFormStorageService;
import ru.naumen.core.server.embeddedapplication.EmbeddedApplicationServiceBean;
import ru.naumen.core.server.eventaction.EventActionServiceBean;
import ru.naumen.core.server.jms.services.JMSQueueStorageService;
import ru.naumen.core.server.modules.ModulesService;
import ru.naumen.core.server.script.storage.ScriptStorageServiceBean;
import ru.naumen.core.server.sets.SettingsSetStorageServiceImpl;
import ru.naumen.core.server.structuredobjectsviews.StructuredObjectsViewStorageServiceImpl;
import ru.naumen.core.server.style.templates.StyleTemplateStorageServiceImpl;
import ru.naumen.core.server.tags.TagStorageServiceImpl;
import ru.naumen.metainfo.server.spi.rt.ResponsibleTransferStorageService;
import ru.naumen.metainfo.server.spi.ui.template.UITemplateMetainfoStorageService;
import ru.naumen.metainfo.server.spi.ui.template.content.ContentTemplateStorageService;
import ru.naumen.sec.server.users.employee.groups.SystemFeatureGroupService;

/**
 * Тестирование логики, специфичной для полной загрузки метаинформации
 *
 * <AUTHOR>
 * @since 26.04.2023
 */
@RunWith(MockitoJUnitRunner.class)
public class FullImportMetaInfoStrategyJdkTest
{
    @InjectMocks
    private AbstractImportMetaInfoStrategy abstractImportMetaInfoStrategy = new FullImportMetaInfoStrategy();
    @Mock
    private MetainfoServiceBean metainfoService;
    @Mock
    private AdminProfilesStorageServiceImpl adminProfilesStorageService;
    @Mock
    private TagStorageServiceImpl tagStorageService;
    @Mock
    private SettingsSetStorageServiceImpl settingsSetStorageService;
    @Mock
    private CustomFormStorageService customFormStorageService;
    @Mock
    private StyleTemplateStorageServiceImpl styleTemplateStorageService;
    @Mock
    private ListTemplateStorageServiceImpl listTemplateStorageService;
    @Mock
    private ContentTemplateStorageService contentTemplateStorageService;
    @Mock
    private UITemplateMetainfoStorageService uiTemplateMetainfoStorageService;
    @Mock
    private StructuredObjectsViewStorageServiceImpl structuredObjectsViewStorageService;
    @Mock
    private EventActionServiceBean eventActionService;
    @Mock
    private JMSQueueStorageService jmsQueueStorageService;
    @Mock
    private ScriptStorageServiceBean scriptStorageService;
    @Mock
    private ModulesService modulesService;
    @Mock
    private SystemFeatureGroupService featureGroupService;
    @Mock
    private ResponsibleTransferStorageService responsibleTransferStorageService;
    @Mock
    private EmbeddedApplicationServiceBean embeddedApplicationServiceBean;

    /**
     * Тестирование отсутствия вызова метода {@link EmbeddedApplicationServiceBean#init()} при переинициализации
     * метаинформации методом AbstractImportMetaInfoStrategy#reinitServices()
     */
    @Test
    public void testReinitServices()
    {
        ReflectionTestUtils.invokeMethod(abstractImportMetaInfoStrategy, "reinitServices");
        verify(embeddedApplicationServiceBean, never()).init();
    }
}
