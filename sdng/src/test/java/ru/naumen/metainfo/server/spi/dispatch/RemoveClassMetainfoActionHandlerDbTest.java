package ru.naumen.metainfo.server.spi.dispatch;

import jakarta.inject.Inject;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.transaction.annotation.Transactional;

import net.customware.gwt.dispatch.server.Dispatch;
import ru.naumen.core.server.dispatch.ObjectTestUtils;
import ru.naumen.core.server.dispatch.SecurityTestHelper;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.utils.UUIDGenerator;
import ru.naumen.metainfo.server.ClassMetainfoServiceException;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.dispatch2.RemoveClassMetainfoAction;
import ru.naumen.metainfo.shared.elements.MetaClassLite.Status;

/**
 * <AUTHOR>
 * @since 17.02.2011
 *
 */
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class RemoveClassMetainfoActionHandlerDbTest
{
    @Inject
    Dispatch dispatch;
    @Inject
    MetainfoService metainfoService;
    @Inject
    ObjectTestUtils utils;

    @Test
    @Transactional
    public void archiveExists() throws Exception
    {
        //настройка системы
        ClassFqn fqn = utils.createCase(ClassFqn.parse(Constants.Employee.CLASS_ID));
        ClassFqn childFqn = utils.createCase(fqn);
        //вызов системы
        dispatch.execute(new RemoveClassMetainfoAction(fqn));
        //проверка утверждений
        Assert.assertEquals("Метакласс не заархивирован", Status.REMOVED, metainfoService.getMetaClass(fqn).getStatus());
        Assert.assertEquals("Дочерний метакласс не заархивирован", Status.REMOVED,
                metainfoService.getMetaClass(childFqn).getStatus());
        //очистка
    }

    @Test(expected = ClassMetainfoServiceException.class)
    @Transactional
    public void archiveHardCoded() throws Exception
    {
        //настройка системы
        ClassFqn fqn = ClassFqn.parse(Constants.Employee.CLASS_ID);
        //вызов системы
        dispatch.execute(new RemoveClassMetainfoAction(fqn));
        //проверка утверждений
        //очистка
    }

    @Test(expected = ClassMetainfoServiceException.class)
    @Transactional
    public void archiveNotExists() throws Exception
    {
        //настройка системы
        ClassFqn fqn = ClassFqn.parse(UUIDGenerator.get().nextUUID());
        //вызов системы
        dispatch.execute(new RemoveClassMetainfoAction(fqn));
        //проверка утверждений
        //очистка
    }

    @Before
    public void setUp()
    {
        SecurityTestHelper.autenticateAsSuperUser();
    }

}
