package ru.naumen.metainfo.server.spi.elements;

import jakarta.inject.Inject;
import jakarta.transaction.Transactional;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import ru.naumen.core.shared.Constants.ServiceCall;
import ru.naumen.core.shared.utils.UUIDGenerator;
import ru.naumen.metainfo.shared.ClassFqn;

/**
 * Тест проверяет работу класса {@link CatalogImpl} с учётом того, что состояние объектов данного класса хранится в кэше.
 * Таким образом, проверяются, в основном, getter'ы и setter'ы - что атрибуты и дочерние элементы хранятся в соответстующих узлах кэша. 
 * 
 * <AUTHOR>
 *
 */
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class CatalogImplDbTest
{
    @Inject
    TransactionalMetaClass transactionalMetaClass;
    private CatalogImpl catalog;

    @Transactional
    @Test
    public void addDescriptions()
    {
        String lang = LocaleContextHolder.getLocale().getLanguage();
        String descr = UUIDGenerator.get().nextUUID();
        String descrOverride = UUIDGenerator.get().nextUUID();
        //
        transactionalMetaClass.addDescription(catalog, lang, descr);
        transactionalMetaClass.setDescription(catalog, descrOverride);
        //
        Assert.assertEquals(descr, catalog.getDescriptionAsProperties().getProperty(lang));
        Assert.assertEquals(descrOverride, catalog.getDescriptionOverrideAsProperties().getProperty(lang));
        Assert.assertEquals(descrOverride, catalog.getDescription());
    }

    @Transactional
    @Test
    public void addTitles()
    {
        String lang = LocaleContextHolder.getLocale().getLanguage();
        String title = UUIDGenerator.get().nextUUID();
        String titleOverride = UUIDGenerator.get().nextUUID();
        //
        transactionalMetaClass.addTitle(catalog, lang, title);
        transactionalMetaClass.setTitle(catalog, titleOverride);
        //
        Assert.assertEquals(title, catalog.getTitleAsProperties().getProperty(lang));
        Assert.assertEquals(titleOverride, catalog.getTitleOverrideAsProperties().getProperty(lang));
        Assert.assertEquals(titleOverride, catalog.getTitle());
    }

    @After
    public void after()
    {
        MetaClassImpl metaClass = ((MetaClassImpl)catalog.getItemMetaClass());
        transactionalMetaClass.deleteCatalog(catalog.getCode());
        transactionalMetaClass.deleteMetaClass(metaClass);
        catalog = null;
    }

    @Before
    public void before()
    {
        ClassFqn fqn = ClassFqn.parse(ServiceCall.CLASS_ID, UUIDGenerator.get().nextUUID());
        MetaClassImpl metaClass = transactionalMetaClass.addMetaClass(fqn, ClassFqn.parse(ServiceCall.CLASS_ID));
        transactionalMetaClass.setHardcoded(metaClass, true);

        catalog = transactionalMetaClass.addCatalog(metaClass, UUIDGenerator.get().nextUUID());
    }
}