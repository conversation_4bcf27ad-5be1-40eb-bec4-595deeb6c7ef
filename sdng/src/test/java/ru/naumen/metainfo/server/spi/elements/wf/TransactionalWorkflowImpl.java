package ru.naumen.metainfo.server.spi.elements.wf;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * 
 * <AUTHOR>
 *
 */
@Component
public class TransactionalWorkflowImpl implements TransactionalWorkflow
{

    @Override
    @Transactional
    public ActionImpl addDeclaredPostAction(WorkflowImpl workflow, String state, String code)
    {
        return workflow.addDeclaredPostAction(state, code);
    }

    @Override
    @Transactional
    public void addDeclaredPostCondition(WorkflowImpl workflow, String state, String code)
    {
        workflow.addDeclaredPostCondition(state, code);
    }

    @Override
    @Transactional
    public void addDeclaredPreAction(WorkflowImpl workflow, String state, String code)
    {
        workflow.addDeclaredPreAction(state, code);
    }

    @Override
    @Transactional
    public void addDeclaredPreCondition(WorkflowImpl workflow, String state, String code)
    {
        workflow.addDeclaredPreCondition(state, code);
    }

    @Override
    @Transactional
    public StateDeclarationImpl addStateDeclaration(WorkflowImpl workflow, String stateCode)
    {
        return workflow.addStateDeclaration(stateCode);
    }

    @Override
    @Transactional
    public StateDeclarationImpl addStateOverride(WorkflowImpl workflow, String stateCode)
    {
        return workflow.addStateOverride(stateCode);
    }

    @Override
    @Transactional
    public void addStateSetting(WorkflowImpl workflow, String stateCode, String code)
    {
        workflow.addStateSetting(stateCode, code);
    }

    @Override
    @Transactional
    public TransitionImpl addTransition(WorkflowImpl workflow, String stateCodeFrom, String stateCodeTo)
    {
        return workflow.addTransition(stateCodeFrom, stateCodeTo);
    }

    @Override
    @Transactional
    public void setEndStateCode(WorkflowImpl workflow, String stateCode)
    {
        workflow.setEndStateCode(stateCode);
    }

    @Override
    @Transactional
    public void setOriginalStateCode(WorkflowImpl workflow, String stateCode)
    {
        workflow.setOriginalStateCode(stateCode);
    }
}
