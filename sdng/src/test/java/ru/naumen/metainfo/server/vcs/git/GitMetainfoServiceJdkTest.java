package ru.naumen.metainfo.server.vcs.git;

import static org.apache.commons.lang3.RandomStringUtils.randomAlphabetic;
import static org.junit.Assert.assertArrayEquals;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.atMost;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static ru.naumen.core.server.script.ScriptHelper.generateModuleChecksum;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

import org.apache.commons.io.IOUtils;
import org.eclipse.jgit.api.Git;
import org.eclipse.jgit.api.errors.GitAPIException;
import org.eclipse.jgit.lib.Constants;
import org.eclipse.jgit.lib.Repository;
import org.eclipse.jgit.revwalk.RevCommit;
import org.eclipse.jgit.revwalk.RevWalk;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import ru.naumen.commons.server.utils.XmlUtils;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.ConfigurationProperties;
import ru.naumen.core.server.git.BaseRepositoryTest;
import ru.naumen.core.server.git.GitRepositoryManager;
import ru.naumen.core.server.git.GitRepositoryManager.RepositoryControl;
import ru.naumen.core.server.git.RepositoryOperations;
import ru.naumen.core.server.script.conf.ScriptContainer;
import ru.naumen.core.server.script.conf.ScriptModule;
import ru.naumen.metainfo.server.libraries.ScriptLibrary;
import ru.naumen.metainfo.server.spi.MetainfoContainer;
import ru.naumen.metainfo.server.spi.MetainfoContainer.Header;
import ru.naumen.metainfo.server.spi.MetainfoExportSource;
import ru.naumen.metainfo.server.spi.importing.MetainfoImportService;
import ru.naumen.metainfo.server.spi.store.Attribute;
import ru.naumen.metainfo.server.spi.store.MetaClass;
import ru.naumen.metainfo.server.spi.store.UserAttribute;
import ru.naumen.metainfo.server.spi.store.UserMetaClass;
import ru.naumen.metainfo.server.spi.store.sec.AccessMatrix;
import ru.naumen.metainfo.server.spi.store.sec.AccessMatrixElement;
import ru.naumen.metainfo.server.spi.store.sec.SecDomain;
import ru.naumen.metainfo.server.spi.store.sec.VersAccessMatrix;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.script.Script;
import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.metainfo.shared.ui.ObjectList;
import ru.naumen.metainfo.shared.ui.UIContainer;

@RunWith(MockitoJUnitRunner.class)
public class GitMetainfoServiceJdkTest extends BaseRepositoryTest
{
    @Mock
    MetainfoExportSource exportSource;

    @Mock
    MetainfoImportService metainfoImportService;

    @Mock
    ConfigurationProperties configurationProperties;

    private GitMetainfoService gitMetainfoService;
    private XmlUtils xmlUtils;
    private GitRepositoryManager gitRepositoryManager;

    private final String branch = randomAlphabetic(10);
    private final String commitMessage = randomAlphabetic(20);

    @Before
    public void setUpGitManager() throws Exception
    {
        gitRepositoryManager = new GitRepositoryManager(folder.newFolder().getAbsolutePath(), getRemoteUri(), 30,
                credentialsProvider, personIdentProvider);
        xmlUtils = new XmlUtils();
        gitMetainfoService = new GitMetainfoService(gitRepositoryManager, exportSource, xmlUtils,
                metainfoImportService, configurationProperties);
    }

    @Test(expected = FxException.class)
    public void testExceptionOnMetainfoMissing() throws GitAPIException
    {
        final String branchName = randomAlphabetic(10);
        remoteGit.branchCreate().setName(branchName).call();
        gitMetainfoService.importFromVCS(branchName);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testExceptionOnNonExistentBranch()
    {
        gitMetainfoService.importFromVCS(randomAlphabetic(10));
    }

    @Test
    public void testExportAndImport()
    {
        final String metainfoVersion = randomAlphabetic(10);

        final ArgumentCaptor<MetainfoContainer> argumentCaptor = ArgumentCaptor.forClass(
                MetainfoContainer.class);

        MetainfoContainer container = new MetainfoContainer();
        final Header head = new Header();
        head.setVersion(metainfoVersion);
        container.setHead(head);
        when(exportSource.exportObject(anyString())).thenReturn(container);
        gitMetainfoService.exportToVCS(branch, commitMessage, "full");

        gitMetainfoService.importFromVCS(branch);
        verify(metainfoImportService).importMetainfo(argumentCaptor.capture());

        final MetainfoContainer importedContainer = argumentCaptor.getValue();
        assertEquals(metainfoVersion, importedContainer.getHead().getVersion());
    }

    @Test
    public void testExportAndImportWithBranchSwitch()
    {
        final String secondBranch = randomAlphabetic(10);

        final ArgumentCaptor<MetainfoContainer> argumentCaptor = ArgumentCaptor.forClass(
                MetainfoContainer.class);

        final ArgumentCaptor<MetainfoContainer> secondBranchArgumentCaptor = ArgumentCaptor.forClass(
                MetainfoContainer.class);

        MetainfoContainer container = new MetainfoContainer();
        final Header head = new Header();
        head.setVersion(branch);
        container.setHead(head);
        when(exportSource.exportObject(anyString())).thenReturn(container);
        gitMetainfoService.exportToVCS(branch, commitMessage, "full");

        container.getHead().setVersion(secondBranch);
        gitMetainfoService.exportToVCS(secondBranch, commitMessage, "full");

        gitMetainfoService.importFromVCS(branch);
        verify(metainfoImportService).importMetainfo(argumentCaptor.capture());

        MetainfoContainer importedContainer = argumentCaptor.getValue();
        assertEquals(branch, importedContainer.getHead().getVersion());

        gitMetainfoService.importFromVCS(secondBranch);
        verify(metainfoImportService, atMost(2)).importMetainfo(secondBranchArgumentCaptor.capture());

        importedContainer = secondBranchArgumentCaptor.getValue();
        assertEquals(secondBranch, importedContainer.getHead().getVersion());
    }

    @Test
    public void testExportMetainfo() throws IOException, GitAPIException
    {
        final String metainfoVersion = randomAlphabetic(10);

        MetainfoContainer container = new MetainfoContainer();
        final Header head = new Header();
        head.setVersion(metainfoVersion);
        container.setHead(head);
        when(exportSource.exportObject(anyString())).thenReturn(container);
        gitMetainfoService.exportToVCS(branch, commitMessage, "full");

        remoteGit.checkout().setName(branch).call();
        final File metainfoOnRemote = remoteGit.getRepository()
                .getWorkTree()
                .getAbsoluteFile()
                .toPath()
                .resolve(GitMetainfoService.METAINFO)
                .resolve("metainfo.xml")
                .toFile();

        try (FileInputStream fileInputStream = new FileInputStream(metainfoOnRemote))
        {
            final MetainfoContainer metainfoContainer = xmlUtils.parseXml(fileInputStream, MetainfoContainer.class,
                    false);
            assertEquals(metainfoVersion, metainfoContainer.getHead().getVersion());
        }
    }

    @Test
    public void testExportMetainfoWithoutAdminLite() throws IOException, GitAPIException
    {
        final String metainfoVersion = randomAlphabetic(10);

        MetainfoContainer container = new MetainfoContainer();
        final Header head = new Header();
        head.setVersion(metainfoVersion);
        container.setHead(head);
        String exportMode = "withoutAdminLite";
        when(exportSource.exportObject(exportMode)).thenReturn(container);
        gitMetainfoService.exportToVCS(branch, commitMessage, exportMode);

        remoteGit.checkout().setName(branch).call();
        final File metainfoOnRemote = remoteGit.getRepository()
                .getWorkTree()
                .getAbsoluteFile()
                .toPath()
                .resolve(GitMetainfoService.METAINFO)
                .resolve("metainfo.xml")
                .toFile();

        try (FileInputStream fileInputStream = new FileInputStream(metainfoOnRemote))
        {
            final MetainfoContainer metainfoContainer = xmlUtils.parseXml(fileInputStream, MetainfoContainer.class,
                    false);
            assertEquals(metainfoVersion, metainfoContainer.getHead().getVersion());
        }
    }

    @Test
    public void testImportFromSpecificCommit()
    {
        MetainfoContainer firstContainer = new MetainfoContainer();
        Header firstHeader = new Header();
        firstHeader.setVersion("first");
        firstContainer.setHead(firstHeader);

        MetainfoContainer secondContainer = new MetainfoContainer();
        Header secondHeader = new Header();
        secondHeader.setVersion("second");
        secondContainer.setHead(secondHeader);

        when(exportSource.exportObject(anyString())).thenReturn(firstContainer);
        final String targetBranch = randomAlphabetic(10);
        gitMetainfoService.exportToVCS(targetBranch, "firstContainer", "full");

        final String commitHash;
        try (RepositoryControl repositoryControl = gitRepositoryManager.acquireRepositoryControl();
             RepositoryOperations repositoryOperations = repositoryControl.operations())
        {
            commitHash = repositoryOperations.getLatestCommitInfo().getCommitHash();
        }
        when(exportSource.exportObject(anyString())).thenReturn(secondContainer);
        gitMetainfoService.exportToVCS(targetBranch, "secondContainer", "full");

        final ArgumentCaptor<MetainfoContainer> argumentCaptor = ArgumentCaptor.forClass(
                MetainfoContainer.class);

        gitMetainfoService.importFromVCS(commitHash);
        verify(metainfoImportService).importMetainfo(argumentCaptor.capture());
        final MetainfoContainer value = argumentCaptor.getValue();
        assertEquals(firstContainer.getHead().getVersion(), value.getHead().getVersion());

        final ArgumentCaptor<MetainfoContainer> branchContainerCaptor = ArgumentCaptor.forClass(
                MetainfoContainer.class);

        gitMetainfoService.importFromVCS(targetBranch);
        verify(metainfoImportService, atMost(2)).importMetainfo(branchContainerCaptor.capture());
        final MetainfoContainer branchContainer = branchContainerCaptor.getValue();
        assertEquals(secondContainer.getHead().getVersion(), branchContainer.getHead().getVersion());
    }

    @Test
    public void testSingleFileFreshMetainfoImport() throws GitAPIException, IOException
    {
        remoteGit.branchCreate().setName(branch).call();
        remoteGit.checkout().setName(branch).call();

        //Создаем файл с метаинфой в remote репозитории
        MetainfoContainer meta = new MetainfoContainer();
        final Header header = new Header();
        header.setVersion(branch);
        meta.setHead(header);
        final Path metainfoFile = remoteGit.getRepository()
                .getWorkTree()
                .toPath()
                .resolve(GitMetainfoService.METAINFO)
                .resolve("metainfo.xml");
        Path parent = metainfoFile.getParent();
        Files.createDirectories(parent);
        writeSomethingToFile(metainfoFile,
                xmlUtils.toXml(meta, false));
        remoteGit.add().addFilepattern(".").call();
        remoteGit.commit().setMessage("Meta commit").call();
        remoteGit.checkout().setName("master").call();

        //Создаем новый экземпляр сервиса работы с гитом,  который склонирует репозиторий
        final GitRepositoryManager gitRepositoryManager = new GitRepositoryManager(folder.newFolder().getAbsolutePath(),
                getRemoteUri(), 30, credentialsProvider, personIdentProvider);
        gitMetainfoService = new GitMetainfoService(gitRepositoryManager, exportSource, xmlUtils,
                metainfoImportService, configurationProperties);

        ArgumentCaptor<MetainfoContainer> captor = ArgumentCaptor.forClass(MetainfoContainer.class);
        gitMetainfoService.importFromVCS(branch);
        verify(metainfoImportService).importMetainfo(captor.capture());
        assertEquals(branch, captor.getValue().getHead().getVersion());
    }

    @Test
    public void testTreeExportAndImport()
    {
        final String metainfoVersion = randomAlphabetic(10);

        final String attributeCode = randomAlphabetic(10);
        final ClassFqn classFqn = ClassFqn.parse(randomAlphabetic(6));

        final String uiContainerCode = randomAlphabetic(10);
        final String contentUuid = randomAlphabetic(10);
        MetainfoContainer container = new MetainfoContainer();
        UserMetaClass mc = new UserMetaClass();

        UserAttribute userAttribute = new UserAttribute();
        userAttribute.setCode(attributeCode);
        mc.getAttribute().add(userAttribute);

        mc.setFqn(classFqn);
        container.getMetaClasses().add(mc);
        final Header head = new Header();
        head.setVersion(metainfoVersion);
        container.setHead(head);

        final UIContainer uiContainer = new UIContainer();
        uiContainer.setFqn(classFqn);
        uiContainer.setCode(uiContainerCode);

        final ObjectList objectList = new ObjectList();
        objectList.setUuid(contentUuid);
        uiContainer.setContent(objectList);

        container.getUi().add(uiContainer);

        when(exportSource.exportObject(anyString())).thenReturn(container);
        gitMetainfoService.exportToVCS(branch, commitMessage, "full");

        ArgumentCaptor<MetainfoContainer> captor = ArgumentCaptor.forClass(MetainfoContainer.class);
        gitMetainfoService.importFromVCS(branch);
        verify(metainfoImportService).importMetainfo(captor.capture());
        final MetainfoContainer importedContainer = captor.getValue();

        final List<MetaClass> metaClasses = importedContainer.getMetaClasses();
        assertEquals(1, metaClasses.size());
        final MetaClass metaClass = metaClasses.get(0);
        assertEquals(classFqn, metaClass.getFqn());

        final List<Attribute> attributes = metaClass.getAttribute();
        assertEquals(1, attributes.size());
        final Attribute attribute = attributes.get(0);
        assertEquals(attributeCode, attribute.getCode());

        final List<UIContainer> ui = container.getUi();
        assertEquals(1, ui.size());
        final UIContainer importedUIContainer = ui.get(0);
        assertEquals(uiContainerCode, importedUIContainer.getCode());
        assertEquals(classFqn, uiContainer.getFqn());

        final Content content = importedUIContainer.getContent();
        assertEquals(ObjectList.class, content.getClass());
        assertEquals(contentUuid, content.getUuid());
    }

    @Test
    public void testScriptExportAndImport()
    {
        String scriptCode = randomAlphabetic(8);

        Script script = new Script();
        String scriptBody = randomAlphabetic(5) + '\n' + randomAlphabetic(5);
        script.setCode(scriptCode);
        script.setBody(scriptBody);

        MetainfoContainer container = new MetainfoContainer();
        container.setHead(new Header());
        container.getScripts().add(script);

        when(exportSource.exportObject(anyString())).thenReturn(container);
        gitMetainfoService.exportToVCS(branch, commitMessage, "full");

        ArgumentCaptor<MetainfoContainer> captor = ArgumentCaptor.forClass(MetainfoContainer.class);
        gitMetainfoService.importFromVCS(branch);
        verify(metainfoImportService).importMetainfo(captor.capture());
        final MetainfoContainer importedContainer = captor.getValue();

        Script importedScript = importedContainer.getScripts().get(0);
        assertEquals("Unexpected script body", scriptBody, importedScript.getBody());
    }

    @Test
    public void testScriptModuleExportAndImport()
    {
        String scriptCode = randomAlphabetic(8);

        ScriptModule scriptModule = new ScriptModule();
        String scriptBody = "\n\n" + randomAlphabetic(5) + "\n\n\n" + randomAlphabetic(5) + "\n";
        scriptModule.setCode(scriptCode);
        String moduleChecksum = generateModuleChecksum(scriptModule.getCodeWithoutEmbeddedApplication(), scriptBody,
                false, false, true);
        scriptModule.setScriptElement(ScriptContainer.create(scriptBody, moduleChecksum));

        String expectedModuleCheckSum = generateModuleChecksum(scriptModule.getCodeWithoutEmbeddedApplication(), scriptBody.strip(), false,
                false, true);

        MetainfoContainer container = new MetainfoContainer();
        container.setHead(new Header());
        container.getScriptModules().add(scriptModule);

        when(exportSource.exportObject(anyString())).thenReturn(container);
        gitMetainfoService.exportToVCS(branch, commitMessage, "full");

        ArgumentCaptor<MetainfoContainer> captor = ArgumentCaptor.forClass(MetainfoContainer.class);
        gitMetainfoService.importFromVCS(branch);
        verify(metainfoImportService).importMetainfo(captor.capture());
        final MetainfoContainer importedContainer = captor.getValue();

        ScriptModule importedModule = (ScriptModule)importedContainer.getScriptModules().get(0);

        String importedCheckSum = generateModuleChecksum(scriptModule.getCode(),
                importedModule.getScriptElement().getBody(), false, false, true);
        assertEquals("Unexpected saved checksum", expectedModuleCheckSum,
                importedModule.getScriptElement().getChecksum());
        assertEquals("Unexpected checksum", expectedModuleCheckSum, importedCheckSum);
        assertEquals("Unexpected script module body", scriptBody.strip(), importedModule.getScriptElement().getBody());
    }

    @Test
    public void testScriptModuleExport() throws IOException
    {
        var scriptModule = new ScriptModule();
        ScriptContainer scriptElement = new ScriptContainer();
        String firstString = randomAlphabetic(6);
        String secondString = randomAlphabetic(6);
        scriptElement.setBody(String.join("\n", firstString, secondString));
        scriptModule.setScriptElement(scriptElement);
        String uberScript = "uberModule";
        scriptModule.setCode(uberScript);

        MetainfoContainer container = new MetainfoContainer();
        container.setHead(new Header());
        container.getScriptModules().add(scriptModule);
        when(exportSource.exportObject(anyString())).thenReturn(container);
        gitMetainfoService.exportToVCS(branch, commitMessage, "full");

        String segmentID = scriptModule.getSegmentID();
        Path scriptPath = Paths.get(GitMetainfoService.METAINFO, scriptModule.getSegmentType(), segmentID,
                segmentID + ".groovy");
        try (RepositoryControl repositoryControl = gitRepositoryManager.acquireRepositoryControl())
        {
            Optional<InputStream> scriptBodyStream = repositoryControl.fileManager()
                    .openInput(scriptPath.toString());
            assertFalse("Script body should be stored", scriptBodyStream.isEmpty());
            try (InputStream inputStream = scriptBodyStream.get())
            {
                List<String> strings = IOUtils.readLines(inputStream, StandardCharsets.UTF_8);
                assertEquals(2, strings.size());
                assertEquals(firstString, strings.get(0));
                assertEquals(secondString, strings.get(1));
            }
        }
    }

    @Test
    public void testScriptExport() throws IOException
    {
        Script scriptModule = new Script();
        String firstString = randomAlphabetic(6);
        String secondString = randomAlphabetic(6);
        scriptModule.setBody(String.join("\n", firstString, secondString));
        String uberScript = "uberModule";
        scriptModule.setCode(uberScript);

        MetainfoContainer container = new MetainfoContainer();
        container.setHead(new Header());
        container.getScripts().add(scriptModule);
        when(exportSource.exportObject(anyString())).thenReturn(container);
        gitMetainfoService.exportToVCS(branch, commitMessage, "full");

        String segmentID = scriptModule.getSegmentID();
        Path scriptPath = Paths.get(GitMetainfoService.METAINFO, scriptModule.getSegmentType(), segmentID,
                segmentID + ".groovy");
        try (RepositoryControl repositoryControl = gitRepositoryManager.acquireRepositoryControl())
        {
            Optional<InputStream> scriptBodyStream = repositoryControl.fileManager()
                    .openInput(scriptPath.toString());
            assertFalse("Script body should be stored", scriptBodyStream.isEmpty());
            try (InputStream inputStream = scriptBodyStream.get())
            {
                List<String> strings = IOUtils.readLines(inputStream, StandardCharsets.UTF_8);
                assertEquals(2, strings.size());
                assertEquals(firstString, strings.get(0));
                assertEquals(secondString, strings.get(1));
            }
        }
    }

    @Test
    public void testLibraryExportAndImport()
    {
        final String libData = randomAlphabetic(10);
        final ScriptLibrary scriptLibrary = new ScriptLibrary();
        scriptLibrary.setName(libData);
        scriptLibrary.setChecksum(libData);
        scriptLibrary.setContent(libData.getBytes());

        MetainfoContainer container = new MetainfoContainer();
        container.setHead(new Header());
        container.getLibraries().add(scriptLibrary);
        when(exportSource.exportObject(anyString())).thenReturn(container);
        gitMetainfoService.exportToVCS(branch, commitMessage, "full");

        ArgumentCaptor<MetainfoContainer> captor = ArgumentCaptor.forClass(MetainfoContainer.class);
        gitMetainfoService.importFromVCS(branch);
        verify(metainfoImportService).importMetainfo(captor.capture());
        final MetainfoContainer importedContainer = captor.getValue();
        final ArrayList<ScriptLibrary> libraries = importedContainer.getLibraries();
        assertEquals("Unexpected amount of imported libraries", 1, libraries.size());

        final ScriptLibrary importedLibrary = libraries.get(0);
        assertEquals("Unexpected imported library name", scriptLibrary.getName(), importedLibrary.getName());
        assertEquals("Unexpected imported library checksum", scriptLibrary.getChecksum(),
                importedLibrary.getChecksum());
        assertArrayEquals("Unexpected imported library content", scriptLibrary.getContent(),
                importedLibrary.getContent());
    }

    @Test
    public void testCopyToRepository() throws IOException, GitAPIException
    {
        final File remoteRepository = folder.newFolder();
        final Git gitRepository = createGitRepository(remoteRepository);
        final String branchName = randomAlphabetic(6);
        final String subdirectory = randomAlphabetic(8);
        final String fileName = randomAlphabetic(7);
        final String fileContent = randomAlphabetic(10);
        final String anotherRemoteCommitMessage = "Commit at another repository";
        writeAndCommitContentAtRemote(branchName, subdirectory, fileName, fileContent, gitRepository,
                anotherRemoteCommitMessage);

        final Repository repository = gitRepository.getRepository();
        final String commitHash;
        try (RevWalk revCommits = new RevWalk(repository))
        {
            commitHash = revCommits.parseCommit(repository.resolve(Constants.HEAD)).getName();
        }
        gitMetainfoService.copyToRepository("file://" + repository.getWorkTree().getAbsolutePath(), "", "", "master",
                commitHash);

        final Set<String> commitMessages = StreamSupport.stream(gitRepository.log().call().spliterator(), false)
                .map(RevCommit::getFullMessage)
                .collect(Collectors.toSet());
        Assert.assertEquals("Unexpected commits amount after copy", 2, commitMessages.size());
        Assert.assertTrue(commitMessages.contains(anotherRemoteCommitMessage));

        try (RepositoryControl repositoryControl = gitRepositoryManager.acquireRepositoryControl();
             final RepositoryOperations operations = repositoryControl.operations())
        {
            Assert.assertEquals(1, operations.showRemoteNames().size());
        }
    }

    @Test
    public void testConflictCopy() throws IOException, GitAPIException
    {
        final File remoteRepository = folder.newFolder();
        final Git gitRepository = createGitRepository(remoteRepository);
        final String branchName = randomAlphabetic(8);
        final String subdirectory = randomAlphabetic(8);
        final String fileName = randomAlphabetic(7);
        final String anotherRemoteCommitMessage = "Commit at another repository";
        writeAndCommitContentAtRemote(branchName, subdirectory, fileName, randomAlphabetic(10), remoteGit,
                "anotherRemoteCommitMessage");
        writeAndCommitContentAtRemote(branchName, subdirectory, fileName, randomAlphabetic(10), gitRepository,
                anotherRemoteCommitMessage);

        final Repository repository = gitRepository.getRepository();
        final String commitHash;
        try (RevWalk revCommits = new RevWalk(repository))
        {
            commitHash = revCommits.parseCommit(repository.resolve(Constants.HEAD)).getName();
        }
        gitMetainfoService.copyToRepository("file://" + repository.getWorkTree().getAbsolutePath(), "", "", branchName,
                commitHash);
        remoteGit.checkout().setName(branchName).call();
        final Set<String> commitMessages = StreamSupport.stream(remoteGit.log().call().spliterator(), false)
                .map(RevCommit::getFullMessage)
                .collect(Collectors.toSet());
        Assert.assertEquals("Unexpected commits amount after copy", 2, commitMessages.size());
        Assert.assertFalse(commitMessages.contains(anotherRemoteCommitMessage));

        try (RepositoryControl repositoryControl = gitRepositoryManager.acquireRepositoryControl();
             final RepositoryOperations operations = repositoryControl.operations())
        {
            Assert.assertEquals(1, operations.showRemoteNames().size());
        }
    }

    @Test
    public void testCopyToMissingBranch()
    {
        final String missingBranchName = randomAlphabetic(10);
        try
        {
            gitMetainfoService.copyToRepository("", "", "", missingBranchName, "");
        }
        catch (IllegalArgumentException e)
        {
            Assert.assertEquals("Branch " + missingBranchName + " not found", e.getMessage());
        }
    }

    @Test
    public void testSecDomainExportAndImport()
    {
        String data = randomAlphabetic(10);
        AccessMatrixElement matrixElement = new AccessMatrixElement();
        matrixElement.setMarker(data);
        matrixElement.setProfile(data);
        matrixElement.setValue(Boolean.TRUE);
        AccessMatrix accessMatrix = new AccessMatrix();
        accessMatrix.getPermission().add(matrixElement);
        VersAccessMatrix versAccessMatrix = new VersAccessMatrix();
        versAccessMatrix.getPermission().add(matrixElement);
        SecDomain domain = new SecDomain();
        domain.setCode(data);
        domain.setAccessMatrix(accessMatrix);
        domain.setVersAccessMatrix(versAccessMatrix);

        MetainfoContainer container = new MetainfoContainer();
        container.setHead(new Header());
        container.getSecDomains().add(domain);
        when(exportSource.exportObject(anyString())).thenReturn(container);
        gitMetainfoService.exportToVCS(branch, commitMessage, "full");

        ArgumentCaptor<MetainfoContainer> captor = ArgumentCaptor.forClass(MetainfoContainer.class);
        gitMetainfoService.importFromVCS(branch);
        verify(metainfoImportService).importMetainfo(captor.capture());
        final MetainfoContainer importedContainer = captor.getValue();
        final ArrayList<SecDomain> domains = importedContainer.getSecDomains();
        assertEquals("Unexpected amount of imported security domains", 1, domains.size());

        final SecDomain importedDomain = domains.get(0);
        assertEquals("Unexpected imported security domain code", domain.getCode(), importedDomain.getCode());
        AccessMatrix importedMatrix = importedDomain.getAccessMatrix();
        assertEquals("Unexpected amount of imported access matrix", 1, importedMatrix.getPermission().size());
        assertEquals("Unexpected imported access matrix permission", accessMatrix.getPermission().get(0),
                importedMatrix.getPermission().get(0));
        VersAccessMatrix importedVersMatrix = importedDomain.getVersAccessMatrix();
        assertEquals("Unexpected amount of imported access matrix", 1, importedVersMatrix.getPermission().size());
        assertEquals("Unexpected imported access matrix permission", versAccessMatrix.getPermission().get(0),
                importedVersMatrix.getPermission().get(0));
    }
}