package ru.naumen.metainfo.server.spi.dispatch;

import static com.google.common.collect.Lists.newArrayList;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

import java.util.Arrays;
import java.util.List;

import jakarta.inject.Inject;

import com.google.common.collect.Lists;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.transaction.annotation.Transactional;

import net.customware.gwt.dispatch.server.Dispatch;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.server.bo.employee.Employee;
import ru.naumen.core.server.dispatch.ObjectTestUtils;
import ru.naumen.core.server.dispatch.SecurityTestHelper;
import ru.naumen.core.server.dispatch.SecurityTestUtils;
import ru.naumen.core.server.jta.TransactionRunner;
import ru.naumen.core.server.script.storage.ScriptStorageService;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.utils.UUIDGenerator;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.AggregateAttributeType;
import ru.naumen.metainfo.shared.dispatch2.GetMetaClassResponse;
import ru.naumen.metainfo.shared.dispatch2.ResetAttributeAction;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.script.Script;
import ru.naumen.metainfo.shared.script.ScriptDto;

/**
 * <AUTHOR>
 * @since 18.02.2011
 *
 */
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class ResetAttributeActionHandlerDbTest
{
    @Inject
    private Dispatch dispatch;
    @Inject
    private MetainfoService metainfoService;
    @Inject
    private ObjectTestUtils utils;
    @Inject
    private SecurityTestUtils securityTestUtils;
    @Inject
    private ScriptStorageService scriptStorageService;

    @Test
    @Transactional
    public void resetFlexAttr() throws Exception
    {
        //настройка системы
        ClassFqn parentFqn = ClassFqn.parse(Constants.Employee.CLASS_ID);
        ClassFqn fqn = utils.createCase(parentFqn);
        String attr1 = utils.createAttribute(parentFqn);
        String attr2 = utils.createAttribute(parentFqn);
        utils.editBoolAttribute(fqn, attr1);
        utils.editBoolAttribute(fqn, attr2);
        //вызов системы
        dispatch.execute(new ResetAttributeAction(fqn, ResetAttributeAction.Type.FLEX));
        //проверка утверждений
        List<String> attrs = Arrays.asList(attr1, attr2);
        for (String attr : attrs)
        {
            Attribute attrInParent = metainfoService.getMetaClass(parentFqn).getAttribute(attr);
            Attribute resetAttr = metainfoService.getMetaClass(fqn).getAttribute(attr);
            Assert.assertEquals(attrInParent.getCode(), resetAttr.getCode());
            Assert.assertEquals(attrInParent.getTitle(), resetAttr.getTitle());
            Assert.assertEquals(attrInParent.getDescription(), resetAttr.getDescription());
            Assert.assertEquals(attrInParent.getViewPresentation().getCode(), resetAttr.getViewPresentation().getCode());
            Assert.assertEquals(attrInParent.getEditPresentation().getCode(), resetAttr.getEditPresentation().getCode());
        }
        //очистка
    }

    @Test
    @Transactional
    public void resetOneAttr() throws Exception
    {
        //настройка системы
        ClassFqn parentFqn = ClassFqn.parse(Constants.Employee.CLASS_ID);
        ClassFqn fqn = utils.createCase(parentFqn);
        String attr = utils.createAttribute(parentFqn);
        utils.editBoolAttribute(fqn, attr);
        //вызов системы
        dispatch.execute(new ResetAttributeAction(fqn, attr, Lists.<ClassFqn> newArrayList()));
        //проверка утверждений
        Attribute attrInParent = metainfoService.getMetaClass(parentFqn).getAttribute(attr);
        Attribute resetAttr = metainfoService.getMetaClass(fqn).getAttribute(attr);
        Assert.assertEquals(attrInParent.getCode(), resetAttr.getCode());
        Assert.assertEquals(attrInParent.getTitle(), resetAttr.getTitle());
        Assert.assertEquals(attrInParent.getDescription(), resetAttr.getDescription());
        Assert.assertEquals(attrInParent.getViewPresentation().getCode(), resetAttr.getViewPresentation().getCode());
        Assert.assertEquals(attrInParent.getEditPresentation().getCode(), resetAttr.getEditPresentation().getCode());
        Assert.assertEquals(attrInParent.isRequired(), resetAttr.isRequired());
        //очистка
    }

    @Test
    @Transactional
    public void resetSystemAttr() throws Exception
    {
        //настройка системы
        ClassFqn parentFqn = ClassFqn.parse(Constants.Employee.CLASS_ID);
        ClassFqn fqn = utils.createCase(parentFqn);
        String attr1 = Constants.Employee.FIRST_NAME;
        String attr2 = Constants.PARENT_ATTR;
        utils.editAttribute(fqn, attr1);
        utils.editAttribute(fqn, attr2);
        //вызов системы
        dispatch.execute(new ResetAttributeAction(fqn, ResetAttributeAction.Type.SYSTEM));
        //проверка утверждений
        List<String> attrs = Arrays.asList(attr1, attr2);
        for (String attr : attrs)
        {
            Attribute attrInParent = metainfoService.getMetaClass(parentFqn).getAttribute(attr);
            Attribute resetAttr = metainfoService.getMetaClass(fqn).getAttribute(attr);
            Assert.assertEquals(attrInParent.getCode(), resetAttr.getCode());
            Assert.assertEquals(attrInParent.getTitle(), resetAttr.getTitle());
            Assert.assertEquals(attrInParent.getDescription(), resetAttr.getDescription());
            Assert.assertEquals(attrInParent.getViewPresentation().getCode(), resetAttr.getViewPresentation().getCode());
            Assert.assertEquals(attrInParent.getEditPresentation().getCode(), resetAttr.getEditPresentation().getCode());
        }
        //очистка
    }

    @Before
    public void setUp()
    {
        SecurityTestHelper.autenticateAsSuperUser();
    }

    /**
     * Проверка на то, что при сбросе атрибута переопределённая информация о скрипте вычисления значения при 
     * редактировании не удаляется из кэша информации о скриптах, а информация об атрибуте
     * удаляется из мест использования скрипта.
     * 
     * @throws Exception
     */
    @Test
    @Transactional
    public void testOverrideAttributeComputableOnFormScriptShouldBeReset() throws Exception
    {
        //настройка системы
        ClassFqn parentFqn = ClassFqn.parse(Constants.Employee.CLASS_ID);
        ClassFqn fqn = utils.createCase(parentFqn);
        String title = UUIDGenerator.get().nextUUID();
        String description = UUIDGenerator.get().nextUUID();
        ScriptDto scriptDto = utils.createScriptDto("return []");
        String code = utils.createAttribute(parentFqn, true, false, false, title, description, false, null, true,
                scriptDto, false, null);

        MetaClass oldMetaclass = metainfoService.getMetaClass(parentFqn);
        Attribute oldAttribute = oldMetaclass.getAttribute(code);

        ScriptDto scriptDtoForReset = utils.createScriptDto("[]");
        Attribute attribute = utils.editAttribute(fqn, title, description, code, false, null, true, scriptDtoForReset,
                false, null, false, null);
        assertReset(fqn, code, oldAttribute, attribute, attribute.getComputableOnFormScript());
    }

    /**
     * Проверка на то, что при сбросе атрибута переопределённая информация о скрипте вычисления значения по-умолчанию
     * не удаляется из кэша информации о скриптах, а информация об атрибуте
     * удаляется из мест использования скрипта.
     * 
     * @throws Exception
     */
    @Test
    @Transactional
    public void testOverrideAttributeScriptForDefaultShouldBeReset() throws Exception
    {
        //настройка системы
        ClassFqn parentFqn = ClassFqn.parse(Constants.Employee.CLASS_ID);
        ClassFqn fqn = utils.createCase(parentFqn);
        String title = UUIDGenerator.get().nextUUID();
        String description = UUIDGenerator.get().nextUUID();
        ScriptDto scriptDto = utils.createScriptDto("return 'asd'");
        String code = utils.createAttribute(parentFqn, true, false, false, title, description, false, null, false,
                null, true, scriptDto);

        MetaClass oldMetaclass = metainfoService.getMetaClass(parentFqn);
        Attribute oldAttribute = oldMetaclass.getAttribute(code);

        ScriptDto scriptDtoForReset = utils.createScriptDto("'asd'");
        Attribute attribute = utils.editAttribute(fqn, title, description, code, false, null, false, null, true,
                scriptDtoForReset, false, null);

        assertReset(fqn, code, oldAttribute, attribute, attribute.getScriptForDefault());
    }

    /**
     * Проверка на то, что после редактирования скрипта фильтрации значений атрибута, у переопределённого
     * атрибута новый номер, а у переопределяемого - старый.
     * 
     * @throws Exception
     */
    @Test
    @Transactional
    public void testOverrideAttributeScriptForFiltrationShouldBeReset() throws Exception
    {
        securityTestUtils.initLicensing();
        ClassFqn parentFqn = Constants.Employee.FQN;
        ClassFqn fqn = utils.createCase(parentFqn);

        ScriptDto scriptDto = utils.createScriptDto("return []");
        String code = utils.createAggregateOUAttribute(parentFqn, null, true, scriptDto);
        MetaClass oldMetaClass = metainfoService.getMetaClass(parentFqn);
        Attribute oldAttribute = oldMetaClass.getAttribute(code);

        ScriptDto scriptDtoForReset = utils.createScriptDto("[]");
        Attribute attribute = utils.editAttribute(fqn, oldAttribute.getTitle(), oldAttribute.getDescription(), code,
                false, null, false, null, false, null, true, scriptDtoForReset);

        assertReset(fqn, code, oldAttribute, attribute, attribute.getScriptForFiltration());
    }

    /**
     * Проверка на то, что при сбросе атрибута переопределённая информация о скрипте вычисления значения
     * не удаляется из кэша информации о скриптах, а информация об атрибуте
     * удаляется из мест использования скрипта.
     * 
     * @throws Exception
     */
    @Test
    @Transactional
    public void testOverrideAttributeScriptShouldBeReset() throws Exception
    {
        //настройка системы
        ClassFqn parentFqn = ClassFqn.parse(Constants.Employee.CLASS_ID);
        ClassFqn fqn = utils.createCase(parentFqn);
        String title = UUIDGenerator.get().nextUUID();
        String description = UUIDGenerator.get().nextUUID();
        ScriptDto scriptDto = utils.createScriptDto("return 'asd'");
        String code = utils.createAttribute(parentFqn, true, false, false, title, description, true, scriptDto, false,
                null, false, null);

        MetaClass oldMetaclass = metainfoService.getMetaClass(parentFqn);
        Attribute oldAttribute = oldMetaclass.getAttribute(code);

        ScriptDto scriptDtoForReset = utils.createScriptDto("'asd'");
        Attribute attribute = utils.editAttribute(fqn, title, description, code, true, scriptDtoForReset, false, null,
                false, null, false, null);

        assertReset(fqn, code, oldAttribute, attribute, attribute.getScript());
    }

    @Test
    public void testResetAggregate() throws Exception
    {
        securityTestUtils.initLicensing();
        ClassFqn[] fqns = TransactionRunner.call(() -> {
            ClassFqn fqn1 = utils.createCase(Constants.Employee.FQN);
            ClassFqn fqn2 = utils.createCase(fqn1);
            return new ClassFqn[] { fqn1, fqn2 };
        });
        ClassFqn fqn1 = fqns[0];
        ClassFqn fqn2 = fqns[1];
        MetaClass metaClass2 = metainfoService.getMetaClass(fqn2);
        Employee employee = utils.createEmployee();
        Object defValue = utils.getTreeDtObject(employee, employee.getParent());

        String code = utils.createAggregateOUAttribute(fqn1, defValue);
        Assert.assertNotNull(metaClass2.getAttribute(code).getDefaultValue());
        Assert.assertNotNull(metaClass2.getAttribute(code + AggregateAttributeType.OU_POSTFIX).getDefaultValue());

        utils.editAttribute(fqn2, code, null);
        metaClass2 = metainfoService.getMetaClass(fqn2);
        Assert.assertNull(metaClass2.getAttribute(code).getDefaultValue());
        Assert.assertNull(metaClass2.getAttribute(code + AggregateAttributeType.OU_POSTFIX).getDefaultValue());

        dispatch.execute(new ResetAttributeAction(fqn2, code, newArrayList(Constants.Employee.FQN, Constants.OU.FQN)));
        metaClass2 = metainfoService.getMetaClass(fqn2);
        Assert.assertNotNull(metaClass2.getAttribute(code).getDefaultValue());
        Assert.assertNotNull(metaClass2.getAttribute(code + AggregateAttributeType.OU_POSTFIX).getDefaultValue());
    }

    private void assertReset(ClassFqn fqn, String code, Attribute oldAttribute, Attribute attribute,
            String resetScriptCode) throws DispatchException
    {
        GetMetaClassResponse metaclassResponse = dispatch.execute(new ResetAttributeAction(fqn, attribute.getCode(),
                Lists.<ClassFqn> newArrayList()));
        MetaClass metaclass = metainfoService.getMetaClass(metaclassResponse.getMetaClass().getFqn());
        Attribute resetAttribute = metaclass.getAttribute(code);

        Script script = scriptStorageService.getScript(resetScriptCode);
        assertNotNull(
                "Информация о переопределённом скрипте сброшенного атрибута не должна быть удалена из кэша информации о скриптах.",
                script);
        assertTrue("Информация об атрибуте должна быть удалена из мест использования скрипта.",
                CollectionUtils.isEmpty(script.getUsagePoints()));
        assertEquals("Информация о скрипте по умолчанию должна быть той же, что и в переопределяемом атрибуте.",
                oldAttribute.getScriptForDefault(), resetAttribute.getScriptForDefault());
        assertEquals("Информация о скрипте фильтрации должна быть той же, что и в переопределяемом атрибуте.",
                oldAttribute.getScriptForFiltration(), resetAttribute.getScriptForFiltration());
        assertEquals(
                "Информация о скрипте вычисления при редактировании должна быть той же, что и в переопределяемом атрибуте.",
                oldAttribute.getComputableOnFormScript(), resetAttribute.getComputableOnFormScript());
        assertEquals("Информация о скрипте должна быть той же, что и в переопределяемом атрибуте.",
                oldAttribute.getScript(), resetAttribute.getScript());

        //Очистка
        utils.deleteUnusedScript(resetScriptCode);
    }
}
