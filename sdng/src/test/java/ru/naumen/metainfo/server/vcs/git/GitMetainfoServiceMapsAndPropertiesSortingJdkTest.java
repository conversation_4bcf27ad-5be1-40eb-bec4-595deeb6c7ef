package ru.naumen.metainfo.server.vcs.git;

import static org.apache.commons.lang3.RandomStringUtils.random;
import static org.apache.commons.lang3.RandomStringUtils.randomAlphabetic;

import java.io.ByteArrayOutputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import org.apache.commons.lang3.RandomUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.Parameterized;
import org.junit.runners.Parameterized.Parameters;

import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.server.customforms.storage.CustomFormInfo;
import ru.naumen.core.server.customforms.storage.FormParameterInfo;
import ru.naumen.core.server.escalation.EscalationContainer;
import ru.naumen.metainfo.server.spi.store.UserMetaClass;
import ru.naumen.metainfo.shared.AdaptedClassFqn;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.ui.ActionTool;
import ru.naumen.metainfo.shared.ui.EmbeddedApplicationContent;
import ru.naumen.metainfo.shared.ui.ToolBar;
import ru.naumen.metainfo.shared.ui.UIContainer;

/**
 * Тестирование разного рода сортировок {@link Map} и {@link ru.naumen.common.shared.utils.IProperties} в JAXB
 * элементах при экспорте метаинформации в Git
 * Записи на диск сегментов метаинформации не происходит, запись сегментов перехватывается.
 * <AUTHOR>
 * @since 17.03.2020
 */
@RunWith(Parameterized.class)
public class GitMetainfoServiceMapsAndPropertiesSortingJdkTest extends BaseGitMetainfoServiceXmlTest
{
    @Parameters(name = "Props {0}")
    public static Collection<Object[]> data()
    {
        return Arrays.asList(new Object[][] {
                { List.of("ZProp", "zProp", "cProp", "yProp") },
                { IntStream.range(0, RandomUtils.nextInt(4, 10))
                        .mapToObj(i -> randomAlphabetic(5)).collect(
                        Collectors.toList()) }
        });
    }

    private final List<String> propertyNames;

    public GitMetainfoServiceMapsAndPropertiesSortingJdkTest(List<String> propertyNames)
    {
        this.propertyNames = propertyNames;
    }

    @Test
    public void testMultipleMapPropertiesSort() throws Exception
    {
        int i = propertyNames.size() / 2;
        List<String> first = propertyNames.subList(0, i);
        List<String> second = propertyNames.subList(i, propertyNames.size());
        EscalationContainer escalationContainer = new EscalationContainer();
        MapProperties firstProperties = new MapProperties();
        first.forEach(s -> firstProperties.put(s, s));

        MapProperties secondProps = new MapProperties();
        second.forEach(s -> secondProps.put(s, s));
        List<MapProperties> valueMapItems = escalationContainer.getValueMapItems();
        valueMapItems.add(firstProperties);
        valueMapItems.add(secondProps);

        container.setEscalationContainer(escalationContainer);

        final ByteArrayOutputStream stream = new ByteArrayOutputStream();
        setupSegmentOutputStream(stream, escalationContainer.getSegmentID());

        gitMetainfoService.exportToVCS(branch, branch, "full");
        byte[] xmlBytes = stream.toByteArray();

        String firstValueMap = "//value-map-item[1]/item/@key";
        String secondValueMap = "//value-map-item[2]/item/@key";
        assertElementsAscendingSort(xmlBytes, firstValueMap, secondValueMap);
    }

    @Test
    public void testMetaClassProperties() throws Exception
    {
        MapProperties mapProperties = new MapProperties();
        propertyNames.forEach(s -> mapProperties.put(s, s));
        UserMetaClass metaClass = new UserMetaClass();
        metaClass.setFqn(ClassFqn.parse(randomAlphabetic(8)));
        metaClass.setProperties(mapProperties);

        container.getMetaClasses().add(metaClass);

        final ByteArrayOutputStream stream = new ByteArrayOutputStream();
        setupSegmentOutputStream(stream, metaClass.getSegmentID());

        gitMetainfoService.exportToVCS(branch, branch, "full");
        String expression = "//properties/item/@key";
        List<String> sortedProps = new ArrayList<>(propertyNames);
        sortedProps.sort(Comparator.naturalOrder());
        assertElementsAscendingSort(stream.toByteArray(), expression);
    }

    @Test
    public void testContentToolProperties() throws Exception
    {
        MapProperties mapProperties = new MapProperties();
        propertyNames.forEach(s -> mapProperties.put(s, s));
        UIContainer uiContainer = new UIContainer();
        String metaClassCode = random(8);
        uiContainer.setFqn(ClassFqn.parse(metaClassCode));
        uiContainer.setCode(metaClassCode);
        ToolBar toolBar = new ToolBar();
        toolBar.setUuid(random(8));

        ActionTool actionTool = new ActionTool();
        actionTool.setAction(randomAlphabetic(8));
        actionTool.setParams(mapProperties);
        toolBar.addTool(actionTool);
        uiContainer.setContent(toolBar);
        container.getUi().add(uiContainer);

        final ByteArrayOutputStream stream = new ByteArrayOutputStream();
        setupSegmentOutputStream(stream, actionTool.getSegmentID());

        gitMetainfoService.exportToVCS(branch, branch, "full");
        String expression = "//params/item/@key";

        assertElementsAscendingSort(stream.toByteArray(), expression);
    }

    @Test
    public void testEmbeddedApplicationContent() throws Exception
    {
        MapProperties mapProperties = new MapProperties();
        propertyNames.forEach(s -> mapProperties.put(s, s));
        UIContainer uiContainer = new UIContainer();
        String code = randomAlphabetic(8);
        uiContainer.setFqn(ClassFqn.parse(code));
        uiContainer.setCode(code);
        EmbeddedApplicationContent embeddedApplicationContent = new EmbeddedApplicationContent();
        embeddedApplicationContent.setUuid(randomAlphabetic(10));
        embeddedApplicationContent.setSerializableParametersValues(mapProperties);
        uiContainer.setContent(embeddedApplicationContent);
        container.getUi().add(uiContainer);

        final ByteArrayOutputStream stream = new ByteArrayOutputStream();
        setupSegmentOutputStream(stream, embeddedApplicationContent.getSegmentID());

        gitMetainfoService.exportToVCS(branch, branch, "full");
        String expression = "//parametersValues/item/@key";
        assertElementsAscendingSort(stream.toByteArray(), expression);
    }

    @Test
    public void testFormParametersSort() throws Exception
    {
        CustomFormInfo customFormInfo = new CustomFormInfo();
        customFormInfo.setCode(randomAlphabetic(8));
        FormParameterInfo formParameterInfo = new FormParameterInfo();
        Map<AdaptedClassFqn, String> complexRelationAggrAttrGroups = new HashMap<>();
        for (String propertyName : propertyNames)
        {
            AdaptedClassFqn adaptedClassFqn = new AdaptedClassFqn();
            adaptedClassFqn.setId(propertyName);
            complexRelationAggrAttrGroups.put(adaptedClassFqn, propertyName);
        }
        formParameterInfo.setComplexRelationAggrAttrGroups(complexRelationAggrAttrGroups);
        customFormInfo.getParameters().add(formParameterInfo);
        container.getCustomForms().add(customFormInfo);
        final ByteArrayOutputStream stream = new ByteArrayOutputStream();
        String segmentID = customFormInfo.getSegmentID();
        setupSegmentOutputStream(stream, segmentID);
        gitMetainfoService.exportToVCS(branch, branch, "full");
        String expression = "//complexRelationAggrAttrGroups/entry/key/id";

        assertElementsAscendingSort(stream.toByteArray(), expression);
    }
}
