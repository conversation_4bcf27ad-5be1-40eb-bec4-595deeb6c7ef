package ru.naumen.metainfo.server.spi.dispatch;

import jakarta.inject.Inject;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.transaction.annotation.Transactional;

import net.customware.gwt.dispatch.server.Dispatch;
import ru.naumen.core.server.dispatch.SecurityTestHelper;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.dispatch2.GetMetaClassesLiteAction;
import ru.naumen.metainfo.shared.dispatch2.GetMetaClassesLiteResponse;

/**
 * 
 * <AUTHOR>
 *
 */
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class GetMetaClassesActionHandlerDbTest
{
    @Inject
    Dispatch dispatch;
    @Inject
    MetainfoService metainfoService;

    @Test
    @Transactional
    public void exists() throws Exception
    {
        // настройка системы
        // вызов системы
        GetMetaClassesLiteAction a = new GetMetaClassesLiteAction();
        GetMetaClassesLiteResponse r = dispatch.execute(a);
        // проверка утверждений
        Assert.assertEquals(metainfoService.getMetaClasses().size(), r.getMetaClasses().size());
        // очистка
    }

    @Before
    public void setUp()
    {
        SecurityTestHelper.autenticateAsSuperUser();
    }
}
