package ru.naumen.metainfo.server.spi.elements;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import jakarta.inject.Inject;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.server.spi.MetainfoServiceBean;
import ru.naumen.metainfo.server.spi.dispatch.HandlerUtils;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.MetaClassLite.Status;
import ru.naumen.metainfo.shared.elements.NullParentRelationImpl;
import ru.naumen.metainfo.shared.elements.RelationImpl;

/**
 * <AUTHOR>
 */
@Component
public class TransactionalMetaClassImpl implements TransactionalMetaClass
{
    @Inject
    MetainfoService metainfoService;

    @Transactional
    @Override
    public AttributeGroupDeclarationImpl addAttributeGroupDeclaration(MetaClassImpl metaclass, String code)
    {
        return metaclass.addAttributeGroupDeclaration(code);
    }

    @Transactional
    @Override
    public AttributeGroupDeclarationImpl addAttributeGroupOverride(MetaClassImpl metaclass, String code)
    {
        return metaclass.addAttributeGroupOverride(code);
    }

    @Transactional
    @Override
    public AttributeGroupDeclarationImpl addAttributeGroupSystemOverride(MetaClassImpl metaclass, String code)
    {
        return metaclass.addAttributeGroupSystemOverride(code);
    }

    @Transactional
    @Override
    public AttributeOverrideImpl addAttributeOverride(MetaClassImpl metaclass, String code)
    {
        return metaclass.addAttributeOverride(code);
    }

    @Transactional
    @Override
    public AttributeOverrideImpl addAttributeSystemOverride(MetaClassImpl metaclass, String code)
    {
        return metaclass.addAttributeSystemOverride(code);
    }

    @Transactional
    @Override
    public CatalogImpl addCatalog(MetaClassImpl metaClass, String uuid)
    {
        CatalogImpl catalog = ((MetainfoServiceBean)metainfoService).addCatalog(uuid);
        catalog.setItemMetaClass(metaClass);
        return catalog;
    }

    @Transactional
    @Override
    public DeclaredAttributeImpl addDeclaredAttribute(MetaClassImpl metaclass, String code)
    {
        return metaclass.addDeclaredAttribute(code);
    }

    @Transactional
    @Override
    public void addDescription(CatalogImpl catalog, String lang, String descr)
    {
        catalog.addDescription(lang, descr);
    }

    @Transactional
    @Override
    public void addDescription(MetaClassImpl metaclass, String lang, String descr)
    {
        metaclass.addDescription(lang, descr);
    }

    @Transactional
    @Override
    public void addDescriptionOverride(MetaClassImpl metaclass, String lang, String descrOverride)
    {
        metaclass.addDescriptionOverride(lang, descrOverride);
    }

    @Override
    @Transactional
    public MetaClassImpl addMetaClass(ClassFqn fqn, ClassFqn parentFqn)
    {
        MetaClassImpl metaClass = ((MetainfoServiceBean)metainfoService).addMetaClass(fqn);
        metaClass.setParent(parentFqn);
        return metaClass;
    }

    @Override
    public RelationImpl addRelation(String code)
    {
        RelationImpl relation = new NullParentRelationImpl();
        relation.setType("fake");
        relation.setCode(code);
        ((MetainfoServiceBean)metainfoService).addRelation(relation);
        return relation;
    }

    @Transactional
    @Override
    public void addTitle(CatalogImpl catalog, String lang, String title)
    {
        catalog.addTitle(lang, title);
    }

    @Transactional
    @Override
    public void addTitle(MetaClassImpl metaclass, String lang, String title)
    {
        metaclass.addTitle(lang, title);
    }

    @Transactional
    @Override
    public void addTitleOverride(MetaClassImpl metaclass, String lang, String titleOverride)
    {
        metaclass.addTitleOverride(lang, titleOverride);
    }

    @Transactional
    @Override
    public void delAttributeOverride(MetaClassImpl metaclass, String code)
    {
        metaclass.delAttributeOverride(code);
    }

    @Transactional
    @Override
    public void delDeclaredAttribute(MetaClassImpl metaclass, String code)
    {
        metaclass.delDeclaredAttribute(code);
    }

    @Transactional
    @Override
    public boolean deleteCatalog(String code)
    {
        return ((MetainfoServiceBean)metainfoService).deleteCatalogFromCache(code);
    }

    @Override
    public void deleteEscalationSchemeFromCache(String code)
    {
        ((MetainfoServiceBean)metainfoService).deleteEscalationSchemeFromCache(code);
    }

    @Transactional
    @Override
    public void deleteMetaClass(MetaClassImpl metaClass)
    {
        ClassFqn currentFqn = metaClass.getFqn();
        metaClass.getParentMetaClass().delChild(currentFqn);
        MetaClassImpl parent = metaClass.getParentMetaClass();
        while (null != parent)
        {
            parent.delDesendant(currentFqn);
            parent = parent.getParentMetaClass();
        }

        HandlerUtils.clearCacheRecursive(metaClass.getCacheNode());
    }

    @Override
    public boolean deleteRelation(RelationImpl rel)
    {
        return ((MetainfoServiceBean)metainfoService).deleteRelation(rel);
    }

    @Override
    public void deleteTimerDefinitionFromCache(String code)
    {
        ((MetainfoServiceBean)metainfoService).deleteTimerDefinitionFromCache(code);
    }

    @Transactional
    @Override
    public void setDescription(CatalogImpl catalog, String descrOverride)
    {
        catalog.setDescription(descrOverride);
    }

    @Transactional
    @Override
    public void setHardcoded(MetaClassImpl metaClass, boolean value)
    {
        metaClass.setHardcoded(value);
    }

    @Transactional
    @Override
    public void setProperties(MetaClassImpl metaclass, boolean abstrct, ClassFqn fqn, boolean hardcoded,
            boolean hasCases, boolean hasUserClasses, boolean singleton, Status status)
    {
        metaclass.setAbstract(abstrct);
        metaclass.setFqn(fqn);
        metaclass.setHardcoded(hardcoded);
        metaclass.setHasCases(hasCases);
        metaclass.setHasUserClasses(hasUserClasses);
        metaclass.setSingleton(singleton);
        metaclass.setStatus(status);
    }

    @Transactional
    @Override
    public void setTitle(CatalogImpl catalog, String titleOverride)
    {
        catalog.setTitle(titleOverride);
    }
}
