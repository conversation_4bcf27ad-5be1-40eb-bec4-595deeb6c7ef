package ru.naumen.metainfo.server.spi.dispatch.eventaction;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static ru.naumen.core.shared.Constants.ScriptsComponentTree.EXISTING_SCRIPT;

import jakarta.inject.Inject;
import jakarta.transaction.Transactional;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import net.customware.gwt.dispatch.server.Dispatch;
import ru.naumen.core.server.dispatch.ObjectTestUtils;
import ru.naumen.core.server.dispatch.SecurityTestHelper;
import ru.naumen.core.server.eventaction.EventActionService;
import ru.naumen.core.server.script.ScriptHelper;
import ru.naumen.core.server.script.storage.ScriptStorageService;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.script.places.EventActionCategories;
import ru.naumen.core.shared.utils.UuidHelper;
import ru.naumen.metainfo.server.spi.events.UserEventsService;
import ru.naumen.metainfo.shared.dispatch2.eventaction.SaveEventAction;
import ru.naumen.metainfo.shared.dispatch2.script.SimpleScriptedResult;
import ru.naumen.metainfo.shared.elements.HasMutableScript;
import ru.naumen.metainfo.shared.eventaction.Action;
import ru.naumen.metainfo.shared.eventaction.ActionType;
import ru.naumen.metainfo.shared.eventaction.Constants;
import ru.naumen.metainfo.shared.eventaction.EventAction;
import ru.naumen.metainfo.shared.eventaction.EventType;
import ru.naumen.metainfo.shared.eventaction.NotificationEventAction;
import ru.naumen.metainfo.shared.eventaction.PlannedEventRule;
import ru.naumen.metainfo.shared.eventaction.ScriptEventAction;
import ru.naumen.metainfo.shared.eventaction.UserEvents;
import ru.naumen.metainfo.shared.events.UserEvent;
import ru.naumen.metainfo.shared.script.Script;
import ru.naumen.metainfo.shared.script.ScriptDto;
import ru.naumen.metainfo.shared.ui.LocalizedString;
import ru.naumen.uniquerandom.UniqueNumbersGenerator;

@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class SaveEventActionHandlerDbTest
{
    @Inject
    private Dispatch dispatch;
    @Inject
    private EventActionService eventActionService;
    @Inject
    private UserEventsService userEventsService;
    @Inject
    private ScriptStorageService scriptStorageService;
    @Inject
    private ObjectTestUtils utils;

    @Before
    public void setUp()
    {
        SecurityTestHelper.autenticateAsSuperUser();
    }

    /**
     * Проверка на то, что, при добавлении действия по событию для пользовательского действия,
     * пользовательское действие создается с атрибутами аналогичными атрибутам действия по событию
     * 
     * @throws Exception
     */
    @Test
    @Transactional
    public void testEventActionUserEventTypeCreation() throws Exception
    {
        //Подготовка
        ScriptDto saveScript = utils.createScriptDto("true");
        EventAction eventAction = generateScriptEventActionUserEvent(saveScript.getCode());
        ((HasMutableScript)eventAction.getAction()).setScript(saveScript.getCode());
        //Выполнение
        SaveEventAction saveAction = new SaveEventAction(eventAction, true);
        saveAction.setScript(saveScript);
        SimpleScriptedResult<DtObject> result = dispatch.execute(saveAction);

        EventAction savedEventAction = eventActionService.getEventAction(result.get().getUUID());
        String userEventUUID = ((UserEvents)savedEventAction.getEvent()).getEventUuids().iterator().next();
        UserEvent savedUserEvent = userEventsService.get(userEventUUID);

        //Проверка
        assertNotNull("Действие по событию сохранено без ссылки на пользовательское действие.", userEventUUID);
        assertNotNull(
                "Пользовательское действие, сохраненное вместе с действием по событию, не найдено в метаинформации.",
                savedUserEvent);
        assertEquals("Идентификатор пользовательского действия не совпадает с идентификатором действия по событию.",
                UuidHelper.toIdStr(savedUserEvent.getUuid()), UuidHelper.toIdStr(savedEventAction.getCode()));
        assertEquals("Название пользовательского действия не совпадает с названием действия по событию.",
                savedEventAction.getTitle(), savedUserEvent.getTitle());
        assertEquals("Метаклассы пользовательского действия не совпадают с метаклассами действия по событию.",
                Sets.newHashSet(savedEventAction.getLinkedClasses()), savedUserEvent.getFqns());
        assertEquals("Состояние пользовательского действия не совпадает с состоянием действия по событию.",
                savedEventAction.isOn(), savedUserEvent.isEnabled());
    }

    /**
     * Проверка на то, что, при добавлении действия по событию с оповещением, информация о скрипте этого действия по 
     * событию добавляется к {@link Action} и в кэш информации о скриптах.
     * 
     * @throws Exception
     */
    @Test
    @Transactional
    public void testNotificationActionScriptInfoShouldBeAddedOnNewConditionSave() throws Exception
    {
        //Подготовка

        ScriptDto script = utils.createScriptDto("true");
        EventAction eventAction = generateNotificationEventAction(script.getCode());
        //Выполнение
        SaveEventAction saveAction = new SaveEventAction(eventAction, true);
        saveAction.setScript(script);
        SimpleScriptedResult<DtObject> result = dispatch.execute(saveAction);
        EventAction savedEventAction = result.get().getProperty(Constants.EventAction.ORIGINAL_EVENT_ACTION);
        NotificationEventAction savedAction = (NotificationEventAction)savedEventAction.getAction();
        EventAction serverEventAction = eventActionService.getEventAction(savedEventAction.getCode());
        NotificationEventAction serverAction = (NotificationEventAction)serverEventAction.getAction();
        //Проверка
        assertTrue("В условии действия по событию на сервере должен быть код скрипта.",
                ScriptHelper.isScriptCodeValid(serverAction.getScript()));
        Script savedScript = scriptStorageService.getScript(serverAction.getScript());
        assertNotNull("Информация о скрипте не найдена в кэше информации о скриптах.", savedScript);
        assertEquals("Идентификатор объекта, содержащего скрипт, не совпал с ожидаемым.", savedEventAction.getCode(),
                savedScript.getUsagePoints().get(0).getLocation());
        assertEquals("Ожидался иной код скрипта.", serverAction.getScript(), savedAction.getScript());
        assertEquals("Ожидалось иное тело скрипта.", script.getBody(), savedScript.getBody());
        assertEquals("Ожидалась иная категория скрипта.", EventActionCategories.EVENTACTION_NOTIFICATION_CUSTOMIZATION,
                savedScript.getUsagePoints().get(0).getCategory());
        assertEquals("Ожидался иной код скрипта.", serverAction.getScript(), savedScript.getCode());
    }

    /**
     * Проверка на то, что, при редактировании скриптового действия по событию, информация о скрипте этого действия по 
     * событию содержится в {@link Action} и в кэше информации о скриптах и содержит тот же номер скрипта, что и до модификации.
     * 
     * @throws Exception
     */
    @Test
    @Transactional
    public void testNotificationActionScriptInfoShouldHaveSameNumberAfterEdit() throws Exception
    {
        //Подготовка и выполнение
        ScriptDto firstScript = utils.createScriptDto("true");
        EventAction eventAction = generateNotificationEventAction(firstScript.getCode());
        ((HasMutableScript)eventAction.getAction()).setScript(firstScript.getCode());
        SaveEventAction saveAction = new SaveEventAction(eventAction, true);
        saveAction.setScript(firstScript);

        SimpleScriptedResult<DtObject> result = dispatch.execute(saveAction);
        EventAction savedEventAction = result.get().getProperty(Constants.EventAction.ORIGINAL_EVENT_ACTION);
        EventAction serverEventActionOld = eventActionService.getEventAction(savedEventAction.getCode());
        NotificationEventAction serverActionOld = (NotificationEventAction)serverEventActionOld.getAction();
        String oldScriptCode = serverActionOld.getScript();

        ScriptDto secondScript = utils.createScriptDto("false");
        secondScript.setCode(oldScriptCode);
        secondScript.setSelectStrategy(EXISTING_SCRIPT);
        ((HasMutableScript)savedEventAction.getAction()).setScript(secondScript.getCode());
        SaveEventAction saveModifiedAction = new SaveEventAction(savedEventAction);
        saveModifiedAction.setScript(secondScript);

        SimpleScriptedResult<DtObject> modifiedResult = dispatch.execute(saveModifiedAction);
        EventAction modifyEventAction = modifiedResult.get().getProperty(Constants.EventAction.ORIGINAL_EVENT_ACTION);
        EventAction serverEventAction = eventActionService.getEventAction(modifyEventAction.getCode());
        NotificationEventAction serverAction = (NotificationEventAction)serverEventAction.getAction();
        String newScriptCode = serverAction.getScript();
        //Проверка
        assertTrue("В условии действия по событию на сервере должен быть код скрипта.",
                ScriptHelper.isScriptCodeValid(serverAction.getScript()));
        Script savedScript = scriptStorageService.getScript(serverAction.getScript());
        assertNotNull("Информация о скрипте не найдена в кэше информации о скриптах.", savedScript);
        assertEquals("Коды оригинального и изменённого скрипта не совпадают.", oldScriptCode, newScriptCode);
        assertEquals("Идентификатор объекта, содержащего скрипт, не совпал с ожидаемым.", savedEventAction.getCode(),
                savedScript.getUsagePoints().get(0).getLocation());
        assertEquals("Ожидалась иная категория скрипта.", EventActionCategories.EVENTACTION_NOTIFICATION_CUSTOMIZATION,
                savedScript.getUsagePoints().get(0).getCategory());
        assertEquals("Ожидался иной код скрипта.", newScriptCode, savedScript.getCode());
    }

    /**
     * Проверка на то, что, при добавлении скриптового действия по событию, информация о скрипте этого действия по 
     * событию добавляется к {@link Action} и в кэш информации о скриптах.
     * 
     * @throws Exception
     */
    @Test
    @Transactional
    public void testScriptActionScriptInfoShouldBeAddedOnNewConditionSave() throws Exception
    {
        //Подготовка
        ScriptDto saveScript = utils.createScriptDto("true");
        EventAction eventAction = generateScriptEventAction(saveScript.getCode());
        ((HasMutableScript)eventAction.getAction()).setScript(saveScript.getCode());
        //Выполнение
        SaveEventAction saveAction = new SaveEventAction(eventAction, true);
        saveAction.setScript(saveScript);
        SimpleScriptedResult<DtObject> result = dispatch.execute(saveAction);
        EventAction savedEventAction = result.get().getProperty(Constants.EventAction.ORIGINAL_EVENT_ACTION);
        EventAction serverEventAction = eventActionService.getEventAction(savedEventAction.getCode());
        ScriptEventAction serverAction = (ScriptEventAction)serverEventAction.getAction();
        //Проверка
        assertTrue("В условии действия по событию на сервере должен быть код скрипта.",
                ScriptHelper.isScriptCodeValid(serverAction.getScript()));
        Script script = scriptStorageService.getScript(serverAction.getScript());
        assertNotNull("Информация о скрипте не найдена в кэше информации о скриптах.", script);
        assertEquals("Идентификатор объекта, содержащего скрипт, не совпал с ожидаемым.", savedEventAction.getCode(),
                script.getUsagePoints().get(0).getLocation());
        assertEquals("Ожидалось иное тело скрипта.", saveScript.getBody(), script.getBody());
        assertEquals("Ожидалась иная категория скрипта.", EventActionCategories.EVENTACTION_SCRIPT,
                script.getUsagePoints().get(0).getCategory());
        assertEquals("Ожидался иной код скрипта.", serverAction.getScript(), script.getCode());
    }

    /**
     * Проверка на то, что, при редактировании скриптового действия по событию, информация о скрипте этого действия по 
     * событию содержится в {@link Action} и в кэше информации о скриптах и содержит тот же номер скрипта, что и до модификации.
     * 
     * @throws Exception
     */
    @Test
    @Transactional
    public void testScriptActionScriptInfoShouldHaveSameNumberAfterEdit() throws Exception
    {
        //Подготовка и выполнение
        ScriptDto saveScript = utils.createScriptDto("true");
        EventAction eventAction = generateScriptEventAction(saveScript.getCode());
        ((HasMutableScript)eventAction.getAction()).setScript(saveScript.getCode());
        SaveEventAction saveAction = new SaveEventAction(eventAction, true);
        saveAction.setScript(saveScript);
        SimpleScriptedResult<DtObject> result = dispatch.execute(saveAction);
        EventAction savedEventAction = result.get().getProperty(Constants.EventAction.ORIGINAL_EVENT_ACTION);
        ScriptEventAction savedAction = (ScriptEventAction)savedEventAction.getAction();
        EventAction serverEventActionOld = eventActionService.getEventAction(savedEventAction.getCode());
        ScriptEventAction serverActionOld = (ScriptEventAction)serverEventActionOld.getAction();
        String oldScriptCode = serverActionOld.getScript();

        saveScript.setCode(savedEventAction.getAction().getScript());
        saveScript.setSelectStrategy(EXISTING_SCRIPT);
        saveScript.setBody("false");

        savedAction.setScript(saveScript.getCode());
        SaveEventAction saveModifiedAction = new SaveEventAction(savedEventAction);
        saveModifiedAction.setScript(saveScript);
        dispatch.execute(saveModifiedAction);
        EventAction serverEventAction = eventActionService.getEventAction(savedEventAction.getCode());
        ScriptEventAction serverAction = (ScriptEventAction)serverEventAction.getAction();
        String newScriptCode = serverAction.getScript();

        //Проверка
        assertTrue("В условии действия по событию на сервере должен быть код скрипта.",
                ScriptHelper.isScriptCodeValid(serverAction.getScript()));
        Script script = scriptStorageService.getScript(serverAction.getScript());
        assertNotNull("Информация о скрипте не найдена в кэше информации о скриптах.", script);
        assertEquals("Коды оригинального и изменённого скрипта не совпадают.", oldScriptCode, newScriptCode);
        assertEquals("Идентификатор объекта, содержащего скрипт, не совпал с ожидаемым.", savedEventAction.getCode(),
                script.getUsagePoints().get(0).getLocation());
        assertEquals("Ожидался иной код скрипта.", saveScript.getCode(), newScriptCode);
        assertEquals("Ожидалась иная категория скрипта.", EventActionCategories.EVENTACTION_SCRIPT,
                script.getUsagePoints().get(0).getCategory());
    }

    private EventAction generateNotificationEventAction(String scriptCode)
    {
        PlannedEventRule event = new PlannedEventRule();
        event.setEventType(EventType.add);
        NotificationEventAction action = new NotificationEventAction();
        action.setActionType(ActionType.NotificationEventAction);
        action.setScript(scriptCode);
        action.setEmails("<EMAIL>");
        action.setMessage(Lists.newArrayList(new LocalizedString("ru","message")));
        action.setSubject(Lists.newArrayList(new LocalizedString("ru", "subj")));
        EventAction eventAction = new EventAction(event, action);
        String code = "" + UniqueNumbersGenerator.nextInt(10000);
        eventAction.setId(code);
        return eventAction;
    }

    private EventAction generateScriptEventAction(String script)
    {
        PlannedEventRule event = new PlannedEventRule();
        event.setEventType(EventType.add);
        ScriptEventAction action = new ScriptEventAction();
        action.setActionType(ActionType.ScriptEventAction);
        action.setScript(script);
        EventAction eventAction = new EventAction(event, action);
        String code = "" + UniqueNumbersGenerator.nextInt(10000);
        eventAction.setId(code);
        return eventAction;
    }

    private EventAction generateScriptEventActionUserEvent(String script)
    {
        UserEvents event = new UserEvents();
        event.setEventType(EventType.userEvent);

        ScriptEventAction action = new ScriptEventAction();
        action.setActionType(ActionType.ScriptEventAction);
        action.setScript(script);

        EventAction eventAction = new EventAction(event, action);
        String code = "" + UniqueNumbersGenerator.nextInt(10000);
        eventAction.setId(code);
        return eventAction;
    }
}
