package ru.naumen.metainfo.server.spi.dispatch.catalog;

import jakarta.inject.Inject;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.transaction.annotation.Transactional;

import net.customware.gwt.dispatch.server.Dispatch;
import ru.naumen.core.server.dispatch.ObjectTestUtils;
import ru.naumen.core.server.dispatch.SecurityTestHelper;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.utils.UUIDGenerator;
import ru.naumen.metainfo.server.ClassMetainfoServiceException;
import ru.naumen.metainfo.shared.dispatch2.catalog.RemoveCatalogAction;
import ru.naumen.metainfo.shared.elements.Catalog;
import ru.naumen.metainfo.shared.elements.MetaClassLite.Status;

/**
 * <AUTHOR>
 * @since 21.02.2011
 *
 */
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class RemoveCatalogActionHandlerDbTest
{
    @Inject
    Dispatch dispatch;
    @Inject
    ObjectTestUtils utils;

    @Test
    @Transactional
    public void archiveAlreadyArchived() throws Exception
    {
        //настройка системы
        Catalog catalog = utils.createCatalog();
        dispatch.execute(new RemoveCatalogAction(catalog.getCode()));
        //вызов системы        
        dispatch.execute(new RemoveCatalogAction(catalog.getCode()));
        //проверка утверждений
        Assert.assertEquals(Status.REMOVED, catalog.getItemMetaClass().getStatus());
        //очистка
    }

    @Test
    @Transactional
    public void archiveExists() throws Exception
    {
        //настройка системы
        Catalog catalog = utils.createCatalog();
        //вызов системы
        dispatch.execute(new RemoveCatalogAction(catalog.getCode()));
        //проверка утверждений
        Assert.assertEquals(Status.REMOVED, catalog.getItemMetaClass().getStatus());
        //очистка
    }

    @Test(expected = ClassMetainfoServiceException.class)
    @Transactional
    public void archiveHardcoded() throws Exception
    {
        //настройка системы
        //вызов системы
        dispatch.execute(new RemoveCatalogAction(Constants.ImpactCatalog.CODE));
        //проверка утверждений
        //очистка
    }

    @Test(expected = ClassMetainfoServiceException.class)
    @Transactional
    public void archiveNotExists() throws Exception
    {
        //настройка системы
        //вызов системы
        dispatch.execute(new RemoveCatalogAction(UUIDGenerator.get().nextUUID()));
        //проверка утверждений
        //очистка
    }

    @Before
    public void setUp()
    {
        SecurityTestHelper.autenticateAsSuperUser();
    }
}
