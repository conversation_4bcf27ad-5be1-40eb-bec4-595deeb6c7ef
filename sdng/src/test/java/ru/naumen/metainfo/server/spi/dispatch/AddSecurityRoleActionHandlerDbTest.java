package ru.naumen.metainfo.server.spi.dispatch;

import jakarta.inject.Inject;
import jakarta.transaction.Transactional;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import net.customware.gwt.dispatch.server.Dispatch;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.server.dispatch.ObjectTestUtils;
import ru.naumen.core.server.dispatch.SecurityTestHelper;
import ru.naumen.core.server.script.storage.ScriptStorageService;
import ru.naumen.core.shared.utils.UUIDGenerator;
import ru.naumen.metainfo.server.SecurityService;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.dispatch2.AddSecurityRoleAction;
import ru.naumen.metainfo.shared.dispatch2.GetSecurityRoleResponse;
import ru.naumen.metainfo.shared.elements.sec.Role;
import ru.naumen.metainfo.shared.script.ScriptDto;
import ru.naumen.metainfo.shared.script.ScriptDtoFactory;

/**
 * 
 * <AUTHOR>
 *
 */
@Transactional
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class AddSecurityRoleActionHandlerDbTest
{
    @Inject
    private Dispatch dispatch;
    @Inject
    private SecurityService securityService;
    @Inject
    private ScriptStorageService scriptStorageService;
    @Inject
    private ObjectTestUtils utils;

    @Test
    public void addNotScriptExists() throws Exception
    {
        // настройка системы
        // вызов системы
        String title = UUIDGenerator.get().nextUUID();
        String code = UUIDGenerator.get().nextUUID();
        MapProperties properties = new MapProperties();
        ScriptDto createScriptDto = utils.createScriptDto("return 1==0");
        ScriptDto withoutScript = ScriptDtoFactory.createWithout();
        properties.setProperty(Constants.Role.SCRIPT_ACCESS_DTO_KEY, createScriptDto);
        properties.setProperty(Constants.Role.SCRIPT_OWNERS_DTO_KEY, withoutScript);
        properties.setProperty(Constants.Role.SCRIPT_LIST_FILTER_DTO_KEY, withoutScript);
        properties.setProperty(Constants.Role.SCRIPT_FAST_LINK_RIGHTS_DTO_KEY, withoutScript);
        AddSecurityRoleAction a = new AddSecurityRoleAction(title, code, null, Role.Type.SCRIPT, properties);
        GetSecurityRoleResponse result = dispatch.execute(a);
        // проверка утверждений
        Assert.assertEquals(title, result.get().getTitle());
        Role grp = securityService.getRole(result.get().get().getCode());
        String scriptBody = scriptStorageService.getScriptBody((String)grp.getProperties().getProperty(
                Constants.Role.SCRIPT_ACCESS_KEY));
        Assert.assertEquals(title, grp.getTitle());
        Assert.assertEquals(Role.Type.SCRIPT, grp.getType());
        Assert.assertEquals("return 1==0", scriptBody);
        // очистка
    }

    @Before
    public void setUp()
    {
        SecurityTestHelper.autenticateAsSuperUser();
    }
}
