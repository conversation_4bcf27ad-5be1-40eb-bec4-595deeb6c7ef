package ru.naumen.metainfo.server.spi;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import com.google.common.collect.Lists;

import jakarta.inject.Inject;
import ru.naumen.Assert;
import ru.naumen.core.server.dispatch.SecurityTestHelper;
import ru.naumen.core.shared.TestUtils;
import ru.naumen.metainfo.shared.elements.Dependence;
import ru.naumen.metainfo.shared.elements.ManyToAnyRelationImpl;
import ru.naumen.metainfo.shared.elements.NullParentRelationImpl;
import ru.naumen.metainfo.shared.elements.RelationImpl;
import ru.naumen.metainfo.shared.elements.UUIDRelationImpl;

/**
 * <AUTHOR>
 */
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class RelationMetaInfoServiceTest
{
    @Inject
    MetainfoServiceBean metaInfoService;

    @Test
    public void addRelation()
    {
        int size = metaInfoService.getRelations().size();

        ManyToAnyRelationImpl manyToAnyRelation = new ManyToAnyRelationImpl();
        initRelation(manyToAnyRelation);
        manyToAnyRelation.setAttribute("manyToAnyRelation");
        metaInfoService.addRelation(manyToAnyRelation);
        size++;

        NullParentRelationImpl nullParentRelation = new NullParentRelationImpl();
        initRelation(nullParentRelation);
        metaInfoService.addRelation(nullParentRelation);
        size++;

        UUIDRelationImpl uuidRelation = new UUIDRelationImpl();
        initRelation(uuidRelation);
        uuidRelation.setAttribute("uuidRelation");
        metaInfoService.addRelation(uuidRelation);
        size++;

        Assert.assertEquals(metaInfoService.getRelations().size(), size);
        Assert.assertContainsCollection(metaInfoService.getRelations(),
                Lists.newArrayList(manyToAnyRelation, nullParentRelation, uuidRelation));
    }

    @Test
    public void deleteRelation()
    {
        int size = metaInfoService.getRelations().size();

        ManyToAnyRelationImpl manyToAnyRelation = new ManyToAnyRelationImpl();
        initRelation(manyToAnyRelation);
        manyToAnyRelation.setAttribute("manyToAnyRelation");
        metaInfoService.addRelation(manyToAnyRelation);

        NullParentRelationImpl nullParentRelation = new NullParentRelationImpl();
        initRelation(nullParentRelation);
        metaInfoService.addRelation(nullParentRelation);

        UUIDRelationImpl uuidRelation = new UUIDRelationImpl();
        initRelation(uuidRelation);
        uuidRelation.setAttribute("uuidRelation");
        metaInfoService.addRelation(uuidRelation);

        metaInfoService.deleteRelation(manyToAnyRelation);
        metaInfoService.deleteRelation(nullParentRelation);
        metaInfoService.deleteRelation(uuidRelation);

        Assert.assertEquals(metaInfoService.getRelations().size(), size);
        Assert.assertFalse(metaInfoService.getRelations().contains(manyToAnyRelation));
        Assert.assertFalse(metaInfoService.getRelations().contains(nullParentRelation));
        Assert.assertFalse(metaInfoService.getRelations().contains(uuidRelation));
    }

    @Before
    public void setUp()
    {
        SecurityTestHelper.autenticateAsSuperUser();
    }

    private void initRelation(RelationImpl relation)
    {
        relation.setCode(TestUtils.randomString());
        relation.setDepends(Lists.<Dependence> newArrayList());
        relation.setHardcoded(true);
        relation.setLeft(TestUtils.createClassFqn());
        relation.setRight(TestUtils.createClassFqn());
        relation.setType(TestUtils.randomString());
    }
}