package ru.naumen.metainfo.server.spi.dispatch;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import net.customware.gwt.dispatch.server.Dispatch;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.server.dispatch.ObjectTestUtils;
import ru.naumen.core.server.dispatch.SecurityTestHelper;
import ru.naumen.core.server.script.ScriptHelper;
import ru.naumen.core.server.script.storage.ScriptStorageService;
import ru.naumen.core.shared.CatalogItemReference;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.core.shared.script.places.AttributeCategories;
import ru.naumen.core.shared.script.places.ScriptHolders;
import ru.naumen.core.shared.utils.UUIDGenerator;
import ru.naumen.metainfo.server.ClassMetainfoServiceException;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.AggregateAttributeType;
import ru.naumen.metainfo.shared.Constants.Presentations;
import ru.naumen.metainfo.shared.Constants.StringAttributeType;
import ru.naumen.metainfo.shared.dispatch2.AddAttributeAction;
import ru.naumen.metainfo.shared.dispatch2.DelAttributeAction;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.script.Script;
import ru.naumen.metainfo.shared.script.ScriptDto;
import ru.naumen.uniquerandom.UniqueNumbersGenerator;

/**
 * 
 * <AUTHOR>
 *
 */
@Transactional
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class AddAttributeActionHandlerDbTest
{
    @Inject
    private Dispatch dispatch;
    @Inject
    private MetainfoService metainfoService;

    @Inject
    private ObjectTestUtils utils;

    @Inject
    private ScriptStorageService scriptStorageService;

    @Test(expected = ClassMetainfoServiceException.class)
    public void addExists() throws Exception
    {
        // настройка системы
        // вызов системы
        ClassFqn fqn = ClassFqn.parse(Constants.Employee.CLASS_ID);
        String code = Constants.Employee.FIRST_NAME;
        String typeCode = StringAttributeType.CODE;
        String title = UUIDGenerator.get().nextUUID();
        String description = UUIDGenerator.get().nextUUID();

        MapProperties typeProps = new MapProperties();
        MapProperties edit = new MapProperties();
        edit.setProperty(Presentations.ATTR_CODE, Presentations.STRING_EDIT);
        MapProperties view = new MapProperties();
        edit.setProperty(Presentations.ATTR_CODE, Presentations.STRING_VIEW);

        //@formatter:off
        AddAttributeAction action = AddAttributeAction.create()
                .setFqn(fqn)
                .setCode(code)
                .setTypeCode(typeCode)
                .setTitle(title)
                .setDescription(description)
                .setEditable(true)
                .setTypeProperties(typeProps)
                .setViewPresentation(view)
                .setEditPresentation(edit);
        //@formatter:on
        dispatch.execute(action);
        // проверка утверждений
        // очистка
    }

    /**
     * Проверка на то, что в атрибут добавляется информация о скрипте вычисления значения.
     * @throws Exception
     */
    @Test
    public void addNotExists_computable() throws Exception
    {
        // настройка системы
        // вызов системы
        String title = UUIDGenerator.get().nextUUID();
        String description = UUIDGenerator.get().nextUUID();

        String scriptBody = "return 'asd'";
        ScriptDto scriptDto = utils.createScriptDto(scriptBody);
        Attribute attribute = addAttribute(Constants.Employee.FQN, true, false, true, title, description, true,
                scriptDto, false, null, false, null);
        String scriptCode = attribute.getScript();
        assertTrue("В вычислимом атрибуте на сервере должен быть код скрипта.",
                ScriptHelper.isScriptCodeValid(scriptCode));
        Script script = scriptStorageService.getScript(scriptCode);
        assertEquals("В кэше информации о скрипте ожидался тот же код скрипта, что и в метаинформации.", scriptCode,
                script.getCode());
        assertEquals("Скрипт должен относиться к категории вычислимых атрибутов.", AttributeCategories.COMPUTABLE,
                script.getUsagePoints().get(0).getCategory());
        assertEquals(ScriptHolders.ATTRIBUTE, script.getUsagePoints().get(0).getHolderType());
        assertEquals("Ожидалось иное расположение атрибута.", attribute.getFqn().toString(),
                script.getUsagePoints().get(0).getLocation());
        assertEquals("Ожидался иной объект, содержащий атрибут со скриптом.", attribute.getDeclaredMetaClass(),
                script.getUsagePoints().get(0).getRelatedMetaClassFqns().iterator().next());
        assertEquals("Ожидалось иное тело скрипта.", scriptBody, script.getBody());
        // очистка
    }

    /**
     * Проверка на то, что в атрибут добавляется информация о скрипте вычисления значения при редактировании.
     * @throws Exception
     */
    @Test
    public void addNotExists_computableOnForm() throws Exception
    {
        // настройка системы
        // вызов системы
        String title = UUIDGenerator.get().nextUUID();
        String description = UUIDGenerator.get().nextUUID();

        String scriptBody = "return []";
        ScriptDto scriptDto = utils.createScriptDto(scriptBody);
        Attribute attribute = addAttribute(Constants.Employee.FQN, true, false, true, title, description, false, null,
                true, scriptDto, false, null);
        String scriptCode = attribute.getComputableOnFormScript();
        assertTrue("В вычислимом при редактировании атрибуте на сервере должен быть код скрипта.",
                ScriptHelper.isScriptCodeValid(scriptCode));
        Script script = scriptStorageService.getScript(scriptCode);
        assertEquals("В кэше информации о скрипте ожидался тот же код скрипта, что и в метаинформации.", scriptCode,
                script.getCode());
        assertEquals("Скрипт должен относиться к категории вычисления значения при редактировании.",
                AttributeCategories.COMPUTABLE_ON_FORM, script.getUsagePoints().get(0).getCategory());
        assertEquals(ScriptHolders.ATTRIBUTE, script.getUsagePoints().get(0).getHolderType());
        assertEquals("Ожидалось иное расположение атрибута.", attribute.getFqn().toString(),
                script.getUsagePoints().get(0).getLocation());
        assertEquals("Ожидался иной объект, содержащий атрибут со скриптом.", attribute.getDeclaredMetaClass(),
                script.getUsagePoints().get(0).getRelatedMetaClassFqns().iterator().next());
        assertEquals("Ожидалось иное тело скрипта.", scriptBody, script.getBody());
        // очистка
    }

    /**
     * Проверка на то, что в атрибут добавляется информация о скрипте вычисления значения по-умолчанию.
     * @throws Exception
     */
    @Test
    public void addNotExists_defaultByScript() throws Exception
    {
        // настройка системы
        // вызов системы
        String title = UUIDGenerator.get().nextUUID();
        String description = UUIDGenerator.get().nextUUID();

        String scriptBody = "return 'asd'";
        ScriptDto scriptDto = utils.createScriptDto(scriptBody);
        Attribute attribute = addAttribute(Constants.Employee.FQN, true, false, true, title, description, false, null,
                false, null, true, scriptDto);
        String scriptCode = attribute.getScriptForDefault();
        assertTrue("В скрипте вычисления значения по-умолчанию для атрибута на сервере должен быть код скрипта.",
                ScriptHelper.isScriptCodeValid(scriptCode));
        Script script = scriptStorageService.getScript(scriptCode);
        assertEquals("В кэше информации о скрипте ожидался тот же код скрипта, что и в метаинформации.", scriptCode,
                script.getCode());
        assertEquals("Скрипт должен относиться к категории вычисления значения по-умолчанию.",
                AttributeCategories.DEFAULT_VALUE, script.getUsagePoints().get(0).getCategory());
        assertEquals(ScriptHolders.ATTRIBUTE, script.getUsagePoints().get(0).getHolderType());
        assertEquals("Ожидалось иное расположение атрибута.", attribute.getFqn().toString(),
                script.getUsagePoints().get(0).getLocation());
        assertEquals("Ожидался иной объект, содержащий атрибут со скриптом.", attribute.getDeclaredMetaClass(),
                script.getUsagePoints().get(0).getRelatedMetaClassFqns().iterator().next());
        assertEquals("Ожидалось иное тело скрипта.", scriptBody, script.getBody());
        // очистка
    }

    /**
     * Проверка на то, что в атрибут добавляется информация о скрипте вычисления значения по-умолчанию.
     * @throws Exception
     */
    @Test
    public void addNotExists_filteredByScript() throws Exception
    {
        // настройка системы
        // вызов системы
        String title = UUIDGenerator.get().nextUUID();
        String description = UUIDGenerator.get().nextUUID();

        String scriptBody = "return []";
        ScriptDto scriptDto = utils.createScriptDto(scriptBody);
        Attribute attribute = addAggregateAttribute(Constants.Employee.FQN, true, false, false, title, description,
                true, scriptDto);
        String scriptCode = attribute.getScriptForFiltration();
        assertTrue("В скрипте фильтрации для атрибута на сервере должен быть код скрипта.",
                ScriptHelper.isScriptCodeValid(scriptCode));
        Script script = scriptStorageService.getScript(scriptCode);
        assertEquals("В кэше информации о скрипте ожидался тот же код скрипта, что и в метаинформации.", scriptCode,
                script.getCode());
        assertEquals("Скрипт должен относиться к категории фильтрации значения.", AttributeCategories.FILTRATION,
                script.getUsagePoints().get(0).getCategory());
        assertEquals(ScriptHolders.ATTRIBUTE, script.getUsagePoints().get(0).getHolderType());
        assertEquals("Ожидалось иное расположение атрибута.", attribute.getFqn().toString(),
                script.getUsagePoints().get(0).getLocation());
        assertEquals("Ожидался иной объект, содержащий атрибут со скриптом.", attribute.getDeclaredMetaClass(),
                script.getUsagePoints().get(0).getRelatedMetaClassFqns().iterator().next());
        assertEquals("Ожидалось иное тело скрипта.", scriptBody, script.getBody());
        // очистка
    }

    @Test
    public void addNotExists_notEditable() throws Exception
    {
        // настройка системы
        // вызов системы 
        String title = UUIDGenerator.get().nextUUID();
        String description = UUIDGenerator.get().nextUUID();
        Attribute attribute = addAttribute(Constants.Employee.FQN, false, true, false, title, description);
        try
        {
            //проверка утверждений        
            assertEquals(title, attribute.getTitle());
            assertEquals(description, attribute.getDescription());
            assertFalse("Должен быть нередактируемым", attribute.isEditable());
            assertTrue("Должен быть обязательным", attribute.isRequired());
            assertFalse("Должен быть неуникальным", attribute.isUnique());
            assertFalse("Должен быть признак пользовательского атрибута", attribute.isHardcoded());
        }
        finally
        {
            // очистка
            dispatch.execute(new DelAttributeAction(Constants.Employee.FQN, attribute.getCode()));
        }
    }

    @Test
    public void addNotExists_notRequired() throws Exception
    {
        // настройка системы
        // вызов системы
        String title = UUIDGenerator.get().nextUUID();
        String description = UUIDGenerator.get().nextUUID();
        Attribute attribute = addAttribute(Constants.Employee.FQN, true, false, false, title, description);

        // проверка утверждений
        Assert.assertEquals(title, attribute.getTitle());
        Assert.assertEquals(description, attribute.getDescription());
        Assert.assertTrue("Должен быть редактируемым", attribute.isEditable());
        Assert.assertFalse("Пользовательские атрибуты создаются не обязательными", attribute.isRequired());
        Assert.assertFalse("Пользовательские атрибуты создаются не уникальными", attribute.isUnique());
        Assert.assertFalse("Должен быть признак пользовательского атрибута", attribute.isHardcoded());
        // очистка
    }

    @Test
    public void addNotExists_required() throws Exception
    {
        // настройка системы
        // вызов системы
        String title = UUIDGenerator.get().nextUUID();
        String description = UUIDGenerator.get().nextUUID();

        Attribute attribute = addAttribute(Constants.Employee.FQN, true, true, true, title, description);
        Assert.assertEquals(title, attribute.getTitle());
        Assert.assertEquals(description, attribute.getDescription());
        Assert.assertTrue("Должен быть редактируемым", attribute.isEditable());
        Assert.assertTrue("Должен быть обязательным", attribute.isRequired());
        Assert.assertTrue("Должен быть уникальным", attribute.isUnique());
        Assert.assertFalse("Должен быть признак пользовательского атрибута", attribute.isHardcoded());
        // очистка
    }

    @Test
    public void addNotExists_unique() throws Exception
    {
        // настройка системы
        // вызов системы
        String title = UUIDGenerator.get().nextUUID();
        String description = UUIDGenerator.get().nextUUID();

        Attribute attribute = addAttribute(Constants.Employee.FQN, true, false, true, title, description);
        Assert.assertEquals(title, attribute.getTitle());
        Assert.assertEquals(description, attribute.getDescription());
        Assert.assertTrue("Должен быть редактируемым", attribute.isEditable());
        Assert.assertFalse("Должен быть необязательным", attribute.isRequired());
        Assert.assertTrue("Должен быть уникальным", attribute.isUnique());
        Assert.assertFalse("Должен быть признак пользовательского атрибута", attribute.isHardcoded());
        // очистка
    }

    /**
     * Проверка на то, что при указании неверного представления для редактирования атрибута, 
     * оно подменяется на значение по умолчанию.
     * @throws Exception
     */
    @Test
    public void badEditPresentation() throws Exception
    {
        // настройка системы
        // вызов системы
        ClassFqn fqn = ClassFqn.parse(Constants.Employee.CLASS_ID);
        String code = "nEx" + UniqueNumbersGenerator.nextInt(1000000);
        String typeCode = StringAttributeType.CODE;
        String title = UUIDGenerator.get().nextUUID();
        String description = UUIDGenerator.get().nextUUID();

        MapProperties typeProps = new MapProperties();
        MapProperties edit = new MapProperties();
        edit.setProperty(Presentations.ATTR_CODE, UUIDGenerator.get().nextUUID());
        MapProperties view = new MapProperties();
        view.setProperty(Presentations.ATTR_CODE, Presentations.STRING_VIEW);

        //@formatter:off
        AddAttributeAction action = AddAttributeAction.create()
                .setFqn(fqn)
                .setCode(code)
                .setTypeCode(typeCode)
                .setTitle(title)
                .setDescription(description)
                .setEditable(true)
                .setTypeProperties(typeProps)
                .setViewPresentation(view)
                .setEditPresentation(edit);
        //@formatter:on
        dispatch.execute(action);

        MetaClass metaClass = metainfoService.getMetaClass(fqn);
        Attribute attribute = metaClass.getAttribute(code);
        // проверка утверждений
        Assert.assertEquals(Presentations.STRING_EDIT, attribute.getEditPresentation().getCode());
        // очистка
    }

    /**
     * Проверка на то, что при указании неверного представления для просмотра атрибута, 
     * оно подменяется на значение по умолчанию.
     * @throws Exception
     */
    @Test
    public void badViewPresentation() throws Exception
    {
        // настройка системы
        // вызов системы
        ClassFqn fqn = ClassFqn.parse(Constants.Employee.CLASS_ID);
        String code = "nEx" + UniqueNumbersGenerator.nextInt(1000000);
        String typeCode = StringAttributeType.CODE;
        String title = UUIDGenerator.get().nextUUID();
        String description = UUIDGenerator.get().nextUUID();

        MapProperties typeProps = new MapProperties();
        MapProperties edit = new MapProperties();
        edit.setProperty(Presentations.ATTR_CODE, Presentations.STRING_EDIT);
        MapProperties view = new MapProperties();
        view.setProperty(Presentations.ATTR_CODE, UUIDGenerator.get().nextUUID());

        //@formatter:off
        AddAttributeAction action = AddAttributeAction.create()
                .setFqn(fqn)
                .setCode(code)
                .setTypeCode(typeCode)
                .setTitle(title)
                .setDescription(description)
                .setEditable(true)
                .setTypeProperties(typeProps)
                .setViewPresentation(view)
                .setEditPresentation(edit);
        //@formatter:on
        dispatch.execute(action);

        MetaClass metaClass = metainfoService.getMetaClass(fqn);
        Attribute attribute = metaClass.getAttribute(code);
        // проверка утверждений
        Assert.assertEquals(Presentations.STRING_VIEW, attribute.getViewPresentation().getCode());
        // очистка
    }

    @Before
    public void setUp() throws Exception
    {
        SecurityTestHelper.autenticateAsSuperUser();
    }

    private Attribute addAggregateAttribute(ClassFqn fqn, boolean isEditable, boolean isRequired, boolean isUnique,
            String title, String description, Boolean isFilteredByScript, ScriptDto scriptForFiltration)
            throws Exception
    {
        // +10 чтобы если UniqueNumbersGenerator.nextInt(10000) вернет число от 0 до 9 substring не валился с ошибкой
        String code = ("notExists" + (UniqueNumbersGenerator.nextInt(10000) + 10)).substring(0, 11);

        String typeCode = AggregateAttributeType.CODE;

        MapProperties typeProps = new MapProperties();
        typeProps.setAll(AggregateAttributeType.AGGREGATE_OU_TYPE_PROPS);
        MapProperties edit = new MapProperties();
        edit.setProperty(Presentations.ATTR_CODE, Presentations.AGGREGATE_EDIT);
        MapProperties view = new MapProperties();
        view.setProperty(Presentations.ATTR_CODE, Presentations.AGGREGATE_VIEW);

        //@formatter:off
        AddAttributeAction action = AddAttributeAction.create()
                .setFqn(fqn)
                .setCode(code)
                .setTypeCode(typeCode)
                .setTitle(title)
                .setDescription(description)
                .setEditable(isEditable)
                .setRequired(isRequired)
                .setUnique(isUnique)
                .setTypeProperties(typeProps)
                .setViewPresentation(view)
                .setEditPresentation(edit)
                .setFilteredByScript(isFilteredByScript)
                .setScriptForFiltration(scriptForFiltration);
        //@formatter:on
        dispatch.execute(action);

        // проверка утверждений
        MetaClass metaClass = metainfoService.getMetaClass(fqn);
        return metaClass.getAttribute(code);
    }

    private Attribute addAttribute(ClassFqn fqn, boolean isEditable, boolean isRequired, boolean isUnique, String title,
            String description) throws Exception
    {
        return addAttribute(fqn, isEditable, isRequired, isUnique, title, description, false, null, false, null, false,
                null);
    }

    private Attribute addAttribute(ClassFqn fqn, boolean isEditable, boolean isRequired, boolean isUnique, String title,
            String description, Boolean isComputable, @Nullable ScriptDto script, Boolean isComputableOnForm,
            @Nullable ScriptDto computableOnFormScript, Boolean isDefaultByScript, @Nullable ScriptDto scriptForDefault)
            throws Exception
    {
        String code = "nEx" + UniqueNumbersGenerator.nextInt(100000);
        String typeCode = StringAttributeType.CODE;

        MapProperties typeProps = new MapProperties();
        MapProperties edit = new MapProperties();
        edit.setProperty(Presentations.ATTR_CODE, Presentations.STRING_EDIT);
        MapProperties view = new MapProperties();
        view.setProperty(Presentations.ATTR_CODE, Presentations.STRING_VIEW);

        SimpleDtObject dto = new SimpleDtObject("ou$88899007777", "", null);
        dto.setProperty(Constants.CatalogItem.ITEM_CODE, "code-of-item");

        //@formatter:off
        AddAttributeAction action = AddAttributeAction.create()
                .setFqn(fqn)
                .setCode(code)
                .setTypeCode(typeCode)
                .setTitle(title)
                .setDescription(description)
                .setEditable(isEditable)
                .setRequired(isRequired)
                .setUnique(isUnique)
                .setTypeProperties(typeProps)
                .setViewPresentation(view)
                .setEditPresentation(edit)
                .setDefaultValue( new CatalogItemReference("systemCatalog", dto))
                .setComputable(isComputable)
                .setScript(script)
                .setComputableOnForm(isComputableOnForm)
                .setComputableOnFormScript(computableOnFormScript)
                .setDefaultByScript(isDefaultByScript)
                .setScriptForDefault(scriptForDefault);
        //@formatter:on
        dispatch.execute(action);

        // проверка утверждений
        MetaClass metaClass = metainfoService.getMetaClass(fqn);
        return metaClass.getAttribute(code);
    }
}
