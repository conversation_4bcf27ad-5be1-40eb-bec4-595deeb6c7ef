package ru.naumen.metainfo.server.spi.elements;

import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.MetaClassLite.Status;
import ru.naumen.metainfo.shared.elements.RelationImpl;

/**
 * <AUTHOR>
 */
public interface TransactionalMetaClass
{
    AttributeGroupDeclarationImpl addAttributeGroupDeclaration(MetaClassImpl metaclass, String code);

    AttributeGroupDeclarationImpl addAttributeGroupOverride(MetaClassImpl metaclass, String code);

    AttributeGroupDeclarationImpl addAttributeGroupSystemOverride(MetaClassImpl metaclass, String code);

    AttributeOverrideImpl addAttributeOverride(MetaClassImpl metaclass, String code);

    AttributeOverrideImpl addAttributeSystemOverride(MetaClassImpl metaclass, String code);

    CatalogImpl addCatalog(MetaClassImpl metaClass, String nextUUID);

    DeclaredAttributeImpl addDeclaredAttribute(MetaClassImpl metaclass, String code);

    void addDescription(CatalogImpl catalog, String lang, String descr);

    void addDescription(MetaClassImpl metaclass, String lang, String descr);

    void addDescriptionOverride(MetaClassImpl metaclass, String lang, String descrOverride);

    MetaClassImpl addMetaClass(ClassFqn fqn, ClassFqn parentFqn);

    RelationImpl addRelation(String code1);

    void addTitle(CatalogImpl catalog, String lang, String title);

    void addTitle(MetaClassImpl metaclass, String lang, String title);

    void addTitleOverride(MetaClassImpl metaclass, String lang, String titleOverride);

    void delAttributeOverride(MetaClassImpl metaclass, String code2);

    void delDeclaredAttribute(MetaClassImpl metaclass, String code2);

    boolean deleteCatalog(String code);

    void deleteEscalationSchemeFromCache(String code);

    void deleteMetaClass(MetaClassImpl metaClass);

    boolean deleteRelation(RelationImpl rel);

    void deleteTimerDefinitionFromCache(String code);

    void setDescription(CatalogImpl catalog, String descrOverride);

    void setHardcoded(MetaClassImpl metaClass, boolean b);

    void setProperties(MetaClassImpl metaclass, boolean abstrct, ClassFqn fqn, boolean hardcoded, boolean hasCases,
            boolean hasUserClasses, boolean singleton, Status status);

    void setTitle(CatalogImpl catalog, String titleOverride);
}
