package ru.naumen.metainfo.server.spi.dispatch;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

import java.util.Arrays;

import jakarta.inject.Inject;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.transaction.annotation.Transactional;

import net.customware.gwt.dispatch.server.Dispatch;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.server.bo.service.SlmService;
import ru.naumen.core.server.dispatch.ObjectTestUtils;
import ru.naumen.core.server.dispatch.SecurityTestUtils;
import ru.naumen.core.server.jta.TransactionRunner;
import ru.naumen.core.server.script.storage.ScriptStorageService;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.AbstractUserEntity;
import ru.naumen.core.shared.Constants.Employee;
import ru.naumen.core.shared.Constants.ServiceCall;
import ru.naumen.core.shared.dispatch.EditObjectAction;
import ru.naumen.core.shared.utils.UUIDGenerator;
import ru.naumen.metainfo.server.ClassMetainfoServiceException;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.dispatch2.DelMetaClassAction;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.script.Script;
import ru.naumen.metainfo.shared.script.ScriptDto;
import ru.naumen.uniquerandom.UniqueNumbersGenerator;

/**
 * <AUTHOR>
 * @since 17.02.2011
 *
 */
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class DelMetaClassActionHandlerDbTest
{
    private static final ClassFqn EMPLOYEE_FQN = ClassFqn.parse(Constants.Employee.CLASS_ID);
    @Inject
    private Dispatch dispatch;
    @Inject
    private MetainfoService metainfoService;
    @Inject
    private ObjectTestUtils utils;
    @Inject
    private SecurityTestUtils securityTestUtils;
    @Inject
    private ScriptStorageService scriptStorageService;

    @Test
    public void delExisted() throws Exception
    {
        //настройка системы
        ClassFqn parentFqn = ClassFqn.parse(Employee.CLASS_ID);

        ClassFqn fqn = TransactionRunner.call(() -> {
            ClassFqn emplCase = utils.createCase(parentFqn);
            utils.createCase(emplCase);
            return emplCase;
        });
        MetaClass parentMetaclass = metainfoService.getMetaClass(parentFqn);
        //вызов системы
        dispatch.execute(new DelMetaClassAction(fqn));
        //проверка утверждений
        Assert.assertFalse(parentMetaclass.getChildren().contains(fqn));
        //очистка
    }

    @Test(expected = ClassMetainfoServiceException.class)
    public void delExisted_objectOfTypeExists() throws Exception
    {
        //настройка системы
        ClassFqn fqn = TransactionRunner.call(() -> {
            ClassFqn parentFqn = ClassFqn.parse(Employee.CLASS_ID);
            ClassFqn emplFqn = utils.createCase(parentFqn);
            utils.createCase(emplFqn);
            return emplFqn;
        });
        utils.createEmployee(fqn);
        //вызов системы
        dispatch.execute(new DelMetaClassAction(fqn));
        //проверка утверждений
        //ожидание исключения
        //очистка
    }

    @Test(expected = ClassMetainfoServiceException.class)
    @Transactional
    public void delHardcoded() throws Exception
    {
        //настройка системы
        ClassFqn fqn = ClassFqn.parse(Employee.CLASS_ID);
        //вызов системы
        dispatch.execute(new DelMetaClassAction(fqn));
        //проверка утверждений
        //очистка
    }

    @Test(expected = ClassMetainfoServiceException.class)
    @Transactional
    public void delNotExisted() throws Exception
    {
        //настройка системы
        ClassFqn fqn = ClassFqn.parse("fqn" + UniqueNumbersGenerator.nextInt(1000000));
        //вызов системы
        dispatch.execute(new DelMetaClassAction(fqn));
        //проверка утверждений
        //очистка
    }

    @Test(expected = ClassMetainfoServiceException.class)
    public void delScCase_serviceContainsCase() throws Exception
    {
        //настройка системы
        ClassFqn scFqn = utils.createCase(ClassFqn.parse(ServiceCall.CLASS_ID));
        SlmService service = utils.createSlmService();
        MapProperties properties = new MapProperties();
        properties.setProperty("callCases", Arrays.asList(scFqn));
        dispatch.execute(new EditObjectAction(service.getUUID(), properties));
        //вызов системы
        dispatch.execute(new DelMetaClassAction(scFqn));
        //проверка утверждений
        //ожидание исключения
        //очистка
    }

    @Test
    public void delScCase_serviceNotContainsCase() throws Exception
    {
        //настройка системы
        ClassFqn scFqn = utils.createCase(ClassFqn.parse(ServiceCall.CLASS_ID));
        utils.createSlmService();
        //вызов системы
        dispatch.execute(new DelMetaClassAction(scFqn));
        //проверка утверждений
        //ожидание исключения
        //очистка
    }

    @Test
    public void delUserClass_otherParent() throws Exception
    {
        //настройка системы
        String classId1 = "class" + UniqueNumbersGenerator.nextInt(10000000);
        String classId2 = "class" + UniqueNumbersGenerator.nextInt(10000000);
        Object[] objs = TransactionRunner.call(() -> {
            ClassFqn userClass1 = utils.createClass(classId1, AbstractUserEntity.FQN);
            ClassFqn userClass2 = utils.createClassContained(classId2, AbstractUserEntity.FQN, userClass1);
            return new Object[] { userClass1, userClass2 };
        });
        ClassFqn userClass1 = (ClassFqn)objs[0];
        ClassFqn userClass2 = (ClassFqn)objs[1];
        //вызов системы
        dispatch.execute(new DelMetaClassAction(userClass1));
        //проверка утверждений
        Assert.assertFalse("Атрибут 'Родитель' удален", metainfoService.getMetaClass(userClass2).getAttributeCodes()
                .contains(Constants.PARENT_ATTR));
        //ожидание исключения
        //очистка
    }

    @Test
    public void delUserClass_selfContained() throws Exception
    {
        //настройка системы
        String classId = "class" + UniqueNumbersGenerator.nextInt(10000000);
        ClassFqn userClass = utils.createClassContained(classId, AbstractUserEntity.FQN, ClassFqn.parse(classId));
        //вызов системы
        dispatch.execute(new DelMetaClassAction(userClass));
        //проверка утверждений
        //ожидание исключения
        //очистка
    }

    @Before
    public void setUp()
    {
        securityTestUtils.autenticateAsSuperUser();
        securityTestUtils.initLicensing();
    }

    /**
     * Проверка на то, что после удаления метакласса, содержащего атрибут, содержащего скрипт вычисления значения атрибута при редактировании,
     * информация о скрипте не удаляется из кэша информации о скриптах, а информация об атрибуте
     * удаляется из мест использования скрипта.
     *  
     * @throws Exception
     */
    @Test
    public void testDeclaredAttributeComputableOnFormScriptInfoShouldBeObliteratedOnDelete() throws Exception
    {
        //настройка системы
        ClassFqn parentFqn = EMPLOYEE_FQN;
        Object[] objs = TransactionRunner.call(() -> {
            ClassFqn fqn = utils.createCase(parentFqn);
            String title = UUIDGenerator.get().nextUUID();
            String description = UUIDGenerator.get().nextUUID();
            ScriptDto scriptDto = utils.createScriptDto("return []");
            String code = utils.createAttribute(fqn, true, false, false, title, description, false, null, true,
                    scriptDto, false, null);
            return new Object[] { fqn, code };
        });
        ClassFqn fqn = (ClassFqn)objs[0];
        String code = (String)objs[1];

        MetaClass metaClass = metainfoService.getMetaClass(fqn);
        Attribute attribute = metaClass.getAttribute(code);
        String scriptCode = attribute.getComputableOnFormScript();

        assertInfoDeletion(fqn, scriptCode);
    }

    /**
     * Проверка на то, что после удаления метакласса, содержащего атрибут, содержащего скрипт вычисления значения атрибута,
     * информация о скрипте не удаляется из кэша информации о скриптах, а информация об атрибуте
     * удаляется из мест использования скрипта.
     * 
     * @throws Exception
     */
    @Test
    public void testDeclaredAttributeComputableScriptShouldBeObliteratedOnDelete() throws Exception
    {
        //настройка системы
        ClassFqn parentFqn = EMPLOYEE_FQN;
        Object[] objs = TransactionRunner.call(() -> {
            ClassFqn fqn = utils.createCase(parentFqn);
            String title = UUIDGenerator.get().nextUUID();
            String description = UUIDGenerator.get().nextUUID();
            ScriptDto scriptDto = utils.createScriptDto("'asd'");
            String code = utils.createAttribute(fqn, true, false, false, title, description, true, scriptDto, false,
                    null, false, null);
            return new Object[] { fqn, code };
        });
        ClassFqn fqn = (ClassFqn)objs[0];
        String code = (String)objs[1];

        MetaClass metaClass = metainfoService.getMetaClass(fqn);
        Attribute attribute = metaClass.getAttribute(code);
        String scriptCode = attribute.getScript();

        assertInfoDeletion(fqn, scriptCode);
    }

    /**
     * Проверка на то, что после удаления метакласса, содержащего атрибут, содержащего скрипт вычисления значения атрибута по-умолчанию,
     * информация о скрипте не удаляется из кэша информации о скриптах, а информация об атрибуте
     * удаляется из мест использования скрипта.
     * 
     * @throws Exception
     */
    @Test
    public void testDeclaredAttributeScriptForDefaultShouldBeObliteratedOnDelete() throws Exception
    {
        //настройка системы
        ClassFqn parentFqn = EMPLOYEE_FQN;
        Object[] objs = TransactionRunner.call(() -> {
            ClassFqn fqn = utils.createCase(parentFqn);
            String title = UUIDGenerator.get().nextUUID();
            String description = UUIDGenerator.get().nextUUID();
            ScriptDto scriptDto = utils.createScriptDto("return 'asd'");
            String code = utils.createAttribute(fqn, true, false, false, title, description, false, null, false, null,
                    true, scriptDto);
            return new Object[] { fqn, code };
        });
        ClassFqn fqn = (ClassFqn)objs[0];
        String code = (String)objs[1];

        MetaClass metaClass = metainfoService.getMetaClass(fqn);
        Attribute attribute = metaClass.getAttribute(code);
        String scriptCode = attribute.getScriptForDefault();

        assertInfoDeletion(fqn, scriptCode);
    }

    /**
     * Проверка на то, что после удаления метакласса, содержащего атрибут, содержащего скрипт фильтрации значений атрибута,
     * информация о скрипте не удаляется из кэша информации о скриптах, а информация об атрибуте
     * удаляется из мест использования скрипта.
     * 
     * @throws Exception
     */
    @Test
    public void testDeclaredAttributeScriptForFiltrationShouldBeObliteratedOnMetaclassDeletion() throws Exception
    {
        securityTestUtils.initLicensing();
        Object[] objs = TransactionRunner.call(() -> {
            ClassFqn fqn = utils.createCase(Constants.Employee.FQN);
            ScriptDto scriptDto = utils.createScriptDto("return []");
            String code = utils.createAggregateOUAttribute(fqn, null, true, scriptDto);
            MetaClass metaClass = metainfoService.getMetaClass(fqn);
            Attribute attribute = metaClass.getAttribute(code);
            String scriptCode = attribute.getScriptForFiltration();
            return new Object[] { fqn, scriptCode };
        });
        ClassFqn fqn = (ClassFqn)objs[0];
        String scriptCode = (String)objs[1];

        assertInfoDeletion(fqn, scriptCode);
    }

    private void assertInfoDeletion(ClassFqn fqn, String scriptCode) throws DispatchException
    {
        dispatch.execute(new DelMetaClassAction(fqn));
        Script script = scriptStorageService.getScript(scriptCode);
        assertNotNull("Информация о скрипте не должна быть удалена из кэша информации о скриптах.", script);
        assertTrue("Информация об атрибуте должна быть удалена из мест использования скрипта.",
                CollectionUtils.isEmpty(script.getUsagePoints()));

        //Очистка
        utils.deleteUnusedScript(scriptCode);
    }
}
