package ru.naumen.metainfo.server.spi.dispatch.wf;

import jakarta.inject.Inject;
import jakarta.transaction.Transactional;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import net.customware.gwt.dispatch.server.Dispatch;
import ru.naumen.core.server.dispatch.ObjectTestUtils;
import ru.naumen.core.server.dispatch.SecurityTestHelper;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.utils.UUIDGenerator;
import ru.naumen.metainfo.server.ClassMetainfoServiceException;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.dispatch2.wf.DelConditionAction;
import ru.naumen.metainfo.shared.elements.wf.Condition;
import ru.naumen.metainfo.shared.elements.wf.State;
import ru.naumen.uniquerandom.RandomUtilsImpl;

/**
 * 
 * <AUTHOR>
 *
 */
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class DelConditionActionHandlerDbTest
{
    @Inject
    Dispatch dispatch;
    @Inject
    MetainfoService metainfoService;
    @Inject
    ObjectTestUtils testUtils;

    @Transactional
    @Test
    public void delExists() throws Exception
    {
        // настройка системы
        ClassFqn fqn = testUtils.createCase(Constants.ServiceCall.FQN);
        String stateCode = RandomUtilsImpl.nextCode64();
        testUtils.createState(fqn, stateCode);
        Condition condition = testUtils.createCondition(fqn, stateCode, true);
        // вызов системы
        DelConditionAction a = new DelConditionAction(fqn, stateCode, condition.getCode(), true);
        dispatch.execute(a);
        // проверка утверждений
        State state = metainfoService.getMetaClass(fqn).getWorkflow().getState(stateCode);
        Assert.assertTrue(state.getPreConditions().isEmpty());
        Assert.assertTrue(state.getPostConditions().isEmpty());
        // очистка
    }

    @Transactional
    @Test(expected = ClassMetainfoServiceException.class)
    public void delNotDeclared() throws Exception
    {
        // настройка системы
        ClassFqn parentFqn = Constants.ServiceCall.FQN;
        ClassFqn fqn = testUtils.createCase(parentFqn);
        String stateCode = RandomUtilsImpl.nextCode64();
        testUtils.createState(parentFqn, stateCode);
        Condition condition = testUtils.createCondition(parentFqn, stateCode, true);
        // вызов системы
        DelConditionAction a = new DelConditionAction(fqn, stateCode, condition.getCode(), true);
        dispatch.execute(a);
        // проверка утверждений
        //ожидание исключения
        // очистка
    }

    @Transactional
    @Test(expected = ClassMetainfoServiceException.class)
    public void delNotExists() throws Exception
    {
        // настройка системы
        ClassFqn fqn = Constants.ServiceCall.FQN;
        String stateCode = RandomUtilsImpl.nextCode64();
        testUtils.createState(fqn, stateCode);
        // вызов системы
        DelConditionAction a = new DelConditionAction(fqn, stateCode, UUIDGenerator.get().nextUUID().substring(0, 15),
                true);
        dispatch.execute(a);
        // проверка утверждений
        //ожидание исключения
        // очистка
    }

    @Before
    public void setUp()
    {
        SecurityTestHelper.autenticateAsSuperUser();
    }
}
