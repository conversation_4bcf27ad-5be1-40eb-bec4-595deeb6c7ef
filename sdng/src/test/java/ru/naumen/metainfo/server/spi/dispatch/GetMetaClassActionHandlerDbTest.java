package ru.naumen.metainfo.server.spi.dispatch;

import jakarta.inject.Inject;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.transaction.annotation.Transactional;

import net.customware.gwt.dispatch.server.Dispatch;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.dispatch.SecurityTestHelper;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.utils.UUIDGenerator;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.dispatch2.GetMetaClassAction;
import ru.naumen.metainfo.shared.dispatch2.GetMetaClassResponse;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.MetaClassLite;

/**
 * 
 * <AUTHOR>
 *
 */
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class GetMetaClassActionHandlerDbTest
{
    @Inject
    Dispatch dispatch;
    @Inject
    MetainfoService metainfoService;

    @Test
    @Transactional
    public void exists() throws Exception
    {
        // настройка системы
        // вызов системы
        ClassFqn fqn = ClassFqn.parse(Constants.Employee.CLASS_ID);
        GetMetaClassAction a = new GetMetaClassAction(fqn);
        GetMetaClassResponse r = dispatch.execute(a);
        // проверка утверждений
        MetaClass metaClass = r.getMetaClass();

        Assert.assertEquals(fqn, metaClass.getFqn());
        Assert.assertEquals(MetaClassLite.Status.DEFAULT, metaClass.getStatus());
        // очистка
    }

    @Test(expected = FxException.class)
    @Transactional
    public void notExists() throws Exception
    {
        // настройка системы
        // вызов системы
        ClassFqn fqn = ClassFqn.parse(UUIDGenerator.get().nextUUID());
        GetMetaClassAction a = new GetMetaClassAction(fqn);
        dispatch.execute(a);
        // проверка утверждений
        // очистка
    }

    @Before
    public void setUp()
    {
        SecurityTestHelper.autenticateAsSuperUser();
    }
}
