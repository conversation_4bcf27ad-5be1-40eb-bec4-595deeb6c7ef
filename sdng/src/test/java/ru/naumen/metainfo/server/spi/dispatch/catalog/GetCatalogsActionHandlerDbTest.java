package ru.naumen.metainfo.server.spi.dispatch.catalog;

import java.util.Arrays;
import java.util.List;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.transaction.annotation.Transactional;

import jakarta.inject.Inject;
import net.customware.gwt.dispatch.server.Dispatch;
import ru.naumen.core.server.dispatch.ObjectTestUtils;
import ru.naumen.core.server.dispatch.SecurityTestHelper;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.dispatch2.catalog.GetCatalogsAction;
import ru.naumen.metainfo.shared.dispatch2.catalog.GetCatalogsResponse;
import ru.naumen.metainfo.shared.elements.Catalog;

/**
 * <AUTHOR>
 * @since 21.02.2011
 *
 */
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class GetCatalogsActionHandlerDbTest
{
    @Inject
    Dispatch dispatch;
    @Inject
    ObjectTestUtils utils;
    @Inject
    MetainfoService metainfoService;

    @Test
    @Transactional
    public void getAllCatalogs() throws Exception
    {
        //настройка системы
        Catalog catalog1 = utils.createCatalog();
        Catalog catalog2 = utils.createCatalog();
        Catalog catalog3 = metainfoService.getCatalog(Constants.ImpactCatalog.CODE);
        List<Catalog> catalogs = Arrays.asList(catalog1, catalog2, catalog3);
        //вызов системы
        GetCatalogsResponse result = dispatch.execute(new GetCatalogsAction());
        //проверка утверждений
        List<Catalog> resultCatalogs = result.getCatalogs().stream().map(DtoContainer::get).toList();
        int found = 0;
        for (Catalog resCat : resultCatalogs)
        {
            for (Catalog cat : catalogs)
            {
                if ((resCat.getCode().equals(cat.getCode()) && resCat.getTitle().equals(cat.getTitle())))
                {
                    found++;
                    continue;
                }
            }
        }
        Assert.assertEquals(3, found);
        //очистка
    }

    @Test
    @Transactional
    public void getSomeCatalogs() throws Exception
    {
        //настройка системы
        Catalog catalog1 = utils.createCatalog();
        Catalog catalog2 = utils.createCatalog();
        //вызов системы
        GetCatalogsResponse result = dispatch.execute(new GetCatalogsAction(Arrays.asList(catalog1.getCode(),
                catalog2.getCode())));
        //проверка утверждений
        List<Catalog> catalogs = result.getCatalogs().stream().map(DtoContainer::get).toList();
        int found = 0;
        for (Catalog cat : catalogs)
        {
            if ((cat.getCode().equals(catalog1.getCode()) && cat.getTitle().equals(catalog1.getTitle()))
                    || ((cat.getCode().equals(catalog2.getCode()) && cat.getTitle().equals(catalog2.getTitle()))))
            {
                found++;
            }
        }
        Assert.assertEquals(2, catalogs.size());
        Assert.assertEquals(2, found);
        //очистка
    }

    @Before
    public void setUp()
    {
        SecurityTestHelper.autenticateAsSuperUser();
    }
}
