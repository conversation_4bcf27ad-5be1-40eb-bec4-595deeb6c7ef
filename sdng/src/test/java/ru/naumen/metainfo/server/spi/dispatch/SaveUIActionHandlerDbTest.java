package ru.naumen.metainfo.server.spi.dispatch;

import jakarta.inject.Inject;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.transaction.annotation.Transactional;

import net.customware.gwt.dispatch.server.Dispatch;
import ru.naumen.core.server.dispatch.ObjectTestUtils;
import ru.naumen.core.server.dispatch.SecurityTestHelper;
import ru.naumen.core.server.flex.spi.ReloadableSessionFactoryBean;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.TestUtils;
import ru.naumen.core.shared.utils.UUIDGenerator;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.UI;
import ru.naumen.metainfo.shared.ContentInfo;
import ru.naumen.metainfo.shared.dispatch2.SaveUIAction;
import ru.naumen.metainfo.shared.ui.Layout;
import ru.naumen.metainfo.shared.ui.LocalizedString;
import ru.naumen.metainfo.shared.ui.Tab;
import ru.naumen.metainfo.shared.ui.TabBar;

/**
 * <AUTHOR>
 * @since 21.02.2011
 *
 */
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class SaveUIActionHandlerDbTest
{
    @Inject
    Dispatch dispatch;
    @Inject
    MetainfoService metainfoService;
    @Inject
    ObjectTestUtils utils;
    @Inject
    ReloadableSessionFactoryBean sessionFactoryBean;

    @Test
    @Transactional
    public void saveUI() throws Exception
    {
        //настройка системы
        ClassFqn fqn = utils.createCase(ClassFqn.parse(Constants.Employee.CLASS_ID));
        sessionFactoryBean.reload();
        String code = TestUtils.randomString();
        TabBar ui = new TabBar();
        ui.setUuid(TabBar.PREFIX + code);
        Tab tab = new Tab();

        ui.getTab().add(tab);
        tab.getCaption().add(new LocalizedString("ru", UUIDGenerator.get().nextUUID()));
        tab.setUuid(code);
        tab.setLayout(new Layout());
        //вызов системы
        dispatch.execute(new SaveUIAction(fqn, ui, null, UI.Form.EDIT));
        //проверка утверждений
        ContentInfo formInfo = metainfoService.getUiForm(fqn, UI.Form.EDIT);
        Assert.assertEquals(fqn, formInfo.getDeclaredMetaclass());
        Assert.assertEquals(ui, formInfo.getContent());
        //очистка
    }

    @Before
    public void setUp()
    {
        SecurityTestHelper.autenticateAsSuperUser();
    }
}
