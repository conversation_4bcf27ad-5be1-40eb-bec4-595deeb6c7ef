package ru.naumen.metainfo.server.spi.dispatch;

import java.util.Arrays;
import java.util.List;

import jakarta.inject.Inject;
import jakarta.transaction.Transactional;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import net.customware.gwt.dispatch.server.Dispatch;
import ru.naumen.core.server.dispatch.ObjectTestUtils;
import ru.naumen.core.server.dispatch.SecurityTestHelper;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.TestUtils;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.server.spi.MetainfoServiceBean;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.AttrGroup;
import ru.naumen.metainfo.shared.Constants.UI;
import ru.naumen.metainfo.shared.dispatch2.ResetMetaClassesAction;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.AttributeGroup;
import ru.naumen.metainfo.shared.ui.Layout;
import ru.naumen.metainfo.shared.ui.LocalizedString;
import ru.naumen.metainfo.shared.ui.Tab;
import ru.naumen.metainfo.shared.ui.TabBar;

/**
 * <AUTHOR>
 * @since 21.02.2011
 *
 */
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class ResetMetaClassesActionHandlerDbTest
{
    @Inject
    Dispatch dispatch;
    @Inject
    MetainfoService metainfoService;
    @Inject
    ObjectTestUtils utils;
    @Inject
    MetainfoServiceBean metainfoServiceBean;

    @Test
    @Transactional
    public void reset() throws Exception
    {
        //настройка системы

        ClassFqn parentFqn = ClassFqn.parse(Constants.Employee.CLASS_ID);

        //атрибуты
        String attr1 = utils.createAttribute(parentFqn);
        String attr2 = utils.createAttribute(parentFqn);

        String attr3 = Constants.AbstractBO.REMOVED;
        String attr4 = utils.createAttribute(parentFqn);

        AttributeGroup grp = utils.createAttributeGroup(parentFqn, Arrays.asList(attr1, Constants.Employee.FIRST_NAME));
        String grpCode = grp.getCode();

        String code = TestUtils.randomString();
        TabBar tabBar = new TabBar();
        tabBar.setUuid(TabBar.PREFIX + code);
        Tab tab = new Tab();
        tab.setUuid(code);
        tab.setLayout(new Layout());
        tab.getCaption().add(new LocalizedString("ru", "caption"));
        tabBar.getTab().add(tab);
        metainfoServiceBean.setUIForm(parentFqn, UI.Form.EDIT, tabBar, true, true);

        ClassFqn fqn = utils.createCase(parentFqn);
        ClassFqn fqn2 = utils.createCase(parentFqn);
        utils.editBoolAttribute(fqn, attr1);
        utils.editBoolAttribute(fqn, attr2);

        utils.editBoolAttribute(fqn2, attr3);

        //группа атрибутов

        utils.editAttributeGroup(fqn, grpCode,
                Arrays.asList(attr2, Constants.PARENT_ATTR, Constants.Employee.LAST_NAME));
        utils.editAttributeGroup(fqn, AttrGroup.SYSTEM,
                Arrays.asList(attr2, Constants.PARENT_ATTR, Constants.Employee.LAST_NAME));
        //ui

        metainfoServiceBean.setUIForm(fqn, UI.Form.EDIT, tabBar, true, false);
        metainfoServiceBean.setUIForm(fqn2, UI.Form.EDIT, tabBar, true, false);
        //вызов систем
        dispatch.execute(new ResetMetaClassesAction(Arrays.asList(fqn, fqn2)));
        //проверка утверждений

        //проверяем, что настройки пользовательских атрибутов не сбросились
        List<String> attrs = Arrays.asList(attr1, attr2);
        for (String attr : attrs)
        {
            assertAttrNotReset(parentFqn, fqn, attr);
        }
        //Проверяем, что настройка системного атрибута сбросилась
        assertAttrReset(parentFqn, fqn, attr3);
        //проверяем, что настройка групп атрибутов сбросилась
        AttributeGroup parentGrp = metainfoService.getMetaClass(parentFqn).getAttributeGroup(grpCode);
        AttributeGroup resetGrp = metainfoService.getMetaClass(fqn).getAttributeGroup(grpCode);
        Assert.assertEquals(parentGrp.getTitle(), resetGrp.getTitle());
        Assert.assertEquals(parentGrp.getAttributeCodes(), resetGrp.getAttributeCodes());

        AttributeGroup parentSystemGrp = metainfoService.getMetaClass(parentFqn).getAttributeGroup(AttrGroup.SYSTEM);
        AttributeGroup resetNotResetGrp = metainfoService.getMetaClass(fqn).getAttributeGroup(AttrGroup.SYSTEM);
        Assert.assertEquals(parentSystemGrp.getTitle(), resetNotResetGrp.getTitle());
        Assert.assertEquals(parentSystemGrp.getAttributeCodes(), resetNotResetGrp.getAttributeCodes());
        //проверяем, что сборосились настройки формы редактирования
        Assert.assertEquals(parentFqn, metainfoService.getUiForm(fqn, UI.Form.EDIT).getDeclaredMetaclass());
        //очистка
    }

    @Before
    public void setUp()
    {
        SecurityTestHelper.autenticateAsSuperUser();
    }

    /**
     * @param parentFqn
     * @param fqn
     * @param attr
     */
    private void assertAttrNotReset(ClassFqn parentFqn, ClassFqn fqn, String attr)
    {
        Attribute attrInParent = metainfoService.getMetaClass(parentFqn).getAttribute(attr);
        Attribute resetAttr = metainfoService.getMetaClass(fqn).getAttribute(attr);
        Assert.assertEquals(attrInParent.getCode(), resetAttr.getCode());
        Assert.assertNotEquals(attrInParent.getTitle(), resetAttr.getTitle());
        Assert.assertNotEquals(attrInParent.getDescription(), resetAttr.getDescription());
        Assert.assertNotEquals(attrInParent.getViewPresentation().getCode(), resetAttr.getViewPresentation().getCode());
        Assert.assertNotEquals(attrInParent.getEditPresentation().getCode(), resetAttr.getEditPresentation().getCode());
    }

    /**
     * @param parentFqn
     * @param fqn
     * @param attr
     */
    private void assertAttrReset(ClassFqn parentFqn, ClassFqn fqn, String attr)
    {
        Attribute attrInParent = metainfoService.getMetaClass(parentFqn).getAttribute(attr);
        Attribute resetAttr = metainfoService.getMetaClass(fqn).getAttribute(attr);
        Assert.assertEquals(attrInParent.getCode(), resetAttr.getCode());
        Assert.assertEquals(attrInParent.getTitle(), resetAttr.getTitle());
        Assert.assertEquals(attrInParent.getDescription(), resetAttr.getDescription());
        Assert.assertEquals(attrInParent.getViewPresentation().getCode(), resetAttr.getViewPresentation().getCode());
        Assert.assertEquals(attrInParent.getEditPresentation().getCode(), resetAttr.getEditPresentation().getCode());
    }

}
