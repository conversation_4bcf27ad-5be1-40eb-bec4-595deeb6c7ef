package ru.naumen.metainfo.server.spi.dispatch.scheduler;

import java.util.Arrays;
import java.util.List;

import jakarta.inject.Inject;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.transaction.annotation.Transactional;

import net.customware.gwt.dispatch.server.Dispatch;
import ru.naumen.core.server.metastorage.impl.metainfo.MetaStorageService;
import ru.naumen.core.server.scheduler.service.SchedulingService;
import ru.naumen.core.shared.common.I18nUtil;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.utils.UUIDGenerator;
import ru.naumen.metainfo.shared.dispatch2.scheduler.GetSchedulerTasksAction;
import ru.naumen.metainfo.shared.scheduler.ExecuteScriptTask;
import ru.naumen.metainfo.shared.scheduler.SchedulerTask;
import ru.naumen.uniquerandom.UniqueNumbersGenerator;

/**
 * <AUTHOR>
 * @since 08.06.2011
 *
 */
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class GetSchedulerTasksActionHandlerDbTest
{
    @Inject
    Dispatch dispatch;
    @Inject
    MetaStorageService metaStorage;
    @Inject
    I18nUtil i18nUtil;
    @Inject
    SchedulingService service;

    @Test
    @Transactional
    public void execute() throws Exception
    {
        //настройка системы
        SchedulerTask schTask1 = new ExecuteScriptTask();
        String code1 = "schTask" + UniqueNumbersGenerator.nextInt(10000);
        String title1 = UUIDGenerator.get().nextUUID();
        schTask1.setCode(code1);
        i18nUtil.updateI18nObjectTitle(schTask1, title1);
        schTask1.setType(ExecuteScriptTask.NAME);
        service.saveSchedulerTask(schTask1);

        SchedulerTask schTask2 = new ExecuteScriptTask();
        String code2 = "schTask" + UniqueNumbersGenerator.nextInt(10000);
        String title2 = UUIDGenerator.get().nextUUID();
        schTask2.setCode(code2);
        i18nUtil.updateI18nObjectTitle(schTask2, title2);
        schTask2.setType(ExecuteScriptTask.NAME);
        service.saveSchedulerTask(schTask2);
        //вызов системы
        List<DtoContainer<SchedulerTask>> result = dispatch.execute(
                new GetSchedulerTasksAction(Arrays.asList(code1, code2))).get();
        //проверка утверждений
        Assert.assertEquals(2, result.size());
        boolean foundTask1 = false;
        boolean foundTask2 = false;
        for (DtoContainer<SchedulerTask> task : result)
        {
            if (code1.equals(task.get().getCode()))
            {
                foundTask1 = true;
            }
            if (code2.equals(task.get().getCode()))
            {
                foundTask2 = true;
            }
        }
        Assert.assertTrue(foundTask1);
        Assert.assertTrue(foundTask2);
        //очистка
    }
}
