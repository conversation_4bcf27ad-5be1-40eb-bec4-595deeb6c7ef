package ru.naumen.metainfo.server.spi.dispatch.wf;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

import jakarta.inject.Inject;
import jakarta.transaction.Transactional;

import com.google.common.base.Function;
import com.google.common.collect.Collections2;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import org.junit.AfterClass;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import net.customware.gwt.dispatch.server.Dispatch;
import ru.naumen.commons.shared.utils.Pair;
import ru.naumen.core.server.dispatch.ObjectTestUtils;
import ru.naumen.core.server.dispatch.SecurityTestHelper;
import ru.naumen.core.server.jta.TransactionRunner;
import ru.naumen.core.server.jta.TransactionRunner.TransactionType;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.ServiceCallWorkflow;
import ru.naumen.core.shared.utils.UUIDGenerator;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.dispatch2.wf.SetTransitionsAction;
import ru.naumen.metainfo.shared.elements.wf.State;
import ru.naumen.metainfo.shared.elements.wf.Transition;
import ru.naumen.metainfo.shared.elements.wf.Workflow;

/**
 * 
 * <AUTHOR>
 *
 */
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class SetTransitionsActionHandlerDbTest
{
    static ClassFqn fqn;

    @AfterClass
    public static void afterClass()
    {
        fqn = null;
    }

    @Inject
    Dispatch dispatch;

    @Inject
    MetainfoService metainfoService;

    @Inject
    ObjectTestUtils objectTestUtils;

    @Transactional
    @Test
    public void clearAndSetTransition() throws Exception
    {
        // настройка системы
        // подготовка запроса
        Workflow wf = metainfoService.getMetaClass(Constants.ServiceCall.FQN).getWorkflow();
        Collection<Pair<String, String>> transition = Lists.newArrayList(prepareExistedTransitions(wf));
        // очистка перехода
        transition.remove(Pair
                .create(ru.naumen.core.shared.Constants.Workflow.REGISTERED, ServiceCallWorkflow.RESOLVED));
        transition.remove(Pair.create(ServiceCallWorkflow.RESOLVED, ru.naumen.core.shared.Constants.Workflow.CLOSED));
        transition.add(Pair.create(ru.naumen.core.shared.Constants.Workflow.REGISTERED,
                ru.naumen.core.shared.Constants.Workflow.CLOSED));
        dispatch.execute(new SetTransitionsAction(fqn, transition, Maps.<Pair<String, String>, String> newHashMap()));
        // добавление перехода
        transition.add(Pair.create(ru.naumen.core.shared.Constants.Workflow.REGISTERED, ServiceCallWorkflow.RESOLVED));
        transition.add(Pair.create(ServiceCallWorkflow.RESOLVED, ru.naumen.core.shared.Constants.Workflow.CLOSED));
        dispatch.execute(new SetTransitionsAction(fqn, transition, Maps.<Pair<String, String>, String> newHashMap()));
        // проверка утверждений
        Transition redifined = metainfoService.getMetaClass(fqn).getWorkflow()
                .getTransition(ru.naumen.core.shared.Constants.Workflow.REGISTERED, ServiceCallWorkflow.RESOLVED);
        Assert.assertNotNull(redifined);
        Assert.assertNotNull(wf.getTransition(ru.naumen.core.shared.Constants.Workflow.REGISTERED,
                ServiceCallWorkflow.RESOLVED));
        // очистка
    }

    @Transactional
    @Test
    public void clearTransition() throws Exception
    {
        // настройка системы
        // подготовка запроса
        Workflow wf = metainfoService.getMetaClass(Constants.ServiceCall.FQN).getWorkflow();
        Collection<Pair<String, String>> transition = Lists.newArrayList(prepareExistedTransitions(wf));
        transition.remove(Pair
                .create(ru.naumen.core.shared.Constants.Workflow.REGISTERED, ServiceCallWorkflow.RESOLVED));
        transition.remove(Pair.create(ServiceCallWorkflow.RESOLVED, ru.naumen.core.shared.Constants.Workflow.CLOSED));
        transition.add(Pair.create(ru.naumen.core.shared.Constants.Workflow.REGISTERED,
                ru.naumen.core.shared.Constants.Workflow.CLOSED));

        SetTransitionsAction a = new SetTransitionsAction(fqn, transition,
                Maps.<Pair<String, String>, String> newHashMap());
        // вызов системы
        dispatch.execute(a);
        // проверка утверждений
        Transition redifined = metainfoService.getMetaClass(fqn).getWorkflow()
                .getTransition(ru.naumen.core.shared.Constants.Workflow.REGISTERED, ServiceCallWorkflow.RESOLVED);
        Assert.assertNull(redifined);
        Assert.assertNotNull(wf.getTransition(ru.naumen.core.shared.Constants.Workflow.REGISTERED,
                ServiceCallWorkflow.RESOLVED));
        // очистка
    }

    @Transactional
    @Test
    public void clearWithTitle() throws Exception
    {
        // настройка системы
        // подготовка запроса
        Workflow wf = metainfoService.getMetaClass(Constants.ServiceCall.FQN).getWorkflow();
        Collection<Pair<String, String>> transition = Lists.newArrayList(prepareExistedTransitions(wf));
        Pair<String, String> redifinedPair = Pair.create(ru.naumen.core.shared.Constants.Workflow.REGISTERED,
                ServiceCallWorkflow.RESOLVED);
        // добавление название переходу в классе
        HashMap<Pair<String, String>, String> titles = Maps.<Pair<String, String>, String> newHashMap();
        titles.put(redifinedPair, UUIDGenerator.get().nextUUID());
        dispatch.execute(new SetTransitionsAction(Constants.ServiceCall.FQN, transition, titles));
        // добавление название переходу в типе
        transition.remove(redifinedPair);
        transition.remove(Pair.create(ServiceCallWorkflow.RESOLVED, ru.naumen.core.shared.Constants.Workflow.CLOSED));
        transition.add(Pair.create(ru.naumen.core.shared.Constants.Workflow.REGISTERED,
                ru.naumen.core.shared.Constants.Workflow.CLOSED));
        dispatch.execute(new SetTransitionsAction(fqn, transition, titles));
        // проверка утверждений
        Workflow caseWf = metainfoService.getMetaClass(fqn).getWorkflow();
        Assert.assertNull(caseWf.getTransition(redifinedPair.getLeft(), redifinedPair.getRight()));
        // очистка
    }

    /**
     * Переопределяем название для перехода
     * 
     * @throws Exception
     */
    @Transactional
    @Test
    public void defineTitle() throws Exception
    {
        // настройка системы
        // подготовка запроса
        Workflow wf = metainfoService.getMetaClass(Constants.ServiceCall.FQN).getWorkflow();
        Collection<Pair<String, String>> transition = prepareExistedTransitions(wf);
        Pair<String, String> redifinedTransition = transition.iterator().next();
        String title = UUIDGenerator.get().nextUUID();

        Map<Pair<String, String>, String> titles = Maps.newHashMap();
        titles.put(redifinedTransition, title);
        SetTransitionsAction a = new SetTransitionsAction(fqn, transition, titles);
        // вызов системы
        dispatch.execute(a);
        // проверка утверждений
        Transition redifined = metainfoService.getMetaClass(fqn).getWorkflow()
                .getTransition(redifinedTransition.getLeft(), redifinedTransition.getRight());
        Assert.assertEquals(title, redifined.getTitle());
        // очистка
    }

    @Transactional
    @Test
    public void redefineTitle() throws Exception
    {
        // настройка системы
        // подготовка запроса
        Workflow wf = metainfoService.getMetaClass(Constants.ServiceCall.FQN).getWorkflow();
        Collection<Pair<String, String>> transition = prepareExistedTransitions(wf);
        Pair<String, String> redifinedPair = transition.iterator().next();
        // установка названия перехода в классе
        HashMap<Pair<String, String>, String> titles = Maps.<Pair<String, String>, String> newHashMap();
        String title1 = UUIDGenerator.get().nextUUID();
        titles.put(redifinedPair, title1);
        dispatch.execute(new SetTransitionsAction(Constants.ServiceCall.FQN, transition, titles));
        // установка названия перехода в типе
        String title2 = UUIDGenerator.get().nextUUID();
        titles.put(redifinedPair, title2);
        dispatch.execute(new SetTransitionsAction(fqn, transition, titles));
        // проверка утверждений
        Workflow caseWf = metainfoService.getMetaClass(fqn).getWorkflow();
        Assert.assertEquals(title1, wf.getTransition(redifinedPair.getLeft(), redifinedPair.getRight()).getTitle());
        Assert.assertEquals(title2, caseWf.getTransition(redifinedPair.getLeft(), redifinedPair.getRight()).getTitle());
        // очистка
        dispatch.execute(new SetTransitionsAction(Constants.ServiceCall.FQN, transition, Maps
                .<Pair<String, String>, String> newHashMap()));
    }

    @Transactional
    @Test
    public void redefineTitle2() throws Exception
    {
        // настройка системы
        // подготовка запроса
        Workflow wf = metainfoService.getMetaClass(Constants.ServiceCall.FQN).getWorkflow();
        Collection<Pair<String, String>> transition = Lists.newArrayList(prepareExistedTransitions(wf));
        // добавлени еперехода в типе
        Pair<String, String> redifinedPair = Pair.create(ru.naumen.core.shared.Constants.Workflow.REGISTERED,
                ru.naumen.core.shared.Constants.Workflow.CLOSED);
        transition.add(redifinedPair);
        HashMap<Pair<String, String>, String> titles = Maps.<Pair<String, String>, String> newHashMap();
        String title1 = UUIDGenerator.get().nextUUID();
        titles.put(redifinedPair, title1);
        dispatch.execute(new SetTransitionsAction(fqn, transition, titles));
        // добавление перехода в классе
        String title2 = UUIDGenerator.get().nextUUID();
        titles.put(redifinedPair, title2);
        dispatch.execute(new SetTransitionsAction(Constants.ServiceCall.FQN, transition, titles));
        // проверка утверждений
        Workflow caseWf = metainfoService.getMetaClass(fqn).getWorkflow();
        Assert.assertEquals(
                title2,
                wf.getTransition(ru.naumen.core.shared.Constants.Workflow.REGISTERED,
                        ru.naumen.core.shared.Constants.Workflow.CLOSED).getTitle());
        Assert.assertEquals(
                title1,
                caseWf.getTransition(ru.naumen.core.shared.Constants.Workflow.REGISTERED,
                        ru.naumen.core.shared.Constants.Workflow.CLOSED).getTitle());
        // очистка
        transition.remove(redifinedPair);
        dispatch.execute(new SetTransitionsAction(fqn, transition, titles));
        dispatch.execute(new SetTransitionsAction(Constants.ServiceCall.FQN, transition, titles));
    }

    @Transactional
    @Test
    public void setNewTransition() throws Exception
    {
        // настройка системы
        State state = objectTestUtils.createState(fqn);
        // подготовка запроса
        Workflow wf = metainfoService.getMetaClass(Constants.ServiceCall.FQN).getWorkflow();
        Collection<Pair<String, String>> transition = Lists.newArrayList(prepareExistedTransitions(wf));
        transition.add(Pair.create(ru.naumen.core.shared.Constants.Workflow.REGISTERED, state.getCode()));
        transition.add(Pair.create(state.getCode(), ru.naumen.core.shared.Constants.Workflow.CLOSED));

        SetTransitionsAction a = new SetTransitionsAction(fqn, transition,
                Maps.<Pair<String, String>, String> newHashMap());
        // вызов системы
        dispatch.execute(a);
        // проверка утверждений
        Transition redifined = metainfoService.getMetaClass(fqn).getWorkflow()
                .getTransition(ru.naumen.core.shared.Constants.Workflow.REGISTERED, state.getCode());
        Assert.assertNotNull(redifined);
        Assert.assertNull(wf.getTransition(ru.naumen.core.shared.Constants.Workflow.REGISTERED, state.getCode()));
        // очистка
    }

    @Before
    public void setUp() throws Exception
    {
        SecurityTestHelper.autenticateAsSuperUser();

        if (fqn == null)
        {
            TransactionRunner.call(TransactionType.NEW, () -> {
                fqn = objectTestUtils.createCase(Constants.ServiceCall.FQN);
                return null;
            });
        }
    }

    protected Collection<Pair<String, String>> prepareExistedTransitions(Workflow wf)
    {
        return Collections2.transform(wf.getActiveTransitions(), new Function<Transition, Pair<String, String>>()
        {
            @Override
            public Pair<String, String> apply(Transition input)
            {
                return Pair.create(input.getBeginState(), input.getEndState());
            }
        });
    }
}
