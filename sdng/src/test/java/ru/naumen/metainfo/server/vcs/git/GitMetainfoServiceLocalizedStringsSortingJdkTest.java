package ru.naumen.metainfo.server.vcs.git;

import static java.util.Comparator.comparing;
import static java.util.Comparator.naturalOrder;
import static java.util.Comparator.nullsLast;
import static org.apache.commons.lang3.RandomStringUtils.randomAlphabetic;

import java.io.ByteArrayOutputStream;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;

import jakarta.annotation.Nullable;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.w3c.dom.Node;

import ru.naumen.core.shared.utils.ILocaleInfo;
import ru.naumen.metainfo.server.spi.store.SearchSetting;
import ru.naumen.metainfo.server.spi.store.UserMetaClass;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.ui.LocalizedString;

/**
 * Тестирование сортировки локализованных строк
 * <AUTHOR>
 * @since 12.03.2020
 */
@RunWith(MockitoJUnitRunner.class)
public class GitMetainfoServiceLocalizedStringsSortingJdkTest extends BaseGitMetainfoServiceXmlTest
{
    private static LocalizedString generateLString(@Nullable String lang)
    {
        LocalizedString localizedString = new LocalizedString();
        if (lang != null)
        {
            localizedString.setLang(lang);
        }
        localizedString.setValue(randomAlphabetic(10));
        return localizedString;
    }

    private String getLangFromNode(Node node)
    {
        Node langAttribute = node.getAttributes().getNamedItem("lang");
        return langAttribute == null ? null : langAttribute.getTextContent();
    }

    private final Comparator<Node> localizedStrComparator =
            comparing(this::getLangFromNode, nullsLast(naturalOrder()))
                    .thenComparing(this::getNodeText, nullsLast(naturalOrder()));

    @Nullable
    private String getNodeText(Node node)
    {
        return node.getTextContent().isEmpty() ? null : node.getTextContent();
    }

    @Test
    public void testMetaClassTitleSorting() throws Exception
    {
        UserMetaClass metaClass = new UserMetaClass();
        metaClass.setFqn(ClassFqn.parse(randomAlphabetic(8)));
        List<LocalizedString> title = metaClass.getTitle();
        addLStrings(title);

        List<LocalizedString> description = metaClass.getDescription();
        addLStrings(description);

        container.getMetaClasses().add(metaClass);

        final ByteArrayOutputStream stream = new ByteArrayOutputStream();
        setupSegmentOutputStream(stream, metaClass.getSegmentID());
        gitMetainfoService.exportToVCS(branch, branch, "full");
        byte[] xmlBytes = stream.toByteArray();

        assertElementSort(xmlBytes, localizedStrComparator, "//title", "//description");
    }

    @Test
    public void testSearchAnalyzersSort() throws Exception
    {
        SearchSetting searchSetting = new SearchSetting();
        searchSetting.setAttrCode(randomAlphabetic(5));
        searchSetting.setCode(randomAlphabetic(5));
        List<LocalizedString> localizedSearchAnalyzer = searchSetting.getLocalizedSearchAnalyzer();
        addLStrings(localizedSearchAnalyzer);

        List<LocalizedString> searchAlias = searchSetting.getSearchAlias();
        addLStrings(searchAlias);

        UserMetaClass metaClass = new UserMetaClass();
        metaClass.setFqn(ClassFqn.parse(randomAlphabetic(8)));
        metaClass.getSearchSetting().add(searchSetting);

        container.getMetaClasses().add(metaClass);

        final ByteArrayOutputStream stream = new ByteArrayOutputStream();
        setupSegmentOutputStream(stream, searchSetting.getSegmentID());
        gitMetainfoService.exportToVCS(branch, branch, "full");
        byte[] xmlBytes = stream.toByteArray();
        assertElementSort(xmlBytes, localizedStrComparator, "//searchAlias", "//localizedSearchAnalyzer");
    }

    private static void addLStrings(List<LocalizedString> localizedStrings)
    {
        localizedStrings.add(generateLString(randomAlphabetic(5)));
        ILocaleInfo.AVAILABLE_LOCALES.forEach(lang -> localizedStrings.add(generateLString(lang)));
        ThreadLocalRandom current = ThreadLocalRandom.current();
        int i = current.nextInt(5, 15);
        for (int j = 0; j < i; j++)
        {
            LocalizedString string = current.nextBoolean() ? generateLString(null) : new LocalizedString();
            localizedStrings.add(string);
        }
    }
}
