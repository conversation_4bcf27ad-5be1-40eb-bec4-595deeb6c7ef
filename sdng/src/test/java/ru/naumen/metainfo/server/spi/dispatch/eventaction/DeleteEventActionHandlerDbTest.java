package ru.naumen.metainfo.server.spi.dispatch.eventaction;

import static org.junit.Assert.assertNotNull;

import jakarta.inject.Inject;
import jakarta.transaction.Transactional;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import com.google.common.collect.Lists;

import net.customware.gwt.dispatch.server.Dispatch;
import ru.naumen.core.server.dispatch.ObjectTestUtils;
import ru.naumen.core.server.dispatch.SecurityTestHelper;
import ru.naumen.core.server.eventaction.EventActionService;
import ru.naumen.core.server.script.storage.ScriptStorageService;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.dispatch2.eventaction.DeleteEventAction;
import ru.naumen.metainfo.shared.dispatch2.eventaction.SaveEventAction;
import ru.naumen.metainfo.shared.dispatch2.script.SimpleScriptedResult;
import ru.naumen.metainfo.shared.eventaction.ActionType;
import ru.naumen.metainfo.shared.eventaction.Constants;
import ru.naumen.metainfo.shared.eventaction.EventAction;
import ru.naumen.metainfo.shared.eventaction.EventType;
import ru.naumen.metainfo.shared.eventaction.NotificationEventAction;
import ru.naumen.metainfo.shared.eventaction.PlannedEventRule;
import ru.naumen.metainfo.shared.eventaction.ScriptEventAction;
import ru.naumen.metainfo.shared.script.ScriptDto;
import ru.naumen.metainfo.shared.ui.LocalizedString;
import ru.naumen.uniquerandom.UniqueNumbersGenerator;

@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class DeleteEventActionHandlerDbTest
{
    @Inject
    private Dispatch dispatch;
    @Inject
    private EventActionService eventActionService;
    @Inject
    private ScriptStorageService scriptStorageService;
    @Inject
    private ObjectTestUtils utils;

    @Before
    public void setUp()
    {
        SecurityTestHelper.autenticateAsSuperUser();
    }

    /**
     * Проверка на то, что при удалении действия по событию (оповещения) информация о соответствующем
     * скрипте удаляется из кэша информации о скриптах.
     * 
     * @throws Exception
     */
    @Test
    @Transactional
    public void testNotificationActionScriptInfoShouldBeObliteratedOnDeletion() throws Exception
    {
        //Подготовка
        PlannedEventRule event = new PlannedEventRule();
        event.setEventType(EventType.add);
        ScriptDto scriptDto = utils.createScriptDto("true");
        NotificationEventAction action = new NotificationEventAction();
        action.setActionType(ActionType.NotificationEventAction);
        action.setScript(scriptDto.getCode());
        action.setEmails("<EMAIL>");
        action.setMessage(Lists.newArrayList(new LocalizedString("ru","message")));
        action.setSubject(Lists.newArrayList(new LocalizedString("ru", "subj")));
        EventAction eventAction = new EventAction(event, action);
        String code = "" + UniqueNumbersGenerator.nextInt(10000);
        eventAction.setId(code);
        SaveEventAction saveAction = new SaveEventAction(eventAction, true);
        saveAction.setScript(scriptDto);
        SimpleScriptedResult<DtObject> result = dispatch.execute(saveAction);
        EventAction savedEventAction = result.get().getProperty(Constants.EventAction.ORIGINAL_EVENT_ACTION);
        NotificationEventAction serverAction = (NotificationEventAction)eventActionService.getEventAction(
                savedEventAction.getCode()).getAction();
        String scriptCode = serverAction.getScript();
        //Выполнение
        DeleteEventAction deleteAction = new DeleteEventAction(savedEventAction);
        dispatch.execute(deleteAction);

        //Проверка
        assertNotNull("Информация о скрипте не должна быть удалена из кэша информации о скриптах.",
                scriptStorageService.getScript(scriptCode));
    }

    /**
     * Проверка на то, что при удалении скриптового действия по событию информация о соответствующем
     * скрипте удаляется из кэша информации о скриптах.
     * 
     * @throws Exception
     */
    @Test
    @Transactional
    public void testScriptActionScriptInfoShouldBeObliteratedOnDeletion() throws Exception
    {
        //Подготовка
        ScriptDto scriptDto = utils.createScriptDto("true");
        PlannedEventRule event = new PlannedEventRule();
        event.setEventType(EventType.add);
        ScriptEventAction action = new ScriptEventAction();
        action.setActionType(ActionType.ScriptEventAction);
        action.setScript(scriptDto.getCode());
        EventAction eventAction = new EventAction(event, action);
        String code = "" + UniqueNumbersGenerator.nextInt(10000);
        eventAction.setId(code);
        SaveEventAction saveAction = new SaveEventAction(eventAction, true);
        saveAction.setScript(scriptDto);
        SimpleScriptedResult<DtObject> result = dispatch.execute(saveAction);
        EventAction savedEventAction = result.get().getProperty(Constants.EventAction.ORIGINAL_EVENT_ACTION);
        ScriptEventAction serverAction = (ScriptEventAction)eventActionService.getEventAction(
                savedEventAction.getCode()).getAction();
        String scriptCode = serverAction.getScript();
        //Выполнение
        DeleteEventAction deleteAction = new DeleteEventAction(savedEventAction);
        dispatch.execute(deleteAction);
        //Проверка
        assertNotNull("Информация о скрипте не должна быть удалена из кэша информации о скриптах.",
                scriptStorageService.getScript(scriptCode));
    }
}
