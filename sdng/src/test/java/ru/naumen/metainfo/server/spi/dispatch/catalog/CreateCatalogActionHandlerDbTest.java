package ru.naumen.metainfo.server.spi.dispatch.catalog;

import jakarta.inject.Inject;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.transaction.annotation.Transactional;

import net.customware.gwt.dispatch.server.Dispatch;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.server.dispatch.SecurityTestHelper;
import ru.naumen.core.shared.utils.UUIDGenerator;
import ru.naumen.metainfo.server.ClassMetainfoServiceException;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.Constants.Catalog;
import ru.naumen.metainfo.shared.dispatch2.catalog.CreateCatalogAction;
import ru.naumen.uniquerandom.UniqueNumbersGenerator;

/**
 * <AUTHOR>
 * @since 21.02.2011
 *
 */
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class CreateCatalogActionHandlerDbTest
{
    @Inject
    Dispatch dispatch;
    @Inject
    MetainfoService metainfoService;

    @Test
    @Transactional
    public void createWithCodeAndTitle() throws Exception
    {
        //настройка системы
        IProperties properties = new MapProperties();
        String code = "cat" + UniqueNumbersGenerator.nextInt(10000);
        String title = UUIDGenerator.get().nextUUID();
        properties.setProperty(Catalog.CODE, code);
        properties.setProperty(Constants.Catalog.TITLE, title);
        //вызов системы
        dispatch.execute(new CreateCatalogAction(properties));
        //проверка утверждений
        ru.naumen.metainfo.shared.elements.Catalog catalog = metainfoService.getCatalog(code);
        Assert.assertEquals(code, catalog.getCode());
        Assert.assertEquals(title, catalog.getTitle());
        Assert.assertTrue(catalog.isFlat());
        Assert.assertFalse(catalog.isWithFolders());
        Assert.assertFalse("Добавленный каталог не должен быть системным", catalog.isHardcoded());
        Assert.assertNull(catalog.getDescription());
        //очистка
    }

    @Test
    @Transactional
    public void createWithFullProperties() throws Exception
    {
        //настройка системы
        IProperties properties = new MapProperties();
        String code = "cat" + UniqueNumbersGenerator.nextInt(10000);
        String title = UUIDGenerator.get().nextUUID();
        String description = UUIDGenerator.get().nextUUID();
        properties.setProperty(Catalog.CODE, code);
        properties.setProperty(Constants.Catalog.TITLE, title);
        properties.setProperty(Constants.Catalog.DESCRIPTION, description);
        properties.setProperty(Constants.Catalog.FLAT, false);
        properties.setProperty(Constants.Catalog.WITH_FOLDERS, true);
        //вызов системы
        dispatch.execute(new CreateCatalogAction(properties));
        //проверка утверждений
        ru.naumen.metainfo.shared.elements.Catalog catalog = metainfoService.getCatalog(code);
        Assert.assertEquals(code, catalog.getCode());
        Assert.assertEquals(title, catalog.getTitle());
        Assert.assertEquals(description, catalog.getDescription());
        Assert.assertFalse(catalog.isFlat());
        Assert.assertTrue(catalog.isWithFolders());
        Assert.assertFalse("Добавленный каталог не должен быть системным", catalog.isHardcoded());
        //очистка
    }

    @Test(expected = ClassMetainfoServiceException.class)
    @Transactional
    public void emtyCode() throws Exception
    {
        //настройка системы
        IProperties properties = new MapProperties();
        properties.setProperty(Catalog.CODE, " ");
        properties.setProperty(Constants.Catalog.TITLE, UUIDGenerator.get().nextUUID());
        //вызов системы
        dispatch.execute(new CreateCatalogAction(properties));
        //проверка утверждений
        //очистка
    }

    @Test(expected = ClassMetainfoServiceException.class)
    @Transactional
    public void emtyTitle() throws Exception
    {
        //настройка системы
        IProperties properties = new MapProperties();
        properties.setProperty(Catalog.CODE, UUIDGenerator.get().nextUUID());
        properties.setProperty(Constants.Catalog.TITLE, " ");
        //вызов системы
        dispatch.execute(new CreateCatalogAction(properties));
        //проверка утверждений
        //очистка
    }

    @Before
    public void setUp()
    {
        SecurityTestHelper.autenticateAsSuperUser();
    }
}
