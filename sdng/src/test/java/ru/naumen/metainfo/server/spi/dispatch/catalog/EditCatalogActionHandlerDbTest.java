package ru.naumen.metainfo.server.spi.dispatch.catalog;

import jakarta.inject.Inject;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.transaction.annotation.Transactional;

import net.customware.gwt.dispatch.server.Dispatch;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.server.dispatch.ObjectTestUtils;
import ru.naumen.core.server.dispatch.SecurityTestHelper;
import ru.naumen.core.shared.utils.UUIDGenerator;
import ru.naumen.metainfo.server.ClassMetainfoServiceException;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.dispatch2.catalog.EditCatalogAction;
import ru.naumen.metainfo.shared.elements.Catalog;

/**
 * <AUTHOR>
 * @since 21.02.2011
 *
 */
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class EditCatalogActionHandlerDbTest
{
    @Inject
    Dispatch dispatch;
    @Inject
    MetainfoService metainfoService;
    @Inject
    ObjectTestUtils utils;

    @Test
    @Transactional
    public void editExists() throws Exception
    {
        //настройка системы
        Catalog catalog = utils.createCatalog();
        String title = UUIDGenerator.get().nextUUID();
        String description = UUIDGenerator.get().nextUUID();
        IProperties prop = new MapProperties();
        prop.setProperty(Constants.Catalog.TITLE, title);
        prop.setProperty(Constants.Catalog.DESCRIPTION, description);
        //вызов системы
        dispatch.execute(new EditCatalogAction(catalog.getCode(), prop));
        //проверка утверждений
        ru.naumen.metainfo.shared.elements.Catalog editedCatalog = metainfoService.getCatalog(catalog.getCode());
        Assert.assertEquals(title, editedCatalog.getTitle());
        Assert.assertEquals(description, editedCatalog.getDescription());
        Assert.assertTrue(editedCatalog.isFlat());
        Assert.assertFalse(editedCatalog.isWithFolders());
        Assert.assertFalse("Каталог не должен быть системным", editedCatalog.isHardcoded());
        //очистка
    }

    @Test(expected = ClassMetainfoServiceException.class)
    @Transactional
    public void editNotExists() throws Exception
    {
        //настройка системы
        //вызов системы
        dispatch.execute(new EditCatalogAction(UUIDGenerator.get().nextUUID(), IProperties.EMPTY));
        //проверка утверждений
        //очистка
    }

    @Before
    public void setUp()
    {
        SecurityTestHelper.autenticateAsSuperUser();
    }
}
