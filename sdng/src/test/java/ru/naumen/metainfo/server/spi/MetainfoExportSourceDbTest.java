package ru.naumen.metainfo.server.spi;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URISyntaxException;
import java.net.URL;

import org.apache.commons.fileupload2.core.FileItem;
import org.apache.logging.log4j.LogManager;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.transaction.annotation.Transactional;

import jakarta.inject.Inject;
import jakarta.xml.bind.helpers.DefaultValidationEventHandler;
import ru.naumen.commons.server.utils.XmlUtils;
import ru.naumen.core.server.dispatch.SecurityTestHelper;
import ru.naumen.core.server.jta.TransactionRunner;
import ru.naumen.core.server.jta.TransactionRunner.TransactionType;
import ru.naumen.core.server.script.js.CustomJavaScriptService;
import ru.naumen.core.server.upload.NestedFileItem;
import ru.naumen.core.server.util.log.LogUtils;
import ru.naumen.core.shared.Constants.Employee;
import ru.naumen.core.shared.Constants.Root;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.server.spi.importing.ImportMetainfoResult;
import ru.naumen.metainfo.shared.ClassFqn;

/**
 * Тест проверяет работу бина {@link MetainfoExportSource}
 *
 * <AUTHOR>
 * @since 27.08.19
 */
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
@Transactional(transactionManager = "txManager")
@Rollback
public class MetainfoExportSourceDbTest
{
    private static final String METAINFO_CUSTOM_JS_EMPL_ATTR = "metainfo_customJS_emplAttr.xml";
    private static final String METAINFO_EMBEDDED_APP_EMPL_ATTR = "metainfo_embeddedApplication_emplAttr.xml";

    @Inject
    private MetainfoExportSource metainfoExportSource;
    @Inject
    private XmlUtils xmlUtils;
    @Inject
    private MetainfoService metainfoService;
    @Inject
    private CustomJavaScriptService customJSService;

    @Before
    public void setUp()
    {
        SecurityTestHelper.autenticateAsSuperUser();
        LogUtils.setLogLevel(LogManager.getRootLogger().getName(), "INFO");
    }

    /**
     * Тестирование отсутствия блокировки в базе данных при наличии нового атрибута
     * у класса "Сотрудник" и наличия кастомных JS скриптов при импорте метаинформации
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$79140283
     * Таймаут 5 минут
     */
    @Test(timeout = 5 * 60 * 1000)
    public void testImportObjectWithCustomJSAndEmplAttr()
            throws URISyntaxException, IOException
    {
        ClassFqn employee = Employee.FQN;
        ClassFqn root = Root.FQN;
        String newTestAttr = "newTestAttr";
        Assert.assertFalse(
                String.format("Attribute '%s' for '%s' already exists",
                        newTestAttr, employee.toString()),
                hasAttribute(employee, newTestAttr));
        Assert.assertFalse(
                String.format("Attribute '%s' for '%s' already exists",
                        newTestAttr, root.toString()),
                hasAttribute(root, newTestAttr));
        Assert.assertNull("Custom JS script already exists", customJSService.get("CustomJSEmptyTest"));

        URL metainfo = getClass().getResource(METAINFO_CUSTOM_JS_EMPL_ATTR);
        NestedFileItem fileItem = new NestedFileItem(new File(metainfo.toURI()));
        MetainfoContainer cnt = parseFileItem(fileItem);
        ImportMetainfoResult importResult = metainfoExportSource.importObject(cnt);

        Assert.assertFalse("Metainfo imported with problems",
                importResult.completedWithProblems());
        Assert.assertTrue(
                String.format("Attribute '%s' has not been created for '%s'",
                        newTestAttr, employee),
                hasAttribute(employee, newTestAttr));
        Assert.assertTrue(
                String.format("Attribute '%s' has not been created for '%s'",
                        newTestAttr, root),
                hasAttribute(root, newTestAttr));
        Assert.assertNotNull("Custom JS script has not been created",
                TransactionRunner.call(TransactionType.NEW, () -> customJSService.get("CustomJSEmptyTest")));
    }

    /**
     * Тестирование отсутствия блокировки в базе данных при наличии нового атрибута
     * у класса "Сотрудник" и наличия кастомных приложений при импорте метаинформации
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$79140283
     * Таймаут 5 минут
     */
    @Test(timeout = 5 * 60 * 1000)
    public void testImportObjectWithEmbeddedApplicationAndEmplAttr()
            throws URISyntaxException, IOException
    {
        ClassFqn employee = Employee.FQN;
        ClassFqn root = Root.FQN;
        String newTestAttr = "newTestAttr1";
        Assert.assertFalse(
                String.format("Attribute '%s' for '%s' already exists",
                        newTestAttr, employee.toString()),
                hasAttribute(employee, newTestAttr));
        Assert.assertFalse(
                String.format("Attribute '%s' for '%s' already exists",
                        newTestAttr, root.toString()),
                hasAttribute(root, newTestAttr));

        URL metainfo = getClass().getResource(METAINFO_EMBEDDED_APP_EMPL_ATTR);
        NestedFileItem fileItem = new NestedFileItem(new File(metainfo.toURI()));
        MetainfoContainer cnt = parseFileItem(fileItem);
        ImportMetainfoResult importResult = metainfoExportSource.importObject(cnt);

        Assert.assertFalse("Metainfo imported with problems",
                importResult.completedWithProblems());
        Assert.assertTrue(
                String.format("Attribute '%s' has not been created for '%s'",
                        newTestAttr, employee),
                hasAttribute(employee, newTestAttr));
        Assert.assertTrue(
                String.format("Attribute '%s' has not been created for '%s'",
                        newTestAttr, root),
                hasAttribute(root, newTestAttr));
    }

    private boolean hasAttribute(ClassFqn fqn, String attrCode)
    {
        return metainfoService.getMetaClass(fqn).hasAttribute(attrCode);
    }

    private MetainfoContainer parseFileItem(FileItem fileItem) throws IOException
    {
        String pkgName = MetainfoContainer.class.getPackage().getName();
        try (InputStream fileContent = fileItem.getInputStream())
        {
            return xmlUtils.parseXml(
                    fileContent, pkgName, new DefaultValidationEventHandler(), false);
        }
    }
}
