package ru.naumen.metainfo.server.spi.importing;

import static org.mockito.Mockito.verify;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import ru.naumen.core.server.license.LicensingService;
import ru.naumen.fts.server.SearchSettingsInitializer;
import ru.naumen.metainfo.server.spi.ExtMetainfoDataProvider;
import ru.naumen.metainfo.server.spi.MetainfoContainer;
import ru.naumen.metainfo.server.spi.MetainfoExportSource;

@RunWith(MockitoJUnitRunner.class)
public class AdditionalActionsMetainfoImportServiceJdkTest
{
    @Mock
    LicensingService licensingService;
    @Mock
    SearchSettingsInitializer searchSettingsInitializer;
    @Mock
    MetainfoExportSource metainfoExportSource;
    @Mock
    ExtMetainfoDataProvider extMetainfoDataProvider;

    @Test
    public void testServicesReloadOnMetainfoImport()
    {
        final AdditionalActionsMetainfoImportService importService = new AdditionalActionsMetainfoImportService(
                metainfoExportSource, licensingService, extMetainfoDataProvider,
                searchSettingsInitializer);

        importService.importMetainfo(new MetainfoContainer());
        verify(licensingService).reload();
        verify(extMetainfoDataProvider).onMetainfoReload();
        verify(searchSettingsInitializer).initDefaultSettings();
    }
}