package ru.naumen.metainfo.server.spi.dispatch;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

import jakarta.inject.Inject;
import jakarta.transaction.Transactional;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import ru.naumen.core.server.dispatch.Dispatch;
import ru.naumen.core.server.dispatch.ObjectTestUtils;
import ru.naumen.core.server.dispatch.SecurityTestHelper;
import ru.naumen.core.server.script.ScriptHelper;
import ru.naumen.core.server.script.storage.ScriptStorageService;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.script.places.ScriptHolders;
import ru.naumen.core.shared.script.places.TimerCategory;
import ru.naumen.core.shared.timer.definition.ScriptTimerCondition;
import ru.naumen.core.shared.timer.definition.TimerDefinition;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.dispatch2.SaveTimerDefinitionAction;
import ru.naumen.metainfo.shared.dispatch2.script.SimpleScriptedResult;
import ru.naumen.metainfo.shared.script.Script;
import ru.naumen.metainfo.shared.script.ScriptDto;
import ru.naumen.uniquerandom.UniqueNumbersGenerator;

@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class SaveTimerDefinitionActionHandlerDbTest
{
    @Inject
    private Dispatch dispatch;
    @Inject
    private ScriptStorageService scriptStorageService;
    @Inject
    private MetainfoService metainfoService;
    @Inject
    private ObjectTestUtils utils;

    @Before
    public void setUp()
    {
        SecurityTestHelper.autenticateAsSuperUser();
    }

    /**
     * Проверка на то, что информация о скриптах условий счётчика времени добавляется
     * к счётчику времени и в кэш информации о скриптах.
     * @throws Exception 
     */
    @Test
    @Transactional
    public void testConditionScriptInfoShouldBeAddedOnNewSave() throws Exception
    {
        //Подготовка
        ScriptDto script1 = utils.createScriptDto("true");
        ScriptDto script2 = utils.createScriptDto("true");
        ScriptDto script3 = utils.createScriptDto("true");
        ScriptDto script4 = utils.createScriptDto("true");

        TimerDefinition timerDefinition = new TimerDefinition();
        String code = "tdef" + UniqueNumbersGenerator.nextInt(10000);
        timerDefinition.setCode(code);
        timerDefinition.setSystem(false);
        timerDefinition.getTargetTypes().add(Constants.Employee.FQN);
        ScriptTimerCondition timerCondition = new ScriptTimerCondition();

        timerCondition.setStartCondition(script1.getCode());
        timerCondition.setStopCondition(script2.getCode());
        timerCondition.setResumeCondition(script3.getCode());
        timerCondition.setPauseCondition(script4.getCode());

        timerDefinition.setTimerCondition(timerCondition);

        SaveTimerDefinitionAction saveAction = new SaveTimerDefinitionAction(timerDefinition, true);
        saveAction.setWithScripts(true);
        saveAction.setStartConditionScript(script1);
        saveAction.setStopConditionScript(script2);
        saveAction.setPauseConditionScript(script3);
        saveAction.setResumeConditionScript(script4);
        //Выполнение
        SimpleScriptedResult<DtoContainer<TimerDefinition>> result = dispatch.execute(saveAction);
        //Проверка
        TimerDefinition savedDefinition = result.get().get();

        TimerDefinition serverDefinition = metainfoService.getTimerDefinition(savedDefinition.getCode());
        ScriptTimerCondition serverCondition = (ScriptTimerCondition)serverDefinition.getTimerCondition();
        String pauseSciptCode = serverCondition.getPauseCondition();
        String resumeSciptCode = serverCondition.getResumeCondition();
        String startSciptCode = serverCondition.getStartCondition();
        String stopSciptCode = serverCondition.getStopCondition();

        assertTrue("В скрипте условия приостановки на сервере должен быть код скрипта.",
                ScriptHelper.isScriptCodeValid(pauseSciptCode));
        assertTrue("В скрипте условия возобновлеия на сервере должен быть код скрипта.",
                ScriptHelper.isScriptCodeValid(resumeSciptCode));
        assertTrue("В скрипте условия запуска на сервере должен быть код скрипта.",
                ScriptHelper.isScriptCodeValid(startSciptCode));
        assertTrue("В скрипте условия остановки на сервере должен быть код скрипта.",
                ScriptHelper.isScriptCodeValid(stopSciptCode));

        assertCondition(savedDefinition, script1.getBody(), startSciptCode, TimerCategory.TIMER_CONDITION);
        assertCondition(savedDefinition, script2.getBody(), stopSciptCode, TimerCategory.TIMER_CONDITION);
        assertCondition(savedDefinition, script3.getBody(), pauseSciptCode, TimerCategory.TIMER_CONDITION);
        assertCondition(savedDefinition, script4.getBody(), resumeSciptCode, TimerCategory.TIMER_CONDITION);
    }

    /**
     * Проверка на то, что информация о скриптах условий счётчика времени остаётся с прежним номером после редактирования.
     * @throws Exception 
     */
    @Test
    @Transactional
    public void testConditionScriptInfoShouldHaveSameNumberOnEditing() throws Exception
    {
        //Подготовка
        ScriptDto script1 = utils.createScriptDto("true");
        ScriptDto script2 = utils.createScriptDto("true");
        ScriptDto script3 = utils.createScriptDto("true");
        ScriptDto script4 = utils.createScriptDto("true");

        TimerDefinition timerDefinition = new TimerDefinition();
        String code = "tdef" + UniqueNumbersGenerator.nextInt(10000);
        timerDefinition.setCode(code);
        timerDefinition.setSystem(false);
        timerDefinition.getTargetTypes().add(Constants.Employee.FQN);
        ScriptTimerCondition timerCondition = new ScriptTimerCondition();

        timerDefinition.setTimerCondition(timerCondition);

        SaveTimerDefinitionAction saveAction = new SaveTimerDefinitionAction(timerDefinition, true);
        saveAction.setWithScripts(true);
        saveAction.setStartConditionScript(script1);
        saveAction.setStopConditionScript(script2);
        saveAction.setPauseConditionScript(script3);
        saveAction.setResumeConditionScript(script4);
        //Выполнение
        SimpleScriptedResult<DtoContainer<TimerDefinition>> result = dispatch.execute(saveAction);

        TimerDefinition savedDefinition = result.get().get();

        ScriptDto newScript1 = utils.createScriptDto("false");
        ScriptDto newScript2 = utils.createScriptDto("false");
        ScriptDto newScript3 = utils.createScriptDto("false");
        ScriptDto newScript4 = utils.createScriptDto("false");

        saveAction = new SaveTimerDefinitionAction(savedDefinition, false);
        saveAction.setWithScripts(true);
        saveAction.setStartConditionScript(newScript1);
        saveAction.setStopConditionScript(newScript2);
        saveAction.setPauseConditionScript(newScript3);
        saveAction.setResumeConditionScript(newScript4);
        //Выполнение
        SimpleScriptedResult<DtoContainer<TimerDefinition>> editResult = dispatch.execute(saveAction);
        //Проверка
        TimerDefinition editDefinition = editResult.get().get();
        TimerDefinition serverDefinition = metainfoService.getTimerDefinition(editDefinition.getCode());
        ScriptTimerCondition serverCondition = (ScriptTimerCondition)serverDefinition.getTimerCondition();
        String pauseSciptCode = serverCondition.getPauseCondition();
        String resumeSciptCode = serverCondition.getResumeCondition();
        String startSciptCode = serverCondition.getStartCondition();
        String stopSciptCode = serverCondition.getStopCondition();

        assertTrue("В скрипте условия приостановки на сервере должен быть код скрипта.",
                ScriptHelper.isScriptCodeValid(pauseSciptCode));
        assertTrue("В скрипте условия возобновлеия на сервере должен быть код скрипта.",
                ScriptHelper.isScriptCodeValid(resumeSciptCode));
        assertTrue("В скрипте условия запуска на сервере должен быть код скрипта.",
                ScriptHelper.isScriptCodeValid(startSciptCode));
        assertTrue("В скрипте условия остановки на сервере должен быть код скрипта.",
                ScriptHelper.isScriptCodeValid(stopSciptCode));

        assertCondition(savedDefinition, newScript1.getBody(), startSciptCode, TimerCategory.TIMER_CONDITION);
        assertCondition(savedDefinition, newScript2.getBody(), stopSciptCode, TimerCategory.TIMER_CONDITION);
        assertCondition(savedDefinition, newScript3.getBody(), pauseSciptCode, TimerCategory.TIMER_CONDITION);
        assertCondition(savedDefinition, newScript4.getBody(), resumeSciptCode, TimerCategory.TIMER_CONDITION);
    }

    private void assertCondition(TimerDefinition timerDefinition, String scriptBody, String scriptCode,
            TimerCategory category)
    {
        Script script = scriptStorageService.getScript(scriptCode);
        assertNotNull(
                "Информация о скрипте условия запуска счётчика должна содержаться в кэше информации о скриптах. category="
                        + category.name(), script);
        assertEquals("Код скрипта не соответствует ожидаемому", scriptCode, script.getCode());
        assertEquals("Ожидалось иное тело скрипта.", scriptBody, script.getBody());
        assertEquals("Ожидалась иная категория.", category, script.getUsagePoints().get(0).getCategory());
        assertEquals("Ожидался иной тип объекта, содержащего скрипт.", ScriptHolders.TIMER, script.getUsagePoints()
                .get(0).getHolderType());
        assertEquals("Ожидался иной объект, содержащий скрипт.",
                timerDefinition.getTargetMetaClasses().get(0).getFqn(), script.getUsagePoints().get(0)
                        .getRelatedMetaClassFqns().iterator().next());
    }
}
