package ru.naumen.metainfo.server.spi.dispatch;

import java.util.ArrayList;

import jakarta.inject.Inject;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.transaction.annotation.Transactional;

import net.customware.gwt.dispatch.server.Dispatch;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.bo.servicecall.ServiceCall;
import ru.naumen.core.server.dispatch.ObjectTestUtils;
import ru.naumen.core.server.dispatch.SecurityTestHelper;
import ru.naumen.core.shared.Constants.AbstractBO;
import ru.naumen.core.shared.Constants.Employee;
import ru.naumen.core.shared.utils.UUIDGenerator;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.StringAttributeType;
import ru.naumen.metainfo.shared.dispatch2.GetCommonMetaClassAction;
import ru.naumen.metainfo.shared.dispatch2.GetMetaClassResponse;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.MetaClassLite;

/**
 * 
 * <AUTHOR>
 *
 */
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class GetCommonMetaClassActionHandlerDbTest
{
    @Inject
    Dispatch dispatch;
    @Inject
    MetainfoService metainfoService;
    @Inject
    ObjectTestUtils utils;

    @Test(expected = FxException.class)
    @Transactional
    public void notExists() throws Exception
    {
        // настройка системы
        // вызов системы       
        ArrayList<ClassFqn> fqnList = new ArrayList<ClassFqn>();
        fqnList.add(ClassFqn.parse(UUIDGenerator.get().nextUUID()));
        GetCommonMetaClassAction a = new GetCommonMetaClassAction(fqnList);
        dispatch.execute(a);
        // проверка утверждений
        // очистка
    }

    @Test
    @Transactional
    public void oneChildCase() throws Exception
    {
        // настройка системы
        ClassFqn employeeFqn = ClassFqn.parse(Employee.CLASS_ID);
        ClassFqn childFqn1 = utils.createCase(employeeFqn);
        utils.createCase(childFqn1);
        utils.createAttribute(childFqn1, StringAttributeType.CODE, UUIDGenerator.get().nextUUID());
        ClassFqn childFqn11 = utils.createCase(childFqn1);
        utils.createCase(childFqn11);
        // вызов системы
        ArrayList<ClassFqn> fqnList = new ArrayList<ClassFqn>();
        fqnList.add(childFqn1);
        fqnList.add(childFqn11);
        GetCommonMetaClassAction a = new GetCommonMetaClassAction(fqnList);
        GetMetaClassResponse r = dispatch.execute(a);
        // проверка утверждений
        MetaClass metaClass = r.getMetaClass();
        org.junit.Assert.assertEquals(childFqn1, metaClass.getFqn());
        org.junit.Assert.assertEquals(MetaClassLite.Status.DEFAULT, metaClass.getStatus());
        // очистка
    }

    @Before
    public void setUp()
    {
        SecurityTestHelper.autenticateAsSuperUser();
    }

    @Test
    @Transactional
    public void twoChildCases() throws Exception
    {
        // настройка системы
        ClassFqn employeeFqn = ClassFqn.parse(Employee.CLASS_ID);
        ClassFqn childFqn1 = utils.createCase(employeeFqn);
        utils.createCase(childFqn1);
        utils.createAttribute(childFqn1, StringAttributeType.CODE, UUIDGenerator.get().nextUUID());
        ClassFqn childFqn11 = utils.createCase(childFqn1);
        utils.createCase(childFqn11);
        ClassFqn childFqn2 = utils.createCase(employeeFqn);
        utils.createCase(childFqn2);
        utils.createAttribute(childFqn2, StringAttributeType.CODE, UUIDGenerator.get().nextUUID());
        ClassFqn childFqn21 = utils.createCase(childFqn2);
        utils.createCase(childFqn21);
        // вызов системы
        ArrayList<ClassFqn> fqnList = new ArrayList<ClassFqn>();
        fqnList.add(childFqn2);
        fqnList.add(childFqn11);
        fqnList.add(childFqn21);
        GetCommonMetaClassAction a = new GetCommonMetaClassAction(fqnList);
        GetMetaClassResponse r = dispatch.execute(a);
        // проверка утверждений
        MetaClass metaClass = r.getMetaClass();
        org.junit.Assert.assertEquals(employeeFqn, metaClass.getFqn());
        org.junit.Assert.assertEquals(MetaClassLite.Status.DEFAULT, metaClass.getStatus());
        // очистка
    }

    @Test
    @Transactional
    public void twoMetaClasses() throws Exception
    {
        // настройка системы
        ClassFqn employeeFqn = ClassFqn.parse(Employee.CLASS_ID);
        ClassFqn companyFqn = ClassFqn.parse(ServiceCall.CLASS_ID);
        ClassFqn abstractBoFqn = ClassFqn.parse(AbstractBO.CLASS_ID);
        // вызов системы
        ArrayList<ClassFqn> fqnList = new ArrayList<ClassFqn>();
        fqnList.add(employeeFqn);
        fqnList.add(companyFqn);
        GetCommonMetaClassAction a = new GetCommonMetaClassAction(fqnList);
        GetMetaClassResponse r = dispatch.execute(a);
        // проверка утверждений
        MetaClass metaClass = r.getMetaClass();
        org.junit.Assert.assertEquals(abstractBoFqn, metaClass.getFqn());
        // очистка
    }
}
