package ru.naumen.metainfo.server.spi.elements.wf;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Set;

import jakarta.inject.Inject;
import jakarta.transaction.Transactional;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import net.customware.gwt.dispatch.server.Dispatch;
import ru.naumen.common.shared.utils.Color;
import ru.naumen.core.server.bo.servicecall.ServiceCall;
import ru.naumen.core.server.dispatch.ObjectTestUtils;
import ru.naumen.core.server.dispatch.SecurityTestHelper;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.utils.UUIDGenerator;
import ru.naumen.metainfo.server.ClassMetainfoServiceException;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.server.spi.elements.MetaClassImpl;
import ru.naumen.metainfo.server.spi.elements.TransactionalMetaClass;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.dispatch2.wf.AddStateAction;
import ru.naumen.metainfo.shared.dispatch2.wf.EditStateAction;
import ru.naumen.metainfo.shared.dispatch2.wf.GetStateResult;
import ru.naumen.metainfo.shared.elements.wf.Action;
import ru.naumen.metainfo.shared.elements.wf.Action.ActionType;
import ru.naumen.metainfo.shared.elements.wf.Condition;
import ru.naumen.metainfo.shared.elements.wf.Condition.ConditionType;
import ru.naumen.metainfo.shared.elements.wf.State;
import ru.naumen.metainfo.shared.elements.wf.State.ResponsibleType;
import ru.naumen.uniquerandom.RandomUtilsImpl;

/**
 * Тест проверяет работу класса {@link MetaClassImpl} с учётом того, что состояние объектов данного класса хранится в кэше.
 * Таким образом, проверяются, в основном, getter'ы и setter'ы - что атрибуты и дочерние элементы хранятся в соответстующих узлах кэша.
 * <AUTHOR>
 *
 */
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class WorkflowImplDbTest
{
    @Inject
    TransactionalMetaClass transactionalMetaClass;
    @Inject
    TransactionalWorkflow transactionalWorkflow;

    private WorkflowImpl workflow;

    @Inject
    Dispatch dispatch;
    @Inject
    MetainfoService metainfoService;
    @Inject
    ObjectTestUtils testUtils;

    @Transactional
    @Test
    public void addDeclaredPostAction()
    {
        String state = RandomUtilsImpl.nextCode64();
        String code = RandomUtilsImpl.nextCode64();
        //
        transactionalWorkflow.addDeclaredPostAction(workflow, state, code);
        //
        ActionImpl loadedAction = workflow.getActionDeclaration(code, false);
        Assert.assertEquals(code, loadedAction.getCode());
    }

    @Transactional
    @Test
    public void addDeclaredPostCondition()
    {
        String state = RandomUtilsImpl.nextCode64();
        String code = RandomUtilsImpl.nextCode64();
        //
        transactionalWorkflow.addDeclaredPostCondition(workflow, state, code);
        //
        ConditionImpl loadedCondition = workflow.getConditionDeclaration(code, false);
        Assert.assertEquals(code, loadedCondition.getCode());
    }

    @Transactional
    @Test
    public void addDeclaredPreAction()
    {
        String state = RandomUtilsImpl.nextCode64();
        String code = RandomUtilsImpl.nextCode64();
        //
        transactionalWorkflow.addDeclaredPreAction(workflow, state, code);
        //
        ActionImpl loadedAction = workflow.getActionDeclaration(code, true);
        Assert.assertEquals(code, loadedAction.getCode());
    }

    @Transactional
    @Test
    public void addDeclaredPreCondition()
    {
        String state = RandomUtilsImpl.nextCode64();
        String code = RandomUtilsImpl.nextCode64();
        //
        transactionalWorkflow.addDeclaredPreCondition(workflow, state, code);
        //
        ConditionImpl loadedCondition = workflow.getConditionDeclaration(code, true);
        Assert.assertEquals(code, loadedCondition.getCode());
    }

    @Transactional
    @Test
    public void addStateDeclaration()
    {
        String stateCode = RandomUtilsImpl.nextCode64();
        transactionalWorkflow.addStateDeclaration(workflow, stateCode);
        StateDeclarationImpl loadedState = workflow.getStateDeclaration(stateCode);
        Assert.assertEquals(stateCode, loadedState.getCode());
    }

    @Transactional
    @Test
    public void addStateOverride()
    {
        String stateCode = RandomUtilsImpl.nextCode64();
        transactionalWorkflow.addStateOverride(workflow, stateCode);
        StateDeclarationImpl loadedState = workflow.getStateOverride(stateCode);
        Assert.assertEquals(stateCode, loadedState.getCode());
    }

    @Transactional
    @Test
    public void addStateSetting()
    {
        String stateCode = RandomUtilsImpl.nextCode64();
        String code = RandomUtilsImpl.nextCode64();
        transactionalWorkflow.addStateSetting(workflow, stateCode, code);
        StateSettingImpl loadedState = workflow.getStateSetting(stateCode, code);
        Assert.assertEquals(code, loadedState.getCode());
    }

    @Transactional
    @Test
    public void addTransition()
    {
        String stateCodeFrom = RandomUtilsImpl.nextCode64();
        String stateCodeTo = RandomUtilsImpl.nextCode64();
        transactionalWorkflow.addStateDeclaration(workflow, stateCodeFrom);
        transactionalWorkflow.addStateDeclaration(workflow, stateCodeTo);
        //
        TransitionImpl transition = transactionalWorkflow.addTransition(workflow, stateCodeFrom, stateCodeTo);
        //
        TransitionImpl loadedTransition = workflow.getDeclaredTransition(stateCodeFrom, stateCodeTo);
        Assert.assertEquals(stateCodeFrom, transition.getBeginState());
        Assert.assertEquals(stateCodeTo, transition.getEndState());
        Assert.assertEquals(stateCodeFrom, loadedTransition.getBeginState());
        Assert.assertEquals(stateCodeTo, loadedTransition.getEndState());
    }

    @After
    public void after()
    {
        if (workflow != null)
        {
            transactionalMetaClass.deleteMetaClass(workflow.getMetaClass());
        }
        workflow = null;
    }

    @Before
    public void before()
    {
        ClassFqn fqn = ClassFqn.parse(ServiceCall.CLASS_ID, RandomUtilsImpl.nextCode64());
        MetaClassImpl metaClass = transactionalMetaClass.addMetaClass(fqn, ClassFqn.parse(ServiceCall.CLASS_ID));
        workflow = metaClass.getWorkflow();
    }

    @Transactional
    @Test
    public void collectStateCodes()
    {
        String stateCode1 = RandomUtilsImpl.nextCode64();
        transactionalWorkflow.addStateDeclaration(workflow, stateCode1);
        String stateCode2 = RandomUtilsImpl.nextCode64();
        transactionalWorkflow.addStateDeclaration(workflow, stateCode2);
        //
        Set<String> codes = workflow.collectStateCodes();
        //
        Assert.assertTrue(codes.containsAll(Arrays.asList(stateCode1, stateCode2)));
    }

    @Test
    public void editStateColor() throws Exception
    {
        after(); //этот тест на полноценные метаклассы, частичные не нужны

        SecurityTestHelper.autenticateAsSuperUser();
        // настройка системы
        ClassFqn fqn = Constants.ServiceCall.FQN;
        String code = RandomUtilsImpl.nextCode64();
        String title = UUIDGenerator.get().nextUUID();

        ClassFqn scFqn = testUtils.getDefaultServiceCallCase();
        ServiceCall sc = testUtils.createServiceCall(scFqn);
        //
        AddStateAction a = new AddStateAction(fqn, code, title, UUIDGenerator.get().nextUUID(), new Color("FF00FF"),
                null, ResponsibleType.EMPLOYEE_AND_TEAM, new ArrayList<>());
        GetStateResult result = dispatch.execute(a);
        State parentState = result.get();
        EditStateAction e = new EditStateAction(scFqn, code, title, UUIDGenerator.get().nextUUID(), new Color("000000"),
                null, ResponsibleType.EMPLOYEE_AND_TEAM, new ArrayList<>());
        result = dispatch.execute(e);
        State childState = result.get();
        //
        Assert.assertEquals(parentState.getTitle(), childState.getTitle());
        Assert.assertEquals(parentState.getCode(), childState.getCode());
        Assert.assertNotEquals(parentState.getColor(), childState.getColor());
        //
        childState = dispatch.execute(new EditStateAction(scFqn, code, title, UUIDGenerator.get().nextUUID(), null,
                null, ResponsibleType.EMPLOYEE_AND_TEAM, new ArrayList<>())).get();
        //
        Assert.assertNull(childState.getColor());
    }

    @Transactional
    @Test
    public void getAction()
    {
        String state = RandomUtilsImpl.nextCode64();
        transactionalWorkflow.addStateDeclaration(workflow, state);
        String state2 = RandomUtilsImpl.nextCode64();
        transactionalWorkflow.addStateDeclaration(workflow, state2);
        String code = RandomUtilsImpl.nextCode64();
        ActionImpl action = transactionalWorkflow.addDeclaredPostAction(workflow, state, code);
        action.setType(ActionType.SCRIPT);
        transactionalWorkflow.addDeclaredPostAction(workflow, state, RandomUtilsImpl.nextCode64());
        transactionalWorkflow.addDeclaredPreAction(workflow, state2, RandomUtilsImpl.nextCode64());
        transactionalWorkflow.addDeclaredPreAction(workflow, state, RandomUtilsImpl.nextCode64());
        //
        Action loadedAction = workflow.getAction(state, code);
        //
        Assert.assertEquals(code, loadedAction.getCode());
        Assert.assertEquals(ActionType.SCRIPT, loadedAction.getType()); // загрузили именно тот объект
    }

    @Transactional
    @Test
    public void getCondition()
    {
        String state = RandomUtilsImpl.nextCode64();
        transactionalWorkflow.addStateDeclaration(workflow, state);
        String state2 = RandomUtilsImpl.nextCode64();
        transactionalWorkflow.addStateDeclaration(workflow, state2);
        String code = RandomUtilsImpl.nextCode64();
        ConditionImpl condition = workflow.addDeclaredPostCondition(state, code);
        condition.setType(ConditionType.SCRIPT);
        transactionalWorkflow.addDeclaredPostCondition(workflow, state, RandomUtilsImpl.nextCode64());
        transactionalWorkflow.addDeclaredPreCondition(workflow, state2, RandomUtilsImpl.nextCode64());
        transactionalWorkflow.addDeclaredPreCondition(workflow, state, RandomUtilsImpl.nextCode64());
        //
        Condition loadedCondition = workflow.getCondition(state, code);
        //
        Assert.assertEquals(code, loadedCondition.getCode());
        Assert.assertEquals(ConditionType.SCRIPT, loadedCondition.getType()); // загрузили именно тот объект
    }

    @Transactional
    @Test
    public void getDeclaredPostActions()
    {
        String state = RandomUtilsImpl.nextCode64();
        String code = RandomUtilsImpl.nextCode64();
        //
        transactionalWorkflow.addDeclaredPostAction(workflow, state, code);
        //
        Collection<Action> actions = workflow.getDeclaredActions(state, false);
        Assert.assertFalse(actions.isEmpty());
        Action loadedAction = actions.iterator().next();
        Assert.assertEquals(code, loadedAction.getCode());
    }

    @Transactional
    @Test
    public void getDeclaredPostConditions()
    {
        String state = RandomUtilsImpl.nextCode64();
        String code = RandomUtilsImpl.nextCode64();
        //
        transactionalWorkflow.addDeclaredPostCondition(workflow, state, code);
        //
        Collection<Condition> conditions = workflow.getDeclaredConditions(state, false);
        Assert.assertFalse(conditions.isEmpty());
        Condition loadedCondition = conditions.iterator().next();
        Assert.assertEquals(code, loadedCondition.getCode());
    }

    @Transactional
    @Test
    public void getDeclaredPreActions()
    {
        String state = RandomUtilsImpl.nextCode64();
        String code = RandomUtilsImpl.nextCode64();
        //
        transactionalWorkflow.addDeclaredPreAction(workflow, state, code);
        //
        Collection<Action> actions = workflow.getDeclaredActions(state, true);
        Assert.assertFalse(actions.isEmpty());
        Action loadedAction = actions.iterator().next();
        Assert.assertEquals(code, loadedAction.getCode());
    }

    @Transactional
    @Test
    public void getDeclaredPreConditions()
    {
        String state = RandomUtilsImpl.nextCode64();
        String code = RandomUtilsImpl.nextCode64();
        //
        transactionalWorkflow.addDeclaredPreCondition(workflow, state, code);
        //
        Collection<Condition> conditions = workflow.getDeclaredConditions(state, true);
        Assert.assertFalse(conditions.isEmpty());
        Condition loadedCondition = conditions.iterator().next();
        Assert.assertEquals(code, loadedCondition.getCode());
    }

    @Test
    public void getTransitionFromParent()
    {
        ClassFqn parentFqn = ClassFqn.parse(ServiceCall.CLASS_ID, RandomUtilsImpl.nextCode64());
        MetaClassImpl parentMetaClass = transactionalMetaClass.addMetaClass(parentFqn,
                ClassFqn.parse(ServiceCall.CLASS_ID));
        WorkflowImpl parentWF = parentMetaClass.getWorkflow();

        String stateCodeFrom = RandomUtilsImpl.nextCode64();
        String stateCodeTo = RandomUtilsImpl.nextCode64();
        parentWF.addStateDeclaration(stateCodeFrom);
        parentWF.addStateDeclaration(stateCodeTo);
        parentWF.addTransition(stateCodeFrom, stateCodeTo);

        ClassFqn fqn = ClassFqn.parse(ServiceCall.CLASS_ID, RandomUtilsImpl.nextCode64());
        MetaClassImpl metaclass = transactionalMetaClass.addMetaClass(fqn, parentFqn);
        WorkflowImpl wf = metaclass.getWorkflow();

        String stateCodeFrom2 = RandomUtilsImpl.nextCode64();
        String stateCodeTo2 = RandomUtilsImpl.nextCode64();
        transactionalWorkflow.addStateDeclaration(wf, stateCodeFrom2);
        transactionalWorkflow.addStateDeclaration(wf, stateCodeTo2);
        transactionalWorkflow.addTransition(wf, stateCodeFrom2, stateCodeTo2);
        transactionalWorkflow.addTransition(wf, stateCodeFrom, stateCodeTo2);
        transactionalWorkflow.addTransition(wf, stateCodeTo, stateCodeTo2);
        //
        TransitionImpl transition = wf.getTransition(stateCodeFrom, stateCodeTo);
        //
        Assert.assertEquals(stateCodeFrom, transition.getBeginState());
        Assert.assertEquals(stateCodeTo, transition.getEndState());

        transactionalMetaClass.deleteMetaClass(metaclass);
        transactionalMetaClass.deleteMetaClass(parentMetaClass);
    }

    @Transactional
    @Test
    public void setEndStateCode()
    {
        String stateCode = RandomUtilsImpl.nextCode64();
        transactionalWorkflow.addStateDeclaration(workflow, stateCode);
        //
        transactionalWorkflow.setEndStateCode(workflow, stateCode);
        //
        Assert.assertEquals(stateCode, workflow.getEndStateCode());
    }

    @Test(expected = ClassMetainfoServiceException.class)
    public void setEndStateCodeWhenStateDoesntExist()
    {
        String stateCode = RandomUtilsImpl.nextCode64();
        transactionalWorkflow.addStateDeclaration(workflow, stateCode);
        //
        transactionalWorkflow.setEndStateCode(workflow, RandomUtilsImpl.nextCode64());
    }

    @Transactional
    @Test
    public void setOriginalStateCode()
    {
        String stateCode = RandomUtilsImpl.nextCode64();
        transactionalWorkflow.addStateDeclaration(workflow, stateCode);
        //
        transactionalWorkflow.setOriginalStateCode(workflow, stateCode);
        //
        Assert.assertEquals(stateCode, workflow.getOriginalStateCode());
    }

    @Test(expected = ClassMetainfoServiceException.class)
    public void setOriginalStateCodeWhenStateDoesntExist()
    {
        String stateCode = RandomUtilsImpl.nextCode64();
        transactionalWorkflow.addStateDeclaration(workflow, stateCode);
        //
        transactionalWorkflow.setOriginalStateCode(workflow, RandomUtilsImpl.nextCode64());
    }

    @Transactional
    @Test
    public void stateDeclarationAndOverrideAreDifferent()
    {
        String stateCode = RandomUtilsImpl.nextCode64();
        //
        StateDeclarationImpl overrideState = transactionalWorkflow.addStateOverride(workflow, stateCode);
        StateDeclarationImpl declState = transactionalWorkflow.addStateDeclaration(workflow, stateCode);
        overrideState.setHardcoded(true);
        declState.setHardcoded(false);
        //
        StateDeclarationImpl loadedDeclState = workflow.getStateDeclaration(stateCode);
        StateDeclarationImpl loadedOverrideState = workflow.getStateOverride(stateCode);
        Assert.assertTrue(loadedOverrideState.isHardcoded());
        Assert.assertFalse(loadedDeclState.isHardcoded());
    }
}