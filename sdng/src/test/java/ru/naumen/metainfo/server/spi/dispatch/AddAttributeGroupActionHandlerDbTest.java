package ru.naumen.metainfo.server.spi.dispatch;

import java.util.ArrayList;
import java.util.Collection;

import jakarta.inject.Inject;
import jakarta.transaction.Transactional;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import net.customware.gwt.dispatch.server.Dispatch;
import ru.naumen.core.server.dispatch.SecurityTestHelper;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.utils.UUIDGenerator;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.dispatch2.AddAttributeGroupAction;
import ru.naumen.metainfo.shared.elements.AttributeGroup;
import ru.naumen.metainfo.shared.elements.MetaClass;

/**
 * <AUTHOR>
 * @since 17.02.2011
 *
 */
@Transactional
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class AddAttributeGroupActionHandlerDbTest
{
    @Inject
    Dispatch dispatch;
    @Inject
    MetainfoService metainfoService;

    @Test
    public void execute() throws Exception
    {
        //настройка системы
        ClassFqn fqn = ClassFqn.parse(Constants.Employee.CLASS_ID);
        MetaClass metaClass = metainfoService.getMetaClass(fqn);
        int groupsCount = metaClass.getAttributeGroups().size();
        String uuid = UUIDGenerator.get().nextUUID();
        //вызов системы
        dispatch.execute(new AddAttributeGroupAction(fqn, uuid, uuid, null, new ArrayList<String>()));
        //проверка утверждений
        metaClass = metainfoService.getMetaClass(fqn);
        Collection<AttributeGroup> groups = metaClass.getAttributeGroups();
        Assert.assertEquals("Группа не добавлена", groupsCount + 1, groups.size());
        boolean foundGroup = false;
        for (AttributeGroup grp : groups)
        {
            if (uuid.equals(grp.getTitle()))
            {
                foundGroup = true;
                break;
            }
        }
        Assert.assertTrue("Группа с указанным названием не найдена", foundGroup);
        //очистка
    }

    @Before
    public void setUp()
    {
        SecurityTestHelper.autenticateAsSuperUser();
    }
}
