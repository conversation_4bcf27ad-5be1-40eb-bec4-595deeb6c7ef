package ru.naumen.metainfo.server.spi.dispatch.eventaction;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;
import static ru.naumen.core.shared.Constants.ScriptsComponentTree.EXISTING_SCRIPT;

import jakarta.inject.Inject;
import jakarta.transaction.Transactional;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import net.customware.gwt.dispatch.server.Dispatch;
import ru.naumen.core.server.dispatch.ObjectTestUtils;
import ru.naumen.core.server.dispatch.SecurityTestHelper;
import ru.naumen.core.server.eventaction.EventActionService;
import ru.naumen.core.server.script.ScriptHelper;
import ru.naumen.core.server.script.storage.ScriptStorageService;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.dispatch2.eventaction.GetActionConditionResponse;
import ru.naumen.metainfo.shared.dispatch2.eventaction.SaveActionConditionAction;
import ru.naumen.metainfo.shared.dispatch2.eventaction.SaveEventAction;
import ru.naumen.metainfo.shared.dispatch2.script.SimpleScriptedResult;
import ru.naumen.metainfo.shared.elements.HasMutableScript;
import ru.naumen.metainfo.shared.eventaction.ActionCondition;
import ru.naumen.metainfo.shared.eventaction.ActionType;
import ru.naumen.metainfo.shared.eventaction.Constants;
import ru.naumen.metainfo.shared.eventaction.EventAction;
import ru.naumen.metainfo.shared.eventaction.EventType;
import ru.naumen.metainfo.shared.eventaction.PlannedEventRule;
import ru.naumen.metainfo.shared.eventaction.ScriptActionCondition;
import ru.naumen.metainfo.shared.eventaction.ScriptEventAction;
import ru.naumen.metainfo.shared.script.Script;
import ru.naumen.metainfo.shared.script.ScriptDto;
import ru.naumen.uniquerandom.UniqueNumbersGenerator;

@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class SaveActionConditionActionHandlerDbTest
{
    @Inject
    private Dispatch dispatch;
    @Inject
    private EventActionService eventActionService;
    @Inject
    private ScriptStorageService scriptStorageService;
    @Inject
    private ObjectTestUtils utils;

    @Before
    public void setUp()
    {
        SecurityTestHelper.autenticateAsSuperUser();
    }

    /**
     * Проверка того, что при сохранении нового условия выполнения действия по событию
     * информация о скрипте условия добавляется к условию и в кэш информации о скриптах.
     * 
     * @throws Exception
     */
    @Test
    @Transactional
    public void testScriptInfoShouldBeAddedOnNewConditionSave() throws Exception
    {
        //Подготовка
        ScriptDto scriptDto = utils.createScriptDto("true");
        EventAction eventAction = generateEventAction(scriptDto.getCode());
        ((HasMutableScript)eventAction.getAction()).setScript(scriptDto.getCode());

        SaveEventAction saveAction = new SaveEventAction(eventAction, true);
        saveAction.setScript(scriptDto);

        SimpleScriptedResult<DtObject> result = dispatch.execute(saveAction);
        EventAction savedEventAction = result.get().getProperty(Constants.EventAction.ORIGINAL_EVENT_ACTION);

        scriptDto.setCode(savedEventAction.getAction().getScript());
        scriptDto.setSelectStrategy(EXISTING_SCRIPT);

        ScriptActionCondition condition = generateCondition(scriptDto.getCode());
        //Выполнение
        SaveActionConditionAction saveConditionAction = new SaveActionConditionAction(savedEventAction, condition);
        saveConditionAction.setScript(scriptDto);

        GetActionConditionResponse conditionResult = dispatch.execute(saveConditionAction);
        ActionCondition savedCondition = conditionResult.getActionCondition().get().get();
        //Проверка
        ScriptActionCondition scriptCondition = (ScriptActionCondition)savedCondition;
        if (scriptCondition != null)
        {
            EventAction serverEventAction = eventActionService.getEventAction(conditionResult.getEventAction()
                    .getCode());
            ScriptActionCondition serverCondition = (ScriptActionCondition)serverEventAction.getConditions().get(0);
            assertTrue("В условии действия по событию на сервере должен быть код скрипта.",
                    ScriptHelper.isScriptCodeValid(serverCondition.getScript()));
            Script script = scriptStorageService.getScript(serverCondition.getScript());
            assertNotNull("Информация о скрипте не найдена в кэше информации о скриптах.", script);
            assertEquals("Ожидалось иное тело скрипта.", scriptDto.getBody(), script.getBody());
            assertEquals("Ожидался иной код скрипта.", serverCondition.getScript(), script.getCode());
        }
        else
        {
            fail("В результате сохранения условия выполнения действия по событию не было возвращено сохранённое действие по событию.");
        }
    }

    /**
     * Проверка того, что при сохранении нового условия выполнения действия по событию
     * информация о скрипте условия добавляется к условию и в кэш информации о скриптах.
     * 
     * @throws Exception
     */
    @Test
    @Transactional
    public void testScriptInfoShouldHaveSameNumberAfterEdit() throws Exception
    {
        //Подготовка
        ScriptDto scriptDto = utils.createScriptDto("true");

        EventAction eventAction = generateEventAction(scriptDto.getCode());

        SaveEventAction saveAction = new SaveEventAction(eventAction, true);
        saveAction.setScript(scriptDto);

        SimpleScriptedResult<DtObject> result = dispatch.execute(saveAction);
        EventAction savedEventAction = result.get().getProperty(Constants.EventAction.ORIGINAL_EVENT_ACTION);
        scriptDto.setCode(savedEventAction.getAction().getScript());
        scriptDto.setSelectStrategy(EXISTING_SCRIPT);

        ScriptActionCondition condition = generateCondition(scriptDto.getCode());
        //Выполнение
        SaveActionConditionAction saveConditionAction = new SaveActionConditionAction(savedEventAction, condition);
        saveConditionAction.setScript(scriptDto);

        GetActionConditionResponse conditionResult = dispatch.execute(saveConditionAction);
        ActionCondition savedCondition = conditionResult.getActionCondition().get().get();
        ScriptActionCondition scriptCondition = (ScriptActionCondition)savedCondition;
        if (scriptCondition != null)
        {
            EventAction serverEventActionOld = eventActionService.getEventAction(conditionResult.getEventAction()
                    .getCode());
            ScriptActionCondition serverConditionOld = (ScriptActionCondition)serverEventActionOld.getConditions().get(
                    0);
            String oldScriptCode = serverConditionOld.getScript();
            scriptDto.setBody("false");
            scriptCondition.setScript(scriptDto.getCode());
            scriptCondition.setSyncVerification(true);

            SaveActionConditionAction saveModifiedConditionAction = new SaveActionConditionAction(savedEventAction,
                    scriptCondition);
            saveModifiedConditionAction.setScript(scriptDto);

            GetActionConditionResponse modifiedConditionResult = dispatch.execute(saveModifiedConditionAction);
            ScriptActionCondition modifiedCondition = (ScriptActionCondition)modifiedConditionResult
                    .getActionCondition().get().get();

            if (modifiedCondition != null)
            {
                //Проверка
                EventAction serverEventAction = eventActionService.getEventAction(conditionResult.getEventAction()
                        .getCode());
                ScriptActionCondition serverCondition = (ScriptActionCondition)serverEventAction.getConditions().get(0);
                assertTrue("В условии действия по событию на сервере должен быть код скрипта.",
                        ScriptHelper.isScriptCodeValid(serverCondition.getScript()));
                assertEquals("Коды оригинального и изменённого скрипта не совпадают.", oldScriptCode,
                        serverCondition.getScript());
                Script script = scriptStorageService.getScript(serverCondition.getScript());
                assertNotNull("Информация о скрипте не найдена в кэше информации о скриптах.", script);
                assertEquals("Ожидался иной код скрипта.", serverCondition.getScript(), script.getCode());
                assertTrue("Условие не стало синхронным", modifiedCondition.isSyncVerification());
            }
            else
            {
                fail("В результате сохранения изменённого условия выполнения действия по событию "
                        + "не было возвращено сохранённое действие по событию.");
            }
        }
        else
        {
            fail("В результате сохранения условия выполнения действия по событию "
                    + "не было возвращено сохранённое действие по событию.");
        }
    }

    private ScriptActionCondition generateCondition(String script)
    {
        ScriptActionCondition condition = new ScriptActionCondition();
        condition.setScript(script);
        return condition;
    }

    private EventAction generateEventAction(String scriptCode)
    {
        PlannedEventRule event = new PlannedEventRule();
        event.setEventType(EventType.add);
        ScriptEventAction action = new ScriptEventAction();
        action.setActionType(ActionType.ScriptEventAction);
        action.setScript(scriptCode);
        EventAction eventAction = new EventAction(event, action);
        String code = "" + UniqueNumbersGenerator.nextInt(10000);
        eventAction.setId(code);
        return eventAction;
    }

}
