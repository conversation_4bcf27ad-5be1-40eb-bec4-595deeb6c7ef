package ru.naumen.metainfo.server.spi.dispatch;

import java.util.Collections;

import jakarta.inject.Inject;
import jakarta.transaction.Transactional;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import net.customware.gwt.dispatch.server.Dispatch;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.core.server.dispatch.SecurityTestHelper;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.utils.UUIDGenerator;
import ru.naumen.metainfo.server.ClassMetainfoServiceException;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.dispatch2.AddMetaClassAction;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.uniquerandom.UniqueNumbersGenerator;

/**
 * 
 * <AUTHOR>
 *
 */
@Transactional
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class AddMetaClassActionHandlerDbTest
{
    @Inject
    Dispatch dispatch;
    @Inject
    MetainfoService metainfoService;

    @Test(expected = ClassMetainfoServiceException.class)
    public void addBadCase() throws Exception
    {
        // настройка системы
        // вызов системы
        ClassFqn fqn = ClassFqn.parse("newClass", "case");
        ClassFqn parent = ClassFqn.parse("userEntity");
        String title = UUIDGenerator.get().nextUUID();
        String description = UUIDGenerator.get().nextUUID();

        dispatch.execute(new AddMetaClassAction(fqn, parent, null, title, description, Collections
                .<String, String> emptyMap(), IProperties.EMPTY));
        // проверка утверждений
        // очистка
    }

    @Test
    public void addNotExists() throws Exception
    {
        // настройка системы
        // вызов системы
        ClassFqn fqn = ClassFqn.parse("nc" + UniqueNumbersGenerator.nextInt());
        ClassFqn parent = ClassFqn.parse("userEntity");
        String title = UUIDGenerator.get().nextUUID();
        String description = UUIDGenerator.get().nextUUID();

        dispatch.execute(new AddMetaClassAction(fqn, parent, null, title, description, Collections
                .<String, String> emptyMap(), IProperties.EMPTY));
        // проверка утверждений
        MetaClass metaClass = metainfoService.getMetaClass(fqn);

        Assert.assertEquals(fqn, metaClass.getFqn());
        Assert.assertEquals(parent, metaClass.getParent());
        Assert.assertEquals(title, metaClass.getTitle());
        Assert.assertEquals(description, metaClass.getDescription());
        Assert.assertFalse(metaClass.isHardcoded());
        Assert.assertFalse(metaClass.isSingleton());
        try
        {
            metaClass.getAttribute(Constants.PARENT_ATTR);
        }
        catch (ClassMetainfoServiceException e)
        {
            // атрибут с кодом "parent" не должен создасться, поэтому появление исключения ожидаемо
            return;
        }
        throw new IllegalStateException("Attribute parent was added.");
        // очистка
    }

    @Test
    public void addNotExistsWithParentAttr() throws Exception
    {
        // настройка системы
        // вызов системы
        ClassFqn fqn = ClassFqn.parse("nc" + UniqueNumbersGenerator.nextInt());
        ClassFqn parent = ClassFqn.parse("userEntity");
        String title = UUIDGenerator.get().nextUUID();
        String description = UUIDGenerator.get().nextUUID();

        ClassFqn parentRel = ClassFqn.parse(Constants.OU.CLASS_ID);
        dispatch.execute(new AddMetaClassAction(fqn, parent, parentRel, title, description, Collections
                .<String, String> emptyMap(), IProperties.EMPTY));
        // проверка утверждений
        MetaClass metaClass = metainfoService.getMetaClass(fqn);

        Assert.assertEquals(fqn, metaClass.getFqn());
        Assert.assertEquals(parent, metaClass.getParent());
        Assert.assertEquals(title, metaClass.getTitle());
        Assert.assertEquals(description, metaClass.getDescription());
        Assert.assertFalse(metaClass.isHardcoded());
        Assert.assertFalse(metaClass.isSingleton());
        Attribute parentAttr = metaClass.getAttribute(Constants.PARENT_ATTR);
        Assert.assertFalse(parentAttr.isEditable());
        Assert.assertEquals(fqn, parentAttr.getDeclaredMetaClass());
        // очистка
    }

    @Before
    public void setUp()
    {
        SecurityTestHelper.autenticateAsSuperUser();
    }

}
