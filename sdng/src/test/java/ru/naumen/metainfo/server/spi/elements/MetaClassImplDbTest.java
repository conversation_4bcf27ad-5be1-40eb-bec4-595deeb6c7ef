package ru.naumen.metainfo.server.spi.elements;

import java.util.Arrays;
import java.util.Collection;
import java.util.HashSet;

import jakarta.inject.Inject;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.transaction.annotation.Transactional;

import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.server.dispatch.ObjectTestUtils;
import ru.naumen.core.server.dispatch.SecurityTestHelper;
import ru.naumen.core.server.jta.TransactionRunner;
import ru.naumen.core.server.jta.TransactionRunner.TransactionType;
import ru.naumen.core.shared.Constants.ServiceCall;
import ru.naumen.core.shared.HasCode;
import ru.naumen.core.shared.utils.UUIDGenerator;
import ru.naumen.metainfo.server.spi.MetainfoServiceBean;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.uniquerandom.UniqueNumbersGenerator;

/**
 * Тест проверяет работу класса {@link MetaClassImpl} с учётом того, что состояние объектов данного класса хранится в кэше.
 * Таким образом, проверяются, в основном, getter'ы и setter'ы - что атрибуты и дочерние элементы хранятся в соответстующих узлах кэша.
 * 
 * <AUTHOR>
 *
 */
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class MetaClassImplDbTest
{

    @Inject
    TransactionalMetaClass transactionalMetaClass;
    @Inject
    ObjectTestUtils utils;
    @Inject
    MetainfoServiceBean metainfoService;
    private MetaClassImpl metaclass;

    @Test
    @Transactional
    public void addAndLoadAttributeGroupDeclaration()
    {
        String code = UUIDGenerator.get().nextUUID();
        //
        AttributeGroupDeclarationImpl group = transactionalMetaClass.addAttributeGroupDeclaration(metaclass, code);
        AttributeGroupDeclarationImpl loadedGroup = metaclass.getAttributeGroupDeclaration(code);
        //
        Assert.assertEquals(code, group.getCode());
        Assert.assertEquals(code, loadedGroup.getCode());
    }

    @Test
    @Transactional
    public void addAndLoadAttributeGroupOverride()
    {
        String code = UUIDGenerator.get().nextUUID();
        //
        AttributeGroupDeclarationImpl group = transactionalMetaClass.addAttributeGroupOverride(metaclass, code);
        AttributeGroupDeclarationImpl loadedGroup = metaclass.getAttributeGroupOverride(code);
        //
        Assert.assertEquals(code, group.getCode());
        Assert.assertEquals(code, loadedGroup.getCode());
    }

    @Test
    @Transactional
    public void addAndLoadAttributeGroupSystemOverride()
    {
        String code = UUIDGenerator.get().nextUUID();
        //
        AttributeGroupDeclarationImpl group = transactionalMetaClass.addAttributeGroupSystemOverride(metaclass, code);
        AttributeGroupDeclarationImpl loadedGroup = metaclass.getAttributeGroupSystemOverride(code);
        //
        Assert.assertEquals(code, group.getCode());
        Assert.assertEquals(code, loadedGroup.getCode());
    }

    @Test
    @Transactional
    public void addAndLoadAttributeOverride()
    {
        String code = UUIDGenerator.get().nextUUID();
        //
        AttributeOverrideImpl attr = transactionalMetaClass.addAttributeOverride(metaclass, code);
        AttributeOverrideImpl loadedAttr = metaclass.getAttributeOverride(code);
        //
        Assert.assertEquals(code, attr.getCode());
        Assert.assertEquals(code, loadedAttr.getCode());
    }

    @Test
    @Transactional
    public void addAndLoadAttributeSystemOverride()
    {
        String code = UUIDGenerator.get().nextUUID();
        //
        AttributeOverrideImpl attr = transactionalMetaClass.addAttributeSystemOverride(metaclass, code);
        AttributeOverrideImpl loadedAttr = metaclass.getAttributeSystemOverride(code);
        //
        Assert.assertEquals(code, attr.getCode());
        Assert.assertEquals(code, loadedAttr.getCode());
    }

    @Test
    @Transactional
    public void addAndLoadDeclaredAttribute()
    {
        String code = UUIDGenerator.get().nextUUID();
        //
        DeclaredAttributeImpl attribute = transactionalMetaClass.addDeclaredAttribute(metaclass, code);
        DeclaredAttributeImpl loadedAttribute = metaclass.getDeclaredAttribute(code);
        //
        Assert.assertEquals(code, attribute.getCode());
        Assert.assertEquals(code, loadedAttribute.getCode());
    }

    @Test
    @Transactional
    public void addDescriptions()
    {
        String lang = LocaleContextHolder.getLocale().getLanguage();
        String descr = UUIDGenerator.get().nextUUID();
        String descrOverride = UUIDGenerator.get().nextUUID();
        //
        transactionalMetaClass.addDescription(metaclass, lang, descr);
        transactionalMetaClass.addDescriptionOverride(metaclass, lang, descrOverride);
        //
        Assert.assertEquals(descr, metaclass.getDescriptionAsProperties().getProperty(lang));
        Assert.assertEquals(descrOverride, metaclass.getDescriptionOverrideAsProperties().getProperty(lang));
    }

    @Test
    @Transactional
    public void addTitles()
    {
        String lang = LocaleContextHolder.getLocale().getLanguage();
        String title = UUIDGenerator.get().nextUUID();
        String titleOverride = UUIDGenerator.get().nextUUID();
        //
        transactionalMetaClass.addTitle(metaclass, lang, title);
        transactionalMetaClass.addTitleOverride(metaclass, lang, titleOverride);
        //
        Assert.assertEquals(title, metaclass.getTitleAsProperties().getProperty(lang));
        Assert.assertEquals(titleOverride, metaclass.getTitleOverrideAsProperties().getProperty(lang));
    }

    @After
    public void after() throws Exception
    {
        utils.deleteMetaClass(metaclass.getFqn());
        metaclass = null;
    }

    /**
     * Проверяем, что группы declaration-override-system - на самом деле разные группы. Добавляем
     * группы с одинаковыми кодами, назначаем им разные названия (title).
     * При проверке загружаем их из кэша и сравниваем названия. Так как группы разные, то название нигде не перекрылось.
     */
    @Test
    @Transactional
    public void allGroupsAreDifferent()
    {
        String code = UUIDGenerator.get().nextUUID();
        AttributeGroupDeclarationImpl group = transactionalMetaClass.addAttributeGroupDeclaration(metaclass, code);
        AttributeGroupDeclarationImpl overrideGroup = transactionalMetaClass.addAttributeGroupOverride(metaclass, code);
        AttributeGroupDeclarationImpl sysOverrideGroup = transactionalMetaClass
                .addAttributeGroupSystemOverride(metaclass, code);
        //
        String lang = LocaleContextHolder.getLocale().getLanguage();
        group.addTitle(lang, UUIDGenerator.get().nextUUID());
        overrideGroup.addTitle(lang, UUIDGenerator.get().nextUUID());
        sysOverrideGroup.addTitle(lang, UUIDGenerator.get().nextUUID());
        //
        AttributeGroupDeclarationImpl loadedGroup = metaclass.getAttributeGroupDeclaration(code);
        AttributeGroupDeclarationImpl loadedGroupOverride = metaclass.getAttributeGroupOverride(code);
        AttributeGroupDeclarationImpl loadedGroupSysOverride = metaclass.getAttributeGroupSystemOverride(code);
        Assert.assertNotSame(loadedGroup.getTitle(), loadedGroupOverride.getTitle());
        Assert.assertNotSame(loadedGroupSysOverride.getTitle(), loadedGroupOverride.getTitle());
        Assert.assertNotSame(loadedGroup.getTitle(), loadedGroupSysOverride.getTitle());

    }

    @Test
    @Transactional
    public void attributesAreDifferent()
    {
        String code = UUIDGenerator.get().nextUUID();
        //
        AttributeOverrideImpl attr = transactionalMetaClass.addAttributeOverride(metaclass, code);
        attr.setScript(UUIDGenerator.get().nextUUID());
        AttributeOverrideImpl sysAttr = transactionalMetaClass.addAttributeSystemOverride(metaclass, code);
        sysAttr.setScript(UUIDGenerator.get().nextUUID());
        //
        AttributeOverrideImpl loadedAttr = metaclass.getAttributeOverride(code);
        AttributeOverrideImpl loadedSysAttr = metaclass.getAttributeSystemOverride(code);
        Assert.assertNotSame(loadedAttr.getScript(), loadedSysAttr.getScript());

    }

    @Before
    public void before() throws Exception
    {
        SecurityTestHelper.autenticateAsSuperUser();

        String code = "Case" + UniqueNumbersGenerator.nextInt(1000000);
        ClassFqn fqn = TransactionRunner.call(TransactionType.NEW,
                () -> utils.createCase(ClassFqn.parse(ServiceCall.CLASS_ID), code));
        metaclass = metainfoService.getMetaClass(fqn);
    }

    @Test
    @Transactional
    public void delAttributeOverride()
    {
        String code1 = UUIDGenerator.get().nextUUID();
        transactionalMetaClass.addAttributeOverride(metaclass, code1);
        String code2 = UUIDGenerator.get().nextUUID();
        transactionalMetaClass.addAttributeOverride(metaclass, code2);
        //
        transactionalMetaClass.delAttributeOverride(metaclass, code2);
        Collection<AttributeOverrideImpl> attributes = metaclass.getAttributeOverrides();
        //
        HashSet<String> codes = new HashSet<>();
        CollectionUtils.transform(attributes, HasCode.CODE_EXTRACTOR, codes);
        Assert.assertTrue(codes.contains(code1));
        Assert.assertFalse(codes.contains(code2));
    }

    @Test
    public void delChild() throws Exception
    {
        ClassFqn fqn1 = utils.createCase(metaclass.getFqn(), "Case" + UniqueNumbersGenerator.nextInt(1000000));
        ClassFqn fqn2 = utils.createCase(metaclass.getFqn(), "Case" + UniqueNumbersGenerator.nextInt(1000000));
        //
        utils.deleteMetaClass(fqn2);
        Collection<ClassFqn> children = metaclass.getChildren();
        //
        Assert.assertTrue(children.contains(fqn1));
        Assert.assertFalse(children.contains(fqn2));
    }

    @Test
    public void delDeclaredAttribute() throws Exception
    {
        String code1 = utils.createStringAttribute(metaclass.getFqn(), false);
        String code2 = utils.createStringAttribute(metaclass.getFqn(), false);
        //
        utils.deleteAttribute(metaclass.getFqn(), code2);

        metaclass = metainfoService.getMetaClass(metaclass.getFqn());
        Collection<DeclaredAttributeImpl> attributes = metaclass.getDeclaredAttributes();
        //
        HashSet<String> codes = new HashSet<>();
        CollectionUtils.transform(attributes, HasCode.CODE_EXTRACTOR, codes);
        Assert.assertTrue(codes.contains(code1));
        Assert.assertFalse(codes.contains(code2));
    }

    /**
     * Добавляем два DeclaredAttribute, а хотим удалить AttributeOverride. В результате ничего не должно удалиться.
     * (проверяется правильность удаления элементов - с учётом иерархии узлов).
     */
    @Test
    @Transactional
    public void delNotExistedAttributeOverride()
    {
        String code1 = UUIDGenerator.get().nextUUID();
        transactionalMetaClass.addDeclaredAttribute(metaclass, code1);
        String code2 = UUIDGenerator.get().nextUUID();
        transactionalMetaClass.addDeclaredAttribute(metaclass, code2);
        //
        transactionalMetaClass.delAttributeOverride(metaclass, code2);
        Collection<DeclaredAttributeImpl> attributes = metaclass.getDeclaredAttributes();
        //
        HashSet<String> codes = new HashSet<>();
        CollectionUtils.transform(attributes, HasCode.CODE_EXTRACTOR, codes);
        Assert.assertTrue(codes.containsAll(Arrays.asList(code1, code2)));
    }

    @Test
    @Transactional
    public void getAttributeOverrides()
    {
        String code1 = UUIDGenerator.get().nextUUID();
        transactionalMetaClass.addAttributeOverride(metaclass, code1);
        String code2 = UUIDGenerator.get().nextUUID();
        transactionalMetaClass.addAttributeOverride(metaclass, code2);
        //
        Collection<AttributeOverrideImpl> attributes = metaclass.getAttributeOverrides();
        //
        HashSet<String> codes = new HashSet<>();
        CollectionUtils.transform(attributes, HasCode.CODE_EXTRACTOR, codes);
        Assert.assertTrue(codes.containsAll(Arrays.asList(code1, code2)));
    }

    @Transactional
    @Test
    public void getChildren() throws Exception
    {
        ClassFqn[] fqns = TransactionRunner.call(TransactionType.NEW, () ->
        {
            ClassFqn fqn1 = utils.createCase(metaclass.getFqn());
            ClassFqn fqn2 = utils.createCase(metaclass.getFqn());
            return new ClassFqn[] { fqn1, fqn2 };
        });

        ClassFqn fqn1 = fqns[0];
        ClassFqn fqn2 = fqns[1];
        //
        Collection<ClassFqn> children = metainfoService.getMetaClass(metaclass.getFqn()).getChildren();
        //
        Assert.assertTrue(children.containsAll(Arrays.asList(fqn1, fqn2)));
    }

    @Test
    @Transactional
    public void getDeclaredAttributes()
    {
        String code1 = UUIDGenerator.get().nextUUID();
        transactionalMetaClass.addDeclaredAttribute(metaclass, code1);
        String code2 = UUIDGenerator.get().nextUUID();
        transactionalMetaClass.addDeclaredAttribute(metaclass, code2);
        //
        Collection<DeclaredAttributeImpl> attributes = metaclass.getDeclaredAttributes();
        //
        HashSet<String> codes = new HashSet<>();
        CollectionUtils.transform(attributes, HasCode.CODE_EXTRACTOR, codes);
        Assert.assertTrue(codes.containsAll(Arrays.asList(code1, code2)));
    }
}