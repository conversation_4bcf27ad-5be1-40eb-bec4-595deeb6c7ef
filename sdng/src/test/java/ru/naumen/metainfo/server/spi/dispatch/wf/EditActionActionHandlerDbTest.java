package ru.naumen.metainfo.server.spi.dispatch.wf;

import jakarta.inject.Inject;
import jakarta.transaction.Transactional;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import net.customware.gwt.dispatch.server.Dispatch;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.server.dispatch.ObjectTestUtils;
import ru.naumen.core.server.dispatch.SecurityTestHelper;
import ru.naumen.core.server.script.storage.ScriptStorageService;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.TestUtils;
import ru.naumen.metainfo.server.ClassMetainfoServiceException;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.dispatch2.wf.EditActionAction;
import ru.naumen.metainfo.shared.elements.wf.Action;
import ru.naumen.metainfo.shared.elements.wf.State;
import ru.naumen.metainfo.shared.script.ScriptDto;
import ru.naumen.uniquerandom.RandomUtilsImpl;

/**
 * 
 * <AUTHOR>
 *
 */
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class EditActionActionHandlerDbTest
{
    @Inject
    private Dispatch dispatch;
    @Inject
    private MetainfoService metainfoService;
    @Inject
    private ObjectTestUtils testUtils;
    @Inject
    private ScriptStorageService scriptStorageService;

    @Transactional
    @Test
    public void editExists() throws Exception
    {
        // настройка системы
        ClassFqn fqn = Constants.ServiceCall.FQN;
        String stateCode = RandomUtilsImpl.nextCode64();
        testUtils.createState(fqn, stateCode);
        Action action = testUtils.createAction(fqn, stateCode, true);
        // вызов системы
        String script = String.format("print('%s')", TestUtils.randomString());
        ScriptDto scriptDto = testUtils.createScriptDto(script);
        IProperties properties = new MapProperties();
        properties.setProperty("scriptDto", scriptDto);
        EditActionAction a = new EditActionAction(fqn, stateCode, true, action.getCode(), action.getTitle(),
                properties);
        dispatch.execute(a);
        // проверка утверждений
        State state = metainfoService.getMetaClass(fqn).getWorkflow().getState(stateCode);
        Action editedAction = state.getPreActions().iterator().next();
        String scriptBody = scriptStorageService
                .getScriptBody((String)editedAction.getProperties().getProperty("script"));
        Assert.assertEquals(action.getTitle(), editedAction.getTitle());
        Assert.assertEquals(action.getType(), editedAction.getType());
        Assert.assertEquals(script, scriptBody);
        // очистка
    }

    @Transactional
    @Test(expected = ClassMetainfoServiceException.class)
    public void editNotDeclared() throws Exception
    {
        // настройка системы
        ClassFqn parentFqn = Constants.ServiceCall.FQN;
        ClassFqn fqn = testUtils.createCase(parentFqn);
        String stateCode = RandomUtilsImpl.nextCode64();
        testUtils.createState(parentFqn, stateCode);
        Action action = testUtils.createAction(parentFqn, stateCode, true);
        // вызов системы
        EditActionAction a = new EditActionAction(fqn, stateCode, true, action.getCode(), action.getTitle(),
                new MapProperties());
        dispatch.execute(a);
        // проверка утверждений
        //ожидание исключения
        // очистка
    }

    @Transactional
    @Test(expected = ClassMetainfoServiceException.class)
    public void editNotExists() throws Exception
    {
        // настройка системы
        ClassFqn fqn = Constants.ServiceCall.FQN;
        String stateCode = RandomUtilsImpl.nextCode64();
        testUtils.createState(fqn, stateCode);
        // вызов системы
        EditActionAction a = new EditActionAction(fqn, stateCode, true, RandomUtilsImpl.nextCode64(),
                TestUtils.randomString(), new MapProperties());
        dispatch.execute(a);
        // проверка утверждений
        //ожидание исключения
        // очистка
    }

    @Before
    public void setUp()
    {
        SecurityTestHelper.autenticateAsSuperUser();
    }
}
