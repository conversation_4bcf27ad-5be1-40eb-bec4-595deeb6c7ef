package ru.naumen.metainfo.server.spi.dispatch.eventaction;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;
import static ru.naumen.core.shared.Constants.ScriptsComponentTree.EXISTING_SCRIPT;

import jakarta.inject.Inject;
import jakarta.transaction.Transactional;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import net.customware.gwt.dispatch.server.Dispatch;
import ru.naumen.core.server.dispatch.ObjectTestUtils;
import ru.naumen.core.server.dispatch.SecurityTestHelper;
import ru.naumen.core.server.eventaction.EventActionService;
import ru.naumen.core.server.script.ScriptHelper;
import ru.naumen.core.server.script.storage.ScriptStorageService;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.dispatch2.eventaction.DeleteActionConditionAction;
import ru.naumen.metainfo.shared.dispatch2.eventaction.GetActionConditionResponse;
import ru.naumen.metainfo.shared.dispatch2.eventaction.SaveActionConditionAction;
import ru.naumen.metainfo.shared.dispatch2.eventaction.SaveEventAction;
import ru.naumen.metainfo.shared.dispatch2.script.SimpleScriptedResult;
import ru.naumen.metainfo.shared.eventaction.ActionType;
import ru.naumen.metainfo.shared.eventaction.Constants;
import ru.naumen.metainfo.shared.eventaction.EventAction;
import ru.naumen.metainfo.shared.eventaction.EventType;
import ru.naumen.metainfo.shared.eventaction.PlannedEventRule;
import ru.naumen.metainfo.shared.eventaction.ScriptActionCondition;
import ru.naumen.metainfo.shared.eventaction.ScriptEventAction;
import ru.naumen.metainfo.shared.script.ScriptDto;
import ru.naumen.uniquerandom.UniqueNumbersGenerator;

@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class DeleteActionConditionActionHandlerDbTest
{
    @Inject
    private Dispatch dispatch;
    @Inject
    private EventActionService eventActionService;
    @Inject
    private ScriptStorageService scriptStorageService;
    @Inject
    private ObjectTestUtils utils;

    @Before
    public void setUp()
    {
        SecurityTestHelper.autenticateAsSuperUser();
    }

    /**
     * Проверка на то, что при удалении условия выполнения действия по событию информация о соответствующем
     * скрипте удаляется из кэша информации о скриптах.
     * 
     * @throws Exception
     */
    @Test
    @Transactional
    public void testConditionInfoShouldBeObliteratedOnDeletion() throws Exception
    {
        //Подготовка и выполнение
        ScriptDto scriptDto = utils.createScriptDto("true");
        PlannedEventRule event = new PlannedEventRule();
        event.setEventType(EventType.add);
        ScriptEventAction action = new ScriptEventAction();
        action.setActionType(ActionType.ScriptEventAction);
        action.setScript(scriptDto.getCode());
        EventAction eventAction = new EventAction(event, action);
        String code = "" + UniqueNumbersGenerator.nextInt(10000);
        eventAction.setId(code);
        SaveEventAction saveAction = new SaveEventAction(eventAction, true);
        saveAction.setScript(scriptDto);

        SimpleScriptedResult<DtObject> result = dispatch.execute(saveAction);
        EventAction savedEventAction = result.get().getProperty(Constants.EventAction.ORIGINAL_EVENT_ACTION);
        scriptDto.setCode(savedEventAction.getAction().getScript());
        scriptDto.setSelectStrategy(EXISTING_SCRIPT);

        ScriptActionCondition condition = new ScriptActionCondition();
        condition.setScript(scriptDto.getCode());
        savedEventAction.getConditions().add(condition);
        SaveActionConditionAction saveConditionAction = new SaveActionConditionAction(savedEventAction, condition);
        saveConditionAction.setScript(scriptDto);
        GetActionConditionResponse conditionResult = dispatch.execute(saveConditionAction);

        EventAction resavedEventAction = conditionResult.getEventAction();
        ScriptActionCondition savedCondition = (ScriptActionCondition)conditionResult.getActionCondition().get().get();
        if (resavedEventAction != null && savedCondition != null)
        {
            EventAction serverEventAction = eventActionService.getEventAction(resavedEventAction.getCode());
            ScriptActionCondition serverCondition = (ScriptActionCondition)serverEventAction
                    .getCondition(savedCondition.getCode());
            assertTrue("В условии действия по событию на сервере должен быть код скрипта.",
                    ScriptHelper.isScriptCodeValid(serverCondition.getScript()));
            String scriptCode = serverCondition.getScript();
            DeleteActionConditionAction deleteConditionAction = new DeleteActionConditionAction(resavedEventAction,
                    savedCondition);
            dispatch.execute(deleteConditionAction);
            //Проверка
            assertNotNull("Информация о скрипте не должна быть удалена из кэша информации о скриптах.",
                    scriptStorageService.getScript(scriptCode));
        }
        else
        {
            fail("В результате сохранения условия выполнения действия по событию не получены сохранённые условие и действие по событию.");
        }
    }

}
