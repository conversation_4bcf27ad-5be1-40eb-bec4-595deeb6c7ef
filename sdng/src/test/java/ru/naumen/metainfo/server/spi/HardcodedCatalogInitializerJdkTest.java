package ru.naumen.metainfo.server.spi;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.*;

import java.util.List;

import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import com.google.common.collect.Lists;

import ru.naumen.core.server.catalog.ImpactCatalogItem;
import ru.naumen.core.server.metastorage.impl.metainfo.MetaStorageService;
import ru.naumen.metainfo.server.spi.elements.CatalogImpl;
import ru.naumen.metainfo.server.spi.store.Catalog;
import ru.naumen.metainfo.server.spi.store.CatalogContainer;

/**
 * Тестирование инициализации метаинформации в части системных справочников {@see HardcodedCatalogInitializer}
 *
 * <AUTHOR>
 * @since 13 декабря 2019 г.
 */
public class HardcodedCatalogInitializerJdkTest
{
    @Mock
    private MetaStorageService metaStorage;
    @Mock
    private MetainfoServicePersister persister;
    @Mock
    private MetainfoServiceBean service;

    @Before
    public void initMocks()
    {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * Тестирование случая, когда при инициализации метаинформации системный справочник уже присутсвует в metaStorage
     * В этом случае, справочник не должен повторно сохраняться в metaStorage, иначе это приводит к изменению версии
     * метаинформации, что может вызвать проблемы при работе кластера.
     * Для более подробной информации см. https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$64557048
     */
    @Test
    public void testSystemCatalogFoundInMetaStorage()
    {
        List<Class<?>> catalogs = Lists.newArrayList(ImpactCatalogItem.class);
        when(service.getCatalog(any())).thenReturn(null);
        when(service.addCatalog(any())).thenReturn(mock(CatalogImpl.class));
        CatalogContainer catalogContainer = mock(CatalogContainer.class);
        Catalog catalog = mock(Catalog.class);
        when(catalogContainer.getCatalog()).thenReturn(catalog);
        when(metaStorage.get(any(), any(), any())).thenReturn(catalogContainer);

        new HardcodedCatalogInitializer(catalogs, metaStorage, persister).initialize(service);
        verifyNoMoreInteractions(persister);
    }

    /**
     * Тестирование случая, когда при инициализации метаинформации системный справочник уже отсутсвует в metaStorage
     * В этом случае, справочник необходимо сохранить в metaStorage
     */
    @Test
    public void testSystemCatalogNotFoundInMetaStorage()
    {
        List<Class<?>> catalogs = Lists.newArrayList(ImpactCatalogItem.class);
        when(service.getCatalog(any())).thenReturn(null);
        CatalogImpl catalog = mock(CatalogImpl.class);
        when(service.addCatalog(any())).thenReturn(catalog);
        when(metaStorage.get(any(), any(), any())).thenReturn(null);

        new HardcodedCatalogInitializer(catalogs, metaStorage, persister).initialize(service);
        verify(persister, times(1)).persistCatalog(catalog);
    }
}
