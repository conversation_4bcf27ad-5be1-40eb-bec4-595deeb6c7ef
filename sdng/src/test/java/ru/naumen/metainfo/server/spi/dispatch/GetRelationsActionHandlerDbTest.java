package ru.naumen.metainfo.server.spi.dispatch;

import static ru.naumen.metainfo.shared.filters.RelationFilters.left;

import java.util.Collection;

import jakarta.inject.Inject;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.transaction.annotation.Transactional;

import net.customware.gwt.dispatch.server.Dispatch;
import ru.naumen.core.server.dispatch.ObjectTestUtils;
import ru.naumen.core.server.dispatch.SecurityTestHelper;
import ru.naumen.core.shared.Constants;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.ObjectAttributeType;
import ru.naumen.metainfo.shared.dispatch2.GetRelationsAction;
import ru.naumen.metainfo.shared.dispatch2.GetRelationsResponse;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.ManyToOneRelation;
import ru.naumen.metainfo.shared.elements.Relation;

/**
 * <AUTHOR>
 * @since 21.02.2011
 */
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class GetRelationsActionHandlerDbTest
{
    @Inject
    Dispatch dispatch;
    @Inject
    MetainfoService metainfoService;
    @Inject
    ObjectTestUtils utils;

    @Test
    @Transactional
    public void getRelations_LeftFilter() throws Exception
    {
        //настройка системы
        ClassFqn fqn1 = utils.createCase(ClassFqn.parse(Constants.Employee.CLASS_ID));
        String attrCode1 = utils.createObjectTypeAttribute(fqn1, Constants.OU.FQN);
        Attribute attr1 = metainfoService.getMetaClass(fqn1).getAttribute(attrCode1);
        ClassFqn fqn2 = utils.createCase(ClassFqn.parse(Constants.Employee.CLASS_ID));
        String attrCode2 = utils.createObjectTypeAttribute(fqn2, Constants.OU.FQN);
        Attribute attr2 = metainfoService.getMetaClass(fqn2).getAttribute(attrCode2);

        String rightFqn1 = attr1.getType().getProperty(ObjectAttributeType.METACLASS_FQN);
        String rightFqn2 = attr2.getType().getProperty(ObjectAttributeType.METACLASS_FQN);
        //вызов системы
        GetRelationsResponse result = dispatch.execute(new GetRelationsAction(left(fqn1)));
        //проверка утверждений
        Collection<Relation> relations = result.getRelations();
        boolean foundFqn1 = false;
        boolean foundFqn2 = false;
        for (Relation r : relations)
        {
            if (r.getType().equals(ru.naumen.metainfo.shared.Constants.ManyToOneRelation.CODE))
            {
                foundFqn1 = relEquals(fqn1, attrCode1, rightFqn1, foundFqn1, r, ((ManyToOneRelation)r));
                foundFqn2 = relEquals(fqn2, attrCode2, rightFqn2, foundFqn2, r, ((ManyToOneRelation)r));
            }
        }
        Assert.assertTrue("Связь не найдена.", foundFqn1);
        Assert.assertFalse("Найдена связь, не удовлетворяющая фильтру.", foundFqn2);
        //очистка
    }

    @Test
    @Transactional
    public void getRelations_nullFilter() throws Exception
    {
        //настройка системы
        ClassFqn fqn = utils.createCase(ClassFqn.parse(Constants.Employee.CLASS_ID));
        String attrCode = utils.createObjectTypeAttribute(fqn, Constants.OU.FQN);
        Attribute attr = metainfoService.getMetaClass(fqn).getAttribute(attrCode);
        String rightFqn = attr.getType().getProperty(ObjectAttributeType.METACLASS_FQN);
        //вызов системы
        GetRelationsResponse result = dispatch.execute(new GetRelationsAction());
        //проверка утверждений
        Collection<Relation> relations = result.getRelations();
        boolean found = false;
        for (Relation r : relations)
        {
            if (r.getType().equals(ru.naumen.metainfo.shared.Constants.ManyToOneRelation.CODE))
            {
                if (fqn.toString().equals(r.getLeft().toString())
                        && attrCode.equals(((ManyToOneRelation)r).getAttribute())
                        && rightFqn.equals(r.getRight().toString()))
                {
                    found = true;
                    break;
                }
            }
        }
        Assert.assertTrue("Связь не найдена.", found);
        //очистка
    }

    @Before
    public void setUp()
    {
        SecurityTestHelper.autenticateAsSuperUser();
    }

    /**
     * @param fqn
     * @param attrCode
     * @param rightFqn
     * @param foundFqn
     * @param r
     * @param casted
     * @return
     */
    private boolean relEquals(ClassFqn fqn, String attrCode, String rightFqn, boolean foundFqn, Relation r,
            ManyToOneRelation casted)
    {
        if (fqn.toString().equals(r.getLeft().toString()) && attrCode.equals(casted.getAttribute())
                && rightFqn.equals(r.getRight().toString()))
        {
            foundFqn = true;

        }
        return foundFqn;
    }
}
