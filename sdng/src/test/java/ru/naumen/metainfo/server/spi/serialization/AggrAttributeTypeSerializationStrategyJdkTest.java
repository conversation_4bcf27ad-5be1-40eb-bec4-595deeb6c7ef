package ru.naumen.metainfo.server.spi.serialization;

import static java.util.Collections.emptyList;
import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.argThat;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static ru.naumen.commons.shared.utils.CollectionUtils.map;
import static ru.naumen.metainfo.shared.Constants.AggregateAttributeType.COMPLEX_RELATION_ATTR_GROUPS;
import static ru.naumen.metainfo.shared.Constants.AggregateAttributeType.PREPARED_ATTRIBUTES;
import static ru.naumen.metainfo.shared.Constants.ObjectAttributeType.COMPLEX_RELATION;

import java.util.List;

import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentMatcher;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import ru.naumen.core.shared.Constants.Employee;
import ru.naumen.core.shared.Constants.OU;
import ru.naumen.core.shared.Constants.Team;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfo.server.spi.elements.AttributeTypeImpl;
import ru.naumen.metainfo.server.spi.store.AttributeType;
import ru.naumen.metainfo.server.spi.store.Property;
import ru.naumen.metainfo.shared.Constants.AggregateAttributeType;
import ru.naumen.metainfo.shared.elements.AttributeDescription;

/**
 * 
 * <AUTHOR>
 * @since 9 марта 2016 г.
 */
public class AggrAttributeTypeSerializationStrategyJdkTest
{
    private static class AttrDescMatcher implements ArgumentMatcher<List<AttributeDescription>>
    {
        private final List<AttributeDescription> attrDescs;

        public AttrDescMatcher(List<AttributeDescription> attrDescs)
        {
            this.attrDescs = attrDescs;
        }

        @Override
        public boolean matches(List<AttributeDescription> list)
        {
            if (list.size() != attrDescs.size())
            {
                return false;
            }
            for (int i = 0; i < attrDescs.size(); ++i)
            {
                if (!matches(list.get(i), attrDescs.get(i)))
                {
                    return false;
                }
            }
            return true;
        }

        private boolean matches(Object obj, AttributeDescription desc)
        {
            if (!(obj instanceof AttributeDescription))
            {
                return false;
            }
            AttributeDescription otherDesc = (AttributeDescription)obj;
            if (!ObjectUtils.equals(otherDesc.getReferenceMetaClass(), desc.getReferenceMetaClass()))
            {
                return false;
            }
            return ObjectUtils.equals(otherDesc, desc);
        }

    }

    private AggrAttributeTypeSerializationStrategy strategy;

    @Before
    public void setUp()
    {
        strategy = new AggrAttributeTypeSerializationStrategy();
    }

    @Test
    public void testDeserializeAttributes()
    {
        AttributeType from = new AttributeType();

        from.getProperty()
                .add(new Property(PREPARED_ATTRIBUTES,
                        "[{\"attr\":\"code_em\",\"parents\":[],\"referenceMetaClass\":\"employee\"},"
                                + "{\"attr\":\"code_ou\",\"parents\":[],\"referenceMetaClass\":\"ou\"}]"));

        AttributeTypeImpl to = mock(AttributeTypeImpl.class);

        strategy.deserialize(from, to);

        List<AttributeDescription> expected = Lists.newArrayList(
                new AttributeDescription("code_em", emptyList(), Employee.FQN),
                new AttributeDescription("code_ou", emptyList(), OU.FQN));
        verify(to).setProperty(eq(PREPARED_ATTRIBUTES), argThat(new AttrDescMatcher(expected)));
    }

    @Test
    public void testSerialize()
    {
        AttributeTypeImpl from = mock(AttributeTypeImpl.class);
        when(from.getCode()).thenReturn(AggregateAttributeType.CODE);
        when(from.propertyNames())
                .thenReturn(Sets.newHashSet(COMPLEX_RELATION, COMPLEX_RELATION_ATTR_GROUPS, PREPARED_ATTRIBUTES));
        when(from.getProperty(COMPLEX_RELATION)).thenReturn(true);
        when(from.getProperty(COMPLEX_RELATION_ATTR_GROUPS))
                .thenReturn(map(OU.FQN, "ouGroup", Team.FQN, "teamGroup", Employee.FQN, "emplGroup"));

        when(from.getProperty(PREPARED_ATTRIBUTES))
                .thenReturn(Lists.newArrayList(new AttributeDescription("code_em", emptyList(), Employee.FQN),
                        new AttributeDescription("code_ou", emptyList())));

        AttributeType to = new AttributeType();

        strategy.serialize(from, to);

        assertEquals(3, to.getProperty().size());
        assertEquals("true", getValue(to.getProperty(), COMPLEX_RELATION));
        assertEquals("{\"ou\":\"ouGroup\",\"team\":\"teamGroup\",\"employee\":\"emplGroup\"}",
                getValue(to.getProperty(), COMPLEX_RELATION_ATTR_GROUPS));

        assertEquals("[{\"attr\":\"code_em\",\"parents\":[],\"referenceMetaClass\":\"employee\"},"
                + "{\"attr\":\"code_ou\",\"parents\":[]}]", getValue(to.getProperty(), PREPARED_ATTRIBUTES));

        assertEquals(AggregateAttributeType.CODE, to.getCode());
    }

    private Object getValue(List<Property> properties, String key)
    {
        for (Property property : properties)
        {
            if (property.getCode().equals(key))
            {
                return property.getValue();
            }
        }
        return null;
    }
}
