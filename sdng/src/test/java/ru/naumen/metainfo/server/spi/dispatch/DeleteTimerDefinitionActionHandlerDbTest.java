package ru.naumen.metainfo.server.spi.dispatch;

import static org.junit.Assert.assertNotNull;

import jakarta.inject.Inject;
import jakarta.transaction.Transactional;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import ru.naumen.core.server.dispatch.Dispatch;
import ru.naumen.core.server.dispatch.ObjectTestUtils;
import ru.naumen.core.server.dispatch.SecurityTestHelper;
import ru.naumen.core.server.script.storage.ScriptStorageService;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.timer.DeleteTimerDefinitionAction;
import ru.naumen.core.shared.timer.definition.ScriptTimerCondition;
import ru.naumen.core.shared.timer.definition.TimerDefinition;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.dispatch2.SaveTimerDefinitionAction;
import ru.naumen.metainfo.shared.dispatch2.script.SimpleScriptedResult;
import ru.naumen.metainfo.shared.script.ScriptDto;
import ru.naumen.uniquerandom.UniqueNumbersGenerator;

@Transactional
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class DeleteTimerDefinitionActionHandlerDbTest
{
    @Inject
    private Dispatch dispatch;
    @Inject
    private ScriptStorageService scriptStorageService;
    @Inject
    private MetainfoService metainfoService;
    @Inject
    private ObjectTestUtils utils;

    @Before
    public void setUp()
    {
        SecurityTestHelper.autenticateAsSuperUser();
    }

    /**
     * Проверка на то, что информация о скриптах условий счётчика времени остаётся с прежним номером после редактирования.
     * @throws Exception 
     */
    @Test
    public void testConditionScriptInfoShouldHaveSameNumberOnEditing() throws Exception
    {
        //Подготовка
        ScriptDto script1 = utils.createScriptDto("true");
        ScriptDto script2 = utils.createScriptDto("true");
        ScriptDto script3 = utils.createScriptDto("true");
        ScriptDto script4 = utils.createScriptDto("true");

        TimerDefinition timerDefinition = new TimerDefinition();
        String code = "tdef" + UniqueNumbersGenerator.nextInt(10000);
        timerDefinition.setCode(code);
        timerDefinition.setSystem(false);
        timerDefinition.getTargetTypes().add(Constants.Employee.FQN);
        ScriptTimerCondition timerCondition = new ScriptTimerCondition();

        timerDefinition.setTimerCondition(timerCondition);

        SaveTimerDefinitionAction saveAction = new SaveTimerDefinitionAction(timerDefinition, true);
        saveAction.setWithScripts(true);
        saveAction.setStartConditionScript(script1);
        saveAction.setStopConditionScript(script2);
        saveAction.setPauseConditionScript(script3);
        saveAction.setResumeConditionScript(script4);
        //Выполнение
        SimpleScriptedResult<DtoContainer<TimerDefinition>> result = dispatch.execute(saveAction);

        TimerDefinition savedDefinition = result.get().get();
        TimerDefinition serverDefinition = metainfoService.getTimerDefinition(savedDefinition.getCode());
        ScriptTimerCondition serverCondition = (ScriptTimerCondition)serverDefinition.getTimerCondition();
        String pauseSciptCode = serverCondition.getPauseCondition();
        String resumeSciptCode = serverCondition.getResumeCondition();
        String startSciptCode = serverCondition.getStartCondition();
        String stopSciptCode = serverCondition.getStopCondition();

        dispatch.execute(new DeleteTimerDefinitionAction(code));
        //Проверка
        assertNotNull(
                "Информация о скрипте приостановки счётчика времени не должна быть удалена из кэша информации о скриптах.",
                scriptStorageService.getScript(pauseSciptCode));
        assertNotNull(
                "Информация о скрипте возобновления счётчика времени не должна быть удалена из кэша информации о скриптах.",
                scriptStorageService.getScript(resumeSciptCode));
        assertNotNull(
                "Информация о скрипте запуска счётчика времени не должна быть удалена из кэша информации о скриптах.",
                scriptStorageService.getScript(startSciptCode));
        assertNotNull(
                "Информация о скрипте остановки счётчика времени не должна быть удалена из кэша информации о скриптах.",
                scriptStorageService.getScript(stopSciptCode));
    }
}
