package ru.naumen.mailreader.server.receiver;

import static org.mockito.Mockito.atLeast;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;

import org.eclipse.angus.mail.imap.IMAPMessage;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import jakarta.mail.Folder;
import jakarta.mail.Message;
import jakarta.mail.internet.MimeMessage;
import ru.naumen.core.server.SpringContext;
import ru.naumen.core.server.mail.SSLSocketMailConfigurator;
import ru.naumen.mailreader.server.MailreaderBeansConfig;
import ru.naumen.mailreader.shared.receiver.InboundMailServerConfig;

/**
 * Тестирование методов класса MailReceiver
 * <AUTHOR>
 * @since 25.06.2018
 */
public class MailReceiverJdkTest
{
    /**
     * <p>Тестирование копирования и сохранения на диск удаленного сообщения</p>
     * <ol>
     *     <li>Создать экземпляр ImapMailReceiverImpl для выполнения действий с сообщениями</li>
     *     <li>Вызвать методы {@link ImapMailReceiverImpl#copyMessage(Message, Folder, Folder)} и
     *     {@link ImapMailReceiverImpl#saveMessageToDisk(MimeMessage)}</li>
     *     <li>Проверить, что хотя бы два раза был вызван метод {@link IMAPMessage#isExpunged()}</li>
     *     <li>Проверить, что никакие другие методы не вызывались</li>
     * </ol>
     */
    @Test
    public void testCleanupForRemovedMail() throws Exception
    {
        try (MockedStatic<SpringContext> springContextMockedStatic = Mockito.mockStatic(SpringContext.class))
        {
            SpringContext springContext = mock(SpringContext.class);
            when(springContext.getBean(DumpWriter.class)).thenReturn(mock(DumpWriter.class));
            when(springContext.getBean(SSLSocketMailConfigurator.class)).thenReturn(mock(SSLSocketMailConfigurator.class));
            springContextMockedStatic.when(SpringContext::getInstance).thenReturn(springContext);
            final InboundMailServerConfig mockConfig = mock(InboundMailServerConfig.class);
            when(mockConfig.getHost()).thenReturn("asd");
            when(mockConfig.getPort()).thenReturn(80);
            when(mockConfig.getFolders()).thenReturn("");

            ImapMailReceiverImpl receiver = new ImapMailReceiverImpl(mock(MailreaderBeansConfig.class), mockConfig);

            IMAPMessage email = mock(IMAPMessage.class);
            when(email.isExpunged()).thenReturn(true);

            Assert.assertNull(receiver.saveMessageToDisk(email));

            receiver.copyMessage(email, mock(Folder.class), mock(Folder.class));

            verify(email, atLeast(2)).isExpunged();
            verifyNoMoreInteractions(email);
        }
    }
}
