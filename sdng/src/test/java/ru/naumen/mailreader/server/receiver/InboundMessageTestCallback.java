/*
*/
package ru.naumen.mailreader.server.receiver;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import ru.naumen.mailreader.server.queue.IInboundMailAddress;
import ru.naumen.mailreader.server.queue.IInboundMailAttachment;
import ru.naumen.mailreader.server.queue.InboundMailMessage;

/**
 * Date: 02.05.2006
 * <AUTHOR>
 */
public class InboundMessageTestCallback implements IMailReceiverCallback
{
    private static final Logger _log = LoggerFactory.getLogger(InboundMessageTestCallback.class);

    @Override
    public boolean handleMessage(InboundMailMessage message) throws Exception
    {
        for (IInboundMailAddress addr : message.getRecipients())
        {
            _log.info("Rcpt: " + addr.getName() + " <" + addr.getAddress() + "> " + addr.getAddressType());
        }
        IInboundMailAddress addr = message.getFrom();
        _log.info("From: " + addr.getName() + " <" + addr.getAddress() + "> " + addr.getAddressType());
        for (IInboundMailAttachment attach : message.getAttachments())
        {
            _log.info("Attachment: " + attach.getFilename() + "; size: " + attach.getData().length + "; type: " + attach.getContentType());
        }
        _log.info("Subject: " + message.getSubject());
        _log.info("Body: " + message.getBody());
        _log.info("");
        return false;
    }
}
