package ru.naumen.mailreader.server.dispatch;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

import java.util.ArrayList;

import jakarta.inject.Inject;

import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import ru.naumen.core.server.dispatch.Dispatch;
import ru.naumen.core.server.dispatch.ObjectTestUtils;
import ru.naumen.core.server.metastorage.impl.metainfo.MetaStorageService;
import ru.naumen.core.server.script.ScriptHelper;
import ru.naumen.core.server.script.storage.ScriptStorageService;
import ru.naumen.core.shared.script.places.OtherCategories;
import ru.naumen.core.shared.script.places.ScriptHolders;
import ru.naumen.mailreader.shared.Constants;
import ru.naumen.mailreader.shared.dispatch.SaveMailProcessorRuleAction;
import ru.naumen.mailreader.shared.receiver.MailProcessorRule;
import ru.naumen.metainfo.shared.dispatch2.script.SimpleScriptedResult;
import ru.naumen.metainfo.shared.script.Script;
import ru.naumen.metainfo.shared.script.ScriptDto;
import ru.naumen.metainfo.shared.ui.LocalizedString;
import ru.naumen.uniquerandom.UniqueNumbersGenerator;

@Ignore
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class SaveMailProcessorRuleActionHandlerDbTest
{
    @Inject
    private Dispatch dispatch;
    @Inject
    private MetaStorageService metaStorage;
    @Inject
    private ScriptStorageService scriptStorageService;
    @Inject
    private ObjectTestUtils utils;

    /**
     * Проверка на то, что информация о скрипте добавляется к правилу обработки почты и
     * в кэш информации о скриптах при сохранении нового правила обработки почты.
     * 
     * @throws Exception
     */
    @Test
    public void testScriptInfoShouldBeAddedOnNewRuleSave() throws Exception
    {
        //Подготовка
        MailProcessorRule mailProcessorRule = new MailProcessorRule();
        ScriptDto script = utils.createScriptDto("true");

        ArrayList<LocalizedString> titles = mailProcessorRule.getTitle();
        String titleString = "rule" + UniqueNumbersGenerator.nextInt(10000);
        LocalizedString title = new LocalizedString("ru", titleString);
        titles.add(title);

        SaveMailProcessorRuleAction saveAction = new SaveMailProcessorRuleAction(mailProcessorRule, script);
        saveAction.setWithScripts(true);
        //Выполнение
        SimpleScriptedResult<MailProcessorRule> result = dispatch.execute(saveAction);
        //Проверка
        MailProcessorRule savedRule = result.get();
        script.setCode(result.get().getScript());
        MailProcessorRule serverRule = metaStorage.get(Constants.MAIL_PROCESSOR_RULE, savedRule.getCode());

        String scriptCode = serverRule.getScript();
        assertTrue("В правиле обработки почты на сервере должен быть код скрипта.",
                ScriptHelper.isScriptCodeValid(scriptCode));

        Script savedScript = scriptStorageService.getScript(scriptCode);
        assertNotNull("Информация о скрипте не найдена в кэше информации о скриптах.", script);
        assertEquals("Код скрипта не совпадает с ожидаемым", scriptCode, script.getCode());
        assertEquals("Тип объекта, содержащего скрипт не совпадает с ожидаемым.", ScriptHolders.MAIL_PROCESSOR_RULE,
                savedScript.getUsagePoints().get(0).getHolderType());
        assertEquals("Категория скрипта не совпадает с ожидаемой.", OtherCategories.MAIL_PROCESSOR_RULE, savedScript
                .getUsagePoints().get(0).getCategory());
        assertEquals("Идентификатор объекта, содержащего скрипт, не совпадает с ожидаемым.", savedRule.getCode(),
                savedScript.getUsagePoints().get(0).getLocation());
        assertEquals("Тело скрипта не совпадает с ожидаемым.", script.getBody(), script.getBody());
    }

    /**
     * Проверка на то, что информация о скрипте содержит тот же номер, что и до редактирования, а так же
     * остальные корректные данные.
     * 
     * @throws Exception
     */
    @Test
    public void testScriptInfoShouldHaveSameNumberAfterEdit() throws Exception
    {
        //Подготовка и выполнение
        MailProcessorRule mailProcessorRule = new MailProcessorRule();
        ScriptDto script = utils.createScriptDto("true");

        ArrayList<LocalizedString> titles = mailProcessorRule.getTitle();
        String titleString = "rule" + UniqueNumbersGenerator.nextInt(10000);
        LocalizedString title = new LocalizedString("ru", titleString);
        titles.add(title);

        SaveMailProcessorRuleAction saveAction = new SaveMailProcessorRuleAction(mailProcessorRule, script);
        saveAction.setWithScripts(true);
        SimpleScriptedResult<MailProcessorRule> result = dispatch.execute(saveAction);

        MailProcessorRule savedRule = result.get();
        MailProcessorRule serverRule = metaStorage.get(Constants.MAIL_PROCESSOR_RULE, savedRule.getCode());

        script = result.getScript(result.get().getScript());
        String scriptCode = serverRule.getScript();

        String newScriptBody = "false";
        script.setBody(newScriptBody);

        SaveMailProcessorRuleAction editAction = new SaveMailProcessorRuleAction(savedRule, script);
        editAction.setWithScripts(true);
        SimpleScriptedResult<MailProcessorRule> editedResult = dispatch.execute(editAction);

        MailProcessorRule editedRule = editedResult.get();
        MailProcessorRule editedServerRule = metaStorage.get(Constants.MAIL_PROCESSOR_RULE, editedRule.getCode());

        String editedScriptCode = editedServerRule.getScript();
        //Проверка
        assertEquals("Коды старого и отредактированного скрипта не совпадают.", scriptCode, editedScriptCode);
        assertTrue("В правиле обработки почты на сервере должен быть код скрипта.",
                ScriptHelper.isScriptCodeValid(scriptCode));
        Script savedScript = scriptStorageService.getScript(scriptCode);
        assertNotNull("Информация о скрипте не найдена в кэше информации о скриптах.", script);
        assertEquals("Код скрипта не совпадает с ожидаемым", scriptCode, script.getCode());
        assertEquals("Тип объекта, содержащего скрипт не совпадает с ожидаемым.", ScriptHolders.MAIL_PROCESSOR_RULE,
                savedScript.getUsagePoints().get(0).getHolderType());
        assertEquals("Категория скрипта не совпадает с ожидаемой.", OtherCategories.MAIL_PROCESSOR_RULE, savedScript
                .getUsagePoints().get(0).getCategory());
        assertEquals("Идентификатор объекта, содержащего скрипт, не совпадает с ожидаемым.", editedRule.getCode(),
                savedScript.getUsagePoints().get(0).getLocation());
        assertEquals("Тело скрипта не совпадает с ожидаемым.", newScriptBody, script.getBody());
    }
}
