package ru.naumen.mailreader.server.receiver;

import java.io.InputStream;
import java.util.List;
import java.util.Properties;

import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import jakarta.activation.DataSource;
import jakarta.mail.Session;
import jakarta.mail.internet.MimeBodyPart;
import jakarta.mail.internet.MimeMessage;
import jakarta.mail.util.ByteArrayDataSource;
import ru.naumen.Assert;
import ru.naumen.core.server.ConfigurationProperties;
import ru.naumen.sec.server.AntivirusValidationService;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.mailreader.server.processor.MailProcessHelper;
import ru.naumen.mailreader.server.queue.IInboundMailAttachment;
import ru.naumen.mailreader.server.queue.IInboundMailMessage;
import ru.naumen.mailreader.server.queue.InboundMailAddress;

/**
 * Тестирование MimeDecoder из mailreader
 * <AUTHOR>
 * @since 31.08.2009
 *
 */
public class MimeDecoderJdkTest
{
    @Mock
    MessageFacade messages;

    @Mock
    ConfigurationProperties configurationProperties;

    @Mock
    AntivirusValidationService antivirusValidationService;

    @Mock
    MessageFacade messageFacade;

    MimeDecoderApacheCommons decoder;

    MailProcessHelper mailProcessHelper;

    /**
     *  Разбор письма c апострофами в адресе
     */
    @Test
    public void apostropheTest() throws Exception
    {
        InputStream is = getClass().getResourceAsStream("ZAO_Zara.msg");
        MimeMessage msg = createMessage(is);
        // адресс используется в случае неудачного разбора адресса из from
        // msg.setHeader("Return-Path", "");
        decoder.decode(msg, false);
    }

    /**
     * Разбор письма отправленного из Lotus Notes, в теле которго содерится картинка
     */
    //TODO NSDPRD-2613
    //@Test
    public void lotusImage() throws Exception
    {
        InputStream is = getClass().getResourceAsStream("test_lotus.eml");
        MimeMessage message = createMessage(is);
        IInboundMailMessage inMessage = decoder.decode(message, true);
        Assert.assertEquals(
                "\r\nBest regards,\r\nOleksandr GUTIANKO\r\nChief Systems Administrator of\r\nService Desk Department\r\nIT Service Support Division\r\nOTP Bank\r\n28 Fizkultury St.\r\nKyiv, UA-01033\r\<EMAIL>\r\n<mailto:<EMAIL>>\r\nwww.otpbank.com.ua <http://www.otpbank.com.ua/>\r\ntel.: 044 490 05 00\r\ninternal phone: 525100\r\n\r\n__________________\r\n\r\n\r\n\r\nBest regards,\r\nOleksandr GUTIANKO\r\nChief Systems Administrator of\r\nService Desk Department\r\nIT Service Support Division\r\nOTP Bank\r\n28 Fizkultury St.\r\nKyiv, UA-01033\r\<EMAIL>\r\n<mailto:<EMAIL>>\r\nwww.otpbank.com.ua <http://www.otpbank.com.ua/>\r\ntel.: 044 490 05 00\r\ninternal phone: 525100",
                inMessage.getBody());
    }

    /**
     * Разбор письма МОЭСК
     */
    @Test
    public void parseMoeskMail() throws Exception
    {
        InputStream is = getClass().getResourceAsStream("moesk_mail.eml");
        MimeMessage message = createMessage(is);
        decoder.decode(message, true);
    }

    /**
     * Разбор письма, сформированного Outlook
     */
    @Test
    public void outlookImage() throws Exception
    {
        InputStream is = getClass().getResourceAsStream("test_outlook.eml");
        MimeMessage message = createMessage(is);
        IInboundMailMessage inMessage = decoder.decode(message, true);
        Assert.assertEquals(
                "\r\n[cid:image001.png@01CD4EE9.180B1B60]\r\n\r\nBest regards,\r\nOleksandr GUTIANKO\r\nChief Systems Administrator of\r\nService Desk Department\r\nIT Service Support Division\r\nOTP Bank\r\n28 Fizkultury St.\r\nKyiv, UA-01033\r\<EMAIL><mailto:<EMAIL>>\r\nwww.otpbank.com.ua<http://www.otpbank.com.ua/>\r\ntel.: 044 490 05 00\r\ninternal phone: 525100\r\n\r\n",
                inMessage.getBody());
    }

    /**
     * Разбор письма, содержащего тестовое вложение
     */
    @Test
    public void parseAttachment() throws Exception
    {
        // настройка системы
        InputStream is = getClass().getResourceAsStream("parse_attachment.mbox");
        MimeMessage message = createMessage(is);
        // вызов системы
        IInboundMailMessage inMessage = decoder.decode(message, false);
        // проверка утверждений
        IInboundMailAttachment attach = inMessage.getAttachments().get(0);
        Assert.assertEquals("Текст вложения", new String(attach.getData()));
        // очистка не требуется
    }

    /**
     * Сохранение исходного письма в зависимости от параметра "ru.naumen.email.saveOriginal"
     */
    @Test
    public void testSaveOriginalEmail() throws Exception
    {
        // настройка системы
        Mockito.when(configurationProperties.getSaveOriginalEmail()).thenReturn(true);
        InputStream is = getClass().getResourceAsStream("parse_attachment.mbox");
        MimeMessage message = createMessage(is);
        // вызов системы
        IInboundMailMessage inMessage = decoder.decode(message, false);
        // проверка утверждений
        Assert.assertEquals("Тело письма\n", inMessage.getHtmlBody());

        // настройка системы
        Mockito.when(configurationProperties.getSaveOriginalEmail()).thenReturn(false);
        // вызов системы
        IInboundMailMessage inMessageSanitize = decoder.decode(message, false);
        // проверка утверждений
        Assert.assertEquals("Тело письма<br>", inMessageSanitize.getHtmlBody());
    }

    /**
     * Разбор заголовка письма подписанного dkim
     * Убедиться, что message-id извлечен корректно.
     */
    @Test
    public void messageIdFromHeaderWithDkim() throws Exception
    {
        InputStream is = getClass().getResourceAsStream("test_dkim_headers.eml");
        MimeMessage message = createMessage(is);
        IInboundMailMessage inMessage = decoder.decode(message, true);
        Assert.assertEquals("<<EMAIL>>", inMessage.getId());
    }
    /**
     * Разбор письма, тело которого в кодировке CP-1251
     */
    @Test
    public void parseBodyCP1251() throws Exception
    {
        // настройка системы
        InputStream is = getClass().getResourceAsStream("cp1251.mbox");
        MimeMessage message = createMessage(is);
        // вызов системы
        IInboundMailMessage inMessage = decoder.decode(message, false);
        // проверка утверждений
        Assert.assertEquals("Тело письма в кодировке cp-1251\n", inMessage.getBody());
        // очистка не требуется
    }

    /**
     * Разбор письма, тело которого в кодировке UTF-8
     */
    @Test
    public void parseBodyUTF8() throws Exception
    {
        // настройка системы
        InputStream is = getClass().getResourceAsStream("utf8.mbox");
        MimeMessage message = createMessage(is);
        // вызов системы
        IInboundMailMessage inMessage = decoder.decode(message, false);
        // проверка утверждений
        Assert.assertEquals("Тело писма в кодировке UTF-8\n", inMessage.getBody());
        // очистка не требуется
    }

    /**
     * Разбор письма, тело которого null
     */
    @Test
    public void parseNullBody() throws Exception
    {
        // настройка системы
        InputStream is = getClass().getResourceAsStream("letter_with_no_body.eml");
        MimeMessage message = createMessage(is);
        // вызов системы
        IInboundMailMessage inMessage = decoder.decode(message, false);
        // проверка утверждений
        Assert.assertEquals(null, inMessage.getBody());
        // очистка не требуется
    }

    /**
     * Разбор письма, тема которого в кодировке UTF-8
     */
    @Test
    public void parseSubjectCP1251() throws Exception
    {
        // настройка системы
        InputStream is = getClass().getResourceAsStream("cp1251.mbox");
        MimeMessage message = createMessage(is);
        // вызов системы
        IInboundMailMessage inMessage = decoder.decode(message, false);
        // проверка утверждений
        Assert.assertEquals("Писмо в кодировке cp-1251", inMessage.getSubject());
        // очистка не требуется
    }

    /**
     * Разбор письма, тема которого в кодировке UTF-8
     */
    @Test
    public void parseSubjectUTF8() throws Exception
    {
        // настройка системы
        InputStream is = getClass().getResourceAsStream("utf8.mbox");
        MimeMessage message = createMessage(is);
        // вызов системы
        IInboundMailMessage inMessage = decoder.decode(message, false);
        // проверка утверждений
        Assert.assertEquals("Письмо в кодировке UTF-8", inMessage.getSubject());
        // очистка не требуется
    }

    @Before
    public void setUp() throws Exception
    {
        MockitoAnnotations.openMocks(this).close();
        this.mailProcessHelper = new MailProcessHelper();
        decoder = new MimeDecoderApacheCommons(messages, configurationProperties, mailProcessHelper);
        Mockito.when(messages.getMessage(Mockito.anyString())).thenReturn("");
        mailProcessHelper.setConfigurationProperties(configurationProperties);
        mailProcessHelper.setAntivirusValidationService(antivirusValidationService);
        Mockito.when(configurationProperties.getAcceptableExtensions()).thenReturn(List.of());
        Mockito.when(configurationProperties.getUploadFileMaxSize()).thenReturn((long)-1);
        Mockito.when(configurationProperties.getUploadFilesGroupMaxSize()).thenReturn((long)-1);
        Mockito.doNothing().when(antivirusValidationService).verifyFile(Mockito.any(), Mockito.any(), Mockito.any());
    }

    /**
     *  Проверка имени отправителя сообщения
     */
    @Test
    public void testGetFromName() throws Exception
    {
        // настройка системы
        InputStream is = getClass().getResourceAsStream("utf8.mbox");
        MimeMessage message = createMessage(is);
        MailParser parser = new MailParser(message, configurationProperties, mailProcessHelper, messageFacade);
        parser.parse();
        // вызов системы
        InboundMailAddress result = MimeDecoderApacheCommons.extractFirstAddress(MimeDecoderApacheCommons
                .getAddressesFrom(message, parser));
        // проверка утверждений
        Assert.assertFalse(result.getName().trim().isEmpty());
        // очистка не требуется
    }

    /**
     * Разбор письма, к которому прикреплен текст с заголовком Content-Disposition : inline
     */
    @Test
    public void textInline() throws Exception
    {
        InputStream is = getClass().getResourceAsStream("test_text_inline.eml");
        MimeMessage message = createMessage(is);
        decoder.decode(message, true);
    }

    /**
     * Разбор письма, у которого на одном уровне вложенности присутствуют 2 multipart/alternative
     */
    @Test
    public void twoAlternative() throws Exception
    {
        InputStream is = getClass().getResourceAsStream("test_outlook_two_alternative.eml");
        MimeMessage message = createMessage(is);
        IInboundMailMessage inMessage = decoder.decode(message, true);
        Assert.assertEquals(
                "\r\n[cid:image001.png@01CD4EE9.180B1B60]\r\n\r\nBest regards,\r\nOleksandr GUTIANKO\r\nChief Systems Administrator of\r\nService Desk Department\r\nIT Service Support Division\r\nOTP Bank\r\n28 Fizkultury St.\r\nKyiv, UA-01033\r\<EMAIL><mailto:<EMAIL>>\r\nwww.otpbank.com.ua<http://www.otpbank.com.ua/>\r\ntel.: 044 490 05 00\r\ninternal phone: 525100\r\n\r\n",
                inMessage.getBody());
    }

    /**
     * Разбор письма без text/html части
     */
    @Test
    public void withoutHtmlPart() throws Exception
    {
        InputStream is = getClass().getResourceAsStream("test_outlook_without_html.eml");
        MimeMessage message = createMessage(is);
        IInboundMailMessage inMessage = decoder.decode(message, true);
        Assert.assertEquals(
                "\r\n[cid:image001.png@01CD4EE9.180B1B60]\r\n\r\nBest regards,\r\nOleksandr GUTIANKO\r\nChief Systems Administrator of\r\nService Desk Department\r\nIT Service Support Division\r\nOTP Bank\r\n28 Fizkultury St.\r\nKyiv, UA-01033\r\<EMAIL><mailto:<EMAIL>>\r\nwww.otpbank.com.ua<http://www.otpbank.com.ua/>\r\ntel.: 044 490 05 00\r\ninternal phone: 525100\r\n\r\n",
                inMessage.getBody());
    }

    /**
     * Разбор письма из outlook содержащего eml-вложение.
     * Убедиться, что название файла-письма извлечено корректно.
     */
    @Test
    public void filenameOfAttachedEmlByOutlook() throws Exception
    {
        InputStream is = getClass().getResourceAsStream("outlook_msg_with_eml_attach.eml");
        MimeMessage message = createMessage(is);
        IInboundMailMessage inMessage = decoder.decode(message, true);
        IInboundMailAttachment attachedEml = null;
        for (IInboundMailAttachment attach : inMessage.getAttachments())
        {
            if (attach.getContentType().equalsIgnoreCase("message/rfc822"))
            {
                attachedEml = attach;
            }
        }
        Assert.assertNotNull(attachedEml);
        Assert.assertEquals("Информационное письмо!.eml", attachedEml.getFilename());
    }

    /**
     * Тестирование разбора письма, в котором адрес получателя заключен в кавычки.
     * Такие письма отправляют некоторые версии Outlook.
     */
    @Test
    public void parseQuotedRecipient() throws Exception
    {
        InputStream is = getClass().getResourceAsStream("test_quoted_recipient.eml");
        MimeMessage message = createMessage(is);
        IInboundMailMessage decoded = decoder.decode(message, true);
        Assert.assertEquals("Количество получателей не совпало с ожидаемым", 1,
                decoded.getRecipients().length);
        Assert.assertEquals("Адрес получателя не совпал с ожидаемым", "<EMAIL>",
                decoded.getRecipients()[0].getAddress());
    }

    /**
     * Разбор письма из outlook содержащего eml-вложение.
     * Убедиться, что inline файл-письмо при разборе поменяло Content-Disposition на attachment.
     */
    @Test
    public void dispositionOfInlinedEmlByOutlook() throws Exception
    {
        InputStream is = getClass().getResourceAsStream("outlook_msg_with_eml_inline_content.eml");
        MimeMessage message = createMessage(is);
        IInboundMailMessage inMessage = decoder.decode(message, true);
        IInboundMailAttachment attachedEml = null;
        for (IInboundMailAttachment attach : inMessage.getAttachments())
        {
            if (attach.getContentType().equalsIgnoreCase("message/rfc822"))
            {
                attachedEml = attach;
            }
        }
        Assert.assertNotNull(attachedEml);
        Assert.assertEquals("attachment", attachedEml.getDisposition());
    }

    /**
     * Разбор письма с вложением forwarded-письма, вложения из которого должны попасть в основное письмо
     */
    @Test
    public void parseForwardedEmailAttachments() throws Exception
    {
        InputStream is = getClass().getResourceAsStream("test_forwarded.eml");
        MimeMessage message = createMessage(is);
        IInboundMailMessage inMessage = decoder.decode(message, true);
        IInboundMailAttachment attachedJpgFromForward = null;
        for (IInboundMailAttachment attach : inMessage.getAttachments())
        {
            if (attach.getFilename().equals("Таможня удалить операцию.jpg")
                    && attach.getDisposition().equalsIgnoreCase("attachment"))
            {
                attachedJpgFromForward = attach;
            }
        }
        Assert.assertNotNull(attachedJpgFromForward);
    }

    static MimeMessage createMessage(InputStream is) throws Exception
    {
        return new MimeMessage(Session.getInstance(new Properties()), is);
    }

    /**
     * Обработка вложений письма, если вложение было обработано, новое вложение не создаётся
     */
    @Test
    public void findAndProcessDataSource() throws Exception
    {
        InputStream is = getClass().getResourceAsStream("utf8.mbox");
        MimeMessage message = new MimeMessage(null, is);
        MailParser parser = new MailParser(message, configurationProperties, mailProcessHelper, messageFacade);

        MimeBodyPart mimeBodyPart = new MimeBodyPart();
        mimeBodyPart.setContentID("test");
        ByteArrayDataSource dataSource = new ByteArrayDataSource(new byte[] {}, "test");
        parser.getDataSourceMap().put(mimeBodyPart.getContentID(), dataSource);

        MimeBodyPart mimeBodyPart2 = new MimeBodyPart();
        mimeBodyPart2.setContentID(null);
        ByteArrayDataSource dataSource2 = new ByteArrayDataSource(new byte[] {}, "test");
        parser.getDataSourceMap().put(String.valueOf(mimeBodyPart2.hashCode()), dataSource2);

        DataSource findDataSource = parser.processDataSource(null, mimeBodyPart);
        Assert.assertEquals(dataSource, findDataSource);

        DataSource findDataSource2 = parser.processDataSource(null, mimeBodyPart2);
        Assert.assertEquals(dataSource2, findDataSource2);
    }
}
