package ru.naumen.mailreader.server.receiver;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.ObjectInput;
import java.io.ObjectInputStream;
import java.io.ObjectOutput;
import java.io.ObjectOutputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Properties;

import org.apache.commons.mail.DataSourceResolver;
import org.apache.commons.mail.EmailConstants;
import org.apache.commons.mail.EmailException;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

import jakarta.activation.DataSource;
import jakarta.inject.Inject;
import jakarta.mail.Address;
import jakarta.mail.internet.InternetAddress;
import jakarta.mail.internet.MimeMessage;
import ru.naumen.core.server.ConfigurationProperties;
import ru.naumen.core.server.dispatch.ObjectTestUtils;
import ru.naumen.core.server.dispatch.SecurityTestHelper;
import ru.naumen.core.server.filestorage.FileService;
import ru.naumen.core.server.mail.IMimeMailWrapper;
import ru.naumen.core.server.mail.ISendMailService;
import ru.naumen.core.server.mail.MimeMailWrapper;
import ru.naumen.core.server.mail.util.DataSourceResolverImpl;
import ru.naumen.core.server.mail.util.ImageEmail;
import ru.naumen.core.server.script.spi.IScriptDtObject;
import ru.naumen.core.server.script.spi.ScriptUtils;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.dto.ISDtObject;
import ru.naumen.mailreader.server.processor.MailProcessHelper;
import ru.naumen.mailsender.server.outgoingmailserver.MailSettingsService;
import ru.naumen.mailsender.server.service.SimpleSendMailServiceImpl;
import ru.naumen.mailsender.shared.MailSenderConstants;
import ru.naumen.metainfo.shared.elements.mail.OutgoingMailServerConfig;


/**
 * Тестирование механизма отправки письма с иcпользованием Java Mail и Commons Email
 * <AUTHOR>
 * @since 12.11.20013
 *
 */
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class MailSenderJDKTest
{
    @Inject
    private PlatformTransactionManager txManager;
    @Inject
    ScriptUtils scriptUtils;
    @Inject
    private ISendMailService simpleSendMail;
    @Inject
    private MailSettingsService mailSettings;
    @Inject
    private ObjectTestUtils testUtils;
    @Inject
    private FileService fileService;
    @Inject
    private ConfigurationProperties configurationProperties;
    @Inject
    private MessageFacade messageFacade;
    @Inject
    private MailProcessHelper mailProcessHelper;

    /**
     * Отправка письма с помощью apache commons email
     * (аттач и инлайн прикрепляются отдельными методами)
     */
    @Test
    @Transactional
    public void testSendMailNewWay() throws Exception
    {
        SecurityTestHelper.autenticateAsSuperUser();
        OutgoingMailServerConfig serverConfig = mailSettings
                .getOutgoingMailServerConfigOrDefault(MailSenderConstants.DEFAULT_CONFIG_CODE);
        IMimeMailWrapper message = simpleSendMail.createMail();
        message.setSubject("Тема сообщения");
        IUUIDIdentifiable fileObj = testUtils.addFile(false);
        ISDtObject file = scriptUtils.get(fileObj.getUUID());
        DataSource source = scriptUtils.getFileDataSource((IScriptDtObject)file);
        message.enableApacheCommonsEmail();
        message.setInline(source);
        message.attachFile(source);
        message.setText("text <img src='" + source.getName() + "'></img>text");
        message.addTo("test", "<EMAIL>");
        message.setFrom("test", "<EMAIL>");
        ImageEmail email = ((MimeMailWrapper)message).email();
        email.setHostName(serverConfig.getHost());
        email.setAuthentication(serverConfig.getUsername(), serverConfig.getPassword());
        email.buildMimeMessage();
        if (email instanceof ImageEmail)
        {
            DataSourceResolver resolver = email.getDataSourceResolver();
            Assert.assertEquals(((DataSourceResolverImpl)resolver).getAttachments().size(), 1);
            Assert.assertEquals(((DataSourceResolverImpl)resolver).getAttachments().size(), 1);
        }
        else
        {
            throw new Exception("Wrong type of commons email message");
        }
    }

    /**
    *  Отправка письма с помощью Java Mail
    *  (аттач и инлайн прикрепляются одним методом, 
    *  используется прежний алгоритм отделения аттача от инлайн)
    */
    @Test
    public void testSendMailOldWay() throws Exception
    {
        SecurityTestHelper.autenticateAsSuperUser();
        IMimeMailWrapper message = simpleSendMail.createMail();
        message.setSubject("Тема сообщения");
        IUUIDIdentifiable fileObj = testUtils.addFile(false);
        ISDtObject file = scriptUtils.get(fileObj.getUUID());
        DataSource source = scriptUtils.getFileDataSource((IScriptDtObject)file);
        message.attachFile(source);
        message.setText("text <img src='" + source.getName() + "'></img>text");
        message.addTo("test", "<EMAIL>");
        message.setFrom("test", "<EMAIL>");
        JavaMailSender mailSender = new JavaMailSenderImpl();
        MimeMessage email = ((MimeMailWrapper)message).toMimeMessage(mailSender);
        MailParser parser = new MailParser(email, configurationProperties, mailProcessHelper, messageFacade);
        parser.parse();
        Assert.assertEquals(0, parser.getAttachmentList().size());
        Assert.assertEquals(1, parser.getInlineList().size());
    }

    @Test
    public void testSendMailWithAbsentImage() throws Exception
    {
        SecurityTestHelper.autenticateAsSuperUser();
        OutgoingMailServerConfig serverConfig = mailSettings
                .getOutgoingMailServerConfigOrDefault(MailSenderConstants.DEFAULT_CONFIG_CODE);
        String imgPath = "file:///";
        IMimeMailWrapper message = simpleSendMail.createMail();
        message.setContentType(MimeMailWrapper.TEXT_HTML);
        message.setSubject("Тема сообщения");
        message.setText("text <img src='" + imgPath + "'></img>text");
        message.addTo("test", "<EMAIL>");
        message.setFrom("test", "<EMAIL>");
        ImageEmail email = ((MimeMailWrapper)message).email();
        email.setHostName(serverConfig.getHost());
        email.setAuthentication(serverConfig.getUsername(), serverConfig.getPassword());

        try
        {
            //DataSourceResolver.resolve вызовется внутри метода
            email.buildMimeMessage();
        }
        catch (Exception e)
        {
            throw new Exception("Can't resolve image: " + e.getMessage());
        }
    }

    @Test
    public void testSendMailWithExternalImage() throws Exception
    {
        SecurityTestHelper.autenticateAsSuperUser();
        OutgoingMailServerConfig serverConfig = mailSettings
                .getOutgoingMailServerConfigOrDefault(MailSenderConstants.DEFAULT_CONFIG_CODE);
        IMimeMailWrapper message = simpleSendMail.createMail();
        message.setContentType(MimeMailWrapper.TEXT_HTML);
        message.setSubject("Тема сообщения");
        message.addTo("test", "<EMAIL>");
        message.setFrom("test", "<EMAIL>");
        String messageText = "text <img src='https://yastatic.net/islands-page/_/UYmX3xSn-Z1WO7vNqgzY2H8bBIc.png'></img> text";
        message.setText(messageText);

        ImageEmail email = ((MimeMailWrapper)message).email();
        email.setHostName(serverConfig.getHost());
        email.setAuthentication(serverConfig.getUsername(), serverConfig.getPassword());

        //DataSourceResolver.resolve вызовется внутри метода
        email.buildMimeMessage();
        Assert.assertFalse(email.hasInlineEmbeds());
    }

    @Test
    @Transactional
    public void testSendMailWithInternalImage() throws Exception
    {
        SecurityTestHelper.autenticateAsSuperUser();
        OutgoingMailServerConfig serverConfig = mailSettings
                .getOutgoingMailServerConfigOrDefault(MailSenderConstants.DEFAULT_CONFIG_CODE);
        IUUIDIdentifiable fileObj = testUtils.addFile(false);

        IMimeMailWrapper message = simpleSendMail.createMail();
        message.setContentType(MimeMailWrapper.TEXT_HTML);
        message.setSubject("Тема сообщения");
        message.addTo("test", "<EMAIL>");
        message.setFrom("test", "<EMAIL>");
        message.setText(
                "text <img src='" + fileService.createFileDownloadLink(fileObj.getUUID()) + "'></img> text");

        final ImageEmail email = ((MimeMailWrapper)message).email();
        email.setHostName(serverConfig.getHost());
        email.setAuthentication(serverConfig.getUsername(), serverConfig.getPassword());

        TransactionTemplate tt = new TransactionTemplate(txManager);
        tt.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
        tt.execute(new TransactionCallback<Boolean>()
        {
            @Override
            public Boolean doInTransaction(TransactionStatus status)
            {
                try
                {
                    //DataSourceResolver.resolve вызовется внутри метода
                    email.buildMimeMessage();
                    return true;
                }
                catch (EmailException e)
                {
                    return false;
                }
            }
        });

        Assert.assertTrue(email.hasInlineEmbeds());
    }
    
    /**
    *  Разделение письма по количеству получателей
    */
    @Test
    public void testSplitByRecipients() throws Exception
    {
        SecurityTestHelper.autenticateAsSuperUser();
        int limit = 4;
        simpleSendMail.setMaxRecipients(limit);

        IMimeMailWrapper message = simpleSendMail.createMail();
        message.setContentType(MimeMailWrapper.TEXT_HTML);
        message.setSubject("Тема сообщения");
        message.setFrom("test", "<EMAIL>");

        String messageText = "text <img src='https://yastatic.net/islands-page/_/UYmX3xSn-Z1WO7vNqgzY2H8bBIc.png'></img> text";
        message.setText(messageText);

        message.addTo("test", "<EMAIL>");
        message.addTo("test", "<EMAIL>");
        message.addTo("test", "<EMAIL>");
        message.addCc("test", "<EMAIL>");
        message.addCc("test", "<EMAIL>");
        message.addCc("test", "<EMAIL>");
        message.addBcc("test", "<EMAIL>");
        message.addBcc("test", "<EMAIL>");
        message.addBcc("test", "<EMAIL>");

        List<IMimeMailWrapper> mailWrappers = ((SimpleSendMailServiceImpl)simpleSendMail).splitByRecipients(message);
        Assert.assertTrue(mailWrappers.size() == 3);

        List<Address> allRecipients = new ArrayList<>();
        for (IMimeMailWrapper mailWrapper : mailWrappers)
        {
            List<Address> recipients = Arrays.asList(((MimeMailWrapper)mailWrapper).getAllRecipients());
            Assert.assertTrue(recipients.size() <= limit);
            allRecipients.addAll(recipients);
        }
        for (Address address : ((MimeMailWrapper)message).getAllRecipients())
        {
            Assert.assertTrue(allRecipients.contains(address));
        }
        Assert.assertEquals(((MimeMailWrapper)message).getAllRecipients().length, allRecipients.size());
    }

    /**
    *  Проверка, что при сериализации/десериализации писем сохраняется "Имя отправителя"
    */
    @Test
    public void testSerializationEmail() throws Exception
    {
        String name = "test";
        String email = "<EMAIL>";

        IMimeMailWrapper message = simpleSendMail.createMail();
        message.setContentType(MimeMailWrapper.TEXT_HTML);
        message.setSubject(name);
        message.addTo(name, email);
        message.setFrom(name, email);
        message.setText(name);

        final ImageEmail imageEmail1 = ((MimeMailWrapper)message).email();
        ImageEmail imageEmail2 = null;

        final ByteArrayOutputStream os = new ByteArrayOutputStream();
        try (ObjectOutput out = new ObjectOutputStream(os))
        {
            out.writeObject(imageEmail1);
        }
        try (ByteArrayInputStream oa = new ByteArrayInputStream(os.toByteArray());
                ObjectInput oi = new ObjectInputStream(oa))
        {
            imageEmail2 = (ImageEmail)oi.readObject();
        }
        
        Assert.assertEquals("Имя отправителя не указано", name, imageEmail2.getFromAddress().getPersonal());
    }
    
    /**
     * Проверка, десериализации письма в "старом" формате
     */
    @Test
    public void testDeserializationEmail()
    {
        String name = "test";
        String email = "<EMAIL>";
        String message = "Ошибка десериализации письма: ";
        ImageEmail imageEmail = null;
        try (ObjectInput oi = new ObjectInputStream(getClass().getResourceAsStream("serializationEmail")))
        {
            imageEmail = (ImageEmail)oi.readObject();
        }
        catch (Exception e)
        {
            Assert.fail(message + e.getMessage());
        }
        Assert.assertEquals(message, email, imageEmail.getFromAddress().getAddress());
        Assert.assertEquals(message, null, imageEmail.getFromAddress().getPersonal());
        Assert.assertEquals(message, name, imageEmail.getSubject());

        InternetAddress to = imageEmail.getToAddresses().iterator().next();
        Assert.assertEquals(message, email, to.getAddress());
        Assert.assertEquals(message, name, to.getPersonal());
    }
        
    /**
    *  Проверка на переопределение настроек почты методом System.setProperty()
    */
    @Test
    public void testOverwriteMailParamsBySystemProperty() throws Exception
    {
        OutgoingMailServerConfig serverConfig = mailSettings
                .getOutgoingMailServerConfigOrDefault(MailSenderConstants.DEFAULT_CONFIG_CODE);
        System.setProperty(EmailConstants.MAIL_SMTP_SSL_ENABLE, Boolean.TRUE.toString());
        System.setProperty(EmailConstants.MAIL_SMTP_AUTH, Boolean.TRUE.toString());

        SecurityTestHelper.autenticateAsSuperUser();
        IMimeMailWrapper message = simpleSendMail.createMail();
        message.setContentType(MimeMailWrapper.TEXT_HTML);
        message.setSubject("Тема сообщения");
        message.setText("test");
        message.setFrom("test", "<EMAIL>");
        message.addTo("test", "<EMAIL>");

        ImageEmail email = ((MimeMailWrapper)message).email();
        email.setHostName(serverConfig.getHost());
        email.buildMimeMessage();

        Properties properties = email.getMailSession().getProperties();
        Assert.assertEquals("", Boolean.FALSE.toString(), properties.getProperty(EmailConstants.MAIL_SMTP_SSL_ENABLE));
        Assert.assertEquals("", Boolean.FALSE.toString(), properties.getProperty(EmailConstants.MAIL_SMTP_AUTH));
    }
}
