package ru.naumen.mailreader.server;

import jakarta.inject.Inject;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

/**
 * Тесты для проверки {@link MailLogService}
 * 
 * <AUTHOR>
 * @since 22 ноября 2018 г.
 */
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "/ru/naumen/core/server/dispatch/fullContext.xml")
public class MailLogServiceDbTest
{
    @Inject
    private MailLogService mailLogService;

    /**
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$67808092
     * Тестирование отсутствия NullPointerException, в случае, если сообщение конструируется для
     * письма, для которого в таблице tbl_mail_sys значение processing_rule_code установлено в null 
     */
    @Test
    public void testNoNpeWhenProcessingRuleCodeNull()
    {
        mailLogService.buildMailDescriptionMessage("mailTitle", null, "ProcessMailEvent.failure");
    }

    /**
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$67808092
     * Тестирование отсутствия NullPointerException, в случае, если сообщение конструируется для
     * письма, правило обработки для которого отсутствует в метаинформации(правило было удалено). 
     */
    @Test
    public void testNoNpeWhenProcessingRuleWasDeleted()
    {
        String deletedRuleCode = "deletedRuleCode";
        mailLogService.buildMailDescriptionMessage("mailTitle", deletedRuleCode, "ProcessMailEvent.failure");
    }
}
