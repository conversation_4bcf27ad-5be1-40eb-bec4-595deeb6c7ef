package ru.naumen.mailreader.server;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import ru.naumen.sec.server.admin.log.MailSettingsLogService;
import ru.naumen.sec.server.encryption.EncryptionServiceBean;
import ru.naumen.core.server.metastorage.impl.metainfo.MetaStorageService;
import ru.naumen.core.server.oauthtoken.OAuthTokenService;
import ru.naumen.core.server.scheduler.service.SchedulingService;
import ru.naumen.mailreader.server.inboundmailserver.InboundMailServerServiceImpl;
import ru.naumen.mailreader.shared.receiver.InboundMailServerConfig;
import ru.naumen.mailreader.shared.receiver.MailProtocol;
import ru.naumen.metainfo.server.spi.MetainfoModification;

/**
 * Тестирование сервиса входящих подключений почты.
 * <AUTHOR>
 * @since Feb 03, 2022
 */
public class InboundMailServerServiceJdkTest
{
    @Mock
    private MailProcessingInfoCache cache;
    @Mock
    private MetaStorageService metaStorage;
    @Mock
    private SchedulingService schedulingService;
    @Mock
    private MailSettingsLogService mailSettingsLogService;
    @Mock
    private MetainfoModification metainfoModification;
    @Mock
    private OAuthTokenService<?> oAuthTokenService;
    private InboundMailServerServiceImpl service;

    @Before
    public void setUp()
    {
        MockitoAnnotations.initMocks(this);
        service = new InboundMailServerServiceImpl(cache, metaStorage, schedulingService, mailSettingsLogService,
                metainfoModification, oAuthTokenService, mock(EncryptionServiceBean.class));
    }

    /**
     * Тестирования наличия вызова обновления токена только в том случае, когда конфигурация почтового сервера настроена
     * на протокол EWS с OAuth.
     */
    @Test
    public void testOAuthTokenRefresh()
    {
        InboundMailServerConfig config = new InboundMailServerConfig();
        config.setConnectionProtocol(MailProtocol.EWS);
        config.setCode("mailConfig");
        when(cache.getInboundMailServerConfig("mailConfig")).thenReturn(config);

        config.setNeedAuth(false);
        assertEquals(config, service.getInboundMailServerConfigForLog("mailConfig"));
        verify(oAuthTokenService, times(0)).refreshToken(any());
        config.setNeedAuth(true);
        assertEquals(config, service.getInboundMailServerConfigForLog("mailConfig"));
        verify(oAuthTokenService, times(1)).refreshToken(any());
    }
}
