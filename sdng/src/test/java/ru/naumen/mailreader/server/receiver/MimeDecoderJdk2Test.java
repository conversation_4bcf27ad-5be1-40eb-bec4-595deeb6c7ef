package ru.naumen.mailreader.server.receiver;

import java.io.InputStream;
import java.util.List;

import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import jakarta.mail.internet.MimeMessage;
import ru.naumen.Assert;
import ru.naumen.core.server.ConfigurationProperties;
import ru.naumen.mailreader.server.queue.IInboundMailAttachment;
import ru.naumen.sec.server.AntivirusValidationService;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.mailreader.server.processor.MailProcessHelper;
import ru.naumen.mailreader.server.queue.IInboundMailMessage;

/**
 * Тестирование MimeDecoder из mailreader
 *
 * <AUTHOR>
 * @since 07.06.2021
 */
public class MimeDecoderJdk2Test
{
    @Mock
    MessageFacade messages;

    @Mock
    ConfigurationProperties configurationProperties;

    @Mock
    AntivirusValidationService antivirusValidationService;

    private MimeDecoderApacheCommons decoder;

    @Before
    public void setUp() throws Exception
    {
        MockitoAnnotations.openMocks(this).close();
        MailProcessHelper mailProcessHelper = new MailProcessHelper();
        decoder = new MimeDecoderApacheCommons(messages, configurationProperties, mailProcessHelper);
        mailProcessHelper.setConfigurationProperties(configurationProperties);
        mailProcessHelper.setAntivirusValidationService(antivirusValidationService);
        Mockito.when(messages.getMessage(Mockito.anyString())).thenReturn("");
        Mockito.when(configurationProperties.getAcceptableExtensions()).thenReturn(List.of());
        Mockito.when(configurationProperties.getUploadFileMaxSize()).thenReturn((long)-1);
        Mockito.when(configurationProperties.getUploadFilesGroupMaxSize()).thenReturn((long)-1);
        Mockito.when(configurationProperties.isNeedCheckWrongSymbolsInSubject()).thenReturn(true);
        Mockito.doNothing().when(antivirusValidationService).verifyFile(Mockito.any(), Mockito.any(), Mockito.any());
    }

    /**
     * Тестирования парсинга неправильно разделённой темой
     */
    @Test
    public void testParseWrongPartitionTheme() throws Exception
    {
        InputStream is = getClass().getResourceAsStream("wrong_theme.eml");
        MimeMessage message = new MimeMessage(null, is);
        String subject = decoder.decode(message, false).getSubject();
        Assert.assertEquals("ABCDEF внутренняя заявка на подбор оборудования №0132-ПК от 6/5/2018 9:42:37 AM",
                subject);
    }

    /**
     * Разбор письма, тема которого состоит из нескольких строк в кодировке UTF-8
     */
    @Test
    public void testParseSubjectSeveralPartUTF8() throws Exception
    {
        InputStream is = getClass().getResourceAsStream("severalPartsSubject.eml");
        MimeMessage message = new MimeMessage(null, is);
        IInboundMailMessage inMessage = decoder.decode(message, false);
        Assert.assertEquals("Письмо в кодировке UTF-8 состоящее из нескольких частей, тк очень длинное", inMessage.getSubject());
    }

    @Test
    public void testParseSubjectWithLongSpecSymbolsWords() throws Exception
    {
        InputStream is = getClass().getResourceAsStream("long_spec_symbols_word.eml");
        MimeMessage message = new MimeMessage(null, is);
        IInboundMailMessage inMessage = decoder.decode(message, false);
        Assert.assertEquals("Акт недостачи из WMS \"МК-Партс\" по партии: ТНР МК-Партс 02042021 00 МСК ОХ Ё!\"№;%:?*"
                + "()_+/*-+{}[]:\"|<>?/?~!@#$%^&*()_", inMessage.getSubject());
    }

    /**
     * Разбор письма из outlook содержащего eml-вложение отправленное из MacOS,
     * в котором есть своё вложение - картинка jpeg.
     * Убедиться, что название файла-письма извлечено корректно.
     */
    @Test
    public void filenameOfAttachedMacOSEmlByOutlookIfHaveAttachJpeg() throws Exception
    {
        InputStream is = getClass().getResourceAsStream("outlook_msg_with_macos_eml_attach_with_jpeg_attach.eml");
        MimeMessage message = MimeDecoderJdkTest.createMessage(is);
        IInboundMailMessage inMessage = decoder.decode(message, true);
        IInboundMailAttachment attachedEml = null;
        for (IInboundMailAttachment attach : inMessage.getAttachments())
        {
            if (attach.getContentType().equalsIgnoreCase("message/rfc822"))
            {
                attachedEml = attach;
            }
        }
        Assert.assertNotNull(attachedEml);
        Assert.assertEquals("Тестовое письмо.eml", attachedEml.getFilename());
    }

    /**
     * Тестирование письма с адресом получателя содержащим точку в конце
     */
    @Test
    public void testEmailRecipientsWithDotInTheEnd() throws Exception
    {
        InputStream is = getClass().getResourceAsStream("recipients_contains_dot_in_the_end.eml");
        MimeMessage message = new MimeMessage(null, is);
        String to = decoder.decode(message, false).getToAsString();
        Assert.assertFalse(to.isEmpty());
    }

    /**
     * Тестирование корректного извлечения названия из прикреплённого к письму eml-файла при обработке входящей почты,
     * если письмо отправлено из Outlook и обработано сервером imap.yandex.ru
     * <br>
     * Проверить, что, несмотря на особенности реализации протокола IMAP со стороны imap.yandex.ru,
     * имя вложения типа message/rfc822 формируется корректно и количество вложений в письме - 1
     * <br>
     * Проверить также, что при другой реализации протокола (например, imap.gmail.com)
     * имя вложения типа message/rfc822 формируется корректно и количество вложений в письме - 1
     */
    @Test
    public void testMessageRFC822AttachmentHaveCorrectName() throws Exception
    {
        List<String> resourceNames = List.of(
                "message-rfc822-attachment(imap.yandex.ru).eml",
                "message-rfc822-attachment(imap.gmail.com).eml");

        for (String resourceName : resourceNames)
        {
            InputStream is = getClass().getResourceAsStream(resourceName);
            MimeMessage message = new MimeMessage(null, is);
            IInboundMailMessage inMessage = decoder.decode(message, false);
            String assertMessage = "Неправильно обработан " + resourceName;
            Assert.assertEquals(assertMessage,"Тест.eml", inMessage.getAttachments().getFirst().getFilename());
            Assert.assertEquals(assertMessage, 1, inMessage.getAttachments().size());
        }
    }
}
