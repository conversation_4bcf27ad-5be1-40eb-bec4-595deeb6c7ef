package ru.naumen.mailreader.server.receiver;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;

import java.util.Properties;

import org.eclipse.angus.mail.util.MailSSLSocketFactory;
import org.junit.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import ru.naumen.Assert;
import ru.naumen.core.server.SpringContext;
import ru.naumen.core.server.mail.SSLSocketMailConfigurator;
import ru.naumen.mailreader.server.MailreaderBeansConfig;
import ru.naumen.mailreader.shared.receiver.InboundMailServerConfig;

/**
 * Тестирование {@link MailReceiverImplBase}
 * <AUTHOR>
 * @since 26.08.19
 */
public class MailReceiverImplBaseJdkTest
{
    /**
     * Проверка создания сессии с игнорированием проверки сертификата для POP3S
     */
    @Test
    public void testCreateSessionWithSkipCertVerificationForSslPop()
    {
        try (MockedStatic<SpringContext> springContextMockedStatic = Mockito.mockStatic(SpringContext.class))
        {
            SpringContext springContext = mock(SpringContext.class);
            when(springContext.getBean(DumpWriter.class)).thenReturn(mock(DumpWriter.class));
            when(springContext.getBean(SSLSocketMailConfigurator.class)).thenReturn(
                    mock(SSLSocketMailConfigurator.class));
            springContextMockedStatic.when(SpringContext::getInstance).thenReturn(springContext);
            MailreaderBeansConfig mailreaderBeansConfig = mock(MailreaderBeansConfig.class);
            InboundMailServerConfig inboundMailServerConfig = new InboundMailServerConfig();
            inboundMailServerConfig.setSkipCertVerification(true);
            inboundMailServerConfig.setSSL(true);

            MailReceiverImplBase mailReceiverImplBase = spy(new PopMailReceiverImpl(mailreaderBeansConfig,
                    inboundMailServerConfig));
            when(mailReceiverImplBase.getProtocolName()).thenReturn("pop3");

            Assert.assertTrue(Boolean.parseBoolean(
                    mailReceiverImplBase.getSession().getProperties().get("mail.pop3.ssl.enable").toString()));
            Assert.assertEquals(mailReceiverImplBase.getSession().getProperties().get("mail.pop3"
                    + ".ssl.socketFactory").getClass(), MailSSLSocketFactory.class);
        }
    }

    /**
     * Проверка создания сессии без игнорирования проверки сертификата для POP3S
     */
    @Test
    public void testCreateSessionWithoutSkipCertVerificationForSslPop()
    {
        try (MockedStatic<SpringContext> springContextMockedStatic = Mockito.mockStatic(SpringContext.class))
        {
            SpringContext springContext = mock(SpringContext.class);
            when(springContext.getBean(DumpWriter.class)).thenReturn(mock(DumpWriter.class));
            when(springContext.getBean(SSLSocketMailConfigurator.class)).thenReturn(
                    mock(SSLSocketMailConfigurator.class));
            springContextMockedStatic.when(SpringContext::getInstance).thenReturn(springContext);
            MailreaderBeansConfig mailreaderBeansConfig = mock(MailreaderBeansConfig.class);
            InboundMailServerConfig inboundMailServerConfig = new InboundMailServerConfig();
            inboundMailServerConfig.setSkipCertVerification(false);
            inboundMailServerConfig.setSSL(false);

            MailReceiverImplBase mailReceiverImplBase = spy(new PopMailReceiverImpl(mailreaderBeansConfig,
                    inboundMailServerConfig));
            when(mailReceiverImplBase.getProtocolName()).thenReturn("pop3");

            Assert.assertNull(mailReceiverImplBase.getSession().getProperties().get("mail.pop3.ssl"
                    + ".enable"));
            Assert.assertNull(mailReceiverImplBase.getSession().getProperties().get("mail.pop3"
                    + ".ssl.socketFactory"));
        }
    }

    /**
     * Проверка создания сессии с игнорированием проверки сертификата для IMAP4S
     */
    @Test
    public void testCreateSessionWithSkipCertVerificationForSslImap()
    {
        try (MockedStatic<SpringContext> springContextMockedStatic = Mockito.mockStatic(SpringContext.class))
        {
            SpringContext springContext = mock(SpringContext.class);
            when(springContext.getBean(DumpWriter.class)).thenReturn(mock(DumpWriter.class));
            when(springContext.getBean(SSLSocketMailConfigurator.class)).thenReturn(
                    mock(SSLSocketMailConfigurator.class));
            springContextMockedStatic.when(SpringContext::getInstance).thenReturn(springContext);
            MailreaderBeansConfig mailreaderBeansConfig = mock(MailreaderBeansConfig.class);
            InboundMailServerConfig inboundMailServerConfig = new InboundMailServerConfig();
            inboundMailServerConfig.setSkipCertVerification(true);
            inboundMailServerConfig.setSSL(true);
            inboundMailServerConfig.setFolders("");

            MailReceiverImplBase mailReceiverImplBase = spy(new ImapMailReceiverImpl(mailreaderBeansConfig,
                    inboundMailServerConfig));
            when(mailReceiverImplBase.getProtocolName()).thenReturn("imap");

            Assert.assertTrue(Boolean.parseBoolean(
                    mailReceiverImplBase.getSession().getProperties().get("mail.imap.ssl.enable").toString()));
            Assert.assertEquals(mailReceiverImplBase.getSession().getProperties().get("mail.imap"
                    + ".ssl.socketFactory").getClass(), MailSSLSocketFactory.class);
        }
    }

    /**
     * Проверка создания сессии по протоколу IMAP с использованием общего почтового ящика
     */
    @Test
    public void testCreateSessionWithSharedEmailImapProtocol()
    {
        try (MockedStatic<SpringContext> springContextMockedStatic = Mockito.mockStatic(SpringContext.class))
        {
            SpringContext springContext = mock(SpringContext.class);
            when(springContext.getBean(DumpWriter.class)).thenReturn(mock(DumpWriter.class));
            when(springContext.getBean(SSLSocketMailConfigurator.class)).thenReturn(
                    mock(SSLSocketMailConfigurator.class));
            springContextMockedStatic.when(SpringContext::getInstance).thenReturn(springContext);
            MailreaderBeansConfig mailreaderBeansConfig = mock(MailreaderBeansConfig.class);
            InboundMailServerConfig inboundMailServerConfig = new InboundMailServerConfig();
            inboundMailServerConfig.setSkipCertVerification(true);
            inboundMailServerConfig.setSSL(true);
            inboundMailServerConfig.setFolders("");
            inboundMailServerConfig.setSharedMailbox("test@test");

            MailReceiverImplBase mailReceiverImplBase = spy(new ImapMailReceiverImpl(mailreaderBeansConfig,
                    inboundMailServerConfig));
            Properties props = new Properties();
            mailReceiverImplBase.customizeConfiguration(props, inboundMailServerConfig);

            Assert.assertTrue(Boolean.parseBoolean(props.getProperty("mail.imap.auth.plain.disable")));
            Assert.assertTrue(Boolean.parseBoolean(props.getProperty("mail.imap.auth.ntlm.disable")));
        }
    }

    /**
     * Проверка создания сессии без игнорирования проверки сертификата для IMAP4S
     */
    @Test
    public void testCreateSessionWithoutSkipCertVerificationForSslImap()
    {
        try (MockedStatic<SpringContext> springContextMockedStatic = Mockito.mockStatic(SpringContext.class))
        {
            SpringContext springContext = mock(SpringContext.class);
            when(springContext.getBean(DumpWriter.class)).thenReturn(mock(DumpWriter.class));
            when(springContext.getBean(SSLSocketMailConfigurator.class)).thenReturn(
                    mock(SSLSocketMailConfigurator.class));
            springContextMockedStatic.when(SpringContext::getInstance).thenReturn(springContext);
            MailreaderBeansConfig mailreaderBeansConfig = mock(MailreaderBeansConfig.class);
            InboundMailServerConfig inboundMailServerConfig = new InboundMailServerConfig();
            inboundMailServerConfig.setSkipCertVerification(false);
            inboundMailServerConfig.setSSL(false);
            inboundMailServerConfig.setFolders("");

            MailReceiverImplBase mailReceiverImplBase = spy(new PopMailReceiverImpl(mailreaderBeansConfig,
                    inboundMailServerConfig));
            when(mailReceiverImplBase.getProtocolName()).thenReturn("imap");

            Assert.assertNull(mailReceiverImplBase.getSession().getProperties().get("mail.imap.ssl"
                    + ".enable"));
            Assert.assertNull(mailReceiverImplBase.getSession().getProperties().get("mail.imap"
                    + ".ssl.socketFactory"));
        }
    }
}
