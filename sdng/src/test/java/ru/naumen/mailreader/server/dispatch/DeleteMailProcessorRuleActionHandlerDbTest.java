package ru.naumen.mailreader.server.dispatch;

import java.util.ArrayList;

import jakarta.inject.Inject;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import ru.naumen.core.server.dispatch.Dispatch;
import ru.naumen.core.server.dispatch.ObjectTestUtils;
import ru.naumen.core.server.dispatch.SecurityTestHelper;
import ru.naumen.core.server.metastorage.impl.metainfo.MetaStorageService;
import ru.naumen.core.server.script.storage.ScriptStorageService;
import ru.naumen.mailreader.shared.Constants;
import ru.naumen.mailreader.shared.dispatch.DeleteMailProcessorRuleAction;
import ru.naumen.mailreader.shared.dispatch.SaveMailProcessorRuleAction;
import ru.naumen.mailreader.shared.receiver.MailProcessorRule;
import ru.naumen.metainfo.shared.dispatch2.script.DeleteScriptAction;
import ru.naumen.metainfo.shared.dispatch2.script.SimpleScriptedResult;
import ru.naumen.metainfo.shared.script.ScriptDto;
import ru.naumen.metainfo.shared.ui.LocalizedString;
import ru.naumen.uniquerandom.UniqueNumbersGenerator;

@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class DeleteMailProcessorRuleActionHandlerDbTest
{
    @Inject
    private Dispatch dispatch;
    @Inject
    private MetaStorageService metaStorage;
    @Inject
    private ScriptStorageService scriptStorageService;
    @Inject
    private ObjectTestUtils utils;

    @Before
    public void setUp()
    {
        SecurityTestHelper.autenticateAsSuperUser();
    }

    /**
     * Проверка на то, что информация о скрипте удаляется из кэша информации о скриптах при
     * удалении правила обработки почты.
     * @throws Exception 
     */
    @Test
    public void testScriptInfoShouldBeObliteratedOnDeletion() throws Exception
    {
        //Подготовка и выполнение
        MailProcessorRule mailProcessorRule = new MailProcessorRule();
        ScriptDto script = utils.createScriptDto("true");

        ArrayList<LocalizedString> titles = mailProcessorRule.getTitle();
        String titleString = "rule" + UniqueNumbersGenerator.nextInt(10000);
        LocalizedString title = new LocalizedString("ru", titleString);
        titles.add(title);

        SaveMailProcessorRuleAction saveAction = new SaveMailProcessorRuleAction(mailProcessorRule, script);
        saveAction.setWithScripts(true);
        SimpleScriptedResult<MailProcessorRule> result = dispatch.execute(saveAction);
        MailProcessorRule savedRule = result.get();

        MailProcessorRule serverRule = metaStorage.get(Constants.MAIL_PROCESSOR_RULE, savedRule.getCode());
        String scriptCode = serverRule.getScript();

        Assert.assertFalse("Место использования должно быть добавлено в скрипт",
                scriptStorageService.getScript(scriptCode).getUsagePoints().isEmpty());

        DeleteMailProcessorRuleAction deleteAction = new DeleteMailProcessorRuleAction(savedRule);
        dispatch.execute(deleteAction);
        //Проверка
        Assert.assertTrue("Место использования должно быть удалено из скрипта",
                scriptStorageService.getScript(scriptCode).getUsagePoints().isEmpty());

        dispatch.execute(new DeleteScriptAction(scriptCode));
    }

}
