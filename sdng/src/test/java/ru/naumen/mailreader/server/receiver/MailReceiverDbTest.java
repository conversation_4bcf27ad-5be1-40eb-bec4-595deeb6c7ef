package ru.naumen.mailreader.server.receiver;

import java.io.File;
import java.util.stream.Stream;

import jakarta.inject.Inject;
import jakarta.mail.internet.MimeMessage;

import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import ru.naumen.core.server.mail.IMimeMailWrapper;
import ru.naumen.core.server.mail.ISendMailService;
import ru.naumen.core.server.mail.MimeMailWrapper;
import ru.naumen.core.shared.TestUtils;
import ru.naumen.mailreader.server.MailreaderBeansConfig;
import ru.naumen.mailreader.shared.receiver.InboundMailServerConfig;

/**
 * Тестирование методов класса MailReceiver
 * <AUTHOR>
 * @since 25.06.2018
 */
@SuppressWarnings("deprecation")
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class MailReceiverDbTest
{
    @Inject
    private MailreaderBeansConfig mailreaderConfig;
    @Inject
    private ISendMailService simpleSendMail;
    @Inject
    private DumpWriter dumper;

    /**
     * Тест на сохранение нескольких писем с одинаковой темой.
     * @throws Exception
     */
    @Ignore
    @Test
    public void testMessageFileNameSavedToDisk() throws Exception
    {
        InboundMailServerConfig config = new InboundMailServerConfig();
        config.setCode(TestUtils.randomString());
        MailReceiverImplBase receiver = new PopMailReceiverImpl(mailreaderConfig, config);
        IMimeMailWrapper message = simpleSendMail.createMail();
        final String subject = TestUtils.randomString();
        message.setSubject(subject);
        message.addTo("test", "<EMAIL>");
        message.setFrom("test", "<EMAIL>");
        message.setText(TestUtils.randomString());
        JavaMailSender mailSender = new JavaMailSenderImpl();
        MimeMessage email = ((MimeMailWrapper)message).toMimeMessage(mailSender);
        Assert.assertNotNull("Failed to save message.", receiver.saveMessageToDisk(email));
        Assert.assertNotNull("Failed to save message.", receiver.saveMessageToDisk(email));
        String path = dumper.dir + File.separator + "pop3_received" + File.separator;
        long emailsAmount = Stream.of(new File(path).listFiles()).filter(input -> input.getName().contains(subject))
                .count();
        Assert.assertEquals("Amount of saved messages and amount of files on disk are not equal.", 2, emailsAmount);
    }
}
