package ru.naumen.mailreader.server.processor.scripted;

import java.io.InputStream;
import java.util.Collections;
import java.util.HashSet;
import java.util.Properties;
import java.util.Set;
import java.util.stream.Collectors;

import jakarta.inject.Inject;
import jakarta.mail.Session;
import jakarta.mail.internet.MimeMessage;

import com.google.common.collect.Sets;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

import ru.naumen.common.CreatedListener;
import ru.naumen.common.shared.utils.DateTimeInterval;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.server.bo.agreement.Agreement;
import ru.naumen.core.server.bo.ou.OU;
import ru.naumen.core.server.dispatch.ObjectTestUtils;
import ru.naumen.core.server.dispatch.SecurityTestUtils;
import ru.naumen.core.server.script.api.IMailApi;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.utils.UUIDGenerator;
import ru.naumen.mailreader.server.processor.MailProcessHelper;
import ru.naumen.mailreader.server.queue.IInboundMailAttachment;
import ru.naumen.mailreader.server.queue.IInboundMailMessage;
import ru.naumen.mailreader.server.receiver.MimeDecoderApacheCommons;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.DateTimeIntervalAttributeType.Interval;

/**
 * Тесты для проверки функционала {@link MailApi}.
 * 
 * <AUTHOR>
 * @since 03.10.14
 */
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class MailApiDbTest
{
    @Inject
    MimeDecoderApacheCommons decoder;
    @Inject
    IMailApi mailApi;
    @Inject
    ObjectTestUtils testUtils;
    @Inject
    SecurityTestUtils securityTestUtils;
    @Inject
    CreatedListener createdListener;
    @Inject
    MailProcessHelper mailProcessHelper;

    @Inject
    PlatformTransactionManager txManager;

    /**
     * Прикрепление вложения методом api.mail.attachMessageAttachments() при передаче в ContentType длиной строки содержащей ContentType
     * @throws Exception
     */
    @Test
    public void attachAttachementWithWrongContentTypeLength() throws Exception
    {
        // настройка системы
        InputStream is = getClass().getResourceAsStream("/ru/naumen/core/server/script/spi/wrongContentType.eml");
        MimeMessage message = createMessage(is);

        // вызов системы
        final IInboundMailMessage inMessage = decoder.decode(message, false);
        final OU ou = testUtils.createOU();
        TransactionTemplate tt = new TransactionTemplate(txManager);
        int attachmentsSize = tt.execute(new TransactionCallback<Integer>()
        {
            @Override
            public Integer doInTransaction(TransactionStatus status)
            {
                return mailApi.attachAllMessageAttachments(ou, inMessage).size();
            }
        });
        // проверка утверждений
        Assert.assertEquals(1, attachmentsSize);
    }

    @Before
    public void setUp() throws Exception
    {
        securityTestUtils.autenticateAsSuperUser();
        securityTestUtils.initLicensing();
        createdListener.setUp();
    }

    @Test
    public void testAddInlineXmlAttachement() throws Exception
    {
        // настройка системы
        InputStream is = getClass().getResourceAsStream(
                "/ru/naumen/mailreader/server/receiver/test_with_inline_xml.eml");
        MimeMessage message = createMessage(is);
        // вызов системы
        final IInboundMailMessage inMessage = decoder.decode(message, false);
        final OU ou = testUtils.createOU();
        TransactionTemplate tt = new TransactionTemplate(txManager);
        int attachmentsSize = tt.execute(new TransactionCallback<Integer>()
        {
            @Override
            public Integer doInTransaction(TransactionStatus status)
            {
                return mailApi.attachAllMessageAttachments(ou, inMessage).size();
            }
        });
        // проверка утверждений
        Assert.assertEquals(1, attachmentsSize);
    }

    /**
     * Проверка метода decoder.decode(message, false), формирующего Content-ID для каждого файла,
     * у которого Content-Disposition: attachment.
     */
    @Test
    public void testAddWithContentDispositionAttachmentXml() throws Exception
    {
        Set<String> goodContentIds = new HashSet<String>(){{
            add("dmsLogo");
            add("mailLogo");
            add("mtsLogo");
            add("velcomLogo");
            add("tdPhone");
        }};
        // настройка системы
        InputStream is = getClass().getResourceAsStream(
                "/ru/naumen/mailreader/server/receiver/test_with_attachment_xml.eml");
        MimeMessage message = createMessage(is);
        // вызов системы
        final IInboundMailMessage inMessage = decoder.decode(message, false);
        Set<IInboundMailAttachment> IInboundMailAttachments = inMessage.getNotInlineAttachments();
        Set<String> result = IInboundMailAttachments.stream()
                .map(IInboundMailAttachment::getContentId)
                .collect(Collectors.toSet());
        // проверка утверждений
        Assert.assertEquals(5, IInboundMailAttachments.size());
        Assert.assertEquals(goodContentIds, result);
    }

    /**
     * Проверка метода obtainOriginalMessageData(), формирующего MIME сообщение.
     */
    @Test
    public void testAttachMessage() throws Exception
    {
        // настройка системы
        InputStream is = getClass().getResourceAsStream(
                "/ru/naumen/mailreader/server/receiver/test_attach_pdf_file.eml");
        MimeMessage message = createMessage(is);
        // вызов системы и проверка утверждений
        IInboundMailMessage inMessage = decoder.decode(message, false);
        Assert.assertNull(inMessage.getOriginalMessage());
        inMessage.setOriginalMessage(mailProcessHelper.obtainOriginalMessageData(inMessage));
        Assert.assertNotNull(inMessage.getOriginalMessage());
    }

    /**
     * Проверка метода obtainOriginalMessageData(), формирующего MIME сообщение для случая,
     * когда в письме есть изображение с цветовым профилем CMYK.
     */
    @Test
    public void testAttachMessageWithCmykProfileImage() throws Exception
    {
        // настройка системы
        InputStream is = getClass().getResourceAsStream(
                "/ru/naumen/mailreader/server/receiver/cmyk.eml");
        MimeMessage message = createMessage(is);
        // вызов системы и проверка утверждений
        IInboundMailMessage inMessage = decoder.decode(message, false);
        Assert.assertNull(inMessage.getOriginalMessage());
        inMessage.setOriginalMessage(mailProcessHelper.obtainOriginalMessageData(inMessage));
        Assert.assertNotNull(inMessage.getOriginalMessage());
    }

    /**
     * Разбор вложений письма. Проверка на содержание файла с недопустимым расширением exe.
     */
    @Test
    public void testMessageHasBadAttachmentsExe() throws Exception
    {
        Set<String> acceptableExtensions = Collections.emptySet();
        Set<String> exceptableExtensions = Sets.newHashSet("exe");
        // настройка системы
        InputStream is = getClass().getResourceAsStream(
                "/ru/naumen/mailreader/server/receiver/test_attach_pdf_file.eml");
        MimeMessage message = createMessage(is);
        // вызов системы
        IInboundMailMessage inMessage = decoder.decode(message, false);
        // проверка утверждений
        boolean hasBadAttachment = mailApi.hasBadAttachment(inMessage, acceptableExtensions, exceptableExtensions);
        Assert.assertFalse(hasBadAttachment);
        // очистка не требуется
    }

    /**
     * Разбор вложений письма. Проверка на содержание файлов с недопустимыми расширениями pdf, exe.
     */
    @Test
    public void testMessageHasBadAttachmentsPdfExe() throws Exception
    {
        Set<String> acceptableExtensions = Collections.emptySet();
        Set<String> exceptableExtensions = Sets.newHashSet("pdf", "exe");
        // настройка системы
        InputStream is = getClass().getResourceAsStream(
                "/ru/naumen/mailreader/server/receiver/test_attach_pdf_file.eml");
        MimeMessage message = createMessage(is);
        // вызов системы
        IInboundMailMessage inMessage = decoder.decode(message, false);
        // проверка утверждений
        boolean hasBadAttachment = mailApi.hasBadAttachment(inMessage, acceptableExtensions, exceptableExtensions);
        Assert.assertTrue(hasBadAttachment);
        // очистка не требуется
    }

    /**
     * Разбор вложений письма. Проверка на содержание файлов с недопустимыми расширениями.
     * Допустимые расширения: jpg, doc.
     */
    @Test
    public void testMessageHasBadAttachmentsWhenAllowedJpgDoc() throws Exception
    {
        Set<String> acceptableExtensions = Sets.newHashSet("jpg", "doc");
        Set<String> exceptableExtensions = Collections.emptySet();
        // настройка системы
        InputStream is = getClass().getResourceAsStream(
                "/ru/naumen/mailreader/server/receiver/test_attach_pdf_file.eml");
        MimeMessage message = createMessage(is);
        // вызов системы
        IInboundMailMessage inMessage = decoder.decode(message, false);
        // проверка утверждений
        boolean hasBadAttachment = mailApi.hasBadAttachment(inMessage, acceptableExtensions, exceptableExtensions);
        Assert.assertTrue(hasBadAttachment);
        // очистка не требуется
    }

    /**
     * Разбор вложений письма. Проверка на содержание файлов с недопустимыми расширениями.
     * Допустимые расширения: jpg, doc, pdf.
     */
    @Test
    public void testMessageHasBadAttachmentsWhenAllowedJpgDocPdf() throws Exception
    {
        Set<String> acceptableExtensions = Sets.newHashSet("jpg", "doc", "pdf");
        Set<String> exceptableExtensions = Collections.emptySet();
        // настройка системы
        InputStream is = getClass().getResourceAsStream(
                "/ru/naumen/mailreader/server/receiver/test_attach_pdf_file.eml");
        MimeMessage message = createMessage(is);
        // вызов системы
        IInboundMailMessage inMessage = decoder.decode(message, false);
        // проверка утверждений
        boolean hasBadAttachment = mailApi.hasBadAttachment(inMessage, acceptableExtensions, exceptableExtensions);
        Assert.assertFalse(hasBadAttachment);
        // очистка не требуется
    }

    /**
     * Проверка метода notifyMailReceived() отправки оповещения о результатах обработки входящей почты.
     */
    @Test
    public void testNotifyMailReceived() throws Exception
    {
        // настройка системы
        IUUIDIdentifiable priorityCatalogItem = testUtils.create(Constants.PriorityCatalog.ITEM_FQN,
                CollectionUtils.<String, Object> map("code", UUIDGenerator.get().nextUUID(), "title", "Низкий"));
        ClassFqn scFqn = testUtils.createCase(Constants.ServiceCall.FQN);
        OU ou = testUtils.createOU();
        Agreement agreement = testUtils.createAgreement();
        testUtils.setObjectRecipientAgreements(ou, agreement);
        IUUIDIdentifiable employee = testUtils.createEmployee(ou);
        testUtils.setObjectRecipientAgreements(employee, agreement);
        testUtils.editAttribute(scFqn, Constants.ServiceCall.PRIORITY, priorityCatalogItem);
        testUtils.editAttribute(scFqn, Constants.ServiceCall.RESOLUTION_TIME, new DateTimeInterval(1, Interval.DAY));
        IUUIDIdentifiable sc = testUtils.createServiceCall(scFqn, ou, agreement);
        // вызов системы и проверка утверждений
        try
        {
            mailApi.notifyMailReceived(sc);
        }
        catch (Exception e)
        {
            Assert.fail();
        }
    }

    private static MimeMessage createMessage(InputStream is) throws Exception
    {
        return new MimeMessage(Session.getInstance(new Properties()), is);
    }
}