package ru.naumen.mailreader.server.processor.scripted;

import static org.mockito.Mockito.*;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

import ru.naumen.common.server.config.SilentModeSettingsProvider;
import ru.naumen.commons.server.utils.RandomTools;
import ru.naumen.core.server.SpringContext;
import ru.naumen.core.server.mail.ISendMailService;
import ru.naumen.core.server.script.api.IMailApi;
import ru.naumen.core.shared.TestUtils;
import ru.naumen.mailreader.server.MailreaderBeansConfig;
import ru.naumen.mailreader.server.inboundmailserver.InboundMailServerService;
import ru.naumen.mailreader.server.processor.MailProcessHelper;
import ru.naumen.mailsender.server.MailBeansConfig;
import ru.naumen.metainfo.server.spi.mail.MailUtils;

public class MailApiJDKTest
{
    private IMailApi mailApi;

    @Before
    public void setUp()
    {
        mailApi = new MailApi(
                mock(ISendMailService.class),
                mock(ISendMailService.class),
                mock(MailProcessHelper.class),
                mock(SilentModeSettingsProvider.class),
                mock(SpringContext.class),
                mock(MailreaderBeansConfig.class),
                mock(MailBeansConfig.class),
                mock(InboundMailServerService.class));
    }

    @Test
    public void testGetNumberWithPrefixRegexPrefix()
    {
        String number = Integer.toString(Math.abs(RandomTools.getIntRandom()));
        String prefix = "d+";
        String actual = mailApi.getNumberWithPrefix(prefix + " " + number, prefix);
        Assert.assertEquals(number, actual);

        actual = mailApi.getNumberWithPrefix(TestUtils.randomString() + prefix + " " + number, prefix);
        Assert.assertEquals(number, actual);
    }

    @Test
    public void testGetNumberWithPrefixRegexPrefixWithoutSpace()
    {
        String number = Integer.toString(Math.abs(RandomTools.getIntRandom()));
        String prefix = "{1}\\d+";
        String actual = mailApi.getNumberWithPrefix(prefix + number, prefix, false);
        Assert.assertEquals(number, actual);
        actual = mailApi.getNumberWithPrefix(prefix + number + TestUtils.randomString(), prefix, false);
        Assert.assertEquals(number, actual);
    }
    
    /**
     * Тестирование метода ru.naumen.metainfo.server.spi.mail.MailUtils.convertLoginToASCII()
     * Корректность конвертации Unicode-символы в логине в ACE (ASCII Compatible Encoding)
     * 
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$59824508
     * 
     */
    @Test
    public void testIsInvalidEmail()
    {
        Assert.assertEquals("<EMAIL>", MailUtils.convertLoginToASCII("<EMAIL>"));
        Assert.assertEquals("<EMAIL>", MailUtils.convertLoginToASCII("<EMAIL>"));
        Assert.assertEquals("<EMAIL>", MailUtils.convertLoginToASCII("<EMAIL>"));
        Assert.assertEquals("<EMAIL>", MailUtils.convertLoginToASCII("<EMAIL>"));
        Assert.assertEquals("<EMAIL>--p1ai", MailUtils.convertLoginToASCII("abcde@русскоязычныйдомен.рф"));
        Assert.assertEquals("<EMAIL>--p1ai", MailUtils.convertLoginToASCII("абвгд@русскоязычныйдомен.рф"));
        Assert.assertEquals("<EMAIL>", MailUtils.convertLoginToASCII("абвгд@somedomain.com"));
        Assert.assertEquals("<EMAIL>--p1ai", MailUtils.convertLoginToASCII("test@наумен.рф"));
        Assert.assertEquals("<EMAIL>--p1ai", MailUtils.convertLoginToASCII("тест@наумен.рф"));
    }
}
