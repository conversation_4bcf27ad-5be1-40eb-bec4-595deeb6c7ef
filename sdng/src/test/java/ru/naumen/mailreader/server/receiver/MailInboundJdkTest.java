package ru.naumen.mailreader.server.receiver;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import java.util.List;

import org.junit.Assert;
import java.io.IOException;
import java.util.Map;

import org.junit.Test;

import ru.naumen.core.shared.constants.FileConstants;
import ru.naumen.mailreader.server.processor.MailProcessHelper;
import ru.naumen.mailreader.server.queue.IInboundMailAttachment;
import ru.naumen.mailreader.server.queue.IInboundMailMessage;
import ru.naumen.mailreader.server.queue.InboundMailAttachment;
import ru.naumen.mailreader.server.queue.InboundMailMessage;

/**
 * Тестирование механизма получения письма
 * <AUTHOR>
 * @since 31.05.2021
 */
public class MailInboundJdkTest
{
    /**
     * Разбор письма с вложением, имя которого больше 255 символов
     */
    @Test
    public void testSubstringMaxLengthFileName()
    {
        IInboundMailAttachment attachedEml = mock(IInboundMailAttachment.class);
        when(attachedEml.getFilename()).thenReturn(
                "Тестовое письмо с очень длинной темой, тестовое письмо с очень длинной темой, тестовое письмо с "
                        + "очень длинной темой, тестовое письмо с очень длинной темой, тестовое письмо с очень "
                        + "длинной темой, тестовое письмо с очень длинной темой, тестовое письмо с очень длинной "
                        + "темой.eml");
        String result = MailProcessHelper.processFileName(attachedEml);
        Assert.assertFalse(result.length() != FileConstants.MAX_TITLE_LENGTH);
        Assert.assertEquals(result,
                "Тестовое письмо с очень длинной темой, тестовое письмо с очень длинной темой, тестовое письмо с "
                        + "очень длинной темой, тестовое письмо с очень длинной темой, тестовое письмо с очень "
                        + "длинной темой, тестовое письмо с очень длинной темой, тестовое письмо с.eml");
    }

    /**
     * Тестирование корректного русского имени файла во вложениях почты.<br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00360 <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$134355715 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Формируем вложение для почты attachment.</li>
     * <li>Задаем закодированное имя вложения в UTF-8 и Content-Type.</li>
     * <li>Формируем почтовое сообщение message.</li>
     * <li>Прикрепляем вложение attachment в сообщение message.</li>
     * <b>Проверка</b>
     * <li>Проверяем, что имя файла корректное и не закодированное.</li>
     * </ol>
     */
    @Test
    public void testCorrectRussianFileNameOnAttachment()
    {
        //подготовка
        IInboundMailAttachment attachment = new InboundMailAttachment();
        attachment.setFilename("UTF-8''%D0%A2%D0%B5%D1%81%D1%82%2E%74%78%74");
        attachment.setContentType("text/plain; charset=UTF-8; name=\"=?UTF-8?B?0KLQtdGB0YIudHh0?=\"");
        IInboundMailMessage message = new InboundMailMessage();
        message.setAttachments(List.of(attachment));

        //проверка
        Assert.assertEquals(message.getAttachments().get(0).getFilename(), "Тест.txt");
    }

    /**
     * Замена символов переноса строки на символ нижнего подчеркивания из имени файла при разборе письма
     */
    @Test
    public void testReplaceBreakSymbolsFileName()
    {
        IInboundMailAttachment attachedEml = mock(IInboundMailAttachment.class);
        when(attachedEml.getFilename()).thenReturn("Название файла \r\n с переносом строки.pdf");
        String result = MailProcessHelper.processFileName(attachedEml);
        Assert.assertEquals(result, "Название файла __ с переносом строки.pdf");
    }

    /**
     * <p>Тестирование корректного наполнения письма параметрами, в которых не указано поле
     * <code>hasParseErrors</code></p>
     */
    @Test
    public void testMailMessageConversionFromMapWithoutHasParseErrorEntry() throws IOException
    {
        InboundMailMessage msg = new InboundMailMessage();
        msg.convertFromMap(Map.of(
                "version", 1,
                "body", "any body",
                "htmlBody", "any html body",
                "date", 1L,
                "original", new byte[] {}));
        Assert.assertFalse(msg.isHasParseErrors());
    }

    @Test
    public void testProcessFileNameWithApplicationOctetStream()
    {
        IInboundMailAttachment attachedEml = mock(IInboundMailAttachment.class);
        when(attachedEml.getContentType()).thenReturn("application/octet-stream; name=\"=?utf-8?B?MQ==?=\"");
        when(attachedEml.getFilename()).thenReturn("Test.txt");
        String result = MailProcessHelper.processFileName(attachedEml);
        Assert.assertEquals(result, "Test.txt");
    }

    /**
     * Тестирование замены символов двоеточие на символ нижнего подчеркивания в имени файла при разборе письма
     */
    @Test
    public void testReplaceDelimiterSymbolsFileName()
    {
        IInboundMailAttachment attachedEml = mock(IInboundMailAttachment.class);
        when(attachedEml.getFilename()).thenReturn("Файл ::.pdf");
        String result = MailProcessHelper.processFileName(attachedEml);
        Assert.assertEquals(result, "Файл __.pdf");
    }

    /**
     * Разбор письма с вариациями хранения имени прикрепленного к письму вложения в блоке content-type
     * с учетом хранения имени по частям и с символом (*) в названии
     */
    @Test
    public void testFileNameInPartsAndWithAsterisk()
    {
        IInboundMailAttachment attachedEml = mock(IInboundMailAttachment.class);

        //name одной строкой
        when(attachedEml.getContentType()).thenReturn(
                "text/xml; charset=\"windows-1251\"; "
                        + "name=\"=?UTF-8?B?0LTQvtC60YPQvNC10L3RgiDRgSDQvdCw0LfQstCw0L3QuNC10Lwg0Lo=?="
                        + " =?UTF-8?B?0LjRgNC40LvQu9C40YbQtdC5INC4INGBINC/0YDQvtCx0LXQu9Cw0Lw=?="
                        + " =?UTF-8?B?0LgueG1s?=\"; ");
        String result = MailProcessHelper.processFileName(attachedEml);
        Assert.assertEquals("документ с названием кириллицей и с пробелами.xml", result);

        //filename одной строкой
        when(attachedEml.getContentType()).thenReturn(
                "text/xml; charset=\"windows-1251\"; "
                        + "filename=\"=?UTF-8?B?0LTQvtC60YPQvNC10L3RgiDRgSDQvdCw0LfQstCw0L3QuNC10Lwg0Lo=?="
                        + " =?UTF-8?B?0LjRgNC40LvQu9C40YbQtdC5INC4INGBINC/0YDQvtCx0LXQu9Cw0Lw=?="
                        + " =?UTF-8?B?0LgueG1s?=\"; ");
        result = MailProcessHelper.processFileName(attachedEml);
        Assert.assertEquals("документ с названием кириллицей и с пробелами.xml", result);

        //name с (*) и filename по частям с двумя символами (*)
        when(attachedEml.getContentType()).thenReturn(
                "text/xml; charset=\"windows-1251\"; "
                        + "name*0=\"=?UTF-8?B?0YLQtdGB0YIg0L3QsNC30LLQsNC90LjQtS54bWw=?=\"; "
                        + "filename*0*=\"=?UTF-8?B?0LTQvtC60YPQvNC10L3RgiDRgSDQvdCw0LfQstCw0L3QuNC10Lwg0Lo=?=\"; "
                        + "filename*1*=\" =?UTF-8?B?0LjRgNC40LvQu9C40YbQtdC5INC4INGBINC/0YDQvtCx0LXQu9Cw0Lw=?=\"; "
                        + "filename*2*=\" =?UTF-8?B?0LgueG1s?=\"; ");
        result = MailProcessHelper.processFileName(attachedEml);
        Assert.assertEquals("документ с названием кириллицей и с пробелами.xml", result);

        //в content-type нет имени
        when(attachedEml.getContentType()).thenReturn(
                "text/xml; charset=\"windows-1251\"; ");
        when(attachedEml.getFilename()).thenReturn(
                "документ с названием кириллицей и с пробелами.xml");
        result = MailProcessHelper.processFileName(attachedEml);
        Assert.assertEquals("документ с названием кириллицей и с пробелами.xml", result);
    }
}