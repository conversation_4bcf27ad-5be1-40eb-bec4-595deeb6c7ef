package ru.naumen.mailreader.server.filters;

import java.io.InputStream;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.Collection;
import java.util.Properties;

import jakarta.inject.Inject;
import jakarta.mail.Session;
import jakarta.mail.internet.MimeMessage;
import javax.sql.DataSource;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import ru.naumen.commons.server.utils.RandomTools;
import ru.naumen.core.server.dispatch.SecurityTestHelper;
import ru.naumen.core.shared.Constants.BlackListContentCatalog;
import ru.naumen.core.shared.Constants.BlackListEmailsCatalog;
import ru.naumen.core.shared.Constants.BlackListSubjectCatalog;
import ru.naumen.mailreader.server.queue.IInboundMailMessage;
import ru.naumen.mailreader.server.receiver.MimeDecoderApacheCommons;

/**
 * Тесты для проверки работоспособности по поиску в Черных справочниках
 * 
 * <AUTHOR>
 * @since 21.05.2014
 */
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "/ru/naumen/core/server/dispatch/fullContext.xml")
public class MailFilterServiceDbTest
{
    @Inject
    MailFilterServiceImpl mailFilterServiceImpl;
    @Inject
    DataSource dataSource;
    @Inject
    MimeDecoderApacheCommons decoder;

    @Before
    public void setUp() throws SQLException
    {
        SecurityTestHelper.autenticateAsSuperUser();
        mailFilterBlackListPrepare("tbl_blacklist_emails", "Badaddress", "<EMAIL>");
        mailFilterBlackListPrepare("tbl_blacklist_content", "nobody", "No body");
        mailFilterBlackListPrepare("tbl_blacklist_subject", "nosubject", "No subject");
    }

    @After
    public void tearDown() throws SQLException
    {
        mailFilterBlackListClean("tbl_blacklist_emails", "Badaddress");
        mailFilterBlackListClean("tbl_blacklist_content", "nobody");
        mailFilterBlackListClean("tbl_blacklist_subject", "nosubject");
    }

    @Test
    public void testBlockMailByBlackListContentFilter() throws Exception
    {
        String checkedField = "No body No body No body";
        try (Connection connection = dataSource.getConnection())
        {
            Collection<String> blackListCatalogObjects = mailFilterServiceImpl.findUUIDsBlackListCatalog(checkedField,
                    BlackListContentCatalog.CODE);
            Assert.assertFalse(blackListCatalogObjects.isEmpty());
        }
    }

    @Test
    public void testBlockMailByBlackListContentFilterIgnoreCase() throws Exception
    {
        String checkedField = "no body no body no body";
        try (Connection connection = dataSource.getConnection())
        {
            Collection<String> blackListCatalogObjects = mailFilterServiceImpl.findUUIDsBlackListCatalog(checkedField,
                    BlackListContentCatalog.CODE);
            Assert.assertFalse(blackListCatalogObjects.isEmpty());
        }
    }

    @Test
    public void testBlockMailByBlackListEmailsFilter() throws Exception
    {
        String checkedField = "<EMAIL>";
        try (Connection connection = dataSource.getConnection())
        {
            Collection<String> blackListEmailsCatalogObjects = mailFilterServiceImpl.findUUIDsBlackListEmails(
                    checkedField, BlackListEmailsCatalog.CODE);
            Assert.assertFalse(blackListEmailsCatalogObjects.isEmpty());
        }
    }

    @Test
    public void testBlockMailByBlackListSubjectFilter() throws Exception
    {
        String checkedField = "No subject No subject No subject";
        try (Connection connection = dataSource.getConnection())
        {
            Collection<String> blackListCatalogObjects = mailFilterServiceImpl.findUUIDsBlackListCatalog(checkedField,
                    BlackListSubjectCatalog.CODE);
            Assert.assertFalse(blackListCatalogObjects.isEmpty());
        }
    }

    @Test
    public void testBlockMailByBlackListSubjectFilterIgnoreCase() throws Exception
    {
        String checkedField = "no subject no subject no subject";
        try (Connection connection = dataSource.getConnection())
        {
            Collection<String> blackListCatalogObjects = mailFilterServiceImpl.findUUIDsBlackListCatalog(checkedField,
                    BlackListSubjectCatalog.CODE);
            Assert.assertFalse(blackListCatalogObjects.isEmpty());
        }
    }

    @Test
    public void testLaggeEmailBody() throws Exception
    {
        InputStream is = getClass().getResourceAsStream("/ru/naumen/mailreader/server/receiver/test_large_body.eml");
        MimeMessage message = new MimeMessage(Session.getInstance(new Properties()), is);
        IInboundMailMessage inMessage = decoder.decode(message, false);
        try (Connection connection = dataSource.getConnection())
        {
            Boolean result = mailFilterServiceImpl.isBlackListCatalogContains(inMessage.getBody(),
                    BlackListSubjectCatalog.CODE);
            Assert.assertFalse(result);
        }
    }

    @Test
    public void testPassMailByBlackListContentFilter() throws Exception
    {
        String checkedField = "NobodyNobodyNobody";
        try (Connection connection = dataSource.getConnection())
        {
            Collection<String> blackListCatalogObjects = mailFilterServiceImpl.findUUIDsBlackListCatalog(checkedField,
                    BlackListContentCatalog.CODE);
            Assert.assertTrue(blackListCatalogObjects.isEmpty());
        }
    }

    @Test
    public void testPassMailByBlackListEmailsFilter() throws Exception
    {
        String checkedField = "<EMAIL>";
        try (Connection connection = dataSource.getConnection())
        {
            Collection<String> blackListEmailsCatalogObjects = mailFilterServiceImpl.findUUIDsBlackListEmails(
                    checkedField, BlackListEmailsCatalog.CODE);
            Assert.assertTrue(blackListEmailsCatalogObjects.isEmpty());
        }
    }

    @Test
    public void testPassMailByBlackListSubjectFilter() throws Exception
    {
        String checkedField = "NosubjectNosubjectNosubject";
        try (Connection connection = dataSource.getConnection())
        {
            Collection<String> blackListCatalogObjects = mailFilterServiceImpl.findUUIDsBlackListCatalog(checkedField,
                    BlackListSubjectCatalog.CODE);
            Assert.assertTrue(blackListCatalogObjects.isEmpty());
        }
    }

    private void mailFilterBlackListClean(String table, String code) throws SQLException
    {
        try (Connection connection = dataSource.getConnection())
        {
            PreparedStatement stmt1 = connection.prepareStatement("delete from " + table + " where code = ?");
            stmt1.setString(1, code);
            stmt1.executeUpdate();
        }
    }

    private void mailFilterBlackListPrepare(String table, String code, String title) throws SQLException
    {
        try (Connection connection = dataSource.getConnection())
        {
            PreparedStatement stmt1 = connection.prepareStatement("insert into " + table
                    + " (id, code, folder, pos, removed, title) values (?,?,?,?,?,?)");
            stmt1.setLong(1, RandomTools.getIntRandom());
            stmt1.setString(2, code);
            stmt1.setBoolean(3, false);
            stmt1.setInt(4, 0);
            stmt1.setBoolean(5, false);
            stmt1.setString(6, title);
            stmt1.executeUpdate();
        }
    }
}
