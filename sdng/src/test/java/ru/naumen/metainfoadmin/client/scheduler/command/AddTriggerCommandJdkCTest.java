package ru.naumen.metainfoadmin.client.scheduler.command;

import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import ru.naumen.common.client.utils.CallbackDecorator;
import ru.naumen.common.client.utils.OnStartCallback;
import ru.naumen.core.SavedParameter;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.shared.TestUtils;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.metainfo.client.MetainfoModificationServiceAsync;
import ru.naumen.metainfo.shared.scheduler.ConcreteDateTrigger;
import ru.naumen.metainfo.shared.scheduler.Trigger;
import ru.naumen.metainfoadmin.client.scheduler.SchedulerGinjector;
import ru.naumen.metainfoadmin.client.scheduler.forms.AddTriggerFormPresenter;

/**
 * <AUTHOR>
 * @since 29.06.2011
 *
 */
public class AddTriggerCommandJdkCTest
{
    @Mock
    OnStartCallback<DtoContainer<Trigger>> callback;
    @Mock
    SchedulerGinjector injector;
    @Mock
    MetainfoModificationServiceAsync metainfoModificationService;

    @Test
    public void execute()
    {
        //настройка системы
        MockitoAnnotations.initMocks(this);
        String schTaskCode = TestUtils.randomString();
        AddTriggerCommand cmd = new AddTriggerCommand(new TriggerCommandParam(null, callback, schTaskCode));
        cmd.injector = injector;
        cmd.metainfoModificationService = metainfoModificationService;
        Trigger trigger = new ConcreteDateTrigger();
        DtoContainer<Trigger> triggerDto = new DtoContainer<>(trigger);
        AddTriggerFormPresenter presenter = Mockito.mock(AddTriggerFormPresenter.class);
        presenter.setSchTaskCode(schTaskCode);
        Mockito.when(injector.addTriggerFormPresenter()).thenReturn(presenter);
        PropertyDialogDisplay editDisplay = Mockito.mock(PropertyDialogDisplay.class);
        Mockito.when(presenter.getDisplay()).thenReturn(editDisplay);

        SavedParameter<CallbackDecorator<DtoContainer<Trigger>, DtoContainer<Trigger>>> saveCallback =
                new SavedParameter<>(1);
        Mockito.doAnswer(saveCallback).when(presenter)
                .init(Mockito.<DtoContainer<Trigger>>any(), Mockito.<CallbackDecorator<DtoContainer<Trigger>, DtoContainer<Trigger>>> any());

        SavedParameter<CallbackDecorator<DtoContainer<Trigger>, DtoContainer<Trigger>>> saveCallback2 =
                new SavedParameter<>(1);
        Mockito.doAnswer(saveCallback2).when(metainfoModificationService)
                .saveTrigger(Mockito.eq(triggerDto), Mockito.<CallbackDecorator<DtoContainer<Trigger>, DtoContainer<Trigger>>> any());

        //вызов системы
        cmd.execute();
        editDisplay.display();
        saveCallback.getValue().onSuccess(triggerDto);
        saveCallback2.getValue().onSuccess(triggerDto);
        //проверка утверждений
        Mockito.verify(presenter).unbind();
        Mockito.verify(presenter).bind();
        Mockito.verify(editDisplay).display();
        Mockito.verify(callback).onStart();
        Mockito.verify(callback).onSuccess(triggerDto);
        //очистка
    }
}
