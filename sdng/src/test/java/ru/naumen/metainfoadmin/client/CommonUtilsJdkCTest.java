package ru.naumen.metainfoadmin.client;

import static com.google.common.collect.Lists.newArrayList;

import java.util.List;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import ru.naumen.core.SuccessAnswer;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.Dialog;
import ru.naumen.core.client.common.DialogCallback;
import ru.naumen.core.client.common.Dialogs;
import ru.naumen.core.client.common.Dialogs.Buttons;
import ru.naumen.core.client.common.Dialogs.DialogResult;
import ru.naumen.core.shared.MockTestUtils;
import ru.naumen.metainfo.client.AdminMetainfoServiceAsync;
import ru.naumen.metainfo.client.MetainfoModificationServiceAsync;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.MetaClassLite;

import com.google.common.base.Predicate;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;

/**
 * <AUTHOR>
 * @since 08.04.2011
 *
 */
@SuppressWarnings("unchecked")
public class CommonUtilsJdkCTest
{
    CommonUtils commonUtils;
    @Mock
    AdminDialogMessages messages;
    @Mock
    CommonMessages cmessages;
    @Mock
    MetainfoUtils metainfoUtils;
    @Mock
    AdminMetainfoServiceAsync metainfoService;
    @Mock
    MetainfoModificationServiceAsync metainfoModificationService;
    @Mock
    Dialogs dialogs;
    @Mock
    EventBus eventBus;
    @Mock
    Dialog dialog;

    @Test
    public void testArchiveClassMetainfo()
    {
        //настройка системы
        MetaClass mc = MockTestUtils.metaClass();
        AsyncCallback<MetaClass> callback = Mockito.mock(AsyncCallback.class);
        SuccessAnswer<DialogResult> sa = new SuccessAnswer<DialogResult>(new DialogResult(Buttons.YES, dialog), 3);
        Mockito.when(
                dialogs.question(Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(),
                        Mockito.any(DialogCallback.class), Mockito.any(Buttons[].class))).thenAnswer(sa);
        //вызов системы
        commonUtils.archiveClassMetainfo(mc, callback);
        //проверка утверждений
        Mockito.verify(metainfoModificationService).removeClassMetainfo(mc.getFqn(), callback);
        //настройка системы
        sa.changeResult(new DialogResult(Buttons.CANCEL, dialog));
        //вызов системы
        commonUtils.archiveClassMetainfo(mc, callback);
        //проверка утверждений
        Mockito.verify(callback).onSuccess(null);
        //очистка
    }

    @Test
    public void testDelClassMetainfo()
    {
        //настройка системы
        MetaClass mc = MockTestUtils.metaClass();
        AsyncCallback<Void> callback = Mockito.mock(AsyncCallback.class);
        SuccessAnswer<DialogResult> sa1 = new SuccessAnswer<DialogResult>(new DialogResult(Buttons.YES, dialog), 3);
        Mockito.when(
                dialogs.question(Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(),
                        Mockito.any(DialogCallback.class), Mockito.any(Buttons[].class))).thenAnswer(sa1);
        List<MetaClassLite> classes = newArrayList();
        SuccessAnswer<List<MetaClassLite>> sa2 = new SuccessAnswer<List<MetaClassLite>>(classes, 2);
        SuccessAnswer<List<MetaClassLite>> sa3 = new SuccessAnswer<List<MetaClassLite>>(classes, 2);
        Mockito.doAnswer(sa2)
                .when(metainfoService)
                .getDescendantClasses(Mockito.any(ClassFqn.class), Mockito.anyBoolean(),
                        Mockito.any(AsyncCallback.class));
        Mockito.doAnswer(sa3)
                .when(metainfoService)
                .getRelatedMetaClasses(Mockito.any(Predicate.class), Mockito.anyBoolean(),
                        Mockito.any(AsyncCallback.class));
        //вызов системы
        commonUtils.delClassMetainfo(mc, callback);
        //проверка утверждений
        Mockito.verify(metainfoModificationService).delClassMetainfo(Mockito.any(ClassFqn.class),
                Mockito.<ClassFqn>any(), Mockito.any(AsyncCallback.class));
        //настройка системы
        sa1.changeResult(new DialogResult(Buttons.CANCEL, dialog));
        //вызов системы
        commonUtils.delClassMetainfo(mc, callback);
        //проверка утверждений
        Mockito.verify(callback).onSuccess(null);
        //очистка
    }

    @Test
    public void testRestoreClassMetainfo()
    {
        //настройка системы
        MetaClass mc = MockTestUtils.metaClass();
        AsyncCallback<MetaClass> callback = Mockito.mock(AsyncCallback.class);
        SuccessAnswer<DialogResult> sa = new SuccessAnswer<DialogResult>(new DialogResult(Buttons.YES, dialog), 3);
        Mockito.when(
                dialogs.question(Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(),
                        Mockito.any(DialogCallback.class), Mockito.any(Buttons[].class))).thenAnswer(sa);
        //вызов системы
        commonUtils.restoreClassMetainfo(mc, callback);
        //проверка утверждений
        Mockito.verify(metainfoModificationService).restoreClassMetainfo(mc.getFqn(), callback);
        //настройка системы
        sa.changeResult(new DialogResult(Buttons.CANCEL, dialog));
        //вызов системы
        commonUtils.restoreClassMetainfo(mc, callback);
        //проверка утверждений
        Mockito.verify(callback).onSuccess(null);
        //очистка
    }

    @Before
    public void setUp()
    {
        MockitoAnnotations.initMocks(this);

        commonUtils = new CommonUtils();
        commonUtils.messages = messages;
        commonUtils.cmessages = cmessages;
        commonUtils.metainfoUtils = metainfoUtils;
        commonUtils.metainfoService = metainfoService;
        commonUtils.metainfoModificationService = metainfoModificationService;
        commonUtils.dialogs = dialogs;
        commonUtils.eventBus = eventBus;
    }

    @After
    public void tearDown()
    {
        commonUtils = null;
        messages = null;
        metainfoUtils = null;
        metainfoService = null;
        dialogs = null;
        eventBus = null;
    }
}
