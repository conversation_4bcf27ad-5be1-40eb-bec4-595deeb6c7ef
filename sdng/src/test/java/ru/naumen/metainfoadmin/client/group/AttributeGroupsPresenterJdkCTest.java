package ru.naumen.metainfoadmin.client.group;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.HashMap;
import java.util.Map;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.event.shared.SimpleEventBus;
import com.google.gwt.place.shared.Place;
import com.google.gwt.place.shared.PlaceController;

import ru.naumen.core.client.TabLayoutDisplay;
import ru.naumen.core.client.common.command.CommandFactory;
import ru.naumen.core.client.content.Context;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonPresenter;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonToolBarDisplay;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonFactory;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.shared.MockTestUtils;
import ru.naumen.core.shared.TestUtils;
import ru.naumen.metainfo.client.MetainfoServiceAsync;
import ru.naumen.metainfo.shared.elements.AttributeGroup;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfoadmin.client.AdminDialogMessages;
import ru.naumen.metainfoadmin.client.MGinjector;
import ru.naumen.metainfoadmin.client.group.GroupGinModule.AddAttributeGroupPresenterFactory;
import ru.naumen.metainfoadmin.client.group.GroupGinModule.AttributeGroupPresenterFactory;
import ru.naumen.metainfoadmin.client.group.command.AddAttrGroupCommand;
import ru.naumen.metainfoadmin.client.group.command.AttributeGroupCommandCode;
import ru.naumen.metainfoadmin.client.group.command.AttributeGroupCommandParam;
import ru.naumen.metainfoadmin.client.group.command.ResetAttrGroupCommand;

/**
 * <AUTHOR>
 * @since 29.07.2010
 *
 */
public class AttributeGroupsPresenterJdkCTest
{
    AttributeGroupsPresenter presenter;

    @Mock
    TabLayoutDisplay display;
    @Mock
    EventBus eventBus;
    @Mock
    MGinjector injector;
    @Mock
    MetainfoServiceAsync metainfoService;
    @Mock
    Context context;
    @Mock
    private AddAttrGroupCommand addAttrGroupCommand;
    @Mock
    private ResetAttrGroupCommand resetAttrGroupCommand;
    @Mock
    ButtonPresenter addButton;
    @Mock
    ButtonPresenter resetButton;
    @Mock
    ButtonFactory buttonToolFactory;
    @Mock
    CommandFactory attrGroupCommandFactory;
    @Mock
    ButtonToolBarDisplay toolBarDisplay;
    @Mock
    AdminDialogMessages messages;
    @Mock
    PlaceController placeController;
    @Mock
    Place place;

    MetaClass metaclass;

    @Before
    public void setUp()
    {
        MockitoAnnotations.initMocks(this);
        metainfoService = mock(MetainfoServiceAsync.class);
        display.setToolBar(toolBarDisplay);

        metaclass = MockTestUtils.metaClass();
        Mockito.when(context.getMetainfo()).thenReturn(metaclass);
        Mockito.when(context.getEventBus()).thenReturn(new SimpleEventBus());
        Mockito.when(metaclass.getParent()).thenReturn(TestUtils.createClassFqn());

        setUpFactory();
        setUpButtonToolBar();
        setUpSchedulerCommandFactory();
        setUpPresenter();
    }

    @After
    public void tearDown()
    {
        display = null;
        injector = null;
        eventBus = null;
        presenter = null;
    }

    @Test
    public void testOnAddGroup()
    {
        //настройка системы
        AddAttributeGroupPresenterFactory factory = mock(AddAttributeGroupPresenterFactory.class);
        AddAttributeGroupPresenter addGrPresenter = mock(AddAttributeGroupPresenter.class);
        PropertyDialogDisplay addGrpDisplay = mock(PropertyDialogDisplay.class);

        when(injector.addAttributeGroupPresenter()).thenReturn(factory);
        when(factory.create(Mockito.eq(context))).thenReturn(addGrPresenter);
        when(addGrPresenter.getDisplay()).thenReturn(addGrpDisplay);

        //вызов системы
        presenter.init(context);
        //        presenter.onAddGroup();
        //проверка утверждений
        //        verify(addGrPresenter, times(1)).bind();
        //очистка
    }

    //TODO dzevako заигнорен до выполнения NSDPRD-11520
    //@Test
    public void testOnBind()
    {
        //настройка системы
        AttributeGroup grp1 = MockTestUtils.attributeGroup();
        MockTestUtils.addAttributeToGroup(metaclass, grp1, MockTestUtils.attribute());
        MockTestUtils.addAttributeGroupToMetaClass(metaclass, grp1);
        AttributeGroup grp2 = MockTestUtils.attributeGroup();
        MockTestUtils.addAttributeToGroup(metaclass, grp2, MockTestUtils.attribute());
        MockTestUtils.addAttributeGroupToMetaClass(metaclass, grp2);

        AttributeGroupPresenterFactory factory = mock(AttributeGroupPresenterFactory.class);
        AttributeGroupPresenter groupPresenter1 = mock(AttributeGroupPresenter.class);
        AttributeGroupPresenter groupPresenter2 = mock(AttributeGroupPresenter.class);
        AttributeGroupDisplay groupDisplay1 = mock(AttributeGroupDisplay.class);
        AttributeGroupDisplay groupDisplay2 = mock(AttributeGroupDisplay.class);

        when(injector.attributeGroupPresenter()).thenReturn(factory);
        when(factory.create(Mockito.eq(context), Mockito.eq(grp1))).thenReturn(groupPresenter1);
        when(factory.create(Mockito.eq(context), Mockito.eq(grp2))).thenReturn(groupPresenter2);
        when(groupPresenter1.getDisplay()).thenReturn(groupDisplay1);
        when(groupPresenter2.getDisplay()).thenReturn(groupDisplay2);
        when(placeController.getWhere()).thenReturn(place);
        //вызов системы
        presenter.init(context);
        presenter.onBind();
        //проверка утверждений
        verify(display, times(1)).addContentDisplay(groupDisplay1, MockTestUtils.randomString());
        verify(display, times(1)).addContentDisplay(groupDisplay2, MockTestUtils.randomString());
        //очистка
    }

    // раскомментировать в NSDPRD-12691
    //@Test
    public void testOnRefreshDisplay()
    {
        //настройка системы
        String code = MockTestUtils.randomString();
        AttributeGroup group = MockTestUtils.attributeGroup(MockTestUtils.randomString());
        MockTestUtils.addAttributeToGroup(metaclass, group, MockTestUtils.attribute());
        MockTestUtils.addAttributeGroupToMetaClass(metaclass, group);

        Map<String, AttributeGroupPresenter> groupPresenters = new HashMap<String, AttributeGroupPresenter>();
        AttributeGroupPresenter grpPresenterForDelete = mock(AttributeGroupPresenter.class);
        groupPresenters.put(code, grpPresenterForDelete);
        //        presenter.groupPresenters = groupPresenters;

        AttributeGroupPresenterFactory factory = mock(AttributeGroupPresenterFactory.class);
        AttributeGroupPresenter groupPresenter = mock(AttributeGroupPresenter.class);
        final AttributeGroupDisplay groupDisplay = mock(AttributeGroupDisplay.class);

        when(injector.attributeGroupPresenter()).thenReturn(factory);
        when(factory.create(Mockito.eq(context), Mockito.eq(group))).thenReturn(groupPresenter);
        when(groupPresenter.getDisplay()).thenReturn(groupDisplay);

        //вызов системы
        presenter.init(context);
        presenter.refreshDisplay();
        //проверка утверждений
        verify(display, times(1)).addContentDisplay(Mockito.eq(groupDisplay), MockTestUtils.randomString());
        //очистка
    }

    private void setUpButtonToolBar()
    {
        //        when(display.getButton(AttributeGroupButtonCode.ADD_ATTR_GROUP)).thenReturn(addButton);
        //        when(display.getButton(AttributeGroupButtonCode.RESET_ATTR_GROUP_SETTING)).thenReturn(resetButton);
    }

    private void setUpFactory()
    {
        when(
                buttonToolFactory.create(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
                        Mockito.<AttributeGroupCommandParam> any())).thenReturn(addButton, resetButton);
    }

    private void setUpPresenter()
    {
        presenter = new AttributeGroupsPresenter(display, eventBus, toolBarDisplay);
        presenter.injector = injector;
        presenter.commandFactory = attrGroupCommandFactory;
        presenter.buttonFactory = buttonToolFactory;
        presenter.messages = messages;
        presenter.placeController = placeController;
    }

    private void setUpSchedulerCommandFactory()
    {
        when(
                (AddAttrGroupCommand)attrGroupCommandFactory.create(
                        Mockito.eq(AttributeGroupCommandCode.ADD_ATTR_GROUP),
                        Mockito.<AttributeGroupCommandParam> any())).thenReturn(addAttrGroupCommand);
        when(
                (ResetAttrGroupCommand)attrGroupCommandFactory.create(
                        Mockito.eq(AttributeGroupCommandCode.RESET_ATTR_GROUP),
                        Mockito.<AttributeGroupCommandParam> any())).thenReturn(resetAttrGroupCommand);
    }
}
