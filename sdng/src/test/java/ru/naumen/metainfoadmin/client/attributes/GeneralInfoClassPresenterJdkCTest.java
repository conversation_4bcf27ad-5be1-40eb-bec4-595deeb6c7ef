package ru.naumen.metainfoadmin.client.attributes;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import com.google.gwt.event.shared.EventBus;

import ru.naumen.core.client.TabLayoutDisplay;
import ru.naumen.core.client.content.Context;
import ru.naumen.core.shared.TestUtils;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfoadmin.client.MGinjector;

/**
 * <AUTHOR>
 * @since 15.11.2010
 *
 */
public class GeneralInfoClassPresenterJdkCTest
{
    @Mock
    TabLayoutDisplay display;
    @Mock
    EventBus eventBus;
    @Mock
    MGinjector injector;
    @Mock
    InfoPresenter currentMetaclassInfoPresenter;
    @Mock
    AttributesPresenter attributesPresenter;
    @Mock
    InfoDisplay currentMetaclassInfoDisplay;
    @Mock
    AttributesDisplay attributesDisplay;

    @Mock
    Context context;
    @Mock
    Context contextType;

    @Mock
    MetaClass metaClass;
    @Mock
    MetaClass metaClassType;

    ClassFqn fqn;
    ClassFqn fqnType;
    GeneralInfoClassPresenter presenter;

    @Test
    public void onBind()
    {
        //настройка системы
        presenter.isRevealed = false;
        //вызов системы
        presenter.init(contextType);
        presenter.bind();
        //проверка утверждений
        verify(currentMetaclassInfoPresenter).init(metaClassType, null, null, null);
        verify(display).insertContentDisplay(currentMetaclassInfoDisplay, "metaClassInfo", -1);
        verify(currentMetaclassInfoPresenter, Mockito.never()).revealDisplay();

        verify(attributesPresenter).init(contextType);
        verify(display).insertContentDisplay(attributesDisplay, "attributesList", -1);
        verify(attributesPresenter, Mockito.never()).revealDisplay();
        //очистка
    }

    @Test
    public void onBind_revealAndCase()
    {
        //настройка системы
        presenter.isRevealed = true;
        //вызов системы
        presenter.init(context);
        presenter.bind();
        //проверка утверждений
        verify(currentMetaclassInfoPresenter).init(metaClass, null, null, null);
        verify(display).insertContentDisplay(currentMetaclassInfoDisplay, "metaClassInfo", -1);
        verify(currentMetaclassInfoPresenter).revealDisplay();

        verify(attributesPresenter).init(context);
        verify(display).insertContentDisplay(attributesDisplay, "attributesList", -1);
        verify(attributesPresenter).revealDisplay();
        //очистка
    }

    @Test
    public void onHide()
    {
        //настройка системы
        //вызов системы
        presenter.onHide();
        //проверка утверждений
        verify(attributesPresenter).hideDisplay();
        verify(currentMetaclassInfoPresenter).hideDisplay();
        //очистка
    }

    @Test
    public void onReveal()
    {
        //настройка системы
        //вызов системы
        presenter.onReveal();
        //проверка утверждений
        verify(attributesPresenter).revealDisplay();
        verify(currentMetaclassInfoPresenter).revealDisplay();
        //очистка
    }

    @Test
    public void onUnbind()
    {
        //настройка системы

        //вызов системы
        presenter.unbind();
        //проверка утверждений
        verify(currentMetaclassInfoPresenter).unbind();
        verify(attributesPresenter).unbind();
        //очистка
    }

    @Before
    public void setUp()
    {
        MockitoAnnotations.initMocks(this);

        fqn = ClassFqn.parse(TestUtils.randomString());
        fqnType = ClassFqn.parse(TestUtils.randomString(), TestUtils.randomString());
        when(metaClass.getFqn()).thenReturn(fqn);
        when(metaClassType.getFqn()).thenReturn(fqnType);

        setUpPresenter();
        setUpContext();
        setUpMetainfo();
        setUpInnerPresenters();

    }

    @After
    public void tearDown()
    {
        display = null;
        eventBus = null;
        presenter = null;
        currentMetaclassInfoPresenter = null;
        attributesPresenter = null;
    }

    private void setUpContext()
    {
        when(context.getMetainfo()).thenReturn(metaClass);
        when(contextType.getMetainfo()).thenReturn(metaClassType);
    }

    private void setUpInnerPresenters()
    {
        when(currentMetaclassInfoPresenter.getDisplay()).thenReturn(currentMetaclassInfoDisplay);
        when(attributesPresenter.getDisplay()).thenReturn(attributesDisplay);
    }

    private void setUpMetainfo()
    {
        when(metaClass.getFqn()).thenReturn(fqn);
    }

    private void setUpPresenter()
    {
        presenter = new GeneralInfoClassPresenter(display, eventBus);
        presenter.currentMetaclassInfoPresenter = currentMetaclassInfoPresenter;
        presenter.attributesPresenter = attributesPresenter;
    }
}
