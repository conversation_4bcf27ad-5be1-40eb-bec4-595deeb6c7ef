package ru.naumen.metainfoadmin.client.attributes.commands;

import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import ru.naumen.core.client.content.Context;
import ru.naumen.core.client.widgets.properties.container.ObjectFormEdit;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfoadmin.client.PropertyFormDisplay;
import ru.naumen.metainfoadmin.client.attributes.forms.AttributeFormPresenter;
import ru.naumen.metainfoadmin.client.attributes.forms.AttributePresenterFactory;

/**
 * <AUTHOR>
 * @since 21.03.2011
 *
 */
public class EditCommandJdkCTest
{
    EditCommand cmd;
    @Mock
    Context context;

    @Mock
    Attribute attr;
    @Mock
    PropertyFormDisplay display;

    @Mock
    AttributePresenterFactory<ObjectFormEdit> presenterFactory;
    @Mock
    AttributeFormPresenter<ObjectFormEdit> presenter;

    @Test
    public void edit()
    {
        //настройка системы 
        MockitoAnnotations.initMocks(this);
        cmd = new EditCommand(new AttributeCommandParam(attr, null, context));
        cmd.presenterFactory = presenterFactory;
        Mockito.when(presenterFactory.create(context)).thenReturn(presenter);
        Mockito.when(presenter.getDisplay()).thenReturn(display);
        //вызов системы
        cmd.execute(attr);
        display.display();
        //проверка утверждений
        Mockito.verify(presenter).bind();
        Mockito.verify(display).display();
        //очистка
    }
}
