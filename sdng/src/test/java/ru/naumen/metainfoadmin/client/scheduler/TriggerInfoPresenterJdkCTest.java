package ru.naumen.metainfoadmin.client.scheduler;

import static org.mockito.Mockito.when;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import org.junit.After;
import org.junit.Assert;
import org.mockito.Mockito;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import com.google.gwt.event.shared.EventBus;

import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.CommonMessagesStub;
import ru.naumen.core.client.common.command.BaseCommand;
import ru.naumen.core.client.content.toolbar.display.FontIconDisplay;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonPresenter;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonToolBarDisplay;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonToolDisplay;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonFactory;
import ru.naumen.core.client.content.toolbar.display.factories.FontIconFactory;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.HasProperties.PropertyRegistration;
import ru.naumen.core.client.widgets.properties.PropertiesGinjector;
import ru.naumen.core.shared.Constants.Tag;
import ru.naumen.core.shared.TestUtils;
import ru.naumen.core.shared.common.Formatters;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.metainfo.client.events.TriggerUpdatedEvent;
import ru.naumen.metainfo.shared.scheduler.ConcreteDateTrigger;
import ru.naumen.metainfo.shared.scheduler.PeriodicTrigger;
import ru.naumen.metainfo.shared.scheduler.SchedulerTask;
import ru.naumen.metainfo.shared.scheduler.Trigger;
import ru.naumen.metainfo.shared.scheduler.Trigger.CalculateStrategies;
import ru.naumen.metainfo.shared.scheduler.TriggerType;
import ru.naumen.metainfoadmin.client.attributes.InfoDisplay;
import ru.naumen.metainfoadmin.client.scheduler.command.DeleteTriggerCommand;
import ru.naumen.metainfoadmin.client.scheduler.command.OffTriggerCommand;
import ru.naumen.metainfoadmin.client.scheduler.command.OnTriggerCommand;
import ru.naumen.metainfoadmin.client.scheduler.command.TriggerCommandParam;
import ru.naumen.metainfoadmin.client.scheduler.forms.creators.TriggerCreatorFactory;

/**
 * <AUTHOR>
 * @since 28.06.2011
 *
 */
public class TriggerInfoPresenterJdkCTest
{
    @Mock
    private EventBus eventBus;
    @Mock
    private InfoDisplay display;
    @Mock
    DeleteTriggerCommand deleteTriggerCommand;
    @Mock
    BaseCommand editTriggerCommand;
    @Mock
    OnTriggerCommand onTriggerCommand;
    @Mock
    OffTriggerCommand offTriggerCommand;
    @Mock
    FontIconFactory<Trigger> iconFactory;
    @Mock
    FontIconDisplay<Trigger> fontIcon;
    @Mock
    SchedulerTaskMessages messages;
    CommonMessages cmessages = new CommonMessagesStub();
    @Mock
    Formatters formatters;
    @Mock
    ButtonFactory buttonToolFactory;
    @Mock
    Property<String> on;
    @Mock
    Property<String> title;
    @Mock
    Property<String> planDateProperty;
    @Mock
    TriggerCreatorFactory factory;
    @Mock
    ButtonToolBarDisplay buttonToolBar;
    @Mock
    ButtonPresenter deleteButton;
    @Mock
    ButtonPresenter editButton;
    @Mock
    ButtonToolDisplay onButton;
    @Mock
    ButtonToolDisplay offButton;
    @Mock
    private DtoContainer<Trigger> trigger;
    @Mock
    private SchedulerTask schedulerTask;
    @Mock
    PropertiesGinjector properties;
    TriggerInfoPresenter presenter;
    String schTaskCode;
    String exDateStr;

    //TODO dzevako заигнорен до выполнения NSDPRD-11520
    //@Test
    public void onBind_concreteDate()
    {
        //настройка системы
        ConcreteDateTrigger trigger = new ConcreteDateTrigger();
        trigger.setType(TriggerType.CONCRETE_DATE);
        trigger.setExecutionDate(new Date());
        prepare(trigger);
        DtoContainer<Trigger> triggerDto = new DtoContainer<>(trigger);

        //вызов системы
        DtoContainer<SchedulerTask> schTaskDto = new DtoContainer<>(schedulerTask);
        schTaskDto.setProperty(Tag.IS_ELEMENT_ENABLED, true);
        presenter.init(triggerDto, schTaskDto);
        presenter.bind();
        //проверка утверждений
        checkOnBind(trigger);
        Assert.assertEquals(1, presenter.otherPropertyRegistrations.size());
        Assert.assertEquals(1, presenter.otherProperties.size());
        //очистка
    }

    //TODO dzevako заигнорен до выполнения NSDPRD-11520
    @SuppressWarnings("unchecked")
    //@Test
    public void onBind_periodicTrigger()
    {
        //настройка системы

        PeriodicTrigger trigger = new PeriodicTrigger();
        trigger.setType(TriggerType.PERIODIC);
        trigger.setPlanExecutionDate(new Date());
        trigger.setStrategy(CalculateStrategies.FROM_START);
        prepare(trigger);
        DtoContainer<Trigger> triggerDto = new DtoContainer<>(trigger);
        presenter.factory = factory;

        String startDate = TestUtils.randomString();
        Mockito.when(messages.startDate()).thenReturn(startDate);
        String period = TestUtils.randomString();
        Mockito.when(messages.period()).thenReturn(period);
        String strategy = TestUtils.randomString();
        Mockito.when(messages.calculationStrategy()).thenReturn(strategy);

        PropertyRegistration<String> startDatePropertyReg = Mockito.mock(PropertyRegistration.class);
        PropertyRegistration<String> periodPropertyReg = Mockito.mock(PropertyRegistration.class);
        PropertyRegistration<String> strategyPropertyReg = Mockito.mock(PropertyRegistration.class);

        Property<String> startDateProperty = Mockito.mock(Property.class);
        Mockito.when(properties.htmlTextProperty()).thenReturn(startDateProperty);
        Mockito.when(display.add(startDateProperty)).thenReturn(startDatePropertyReg);
        Mockito.when(startDatePropertyReg.getProperty()).thenReturn(startDateProperty);
        Property<String> periodProperty = Mockito.mock(Property.class);
        Mockito.when(properties.htmlTextProperty()).thenReturn(periodProperty);
        Mockito.when(display.add(periodProperty)).thenReturn(periodPropertyReg);
        Mockito.when(periodPropertyReg.getProperty()).thenReturn(periodProperty);
        Property<String> strategyProperty = Mockito.mock(Property.class);
        Mockito.when(properties.htmlTextProperty()).thenReturn(strategyProperty);
        Mockito.when(display.add(strategyProperty)).thenReturn(strategyPropertyReg);
        Mockito.when(strategyPropertyReg.getProperty()).thenReturn(strategyProperty);

        Map<String, String> strategies = new HashMap<>();
        Mockito.when(factory.getCalculateStrategies()).thenReturn(strategies);
        //вызов системы
        DtoContainer<SchedulerTask> schTaskDto = new DtoContainer<>(schedulerTask);
        schTaskDto.setProperty(Tag.IS_ELEMENT_ENABLED, true);
        presenter.init(triggerDto, schTaskDto);
        presenter.bind();
        //проверка утверждений
        checkOnBind(trigger);
        Assert.assertEquals(4, presenter.otherPropertyRegistrations.size());
        Assert.assertEquals(4, presenter.otherProperties.size());
        //        Mockito.verify(startDateProperty).setValue(Mockito.anyString());
        //        Mockito.verify(periodProperty).setValue(Mockito.anyString());
        //        Mockito.verify(strategyProperty).setValue(Mockito.anyString());
        //очистка
    }

    @After
    public void tearDown()
    {
        presenter = null;
        schTaskCode = null;
        exDateStr = null;
    }

    private void checkOnBind(Trigger trigger)
    {
        Assert.assertNotNull(presenter.refreshCallback);
        Mockito.verify(eventBus).addHandler(TriggerUpdatedEvent.getType(), presenter);
        Mockito.verify(display).add(title);
        Mockito.verify(title).setCaption(Mockito.anyString());
        Mockito.verify(display).add(on);
        Mockito.verify(on).setCaption(Mockito.anyString());
        //        Mockito.verify(planDateProperty).setValue(exDateStr);
    }

    @SuppressWarnings("unchecked")
    private void prepare(Trigger trigger)
    {
        MockitoAnnotations.initMocks(this);
        setUpFactory();
        setUpDisplay();
        setUpPresenter();

        schTaskCode = TestUtils.randomString();
        trigger.setSchTaskCode(schTaskCode);
        trigger.setEnabled(true);

        String planExecutionDate = TestUtils.randomString();
        Mockito.when(properties.htmlTextProperty()).thenReturn(planDateProperty);
        Mockito.when(messages.planExecutionDate()).thenReturn(planExecutionDate);
        PropertyRegistration<String> planDatePropertyReg = Mockito.mock(PropertyRegistration.class);
        Mockito.when(display.add(planDateProperty)).thenReturn(planDatePropertyReg);
        Mockito.when(planDatePropertyReg.getProperty()).thenReturn(planDateProperty);
        exDateStr = TestUtils.randomString();
        Mockito.when(formatters.formatDateTime(trigger.getPlanExecutionDate())).thenReturn(exDateStr);
        Mockito.when(iconFactory.create(Mockito.anyString())).thenReturn(fontIcon);
    }

    private void setUpDisplay()
    {
        when(display.getToolBar()).thenReturn(buttonToolBar);
    }

    private void setUpFactory()
    {
        when(
                buttonToolFactory.create(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
                        Mockito.<TriggerCommandParam> any())).thenReturn(editButton, deleteButton);
    }

    private void setUpPresenter()
    {
        presenter = new TriggerInfoPresenter(display, eventBus);
        presenter.messages = messages;
        presenter.cmessages = cmessages;
        presenter.formatters = formatters;
        presenter.on = on;
        presenter.title = title;
        presenter.buttonFactory = buttonToolFactory;
        presenter.properties = properties;
        presenter.iconFactory = iconFactory;
        presenter.init(trigger, null);
    }
}
