package ru.naumen.metainfoadmin.client.style.templates.preview.engine;

import java.util.HashMap;
import java.util.Map;

import org.junit.Assert;
import org.junit.Test;

/**
 * <AUTHOR>
 * @since Jan 24, 2017
 */
public class SimplePreviewTemplateEngineJdkTest
{
    private final SimplePreviewTemplateEngine engine = new SimplePreviewTemplateEngine();

    @Test
    public void testBracketsExpression()
    {
        Map<String, Object> bindings = new HashMap<>();
        bindings.put("content", "THE CONTENT");

        Assert.assertEquals("Print THE CONTENT here.", engine.render("Print ${content} here.", bindings));
        Assert.assertEquals("Print THE CONTENThere.", engine.render("Print ${content}here.", bindings));
        Assert.assertEquals("Print THE CONTENT here.", engine.render("Print ${  content  } here.", bindings));
        Assert.assertEquals("Print THE CONTENT here.", engine.render("Print ${  content} here.", bindings));
        Assert.assertEquals("Print ${d.content} here.", engine.render("Print ${d.content} here.", bindings));
        Assert.assertEquals("Print ${content.f} here.", engine.render("Print ${content.f} here.", bindings));
    }

    @Test
    public void testJspTagsExpression()
    {
        Map<String, Object> bindings = new HashMap<>();
        bindings.put("content", "THE CONTENT");

        Assert.assertEquals("Print THE CONTENT here.", engine.render("Print <%=content%> here.", bindings));
        Assert.assertEquals("Print THE CONTENThere.", engine.render("Print <%=content%>here.", bindings));
        Assert.assertEquals("Print THE CONTENT here.", engine.render("Print <%= content %> here.", bindings));
        Assert.assertEquals("Print THE CONTENT here.", engine.render("Print <%= content%> here.", bindings));
        Assert.assertEquals("Print <%=d.content%> here.", engine.render("Print <%=d.content%> here.", bindings));
        Assert.assertEquals("Print <%=content.f%> here.", engine.render("Print <%=content.f%> here.", bindings));
    }

    @Test
    public void testPlainExpression()
    {
        Map<String, Object> bindings = new HashMap<>();
        bindings.put("content", "THE CONTENT");

        Assert.assertEquals("Print THE CONTENT here.", engine.render("Print $content here.", bindings));
        Assert.assertEquals("Print $contenthere.", engine.render("Print $contenthere.", bindings));
        Assert.assertEquals("Print THE CONTENT", engine.render("Print $content", bindings));
    }

    @Test
    public void testReservedChars()
    {
        Map<String, Object> bindings = new HashMap<>();
        bindings.put("conten\\E\\wt", "THE \\E CONTENT");
        bindings.put("con\\tent", "THE \\ CONTENT");
        bindings.put("conte$nt", "THE $ CONTENT");

        Assert.assertEquals("Print THE \\E CONTENT here.", engine.render("Print ${conten\\E\\wt} here.", bindings));
        Assert.assertEquals("Print THE \\ CONTENT here.", engine.render("Print ${con\\tent} here.", bindings));
        Assert.assertEquals("Print THE $ CONTENT here.", engine.render("Print ${conte$nt} here.", bindings));

        Assert.assertEquals("Print THE \\E CONTENT here.", engine.render("Print $conten\\E\\wt here.", bindings));
        Assert.assertEquals("Print THE \\ CONTENT here.", engine.render("Print $con\\tent here.", bindings));
        Assert.assertEquals("Print THE $ CONTENT here.", engine.render("Print $conte$nt here.", bindings));

        Assert.assertEquals("Print THE \\E CONTENT here.", engine.render("Print <%=conten\\E\\wt%> here.", bindings));
        Assert.assertEquals("Print THE \\ CONTENT here.", engine.render("Print <%=con\\tent%> here.", bindings));
        Assert.assertEquals("Print THE $ CONTENT here.", engine.render("Print <%=conte$nt%> here.", bindings));
    }
}
