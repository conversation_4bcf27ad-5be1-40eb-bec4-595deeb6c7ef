package ru.naumen.metainfoadmin.client.group;

import static org.mockito.Mockito.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.event.shared.SimpleEventBus;
import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.safehtml.shared.SafeHtmlUtils;
import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.SavedCallback;
import ru.naumen.core.SavedParameter;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.CommonMessagesStub;
import ru.naumen.core.client.common.Dialogs;
import ru.naumen.core.client.common.command.CommandFactory;
import ru.naumen.core.client.content.Context;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonPresenter;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonToolBarDisplay;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonFactory;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.shared.MockTestUtils;
import ru.naumen.core.shared.TestUtils;
import ru.naumen.core.shared.common.Formatters;
import ru.naumen.metainfo.client.MetainfoModificationServiceAsync;
import ru.naumen.metainfo.client.events.MetainfoUpdatedEvent;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.MetainfoUtilsStub;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.AttributeGroup;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfoadmin.client.AdminDialogMessages;
import ru.naumen.metainfoadmin.client.AdminDialogMessagesStub;
import ru.naumen.metainfoadmin.client.MGinjector;
import ru.naumen.metainfoadmin.client.group.AttributeGroupDisplay.MoveAttrEvent;
import ru.naumen.metainfoadmin.client.group.GroupGinModule.EditAttributesPresenterFactory;
import ru.naumen.metainfoadmin.client.group.command.AttributeGroupCommandCode;
import ru.naumen.metainfoadmin.client.group.command.AttributeGroupCommandParam;
import ru.naumen.metainfoadmin.client.group.command.DeleteAttrGroupCommand;
import ru.naumen.metainfoadmin.client.group.command.EditAttrGroupCommand;
import ru.naumen.metainfoadmin.client.group.command.ResetAttrGroupCommand;
import ru.naumen.metainfoadmin.client.group.command.ShowUsageAttrGroupCommand;

/**
 * <AUTHOR>
 * @since 29.07.2010
 *
 */
public class AttributeGroupPresenterJdkCTest
{
    AttributeGroupPresenter presenter;
    @Mock
    AttributeGroupDisplay display;
    @Mock
    EventBus eventBus;
    @Mock
    MGinjector injector;
    @Mock
    MetainfoModificationServiceAsync metainfoModificationService;
    @Mock
    Context context;
    @Mock
    Formatters formatters;
    @Mock
    private DeleteAttrGroupCommand deleteAttrGroupCommand;
    @Mock
    private EditAttrGroupCommand editAttrGroupCommand;
    @Mock
    private ShowUsageAttrGroupCommand showUsageAttrGroupCommand;
    @Mock
    private ResetAttrGroupCommand resetAttrGroupCommand;
    @Mock
    ButtonPresenter editButton;
    @Mock
    ButtonPresenter deleteButton;
    @Mock
    ButtonPresenter resetButton;
    @Mock
    ButtonToolBarDisplay buttonToolBar;
    @Mock
    ButtonFactory buttonFactory;
    @Mock
    CommandFactory commandFactory;

    MetaClass metainfo;
    MetainfoUtils metainfoUtils;
    AttributeGroup group;
    SafeHtml grpTitle;
    AdminDialogMessages messages = new AdminDialogMessagesStub();
    CommonMessages cmessages = new CommonMessagesStub();

    @Before
    public void setUp()
    {
        MockitoAnnotations.initMocks(this);

        metainfoUtils = new MetainfoUtilsStub();
        grpTitle = SafeHtmlUtils.fromTrustedString(TestUtils.uniqueRandomString("title"));

        group = MockTestUtils.attributeGroup(grpTitle.asString(), MockTestUtils.randomString());

        metainfo = MockTestUtils.metaClass();
        MockTestUtils.addAttributeGroupToMetaClass(metainfo, group);
        Mockito.when(context.getMetainfo()).thenReturn(metainfo);
        Mockito.when(context.getEventBus()).thenReturn(new SimpleEventBus());
        Mockito.when(context.getContextProperty(Mockito.anyString())).thenReturn(new HashSet<>());

        Mockito.when(formatters.escapeHtmlSymbols(grpTitle.asString())).thenReturn(grpTitle);
        Mockito.when(formatters.normalize(grpTitle)).thenReturn(grpTitle);

        setUpFactory();
        setUpButtonToolBar();
        setUpDisplay();
        setUpSchedulerCommandFactory();
        setUpPresenter();
    }

    @After
    public void tearDown()
    {
        display = null;
        eventBus = null;
        grpTitle = null;
        group = null;
        metainfo = null;
        metainfoUtils = null;
        presenter = null;
        injector = null;
        messages = null;
        cmessages = null;
    }

    //TODO dzevako заигнорен до выполнения NSDPRD-11520
    //@Test
    public void testOnBind()
    {
        //настройка системы
        Attribute attr = MockTestUtils.attribute();
        MockTestUtils.addAttributeToGroup(metainfo, group, attr);
        MockTestUtils.addAttributeToMetaClass(metainfo, attr);

        //вызов системы
        presenter.onBind();
        //проверка утверждений
        //отсутствие исключений
        //очистка
    }

    //TODO dzevako заигнорен до выполнения NSDPRD-11520
    //@Test
    public void testOnBind_afterUpdateMetainfo()
    {
        //настройка системы
        Attribute attr = MockTestUtils.attribute();
        MockTestUtils.addAttributeToGroup(metainfo, group, attr);
        MockTestUtils.addAttributeToMetaClass(metainfo, attr);

        eventBus.fireEvent(new MetainfoUpdatedEvent(metainfo));
        //вызов системы
        presenter.onBind();
        metainfo.getAttributeGroups().size();
        //проверка утверждений
        //очистка
    }

    @SuppressWarnings("unchecked")
    @Test
    public void testOnCanNotDeleteGroup()
    {
        //настройка системы
        eventBus.addHandler(BeforeDeleteGroupEvent.getType(), new BeforeDeleteGroupHandler()
        {
            @Override
            public void onBeforeDeleteGroup(BeforeDeleteGroupEvent event)
            {
                event.cancel("");
            }
        });

        SavedCallback<Dialogs.Buttons> qustionClb = new SavedCallback<Dialogs.Buttons>(2);
        //        Mockito.doAnswer(qustionClb).when(presenter.dialogs)
        //                .question(anyString(), anyString(), any(AsyncCallback.class));
        //вызов системы
        //        presenter.onDeleteGroup();

        //проверка утверждений
        //очистка

    }

    @Test
    public void testOnEditGroup()
    {
        //настройка системы
        EditAttributesPresenterFactory factory = mock(EditAttributesPresenterFactory.class);
        EditAttributeGroupPresenter editPresenter = mock(EditAttributeGroupPresenter.class);
        PropertyDialogDisplay editDisplay = mock(PropertyDialogDisplay.class);

        when(injector.editAttributesPresenter()).thenReturn(factory);
        when(factory.create(Mockito.eq(context), Mockito.eq(group))).thenReturn(editPresenter);
        when(editPresenter.getDisplay()).thenReturn(editDisplay);
        //вызов системы
        //                presenter.onChangeSet();
        //проверка утверждений
        //  verify(editPresenter, times(1)).bind();
        //        verify(renamePresenter, times(1)).addSaveHandler(any(SaveHandler.class));
        //        verify(renameDisplay, times(1)).show();
        //очистка
    }

    @SuppressWarnings("unchecked")
    @Test
    public void testOnMoveDown()
    {
        //настройка системы
        ArrayList<Attribute> attributes = new ArrayList<Attribute>();
        for (int i = 0; i < 3; i++)
        {
            Attribute attr = MockTestUtils.attribute();
            MockTestUtils.addAttributeToGroup(metainfo, group, attr);
            attributes.add(attr);
        }

        SavedParameter<List<String>> attrSave = new SavedParameter<List<String>>(2);
        Mockito.doAnswer(attrSave)
                .when(metainfoModificationService)
                .editAttributeGroup(eq(group), anyString(), anyList(), any(AsyncCallback.class),
                        anyBoolean());
        //вызов системы
        MoveAttrEvent event = new MoveAttrEvent(attributes.get(1).getCode(), 1);
        presenter.onEditAttribute(event);
        //проверка утверждений
        List<String> codes = attrSave.getValue();
        Assert.assertEquals(attributes.get(0).getCode(), codes.get(0));
        Assert.assertEquals(attributes.get(2).getCode(), codes.get(1));
        Assert.assertEquals(attributes.get(1).getCode(), codes.get(2));
        //очистка
    }

    @SuppressWarnings("unchecked")
    @Test
    public void testOnMoveUp()
    {
        //настройка системы
        ArrayList<Attribute> attributes = new ArrayList<Attribute>();
        for (int i = 0; i < 3; i++)
        {
            Attribute attr = MockTestUtils.attribute();
            MockTestUtils.addAttributeToGroup(metainfo, group, attr);
            attributes.add(attr);
        }

        SavedParameter<List<String>> attrSave = new SavedParameter<List<String>>(2);
        Mockito.doAnswer(attrSave)
                .when(metainfoModificationService)
                .editAttributeGroup(eq(group), anyString(), anyList(), any(AsyncCallback.class),
                        anyBoolean());
        //вызов системы
        MoveAttrEvent event = new MoveAttrEvent(attributes.get(1).getCode(), -1);
        presenter.onEditAttribute(event);
        //проверка утверждений
        List<String> codes = attrSave.getValue();
        Assert.assertEquals(attributes.get(1).getCode(), codes.get(0));
        Assert.assertEquals(attributes.get(0).getCode(), codes.get(1));
        Assert.assertEquals(attributes.get(2).getCode(), codes.get(2));
        //очистка
    }

    @SuppressWarnings({ "rawtypes", "unchecked" })
    @Test
    public void testRefreshDisplay()
    {
        //настройка системы
        Attribute attr = MockTestUtils.attribute();
        MockTestUtils.addAttributeToGroup(metainfo, group, attr);
        MockTestUtils.addAttributeToMetaClass(metainfo, attr);

        //вызов системы
        presenter.refreshDisplay();
        //проверка утверждений
        verify(display).setAttributes(new ArrayList(Arrays.asList(attr)), new HashSet<>(),
                new HashSet<>(Set.of(attr.getCode())));
        verify(display).setCaption(grpTitle.asString());
        //очистка
    }

    @SuppressWarnings({ "rawtypes", "unchecked" })
    @Test
    public void testRefreshDisplay_SystemAttributes()
    {
        //настройка системы
        Attribute attr = MockTestUtils.attribute();
        MockTestUtils.addAttributeToGroup(metainfo, group, attr);
        MockTestUtils.addAttributeToMetaClass(metainfo, attr);

        //вызов системы
        presenter.refreshDisplay();
        //проверка утверждений
        verify(display).setAttributes(new ArrayList(Arrays.asList(attr)), new HashSet<>(),
                new HashSet<>(Set.of(attr.getCode())));
        verify(display).setCaption(grpTitle.asString());
        //очистка
    }

    private void setUpButtonToolBar()
    {
        //        when(
        //                buttonToolBar
        //                        .getButton(ru.naumen.core.client.content.toolbar.display.factories.ButtonCode<T>.EDIT))
        //                .thenReturn(editButton);
        //        when(
        //                buttonToolBar
        //                        .getButton(ru.naumen.core.client.content.toolbar.display.factories.ButtonCode<T>.DELETE))
        //                .thenReturn(deleteButton);
        //        when(buttonToolBar.getButton(AttributeGroupButtonCode.RESET_ATTR_GROUP_SETTING)).thenReturn(resetButton);
    }

    private void setUpDisplay()
    {
        when(display.getToolBar()).thenReturn(buttonToolBar);
    }

    private void setUpFactory()
    {

        when(
                buttonFactory.create(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
                        Mockito.<AttributeGroupCommandParam> any())).thenReturn(editButton, deleteButton, resetButton);
    }

    private void setUpPresenter()
    {
        presenter = new AttributeGroupPresenter(context, group, display, eventBus);
        presenter.messages = messages;
        presenter.cmessages = cmessages;
        presenter.metainfoModificationService = metainfoModificationService;
        presenter.buttonFactory = buttonFactory;
        presenter.commandFactory = commandFactory;
    }

    private void setUpSchedulerCommandFactory()
    {
        when(
                (EditAttrGroupCommand)commandFactory.create(
                        Mockito.eq(AttributeGroupCommandCode.EDIT_ATTR_GROUP),
                        Mockito.<AttributeGroupCommandParam> any())).thenReturn(editAttrGroupCommand);
        when(
                (DeleteAttrGroupCommand)commandFactory.create(
                        Mockito.eq(AttributeGroupCommandCode.DELETE_ATTR_GROUP),
                        Mockito.<AttributeGroupCommandParam> any())).thenReturn(deleteAttrGroupCommand);

        when(
                (ShowUsageAttrGroupCommand)commandFactory.create(
                        Mockito.eq(AttributeGroupCommandCode.SHOW_USAGE),
                        Mockito.<AttributeGroupCommandParam> any())).thenReturn(showUsageAttrGroupCommand);
    }
}
