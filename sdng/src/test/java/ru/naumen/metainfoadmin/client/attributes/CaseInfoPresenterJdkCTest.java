package ru.naumen.metainfoadmin.client.attributes;

import static org.mockito.Mockito.when;

import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import ru.naumen.core.client.content.toolbar.display.buttons.ButtonToolBarDisplay;
import ru.naumen.core.shared.MockTestUtils;
import ru.naumen.core.shared.TestUtils;
import ru.naumen.core.shared.utils.UUIDGenerator;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.elements.MetaClass;

import com.google.gwt.event.shared.EventBus;

/**
 * <AUTHOR>
 * @since 14.10.2010
 * 
 */
public class CaseInfoPresenterJdkCTest
{
    @Mock
    InfoDisplay display;
    @Mock
    EventBus eventBus;
    @Mock
    MetainfoUtils utils;
    @Mock
    ButtonToolBarDisplay toolbarDisplay;

    InfoPresenter presenter;
    String code;
    String title;
    String description;

    @Before
    public void setup()
    {
        MockitoAnnotations.initMocks(this);
        when(display.getToolBar()).thenReturn(toolbarDisplay);
        presenter = new InfoPresenter(display, eventBus);
        presenter.metainfoUtils = utils;
    }

    @Test
    public void testInit()
    {
        //настройка
        code = UUIDGenerator.get().nextUUID();
        title = UUIDGenerator.get().nextUUID();
        description = UUIDGenerator.get().nextUUID();

        ClassFqn fqn = ClassFqn.parse(TestUtils.randomString(), code);
        MetaClass metaclass = MockTestUtils.metaClass(fqn);
        when(metaclass.getTitle()).thenReturn(title);
        when(metaclass.getDescription()).thenReturn(description);
        //вызов

        //presenter.init(context);
        //проверка

        //   Mockito.verify(display).setData(title, code, description);
        //очистка
    }

}
