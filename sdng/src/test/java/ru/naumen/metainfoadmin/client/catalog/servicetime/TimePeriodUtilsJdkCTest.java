package ru.naumen.metainfoadmin.client.catalog.servicetime;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.junit.Assert;
import org.junit.Test;

import ru.naumen.commons.shared.utils.Pair;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.core.shared.utils.TimePeriodUtils;

/**
 * <AUTHOR>
 * @since 12.12.2011
 *
 */
public class TimePeriodUtilsJdkCTest
{

    @Test
    //public static boolean cover(Long start1, Long end1, Long start2, Long end2)
    public void cover()
    {
        //настройка системы
        boolean[] expecteds = { true, false };
        Long[][] params = { { 0L, 10L, 1L, 2L }, { 0L, 10L, -1L, 2L } };
        //вызов системы
        //проверка утверждений        
        for (int i = 0; i < expecteds.length; i++)
        {
            Assert.assertEquals(expecteds[i],
                    TimePeriodUtils.cover(params[i][0], params[i][1], params[i][2], params[i][3]));
        }
        //очистка
    }

    @Test
    //public static boolean endInside(Long x, Long start, Long end)
    public void endInside()
    {
        //настройка системы
        boolean[] expecteds = { false, true, false };
        Long[][] params = { { 0L, 10L, 20L }, { 15L, 10L, 20L }, { 10L, 10L, 20L } };
        //вызов системы
        //проверка утверждений        
        for (int i = 0; i < expecteds.length; i++)
        {
            Assert.assertEquals(expecteds[i], TimePeriodUtils.endInside(params[i][0], params[i][1], params[i][2]));
        }
        //очистка
    }

    @Test
    //public static String formatTime(Long time)
    public void formatTime()
    {
        //настройка системы
        String[] expecteds = { "0:00", "11:23", "" };
        Long[] params = { 0L, 1000L * 60 * (11 * 60 + 23), null };
        //вызов системы
        //проверка утверждений        
        for (int i = 0; i < expecteds.length; i++)
        {
            Assert.assertEquals(expecteds[i], TimePeriodUtils.formatTime(params[i]));
        }
        //очистка
    }

    @Test
    //public static String formatTimePeriod(Long start, Long end)
    public void formatTimePeriod()
    {
        //настройка системы
        String[] expecteds = { "0:00-11:23", "-0:00" };
        Long[][] params = { { 0L, 1000L * 60 * (11 * 60 + 23) }, { null, 0L } };
        //вызов системы
        //проверка утверждений        
        for (int i = 0; i < expecteds.length; i++)
        {
            Assert.assertEquals(expecteds[i], TimePeriodUtils.formatTimePeriod(params[i][0], params[i][1]));
        }
        //очистка
    }

    @Test
    //public static List<Pair<Long, Long>> getPeriodsByDate(List<DtObject> periods, Object date, String dateProperty)
    public void getPeriodsByDate()
    {
        //настройка системы
        Object date = new Date();
        String dateProperty = "test";
        List<DtObject> periods = new ArrayList<>();
        DtObject period;

        period = new SimpleDtObject();
        period.setProperty(dateProperty, date);
        period.setProperty(Constants.TimePeriod.START_TIME, 10L);
        period.setProperty(Constants.TimePeriod.END_TIME, 20L);
        periods.add(period);

        period = new SimpleDtObject();
        period.setProperty("", date);
        period.setProperty(Constants.TimePeriod.START_TIME, 0L);
        period.setProperty(Constants.TimePeriod.END_TIME, 5L);
        periods.add(period);
        //вызов системы
        List<Pair<Long, Long>> actuals = TimePeriodUtils.getPeriodsByDate(periods, date, dateProperty);
        //проверка утверждений
        Assert.assertEquals("Должен быть 1 период", 1, actuals.size());
        Assert.assertEquals("Начало", Long.valueOf(10L), actuals.get(0).left);
        Assert.assertEquals("Начало", Long.valueOf(20L), actuals.get(0).right);
        //очистка        
    }

    @Test
    //public static <T> Map<T, List<String>> groupTimePeriods(List<DtObject> timePeriods, String groupProperty, Comparator treeMapComparator)
    public void groupTimePeriods()
    {
        //настройка системы
        Object date = new Date();
        String groupProperty = "test";
        List<DtObject> periods = new ArrayList<>();
        DtObject period;

        period = new SimpleDtObject();
        period.setProperty(groupProperty, date);
        period.setProperty(Constants.TimePeriod.START_TIME, 7 * 60 * 60 * 1000L);
        period.setProperty(Constants.TimePeriod.END_TIME, 9 * 60 * 60 * 1000L);
        periods.add(period);

        period = new SimpleDtObject();
        period.setProperty(groupProperty, date);
        period.setProperty(Constants.TimePeriod.START_TIME, 0L);
        period.setProperty(Constants.TimePeriod.END_TIME, 30 * 60 * 1000L);
        periods.add(period);

        period = new SimpleDtObject();
        period.setProperty("", date);
        period.setProperty(Constants.TimePeriod.START_TIME, 0L);
        period.setProperty(Constants.TimePeriod.END_TIME, 5L);
        periods.add(period);

        //вызов системы
        Map<Date, List<String>> actuals = TimePeriodUtils.groupTimePeriods(periods, groupProperty);
        //проверка утверждений        
        Assert.assertEquals("Должна быть 1 дата", 1, actuals.size());
        List<String> periodsAsStrings = actuals.get(date);
        Assert.assertNotNull("Должны быть периоды на нужную дату", periodsAsStrings);
        Assert.assertEquals("0:00-0:30", periodsAsStrings.get(0));
        Assert.assertEquals("7:00-9:00", periodsAsStrings.get(1));
        //очистка
    }

    @Test
    //public static <T extends Comparable<? super T>> Map<T, List<String>> groupTimePeriods(List<DtObject> timePeriods, String groupProperty, Comparator<T> treeMapComparator)
    /**
     * Тестрование использования обратного компаратора
     * Подготовка:
     * Создать 5 простых DTO объектов, заполнить property "test" значениями дат 1 января 2018-2022 годов
     * Добавить объекты в список всех периодов
     * Далее вызвать метод groupTimePeriods, используя обратный компаратор дат
     * Проверка:
     * Первый элемент внутри сформированного ассоциативного массива - самая новая дата
     */
    public void groupTimePeriodsWithReversedComparator()
    {
        String groupProperty = "test";
        List<DtObject> periods = new ArrayList<>();

        DtObject january2018Period = new SimpleDtObject();
        january2018Period.setProperty(groupProperty, new GregorianCalendar(2018, Calendar.JANUARY, 1).getTime());

        DtObject january2019Period = new SimpleDtObject();
        january2019Period.setProperty(groupProperty, new GregorianCalendar(2019, Calendar.JANUARY, 1).getTime());

        DtObject january2020Period = new SimpleDtObject();
        january2020Period.setProperty(groupProperty, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        DtObject january2021Period = new SimpleDtObject();
        january2021Period.setProperty(groupProperty, new GregorianCalendar(2021, Calendar.JANUARY, 1).getTime());

        DtObject january2022Period = new SimpleDtObject();
        january2022Period.setProperty(groupProperty, new GregorianCalendar(2022, Calendar.JANUARY, 1).getTime());

        periods.add(january2018Period);
        periods.add(january2019Period);
        periods.add(january2020Period);
        periods.add(january2021Period);
        periods.add(january2022Period);

        Map<Date, List<String>> actuals = TimePeriodUtils.groupTimePeriods(periods, groupProperty, Comparator.<Date> naturalOrder().reversed());
        Set<Date> dates = actuals.keySet();

        Assert.assertEquals("Первый элемент - самая новейшая дата", january2022Period.getProperty("test"), dates.iterator().next());
    }

    @Test
    //public static <T extends Comparable<? super T>> Map<T, List<String>> groupTimePeriods(List<DtObject> timePeriods, String groupProperty, Comparator<T> treeMapComparator)
    /**
     * Тестрование использования естественного компаратора
     * Подготовка:
     * Создать 5 простых DTO объектов, заполнить property "test" значениями дат 1 января 2018-2022 годов
     * Добавить объекты в список всех периодов
     * Далее вызвать метод groupTimePeriods, используя естественный компаратор дат
     * Проверка:
     * Первый элемент внутри сформированного ассоциативного массива - самая старая дата
     */
    public void groupTimePeriodsWithNaturalComparator()
    {
        String groupProperty = "test";
        List<DtObject> periods = new ArrayList<>();

        DtObject january2018Period = new SimpleDtObject();
        january2018Period.setProperty(groupProperty, new GregorianCalendar(2018, Calendar.JANUARY, 1).getTime());

        DtObject january2019Period = new SimpleDtObject();
        january2019Period.setProperty(groupProperty, new GregorianCalendar(2019, Calendar.JANUARY, 1).getTime());

        DtObject january2020Period = new SimpleDtObject();
        january2020Period.setProperty(groupProperty, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        DtObject january2021Period = new SimpleDtObject();
        january2021Period.setProperty(groupProperty, new GregorianCalendar(2021, Calendar.JANUARY, 1).getTime());

        DtObject january2022Period = new SimpleDtObject();
        january2022Period.setProperty(groupProperty, new GregorianCalendar(2022, Calendar.JANUARY, 1).getTime());

        periods.add(january2018Period);
        periods.add(january2019Period);
        periods.add(january2020Period);
        periods.add(january2021Period);
        periods.add(january2022Period);

        Map<Date, List<String>> actuals = TimePeriodUtils.groupTimePeriods(periods, groupProperty, Comparator.<Date> naturalOrder());
        Set<Date> dates = actuals.keySet();

        Assert.assertEquals("Первый элемент - самая старая дата", january2018Period.getProperty("test"), dates.iterator().next());
    }

    @Test
    //public static boolean isIntersection(Long start, Long end, List<Pair<Long, Long>> periods)
    public void isIntersection()
    {
        //настройка системы
        List<Pair<Long, Long>> periods = new ArrayList<>();
        periods.add(new Pair<>(10L, 20L));
        periods.add(new Pair<>(30L, 40L));

        boolean[] expecteds = { false, true };
        Long[][] params = { { 0L, 10L }, { 15L, 10L } };
        //вызов системы
        //проверка утверждений        
        for (int i = 0; i < expecteds.length; i++)
        {
            Assert.assertEquals(expecteds[i], TimePeriodUtils.isIntersection(params[i][0], params[i][1], periods));
        }
        //очистка
    }

    @Test
    //public static boolean startInside(Long x, Long start, Long end)
    public void startInside()
    {
        //настройка системы
        boolean[] expecteds = { true, false, true };
        Long[][] params = { { 0L, 0L, 10L }, { 10L, 0L, 10L }, { 10L, 0L, 100L } };
        //вызов системы
        //проверка утверждений        
        for (int i = 0; i < expecteds.length; i++)
        {
            Assert.assertEquals(expecteds[i], TimePeriodUtils.startInside(params[i][0], params[i][1], params[i][2]));
        }
        //очистка
    }
}
