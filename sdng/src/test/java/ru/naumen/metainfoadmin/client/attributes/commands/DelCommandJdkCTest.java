package ru.naumen.metainfoadmin.client.attributes.commands;

import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.when;

import java.util.ArrayList;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import com.google.gwt.user.client.rpc.AsyncCallback;

import net.customware.gwt.dispatch.client.DispatchAsync;
import net.customware.gwt.dispatch.shared.Action;
import net.customware.gwt.dispatch.shared.Result;
import ru.naumen.core.SuccessAnswer;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.Dialog;
import ru.naumen.core.client.common.DialogCallback;
import ru.naumen.core.client.common.Dialogs;
import ru.naumen.core.client.common.Dialogs.Buttons;
import ru.naumen.core.client.common.Dialogs.DialogResult;
import ru.naumen.core.client.content.Context;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.shared.TestUtils;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.metainfo.client.MetainfoModificationServiceAsync;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.AttributeType;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfoadmin.client.AdminDialogMessages;

/**
 * <AUTHOR>
 * @since 21.03.2011
 *
 */
public class DelCommandJdkCTest
{
    public static class DispatchStub implements DispatchAsync
    {
        private final Result callbackObject;

        public DispatchStub(Result callbackObject)
        {
            this.callbackObject = callbackObject;
        }

        @Override
        @SuppressWarnings("unchecked")
        public <A extends Action<R>, R extends Result> void execute(A action, AsyncCallback<R> callback)
        {
            callback.onSuccess((R)callbackObject);
        }
    }

    DelCommand cmd;
    @Mock
    Dialogs dialogs;

    @Mock
    CommonMessages messages;
    @Mock
    AdminDialogMessages dialogMessages;
    @Mock
    Attribute attr;
    DispatchAsync dispatch;

    @Mock
    MetainfoModificationServiceAsync metainfoModificationService;
    @Mock
    Context context;
    @Mock
    MetaClass metainfo;
    @Mock
    Dialog dialog;
    @Mock
    AttributeType attrType;
    ClassFqn fqn;

    @Test
    public void delete()
    {
        //настройка системы        
        when(attr.isHardcoded()).thenReturn(false);

        when(attr.getDeclaredMetaClass()).thenReturn(fqn);
        when(dialogs.question(Mockito.any(), anyString(), anyString(), Mockito.<DialogCallback> any())).thenAnswer(
                new SuccessAnswer<DialogResult>(new DialogResult(Buttons.YES, dialog), 3));
        //вызов системы
        cmd.execute(attr);
        //проверка утверждений
        Mockito.verify(metainfoModificationService).delAttribute(Mockito.eq(attr),
                Mockito.<BasicCallback<MetaClass>> any());
        //очистка
    }

    @Test
    public void delFromDeclaredMetaclass()
    {
        //настройка системы
        ClassFqn declaredFqn = ClassFqn.parse(TestUtils.randomString());
        when(attr.isHardcoded()).thenReturn(false);

        when(attr.getDeclaredMetaClass()).thenReturn(declaredFqn);
        //вызов системы
        cmd.execute(attr);
        //проверка утверждений
        Mockito.verify(dialogs).error(Mockito.anyString());
        Mockito.verifyNoMoreInteractions(dialogs);
        //очистка
    }

    @Test
    public void delHardCoded()
    {
        //настройка системы
        when(attr.isHardcoded()).thenReturn(true);
        //вызов системы
        cmd.execute(attr);
        //проверка утверждений
        Mockito.verify(dialogs).error(Mockito.anyString());
        Mockito.verifyNoMoreInteractions(dialogs);
        //очистка
    }

    @Before
    public void setUp()
    {
        MockitoAnnotations.initMocks(this);

        dispatch = new DispatchStub(new SimpleResult<>(new ArrayList<MetaClassLite>()));
        cmd = new DelCommand(new AttributeCommandParam(attr, null, context), dialogs, dispatch);
        cmd.messages = messages;
        cmd.metainfoModificationService = metainfoModificationService;
        cmd.dialogMessages = dialogMessages;
        fqn = ClassFqn.parse(TestUtils.randomString());
        when(context.getMetainfo()).thenReturn(metainfo);
        when(metainfo.getFqn()).thenReturn(fqn);
        String question = TestUtils.randomString();
        when(messages.confirmDeleteQuestion(Mockito.any(), Mockito.any())).thenReturn(question);
        when(attr.getType()).thenReturn(attrType);
    }

    @After
    public void tearDown()
    {
        cmd = null;
        fqn = null;
    }

    @Test
    public void userCanceledDeletion()
    {
        //настройка системы
        ClassFqn fqn = ClassFqn.parse(TestUtils.randomString());
        when(attr.isHardcoded()).thenReturn(false);
        when(context.getMetainfo()).thenReturn(metainfo);
        when(metainfo.getFqn()).thenReturn(fqn);
        when(attr.getDeclaredMetaClass()).thenReturn(fqn);
        when(dialogs.question(anyString(), anyString(), Mockito.<DialogCallback> any())).thenAnswer(
                new SuccessAnswer<Buttons>(Buttons.NO, 2));
        //вызов системы
        cmd.execute(attr);
        //проверка утверждений
        Mockito.verifyNoMoreInteractions(metainfoModificationService);
        //очистка
    }
}
