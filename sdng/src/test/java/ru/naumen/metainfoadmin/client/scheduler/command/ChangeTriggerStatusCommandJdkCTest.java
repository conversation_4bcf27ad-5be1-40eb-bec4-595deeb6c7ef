package ru.naumen.metainfoadmin.client.scheduler.command;

import org.junit.Assert;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import ru.naumen.common.client.utils.CallbackDecorator;
import ru.naumen.common.client.utils.OnStartCallback;
import ru.naumen.core.SavedParameter;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.metainfo.client.MetainfoModificationServiceAsync;
import ru.naumen.metainfo.shared.scheduler.PeriodicTrigger;
import ru.naumen.metainfo.shared.scheduler.Trigger;

/**
 * <AUTHOR>
 * @since 01.07.2011
 *
 */
public class ChangeTriggerStatusCommandJdkCTest
{
    @Mock
    OnStartCallback<DtoContainer<Trigger>> callback;

    @Mock
    MetainfoModificationServiceAsync metainfoModificationService;

    @Test
    public void execute()
    {
        //настройка системы
        MockitoAnnotations.initMocks(this);

        Trigger trigger = new PeriodicTrigger();
        trigger.setEnabled(true);
        DtoContainer<Trigger> triggerDto = new DtoContainer<>(trigger);
        ChangeTriggerStatusCommand cmd = new OnTriggerCommand(new CommandParam<DtoContainer<Trigger>,
                DtoContainer<Trigger>>(triggerDto, callback));
        cmd.metainfoModificationService = metainfoModificationService;
        SavedParameter<CallbackDecorator<DtoContainer<Trigger>, DtoContainer<Trigger>>> saveCallback =
                new SavedParameter<>(1);
        Mockito.doAnswer(saveCallback).when(metainfoModificationService)
                .saveTrigger(Mockito.eq(triggerDto), Mockito.<CallbackDecorator<DtoContainer<Trigger>, DtoContainer<Trigger>>> any());
        //вызов системы
        cmd.execute();
        saveCallback.getValue().onSuccess(triggerDto);
        //проверка утверждений
        Mockito.verify(callback).onSuccess(triggerDto);
        Assert.assertFalse(trigger.isEnabled());
        //очистка
    }
}
