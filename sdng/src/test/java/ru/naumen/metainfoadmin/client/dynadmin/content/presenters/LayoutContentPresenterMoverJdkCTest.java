/**
 * 
 */
package ru.naumen.metainfoadmin.client.dynadmin.content.presenters;

import java.util.ArrayList;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import ru.naumen.core.client.layout.Band;
import ru.naumen.metainfo.shared.ui.FlowContent;
import ru.naumen.metainfo.shared.ui.Position;
import ru.naumen.metainfoadmin.client.common.content.FlowPresenter;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;

import com.google.common.collect.Lists;

/**
 * Тестирование алгоритма перемещения контента вверх\вниз на карточке метакласса
 * <AUTHOR>
 * @since 26 янв. 2016 г.
 *
 */
public class LayoutContentPresenterMoverJdkCTest
{
    @Mock
    FlowPresenter<FlowContent, UIContext> presenter1;
    @Mock
    FlowPresenter<FlowContent, UIContext> presenter2;
    @Mock
    FlowPresenter<FlowContent, UIContext> presenter3;
    @Mock
    FlowPresenter<FlowContent, UIContext> presenter4;
    @Mock
    FlowContent contentFull;
    @Mock
    FlowContent contentLeft;
    @Mock
    FlowContent contentRight;

    @Before
    public void setUp()
    {
        MockitoAnnotations.initMocks(this);

        Mockito.when(contentFull.getPosition()).thenReturn(Position.FULL);
        Mockito.when(contentLeft.getPosition()).thenReturn(Position.LEFT);
        Mockito.when(contentRight.getPosition()).thenReturn(Position.RIGHT);
    }

    /**
     * Тест на слияние лент, когда лента с одним позиционированием посередние двух лент с другим позиционированием, 
     * и двигаем контент из средней ленты - первая и последняя в итоге должны слиться
     */
    @Test
    public void testJoinBands1()
    {
        Mockito.when(presenter3.getContent()).thenReturn(contentLeft);

        ArrayList<Band<FlowPresenter<FlowContent, UIContext>>> bands = Lists.newArrayList();
        Band<FlowPresenter<FlowContent, UIContext>> band1 = new Band<FlowPresenter<FlowContent, UIContext>>(1);
        Band<FlowPresenter<FlowContent, UIContext>> band2 = new Band<FlowPresenter<FlowContent, UIContext>>(2);
        Band<FlowPresenter<FlowContent, UIContext>> band3 = new Band<FlowPresenter<FlowContent, UIContext>>(1);

        bands.add(band1);
        bands.add(band2);
        bands.add(band3);

        band1.getColumn(0).add(presenter1);
        band2.getColumn(0).add(presenter3);
        band3.getColumn(0).add(presenter4);

        LayoutContentPresenterMover mover = new LayoutContentPresenterMover(bands);
        mover.moveUp(presenter3);

        Assert.assertEquals(2, bands.size());
        Assert.assertEquals(1, bands.get(0).countPresenters());
        Assert.assertEquals(2, bands.get(1).countPresenters());
        Assert.assertEquals(0, bands.get(0).getColumn(0).getPresenters().indexOf(presenter3));
        Assert.assertEquals(0, bands.get(1).getColumn(0).getPresenters().indexOf(presenter1));
        Assert.assertEquals(1, bands.get(1).getColumn(0).getPresenters().indexOf(presenter4));
    }

    /**
     * Тест на слияние лент, когда 4 ленты поочередно с разным позиционированием 
     * В итоге 1 и 3, 2 и 4 должны слиться
     */
    @Test
    public void testJoinBands2()
    {
        Mockito.when(presenter3.getContent()).thenReturn(contentFull);

        ArrayList<Band<FlowPresenter<FlowContent, UIContext>>> bands = Lists.newArrayList();
        Band<FlowPresenter<FlowContent, UIContext>> band1 = new Band<FlowPresenter<FlowContent, UIContext>>(1);
        Band<FlowPresenter<FlowContent, UIContext>> band2 = new Band<FlowPresenter<FlowContent, UIContext>>(2);
        Band<FlowPresenter<FlowContent, UIContext>> band3 = new Band<FlowPresenter<FlowContent, UIContext>>(1);
        Band<FlowPresenter<FlowContent, UIContext>> band4 = new Band<FlowPresenter<FlowContent, UIContext>>(2);

        bands.add(band1);
        bands.add(band2);
        bands.add(band3);
        bands.add(band4);

        band1.getColumn(0).add(presenter1);
        band2.getColumn(0).add(presenter2);
        band3.getColumn(0).add(presenter3);
        band4.getColumn(0).add(presenter4);

        LayoutContentPresenterMover mover = new LayoutContentPresenterMover(bands);
        mover.moveUp(presenter3);

        Assert.assertEquals(2, bands.size());
        Assert.assertEquals(2, bands.get(0).countPresenters());
        Assert.assertEquals(2, bands.get(1).countPresenters());
        Assert.assertEquals(0, bands.get(0).getColumn(0).getPresenters().indexOf(presenter1));
        Assert.assertEquals(1, bands.get(0).getColumn(0).getPresenters().indexOf(presenter3));
        Assert.assertEquals(0, bands.get(1).getColumn(0).getPresenters().indexOf(presenter2));
        Assert.assertEquals(1, bands.get(1).getColumn(0).getPresenters().indexOf(presenter4));
    }

    /**
     * Тест на перемещение презентера вниз внутри ленты, на весь экран
     */
    @Test
    public void testMoveDownInBandFull()
    {
        ArrayList<Band<FlowPresenter<FlowContent, UIContext>>> bands = Lists.newArrayList();
        Band<FlowPresenter<FlowContent, UIContext>> band1 = new Band<FlowPresenter<FlowContent, UIContext>>(1);

        bands.add(band1);

        band1.getColumn(0).add(presenter1);
        band1.getColumn(0).add(presenter2);

        LayoutContentPresenterMover mover = new LayoutContentPresenterMover(bands);
        mover.moveDown(presenter1);
        Assert.assertEquals(1, bands.size());
        Assert.assertEquals(2, bands.get(0).countPresenters());
        Assert.assertEquals(0, bands.get(0).getColumn(0).getPresenters().indexOf(presenter2));
        Assert.assertEquals(1, bands.get(0).getColumn(0).getPresenters().indexOf(presenter1));
    }

    /**
     * Тест на перемещение презентера вниз внутри ленты, слева
     */
    @Test
    public void testMoveDownInBandLeft()
    {
        ArrayList<Band<FlowPresenter<FlowContent, UIContext>>> bands = Lists.newArrayList();
        Band<FlowPresenter<FlowContent, UIContext>> band1 = new Band<FlowPresenter<FlowContent, UIContext>>(2);

        bands.add(band1);

        band1.getColumn(0).add(presenter1);
        band1.getColumn(0).add(presenter2);

        LayoutContentPresenterMover mover = new LayoutContentPresenterMover(bands);
        mover.moveDown(presenter1);
        Assert.assertEquals(1, bands.size());
        Assert.assertEquals(2, bands.get(0).countPresenters());
        Assert.assertEquals(0, bands.get(0).getColumn(0).getPresenters().indexOf(presenter2));
        Assert.assertEquals(1, bands.get(0).getColumn(0).getPresenters().indexOf(presenter1));
    }

    /**
     * Тест на перемещение презентера вниз внутри ленты, справа
     */
    @Test
    public void testMoveDownInBandRight()
    {
        ArrayList<Band<FlowPresenter<FlowContent, UIContext>>> bands = Lists.newArrayList();
        Band<FlowPresenter<FlowContent, UIContext>> band1 = new Band<FlowPresenter<FlowContent, UIContext>>(2);

        bands.add(band1);

        band1.getColumn(1).add(presenter1);
        band1.getColumn(1).add(presenter2);

        LayoutContentPresenterMover mover = new LayoutContentPresenterMover(bands);
        mover.moveDown(presenter1);
        Assert.assertEquals(1, bands.size());
        Assert.assertEquals(2, bands.get(0).countPresenters());
        Assert.assertEquals(0, bands.get(0).getColumn(1).getPresenters().indexOf(presenter2));
        Assert.assertEquals(1, bands.get(0).getColumn(1).getPresenters().indexOf(presenter1));
    }

    /**
     * Тест на перемещение презентера вниз из одной ленты в другую, обе - на весь экран
     */
    @Test
    public void testMoveDownInBandsSameColCountFull()
    {
        Mockito.when(presenter2.getContent()).thenReturn(contentFull);

        ArrayList<Band<FlowPresenter<FlowContent, UIContext>>> bands = Lists.newArrayList();
        Band<FlowPresenter<FlowContent, UIContext>> band1 = new Band<FlowPresenter<FlowContent, UIContext>>(1);
        Band<FlowPresenter<FlowContent, UIContext>> band2 = new Band<FlowPresenter<FlowContent, UIContext>>(1);

        bands.add(band1);
        bands.add(band2);

        band1.getColumn(0).add(presenter1);
        band1.getColumn(0).add(presenter2);
        band2.getColumn(0).add(presenter3);
        band2.getColumn(0).add(presenter4);

        LayoutContentPresenterMover mover = new LayoutContentPresenterMover(bands);
        mover.moveDown(presenter2);
        Assert.assertEquals(2, bands.size());
        Assert.assertEquals(1, bands.get(0).countPresenters());
        Assert.assertEquals(3, bands.get(1).countPresenters());
        Assert.assertEquals(0, bands.get(0).getColumn(0).getPresenters().indexOf(presenter1));
        Assert.assertEquals(0, bands.get(1).getColumn(0).getPresenters().indexOf(presenter3));
        Assert.assertEquals(1, bands.get(1).getColumn(0).getPresenters().indexOf(presenter2));
        Assert.assertEquals(2, bands.get(1).getColumn(0).getPresenters().indexOf(presenter4));
    }

    /**
     * Тест на перемещение презентера вниз из одной ленты в другую, обе - слева
     */
    @Test
    public void testMoveDownInBandsSameColCountLeft()
    {
        Mockito.when(presenter2.getContent()).thenReturn(contentLeft);

        ArrayList<Band<FlowPresenter<FlowContent, UIContext>>> bands = Lists.newArrayList();
        Band<FlowPresenter<FlowContent, UIContext>> band1 = new Band<FlowPresenter<FlowContent, UIContext>>(2);
        Band<FlowPresenter<FlowContent, UIContext>> band2 = new Band<FlowPresenter<FlowContent, UIContext>>(2);

        bands.add(band1);
        bands.add(band2);

        band1.getColumn(0).add(presenter1);
        band1.getColumn(0).add(presenter2);
        band2.getColumn(0).add(presenter3);
        band2.getColumn(0).add(presenter4);

        LayoutContentPresenterMover mover = new LayoutContentPresenterMover(bands);
        mover.moveDown(presenter2);

        Assert.assertEquals(2, bands.size());
        Assert.assertEquals(1, bands.get(0).countPresenters());
        Assert.assertEquals(3, bands.get(1).countPresenters());
        Assert.assertEquals(0, bands.get(0).getColumn(0).getPresenters().indexOf(presenter1));
        Assert.assertEquals(0, bands.get(1).getColumn(0).getPresenters().indexOf(presenter3));
        Assert.assertEquals(1, bands.get(1).getColumn(0).getPresenters().indexOf(presenter2));
        Assert.assertEquals(2, bands.get(1).getColumn(0).getPresenters().indexOf(presenter4));
    }

    /**
     * Тест на перемещение презентера вниз из одной ленты в другую, обе - справа
     */
    @Test
    public void testMoveDownInBandsSameColCountRight()
    {
        Mockito.when(presenter2.getContent()).thenReturn(contentRight);

        ArrayList<Band<FlowPresenter<FlowContent, UIContext>>> bands = Lists.newArrayList();
        Band<FlowPresenter<FlowContent, UIContext>> band1 = new Band<FlowPresenter<FlowContent, UIContext>>(2);
        Band<FlowPresenter<FlowContent, UIContext>> band2 = new Band<FlowPresenter<FlowContent, UIContext>>(2);

        bands.add(band1);
        bands.add(band2);

        band1.getColumn(1).add(presenter1);
        band1.getColumn(1).add(presenter2);
        band2.getColumn(1).add(presenter3);
        band2.getColumn(1).add(presenter4);

        LayoutContentPresenterMover mover = new LayoutContentPresenterMover(bands);
        mover.moveDown(presenter2);

        Assert.assertEquals(2, bands.size());
        Assert.assertEquals(1, bands.get(0).countPresenters());
        Assert.assertEquals(3, bands.get(1).countPresenters());
        Assert.assertEquals(0, bands.get(0).getColumn(1).getPresenters().indexOf(presenter1));
        Assert.assertEquals(0, bands.get(1).getColumn(1).getPresenters().indexOf(presenter3));
        Assert.assertEquals(1, bands.get(1).getColumn(1).getPresenters().indexOf(presenter2));
        Assert.assertEquals(2, bands.get(1).getColumn(1).getPresenters().indexOf(presenter4));
    }

    /**
     * Тест на перемещение презентера вверх внутри ленты, на весь экран
     */
    @Test
    public void testMoveUpInBandFull()
    {
        ArrayList<Band<FlowPresenter<FlowContent, UIContext>>> bands = Lists.newArrayList();
        Band<FlowPresenter<FlowContent, UIContext>> band1 = new Band<FlowPresenter<FlowContent, UIContext>>(1);

        bands.add(band1);

        band1.getColumn(0).add(presenter1);
        band1.getColumn(0).add(presenter2);

        LayoutContentPresenterMover mover = new LayoutContentPresenterMover(bands);
        mover.moveUp(presenter2);

        Assert.assertEquals(1, bands.size());
        Assert.assertEquals(2, bands.get(0).countPresenters());
        Assert.assertEquals(0, bands.get(0).getColumn(0).getPresenters().indexOf(presenter2));
        Assert.assertEquals(1, bands.get(0).getColumn(0).getPresenters().indexOf(presenter1));
    }

    /**
     * Тест на перемещение презентера вверх внутри ленты, слева
     */
    @Test
    public void testMoveUpInBandLeft()
    {
        ArrayList<Band<FlowPresenter<FlowContent, UIContext>>> bands = Lists.newArrayList();
        Band<FlowPresenter<FlowContent, UIContext>> band1 = new Band<FlowPresenter<FlowContent, UIContext>>(2);

        bands.add(band1);

        band1.getColumn(0).add(presenter1);
        band1.getColumn(0).add(presenter2);

        LayoutContentPresenterMover mover = new LayoutContentPresenterMover(bands);
        mover.moveUp(presenter2);

        Assert.assertEquals(1, bands.size());
        Assert.assertEquals(2, bands.get(0).countPresenters());
        Assert.assertEquals(0, bands.get(0).getColumn(0).getPresenters().indexOf(presenter2));
        Assert.assertEquals(1, bands.get(0).getColumn(0).getPresenters().indexOf(presenter1));
    }

    /**
     * Тест на перемещение презентера вверх внутри ленты, справа
     */
    @Test
    public void testMoveUpInBandRight()
    {
        ArrayList<Band<FlowPresenter<FlowContent, UIContext>>> bands = Lists.newArrayList();
        Band<FlowPresenter<FlowContent, UIContext>> band1 = new Band<FlowPresenter<FlowContent, UIContext>>(2);

        bands.add(band1);

        band1.getColumn(1).add(presenter1);
        band1.getColumn(1).add(presenter2);

        LayoutContentPresenterMover mover = new LayoutContentPresenterMover(bands);
        mover.moveUp(presenter2);

        Assert.assertEquals(1, bands.size());
        Assert.assertEquals(2, bands.get(0).countPresenters());
        Assert.assertEquals(0, bands.get(0).getColumn(1).getPresenters().indexOf(presenter2));
        Assert.assertEquals(1, bands.get(0).getColumn(1).getPresenters().indexOf(presenter1));
    }

    /**
     * Тест на перемещение презентера вверх из ленты на весь экран через ленту с двумя колонками, в которой контенты расположены в обеих колонках 
     * (контент должен перескочить через первую ленту и создать себе новую ленту)
     */
    @Test
    public void testMoveUpInBandsDiffColCountFromFull()
    {
        Mockito.when(presenter3.getContent()).thenReturn(contentFull);

        ArrayList<Band<FlowPresenter<FlowContent, UIContext>>> bands = Lists.newArrayList();
        Band<FlowPresenter<FlowContent, UIContext>> band1 = new Band<FlowPresenter<FlowContent, UIContext>>(2);
        Band<FlowPresenter<FlowContent, UIContext>> band2 = new Band<FlowPresenter<FlowContent, UIContext>>(1);

        bands.add(band1);
        bands.add(band2);

        band1.getColumn(0).add(presenter1);
        band1.getColumn(1).add(presenter2);
        band2.getColumn(0).add(presenter3);
        band2.getColumn(0).add(presenter4);

        LayoutContentPresenterMover mover = new LayoutContentPresenterMover(bands);
        mover.moveUp(presenter3);
        Assert.assertEquals(3, bands.size());
        Assert.assertEquals(1, bands.get(0).countPresenters());
        Assert.assertEquals(2, bands.get(1).countPresenters());
        Assert.assertEquals(1, bands.get(2).countPresenters());
        Assert.assertEquals(0, bands.get(0).getColumn(0).getPresenters().indexOf(presenter3));
        Assert.assertEquals(0, bands.get(1).getColumn(0).getPresenters().indexOf(presenter1));
        Assert.assertEquals(0, bands.get(1).getColumn(1).getPresenters().indexOf(presenter2));
        Assert.assertEquals(0, bands.get(2).getColumn(0).getPresenters().indexOf(presenter4));

    }

    /**
     * Тест на перемещение презентера вверх из ленты на весь экран через ленту с двумя колонками, в которой контенты расположены только слева 
     * (последний контент в первой ленте должен выделиться в отдельную ленту)
     */
    @Test
    public void testMoveUpInBandsDiffColCountFromFullToLeftOnly()
    {
        Mockito.when(presenter3.getContent()).thenReturn(contentFull);

        ArrayList<Band<FlowPresenter<FlowContent, UIContext>>> bands = Lists.newArrayList();
        Band<FlowPresenter<FlowContent, UIContext>> band1 = new Band<FlowPresenter<FlowContent, UIContext>>(2);
        Band<FlowPresenter<FlowContent, UIContext>> band2 = new Band<FlowPresenter<FlowContent, UIContext>>(1);

        bands.add(band1);
        bands.add(band2);

        band1.getColumn(0).add(presenter1);
        band1.getColumn(0).add(presenter2);
        band2.getColumn(0).add(presenter3);
        band2.getColumn(0).add(presenter4);

        LayoutContentPresenterMover mover = new LayoutContentPresenterMover(bands);
        mover.moveUp(presenter3);

        Assert.assertEquals(4, bands.size());
        Assert.assertEquals(1, bands.get(0).countPresenters());
        Assert.assertEquals(1, bands.get(1).countPresenters());
        Assert.assertEquals(1, bands.get(2).countPresenters());
        Assert.assertEquals(1, bands.get(3).countPresenters());
        Assert.assertEquals(0, bands.get(0).getColumn(0).getPresenters().indexOf(presenter1));
        Assert.assertEquals(0, bands.get(1).getColumn(0).getPresenters().indexOf(presenter3));
        Assert.assertEquals(0, bands.get(2).getColumn(0).getPresenters().indexOf(presenter2));
        Assert.assertEquals(0, bands.get(3).getColumn(0).getPresenters().indexOf(presenter4));
    }

    /**
     * Тест на перемещение презентера вверх из ленты слева в ленту по всей ширине с одним контентом
     * (презентер должен перепрыгнуть через всю ленту)
     */
    @Test
    public void testMoveUpInBandsDiffColCountFromLeftToFullWithOne()
    {
        Mockito.when(presenter3.getContent()).thenReturn(contentLeft);

        ArrayList<Band<FlowPresenter<FlowContent, UIContext>>> bands = Lists.newArrayList();
        Band<FlowPresenter<FlowContent, UIContext>> band1 = new Band<FlowPresenter<FlowContent, UIContext>>(1);
        Band<FlowPresenter<FlowContent, UIContext>> band2 = new Band<FlowPresenter<FlowContent, UIContext>>(2);

        bands.add(band1);
        bands.add(band2);

        band1.getColumn(0).add(presenter1);
        band2.getColumn(0).add(presenter3);
        band2.getColumn(0).add(presenter4);

        LayoutContentPresenterMover mover = new LayoutContentPresenterMover(bands);
        mover.moveUp(presenter3);

        Assert.assertEquals(3, bands.size());
        Assert.assertEquals(1, bands.get(0).countPresenters());
        Assert.assertEquals(1, bands.get(1).countPresenters());
        Assert.assertEquals(1, bands.get(2).countPresenters());
        Assert.assertEquals(0, bands.get(0).getColumn(0).getPresenters().indexOf(presenter3));
        Assert.assertEquals(0, bands.get(1).getColumn(0).getPresenters().indexOf(presenter1));
        Assert.assertEquals(0, bands.get(2).getColumn(0).getPresenters().indexOf(presenter4));
    }

    /**
     * Тест на перемещение презентера вверх из ленты слева в ленту по всей ширине с несколькими контентом
     * (презентер должен перепрыгнуть последний контент первой ленты)
     */
    @Test
    public void testMoveUpInBandsDiffColCountFromLeftToFullWithSeveral()
    {
        Mockito.when(presenter3.getContent()).thenReturn(contentLeft);

        ArrayList<Band<FlowPresenter<FlowContent, UIContext>>> bands = Lists.newArrayList();
        Band<FlowPresenter<FlowContent, UIContext>> band1 = new Band<FlowPresenter<FlowContent, UIContext>>(1);
        Band<FlowPresenter<FlowContent, UIContext>> band2 = new Band<FlowPresenter<FlowContent, UIContext>>(2);

        bands.add(band1);
        bands.add(band2);

        band1.getColumn(0).add(presenter1);
        band1.getColumn(0).add(presenter2);
        band2.getColumn(0).add(presenter3);
        band2.getColumn(0).add(presenter4);

        LayoutContentPresenterMover mover = new LayoutContentPresenterMover(bands);
        mover.moveUp(presenter3);

        Assert.assertEquals(4, bands.size());
        Assert.assertEquals(1, bands.get(0).countPresenters());
        Assert.assertEquals(1, bands.get(1).countPresenters());
        Assert.assertEquals(1, bands.get(2).countPresenters());
        Assert.assertEquals(1, bands.get(3).countPresenters());
        Assert.assertEquals(0, bands.get(0).getColumn(0).getPresenters().indexOf(presenter1));
        Assert.assertEquals(0, bands.get(1).getColumn(0).getPresenters().indexOf(presenter3));
        Assert.assertEquals(0, bands.get(2).getColumn(0).getPresenters().indexOf(presenter2));
        Assert.assertEquals(0, bands.get(3).getColumn(0).getPresenters().indexOf(presenter4));
    }

    /**
     * Тест на перемещение презентера вверх из одной ленты в другую, обе - на весь экран
     */
    @Test
    public void testMoveUpInBandsSameColCountFull()
    {
        Mockito.when(presenter3.getContent()).thenReturn(contentFull);

        ArrayList<Band<FlowPresenter<FlowContent, UIContext>>> bands = Lists.newArrayList();
        Band<FlowPresenter<FlowContent, UIContext>> band1 = new Band<FlowPresenter<FlowContent, UIContext>>(1);
        Band<FlowPresenter<FlowContent, UIContext>> band2 = new Band<FlowPresenter<FlowContent, UIContext>>(1);

        bands.add(band1);
        bands.add(band2);

        band1.getColumn(0).add(presenter1);
        band1.getColumn(0).add(presenter2);
        band2.getColumn(0).add(presenter3);
        band2.getColumn(0).add(presenter4);

        LayoutContentPresenterMover mover = new LayoutContentPresenterMover(bands);
        mover.moveUp(presenter3);

        Assert.assertEquals(2, bands.size());
        Assert.assertEquals(3, bands.get(0).countPresenters());
        Assert.assertEquals(1, bands.get(1).countPresenters());
        Assert.assertEquals(0, bands.get(0).getColumn(0).getPresenters().indexOf(presenter1));
        Assert.assertEquals(1, bands.get(0).getColumn(0).getPresenters().indexOf(presenter3));
        Assert.assertEquals(2, bands.get(0).getColumn(0).getPresenters().indexOf(presenter2));
        Assert.assertEquals(0, bands.get(1).getColumn(0).getPresenters().indexOf(presenter4));
    }

    /**
     * Тест на перемещение презентера вверх из одной ленты в другую, обе - слева
     */
    @Test
    public void testMoveUpInBandsSameColCountLeft()
    {
        Mockito.when(presenter3.getContent()).thenReturn(contentLeft);

        ArrayList<Band<FlowPresenter<FlowContent, UIContext>>> bands = Lists.newArrayList();
        Band<FlowPresenter<FlowContent, UIContext>> band1 = new Band<FlowPresenter<FlowContent, UIContext>>(2);
        Band<FlowPresenter<FlowContent, UIContext>> band2 = new Band<FlowPresenter<FlowContent, UIContext>>(2);

        bands.add(band1);
        bands.add(band2);

        band1.getColumn(0).add(presenter1);
        band1.getColumn(0).add(presenter2);
        band2.getColumn(0).add(presenter3);
        band2.getColumn(0).add(presenter4);

        LayoutContentPresenterMover mover = new LayoutContentPresenterMover(bands);
        mover.moveUp(presenter3);
        Assert.assertEquals(2, bands.size());
        Assert.assertEquals(3, bands.get(0).countPresenters());
        Assert.assertEquals(1, bands.get(1).countPresenters());
        Assert.assertEquals(0, bands.get(0).getColumn(0).getPresenters().indexOf(presenter1));
        Assert.assertEquals(1, bands.get(0).getColumn(0).getPresenters().indexOf(presenter3));
        Assert.assertEquals(2, bands.get(0).getColumn(0).getPresenters().indexOf(presenter2));
        Assert.assertEquals(0, bands.get(1).getColumn(0).getPresenters().indexOf(presenter4));
    }

    /**
     * Тест на перемещение презентера вверх из одной ленты в другую, обе - справа
     */
    @Test
    public void testMoveUpInBandsSameColCountRight()
    {
        Mockito.when(presenter3.getContent()).thenReturn(contentRight);

        ArrayList<Band<FlowPresenter<FlowContent, UIContext>>> bands = Lists.newArrayList();
        Band<FlowPresenter<FlowContent, UIContext>> band1 = new Band<FlowPresenter<FlowContent, UIContext>>(2);
        Band<FlowPresenter<FlowContent, UIContext>> band2 = new Band<FlowPresenter<FlowContent, UIContext>>(2);

        bands.add(band1);
        bands.add(band2);

        band1.getColumn(1).add(presenter1);
        band1.getColumn(1).add(presenter2);
        band2.getColumn(1).add(presenter3);
        band2.getColumn(1).add(presenter4);

        LayoutContentPresenterMover mover = new LayoutContentPresenterMover(bands);
        mover.moveUp(presenter3);
        Assert.assertEquals(2, bands.size());
        Assert.assertEquals(3, bands.get(0).countPresenters());
        Assert.assertEquals(1, bands.get(1).countPresenters());
        Assert.assertEquals(0, bands.get(0).getColumn(1).getPresenters().indexOf(presenter1));
        Assert.assertEquals(1, bands.get(0).getColumn(1).getPresenters().indexOf(presenter3));
        Assert.assertEquals(2, bands.get(0).getColumn(1).getPresenters().indexOf(presenter2));
        Assert.assertEquals(0, bands.get(1).getColumn(1).getPresenters().indexOf(presenter4));
    }

    /**
     * Тест на перемещение презентера вверх из одной ленты в другую, в первой все контенты слева, во второй - справа
     */
    @Test
    public void testMoveUpInBandsSameColCountRightToLeftFilled()
    {
        Mockito.when(presenter3.getContent()).thenReturn(contentRight);

        ArrayList<Band<FlowPresenter<FlowContent, UIContext>>> bands = Lists.newArrayList();
        Band<FlowPresenter<FlowContent, UIContext>> band1 = new Band<FlowPresenter<FlowContent, UIContext>>(2);
        Band<FlowPresenter<FlowContent, UIContext>> band2 = new Band<FlowPresenter<FlowContent, UIContext>>(2);

        bands.add(band1);
        bands.add(band2);

        band1.getColumn(0).add(presenter1);
        band1.getColumn(0).add(presenter2);
        band2.getColumn(1).add(presenter3);
        band2.getColumn(1).add(presenter4);

        LayoutContentPresenterMover mover = new LayoutContentPresenterMover(bands);
        mover.moveUp(presenter3);
        Assert.assertEquals(2, bands.size());
        Assert.assertEquals(3, bands.get(0).countPresenters());
        Assert.assertEquals(1, bands.get(1).countPresenters());
        Assert.assertEquals(0, bands.get(0).getColumn(0).getPresenters().indexOf(presenter1));
        Assert.assertEquals(1, bands.get(0).getColumn(0).getPresenters().indexOf(presenter2));
        Assert.assertEquals(0, bands.get(0).getColumn(1).getPresenters().indexOf(presenter3));
        Assert.assertEquals(0, bands.get(1).getColumn(1).getPresenters().indexOf(presenter4));
    }
}