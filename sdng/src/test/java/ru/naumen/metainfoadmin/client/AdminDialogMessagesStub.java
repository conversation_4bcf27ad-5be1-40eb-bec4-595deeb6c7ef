package ru.naumen.metainfoadmin.client;

import jakarta.annotation.Nullable;

/**
 * Для использования в тестах
 * <AUTHOR>
 * @since 07.09.2011
 *
 */
public class AdminDialogMessagesStub implements AdminDialogMessages
{

    /**
     * Возвращает название метода, который вызвал этот метод
     * @return
     */
    public static String getCallerMethodName()
    {
        StackTraceElement[] e = Thread.currentThread().getStackTrace();
        StackTraceElement s = e[2];
        return s.getMethodName();
    }

    @Override
    public String addingContent()
    {
        return getCallerMethodName();
    }

    @Override
    public String additionalContacts()
    {
        return getCallerMethodName();
    }

    @Override
    public String addTabDialogCaption()
    {
        return getCallerMethodName();
    }

    @Override
    public String clientInfo()
    {
        return getCallerMethodName();
    }

    @Override
    public String contentProfiles()
    {
        return getCallerMethodName();
    }

    @Override
    public String contentVersProfiles()
    {
        return getCallerMethodName();
    }

    @Override
    public String contentType()
    {
        return getCallerMethodName();
    }

    @Override
    public String deleteTabMessage()
    {
        return getCallerMethodName();
    }

    @Override
    public String delLastTabError()
    {
        return getCallerMethodName();
    }

    @Override
    public String delTabCaption()
    {
        return getCallerMethodName();
    }

    @Override
    public String editForm()
    {
        return getCallerMethodName();
    }

    @Override
    public String editUI()
    {
        return getCallerMethodName();
    }

    @Override
    public String emplAttributeGroup()
    {
        return getCallerMethodName();
    }

    @Override
    public String newForm()
    {
        return getCallerMethodName();
    }

    @Override
    public String ouAttributeGroup()
    {
        return getCallerMethodName();
    }

    @Override
    public String place(String formTitle)
    {
        return getCallerMethodName();
    }

    @Override
    public String position()
    {
        return getCallerMethodName();
    }

    @Override
    public String relObjectClass()
    {
        return getCallerMethodName();
    }

    @Override
    public String removed(String title)
    {
        return getCallerMethodName();
    }

    @Override
    public String resetToSystemBtn()
    {
        return getCallerMethodName();
    }

    @Override
    public String resetUI()
    {
        return getCallerMethodName();
    }

    @Override
    public String tabGenitive()
    {
        return getCallerMethodName();
    }

    @Override
    public String teamAttributeGroup()
    {
        return getCallerMethodName();
    }

    @Override
    public String toFull()
    {
        return getCallerMethodName();
    }

    @Override
    public String toLeft()
    {
        return getCallerMethodName();
    }

    @Override
    public String toRight()
    {
        return getCallerMethodName();
    }

    @Override
    public String addCaseDialogCaption(String classTitle)
    {
        return null;
    }

    @Override
    public String addClassDialogCaption()
    {
        return null;
    }

    @Override
    public @Nullable
    String addWorkflowAttention()
    {
        return null;
    }

    @Override
    public String addWorkflowRequestTimedOut(String metaClassName)
    {
        return null;
    }

    @Override
    public String addWorkflowRequestTimedOutTitle()
    {
        return null;
    }

    @Override
    public String attributeIsUsedInTabTitle(String attributeTitle, String metaClass)
    {
        return null;
    }

    @Override
    public String attributeIsUsedInTabTitleMultiple(String attributeTitle, String metaClasses)
    {
        return null;
    }

    @Override
    public String attributesBackLinksNotCopy(String Metaclass, String attributeTitle)
    {
        return null;
    }

    @Override
    public String classCodeDuplication()
    {
        return null;
    }

    @Override
    public @Nullable String confirmAddWorkflowQuestion(String title)
    {
        return null;
    }

    @Override
    public @Nullable String confirmOperation()
    {
        return null;
    }

    @Override
    public String copyCaseDialogCaption()
    {
        return null;
    }

    @Override
    public String copyClassDialogCaption()
    {
        return null;
    }

    @Override
    public String editCaseDialogCaption()
    {
        return null;
    }

    @Override
    public String editClassDialogCaption()
    {
        return null;
    }

    @Override
    public String etc()
    {
        return null;
    }

    @Override
    public String hasResponsiblePropertyCaption()
    {
        return null;
    }

    @Override
    public String hasWorkflowPropertyCaption()
    {
        return null;
    }

    @Override
    public String help()
    {
        return null;
    }

    @Override
    public String helpDigitsCountRestriction()
    {
        return null;
    }

    @Override
    public String namingRule(String attributeTitle)
    {
        return null;
    }

    @Override
    public String numerationRule(String attributeTitle)
    {
        return null;
    }

    @Override
    public String overrideTabTitleAttribute()
    {
        return null;
    }

    @Override
    public String parentMetaclass()
    {
        return null;
    }

    @Override
    public String parentType()
    {
        return null;
    }

    @Override
    public String quotaExpirationDate()
    {
        return null;
    }

    @Override
    public String quotaName()
    {
        return null;
    }

    @Override
    public String quotaRemainder()
    {
        return null;
    }

    @Override
    public String quotingEnabled()
    {
        return null;
    }

    @Override
    public String responsibilityTransferTableEnabled()
    {
        return null;
    }

    @Override
    public String tabTitleAttribute()
    {
        return null;
    }

    @Override
    public String toNestedItselfMetaClassAttention(String metaClassTitle)
    {
        return null;
    }

    @Override
    public String toNonNestedfMetaClassAttention(String metaClassTitle, String parentAttributeTitle)
    {
        return null;
    }

    @Override
    public String toSameCls()
    {
        return null;
    }

    @Override
    public String typeCodeDuplication()
    {
        return null;
    }

    @Override
    @Description("Заголовок диалога")
    public String addGroupDialogCaption()
    {
        return null;
    }

    @Override
    public String addingMarker()
    {
        return null;
    }

    @Override
    @Description("Кнопка добавления новой группы")
    public String addNewGroup()
    {
        return null;
    }

    @Override
    @Description("запрос подтверждения удаления группы")
    public String confirmDeleteGroup(String grpTitle)
    {
        return null;
    }

    @Override
    @Description("Часть сообщения пользователю")
    public String deletionImpossible(String grpTitle, String place)
    {
        return String.format("Группа атрибутов ''%s'' не может быть удалена. Группа используется в настройках %s ",
                grpTitle, place);
    }

    @Override
    @Description("Заголовок диалога")
    public String editAttrGroupDialogCaption()
    {
        return null;
    }

    @Override
    public String editingMarker()
    {
        return null;
    }

    @Override
    public String groupAttributes()
    {
        return null;
    }

    @Override
    public String resetSettings()
    {
        return null;
    }

    @Override
    @Description("Изменить набор атрибутов в группе")
    public String selectAttributes()
    {
        return null;
    }

    @Override
    public String settingsSet()
    {
        return null;
    }
}
