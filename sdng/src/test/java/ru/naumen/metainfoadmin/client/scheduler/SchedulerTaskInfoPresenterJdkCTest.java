package ru.naumen.metainfoadmin.client.scheduler;

import static org.mockito.Mockito.when;

import jakarta.inject.Named;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.place.shared.PlaceController;
import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.safehtml.shared.SafeHtmlUtils;

import ru.naumen.common.ReturnFirstArgAnswer;
import ru.naumen.common.client.utils.OnStartCallback;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.CommonMessagesStub;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonPresenter;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonToolBarDisplay;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonFactory;
import ru.naumen.core.client.widgets.Attention;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.shared.Constants.Tag;
import ru.naumen.core.shared.TestUtils;
import ru.naumen.core.shared.common.Formatters;
import ru.naumen.core.shared.common.I18nUtil;
import ru.naumen.core.shared.common.I18nUtilStub;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.metainfo.client.events.SchedulerTaskUpdatedEvent;
import ru.naumen.metainfo.shared.scheduler.ExecuteScriptTask;
import ru.naumen.metainfo.shared.scheduler.ExecuteScriptTaskDto;
import ru.naumen.metainfo.shared.scheduler.ExecuteScriptTaskDtoFactory;
import ru.naumen.metainfo.shared.scheduler.SchedulerTask;
import ru.naumen.metainfo.shared.script.ScriptDto;
import ru.naumen.metainfo.shared.script.ScriptDtoFactory;
import ru.naumen.metainfo.shared.ui.LocalizedString;
import ru.naumen.metainfoadmin.client.attributes.InfoDisplay;
import ru.naumen.metainfoadmin.client.scheduler.command.DeleteSchTaskCommand;
import ru.naumen.metainfoadmin.client.scheduler.command.EditSchTaskCommand;
import ru.naumen.metainfoadmin.client.tags.formatters.TagPropertyFormatter;
import ru.naumen.uniquerandom.UniqueNumbersGenerator;

/**
 * <AUTHOR>
 * @since 06.06.2011
 *
 */
public class SchedulerTaskInfoPresenterJdkCTest
{
    @Mock
    private EventBus eventBus;
    @Mock
    private InfoDisplay display;
    @Mock
    private SchedulerTaskMessages messages;
    @Mock
    @Named("text")
    Property<String> title;
    @Mock
    @Named("text")
    Property<String> code;
    @Mock
    @Named("text")
    Property<String> description;
    @Mock
    @Named("text")
    Property<String> lastExecutionDate;
    @Mock
    @Named("text")
    Property<String> planExecutionDate;
    @Mock
    @Named("text")
    Property<ScriptDto> script;
    @Mock
    @Named("htmlText")
    Property<String> tags;
    @Mock
    Formatters formatters;
    @Mock
    ButtonFactory buttonToolFactory;
    @Mock
    ButtonPresenter runButton;
    @Mock
    ButtonPresenter editButton;
    @Mock
    ButtonPresenter deleteButton;
    @Mock
    ButtonToolBarDisplay buttonToolBar;
    @Mock
    PlaceController placeController;
    @Mock
    OnStartCallback<?> refreshCallback;
    @Mock
    DeleteSchTaskCommand deleteSchTaskCommand;
    @Mock
    EditSchTaskCommand editSchTaskCommand;
    @Mock
    TagPropertyFormatter tagPropertyFormatter;
    @Mock
    Attention attentionWidget;
    I18nUtil i18nUtil;

    ExecuteScriptTaskInfoPresenter presenter;
    ExecuteScriptTask schTask;
    CommonMessages cmessages = new CommonMessagesStub();
    String CURRENT_LANG = "ru";

    @Test
    public void onSchedulerTaskUpdated()
    {
        //настройка системы
        ExecuteScriptTask newTask = new ExecuteScriptTask();
        newTask.getTitle().add(new LocalizedString(CURRENT_LANG, TestUtils.randomString()));
        newTask.getDescription().add(new LocalizedString(CURRENT_LANG, TestUtils.randomString()));
        newTask.setType(ExecuteScriptTask.NAME);
        newTask.setCode(TestUtils.randomString());
        ScriptDto script = ScriptDtoFactory.createNew();
        script.setCode("script" + UniqueNumbersGenerator.nextInt(10000));
        newTask.setScript(script.getCode());
        ExecuteScriptTaskDto scriptedTask = ExecuteScriptTaskDtoFactory.create(newTask, script);
        DtoContainer<SchedulerTask> taskDto = new DtoContainer<>(scriptedTask);
        taskDto.setProperty(Tag.IS_ELEMENT_ENABLED, true);
        SchedulerTaskUpdatedEvent e = new SchedulerTaskUpdatedEvent(taskDto);

        //вызов системы
        presenter.onSchedulerTaskUpdated(e);
        //проверка утверждений
        Mockito.verify(title).setValue(i18nUtil.getLocalizedTitle(newTask));
        Mockito.verify(code).setValue(newTask.getCode());
        Mockito.verify(description).setValue(i18nUtil.getLocalizedDescription(newTask));
        Mockito.verify(tags).setValue("");
        Mockito.verify(this.script).setValue(script);
        //очистка
    }

    @Before
    public void setUp()
    {
        MockitoAnnotations.initMocks(this);

        i18nUtil = new I18nUtilStub();
        setUpFactory();
        setUpDisplay();
        setUpPresenter();
        setUpSchTask();
        Mockito.when(formatters.formatText(Mockito.anyString())).thenAnswer(ReturnFirstArgAnswer.getInstance());
        Mockito.when(formatters.normalize(Mockito.<SafeHtml> any())).thenAnswer(ReturnFirstArgAnswer.getInstance());
        Mockito.when(tagPropertyFormatter.formatToAnchors(Mockito.any())).thenReturn(SafeHtmlUtils.EMPTY_SAFE_HTML);
        Mockito.when(display.getAttention()).thenReturn(attentionWidget);
    }

    @After
    public void tearDown()
    {
        presenter = null;
        schTask = null;
    }

    private void setUpDisplay()
    {
        when(display.getToolBar()).thenReturn(buttonToolBar);
    }

    private void setUpFactory()
    {
        when(buttonToolFactory.create(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
                Mockito.<CommandParam> any())).thenReturn(runButton, editButton, deleteButton);
    }

    private void setUpPresenter()
    {
        presenter = new ExecuteScriptTaskInfoPresenter(display, eventBus);
        presenter.title = title;
        presenter.code = code;
        presenter.description = description;
        presenter.lastExecutionDate = lastExecutionDate;
        presenter.planExecutionDate = planExecutionDate;
        presenter.script = script;
        presenter.tags = tags;
        presenter.formatters = formatters;
        presenter.i18nUtil = i18nUtil;
        presenter.tagPropertyFormatter = tagPropertyFormatter;
    }

    private void setUpSchTask()
    {
        schTask = new ExecuteScriptTask();
        schTask.getTitle().add(new LocalizedString(CURRENT_LANG, TestUtils.randomString()));
        schTask.getDescription().add(new LocalizedString(CURRENT_LANG, TestUtils.randomString()));
        schTask.setType(ExecuteScriptTask.NAME);
        schTask.setScript(TestUtils.randomString());
    }
}
