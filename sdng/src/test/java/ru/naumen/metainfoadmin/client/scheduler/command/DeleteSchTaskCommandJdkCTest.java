package ru.naumen.metainfoadmin.client.scheduler.command;

import java.util.Collection;

import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import com.google.common.collect.Lists;
import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.common.Dialogs;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.metainfo.client.MetainfoModificationServiceAsync;
import ru.naumen.metainfo.shared.scheduler.SchedulerTask;
import ru.naumen.metainfoadmin.client.scheduler.SchedulerTaskMessages;

/**
 * <AUTHOR>
 * @since 01.07.2011
 *
 */
public class DeleteSchTaskCommandJdkCTest
{
    @Mock
    private Dialogs dialogs;
    @Mock
    private SchedulerTaskMessages messages;
    @Mock
    private MetainfoModificationServiceAsync metainfoModificationService;
    @Mock
    private AsyncCallback<Void> callback;
    @Mock
    private SchedulerTask schTask;

    /**
     * Проверяем, что при вызове onDialogSuccess будет вызыван deleteSchedulerTask с нужными параметрами
     */
    @Test
    public void execute()
    {
        //настройка системы
        MockitoAnnotations.initMocks(this);
        CommandParam<Collection<DtObject>, Void> param = new CommandParam<Collection<DtObject>, Void>(
                Lists.newArrayList(new SimpleDtObject(schTask.getCode(), "")), callback);
        DeleteSchTaskCommand cmd = new DeleteSchTaskCommand(param, dialogs);
        cmd.messages = messages;
        cmd.metainfoModificationService = metainfoModificationService;
        //вызов системы
        cmd.onDialogSuccess(param);
        //проверка утверждений
        Mockito.verify(metainfoModificationService).deleteSchedulerTask(Lists.newArrayList(schTask.getCode()),
                callback);
        //очистка
    }

}
