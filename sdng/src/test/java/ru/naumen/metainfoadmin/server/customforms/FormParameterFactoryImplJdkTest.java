package ru.naumen.metainfoadmin.server.customforms;

import static java.util.Arrays.asList;
import static java.util.Collections.emptyList;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import static ru.naumen.metainfo.shared.Constants.AggregateAttributeType.AGGREGATE_CLASSES;
import static ru.naumen.metainfo.shared.Constants.ObjectAttributeType.METACLASS_FQN;
import static ru.naumen.metainfo.shared.Constants.ObjectAttributeType.PERMITTED_TYPES;
import static ru.naumen.metainfo.shared.Constants.Presentations.SELECT_SORTING;

import java.util.List;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.server.attrdescription.resolvers.ResolverContext;
import ru.naumen.core.server.attrdescription.resolvers.ResolverUtils;
import ru.naumen.core.server.bo.permittedtyperelation.PermittedTypeRelationUtils;
import ru.naumen.core.server.customforms.FormParameter;
import ru.naumen.core.server.customforms.ParameterType;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.Employee;
import ru.naumen.core.shared.Constants.OU;
import ru.naumen.core.shared.Constants.Team;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.core.shared.utils.ILocaleInfo;
import ru.naumen.metainfo.server.spi.dispatch.HandlerUtils;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.AggregateAttributeType;
import ru.naumen.metainfo.shared.Constants.CatalogItemAttributeType;
import ru.naumen.metainfo.shared.Constants.ObjectAttributeType;
import ru.naumen.metainfo.shared.Constants.Presentations;
import ru.naumen.metainfo.shared.Constants.StringAttributeType;
import ru.naumen.metainfo.shared.elements.AttributeDescription;
import ru.naumen.metainfo.shared.elements.AttributeType;
import ru.naumen.metainfo.shared.script.ScriptDto;
import ru.naumen.metainfoadmin.shared.customforms.SaveFormParameterAction;

/**
 * 
 * <AUTHOR>
 * @since 20 апр. 2016 г.
 */
public class FormParameterFactoryImplJdkTest
{
    private static final ClassFqn OU_CASE1 = ClassFqn.parse("ou$ouCase1");
    private static final ClassFqn OU_CASE2 = ClassFqn.parse("ou$ouCase2");

    private HandlerUtils handlerUtils;

    private FormParameterFactoryImpl factory;

    @Before
    public void setUp()
    {
        handlerUtils = mock(HandlerUtils.class);
        PermittedTypeRelationUtils permittedTypeRelationUtils = mock(PermittedTypeRelationUtils.class);
        when(permittedTypeRelationUtils.getPermittedTypes((ParameterType)any())).then(invocation ->
        {
            ParameterType type = (ParameterType)invocation.getArguments()[0];
            return type.getPermittedTypes();
        });

        ResolverUtils resolverUtils = mock(ResolverUtils.class);
        when(resolverUtils.resolv(any())).then(invocation ->
        {
            ResolverContext context = (ResolverContext)invocation.getArguments()[0];
            return context.getRawValue();
        });

        ILocaleInfo localeInfo = mock(ILocaleInfo.class);
        when(localeInfo.getCurrentLang()).thenReturn("en");

        factory = new FormParameterFactoryImpl(resolverUtils, handlerUtils, localeInfo, permittedTypeRelationUtils);
    }

    @Test
    public void testCreateAggrAttribute()
    {
        IProperties typeProperties = new MapProperties();
        typeProperties.setProperty(AGGREGATE_CLASSES, Lists.newArrayList(Employee.FQN, OU.FQN, Team.FQN));

        //@formatter:off
        SaveFormParameterAction action = SaveFormParameterAction.create()
                .setCode("paramCode")
                .setTitle("paramTitle")
                .setTypeCode(AggregateAttributeType.CODE)
                .setTypeProperties(typeProperties)
                .setViewPresentation(createPresentation(Presentations.AGGREGATE_VIEW))
                .setEditPresentation(createPresentation(Presentations.AGGREGATE_EDIT));
        //@formatter:on

        FormParameterInfo parameterInfo = factory.create(action);
        FormParameter parameter = parameterInfo.getParameter();

        assertEquals("paramCode", parameter.getCode());
        assertEquals("paramTitle", parameter.getTitle());

        AttributeType attrType = parameter.getType();
        assertEquals(AggregateAttributeType.CODE, attrType.getCode());
        assertEquals(parameter, attrType.getAttribute());

        List<AttributeDescription> expectedAttrs = Lists.newArrayList(
                new AttributeDescription("paramCode_em", emptyList()),
                new AttributeDescription("paramCode_ou", emptyList()),
                new AttributeDescription("paramCode_te", emptyList()));
        List<AttributeDescription> attrs = attrType.<ru.naumen.metainfo.shared.elements.AggregateAttributeType> cast()
                .getAttributes();
        assertEquals(expectedAttrs, attrs);

        assertEquals(Employee.FQN, attrs.get(0).getReferenceMetaClass());
        assertEquals(OU.FQN, attrs.get(1).getReferenceMetaClass());
        assertEquals(Team.FQN, attrs.get(2).getReferenceMetaClass());

        assertEquals(asList(Constants.PARENT_ATTR, Employee.TEAMS), attrs.get(0).getParents());
        assertEquals(emptyList(), attrs.get(1).getParents());
        assertEquals(emptyList(), attrs.get(2).getParents());
    }

    @Test
    public void testCreateCatalogItemWithSort()
    {
        IProperties editPrs = createPresentation(Presentations.CATALOG_ITEM_EDIT);
        editPrs.setProperty(SELECT_SORTING, "title");

        //@formatter:off
        SaveFormParameterAction action = SaveFormParameterAction.create()
                .setCode("paramCode")
                .setTitle("paramTitle")
                .setTypeCode(CatalogItemAttributeType.CODE)
                .setViewPresentation(createPresentation(Presentations.AGGREGATE_VIEW))
                .setEditPresentation(editPrs);
        //@formatter:on

        FormParameterInfo parameterInfo = factory.create(action);
        FormParameter parameter = parameterInfo.getParameter();

        assertEquals("title", parameter.getEditPresentation().getProperty(SELECT_SORTING));
    }

    @Test
    public void testCreateComputableOnForm()
    {
        ScriptDto script = new ScriptDto("script-code");

        //@formatter:off
        SaveFormParameterAction action = SaveFormParameterAction.create()
                .setCode("paramCode")
                .setTitle("paramTitle")
                .setTypeCode(StringAttributeType.CODE)
                .setViewPresentation(createPresentation(Presentations.STRING_VIEW))
                .setEditPresentation(createPresentation(Presentations.STRING_EDIT))
                .setComputableOnForm(true)
                .setComputableOnFormScript(script);
        //@formatter:on

        FormParameterInfo parameterInfo = factory.create(action);

        assertEquals(script, parameterInfo.getComputableOnFormScript());
    }

    @Test
    public void testCreateFilteredBolinks()
    {
        ScriptDto filtrationScript = new ScriptDto("script-code");
        //@formatter:off
        SaveFormParameterAction action = SaveFormParameterAction.create()
                .setCode("paramCode")
                .setTitle("paramTitle")
                .setTypeCode(ObjectAttributeType.CODE)
                .setTypeProperties(getTypeProperties())
                .setEditable(true)
                .setRequired(true)
                .setViewPresentation(createPresentation(Presentations.BO_REFERENCE))
                .setEditPresentation(createPresentation(Presentations.BO_SELECT))
                .setFilteredByScript(true)
                .setScriptForFiltration(filtrationScript);
        //@formatter:on

        FormParameterInfo parameterInfo = factory.create(action);

        assertEquals(filtrationScript, parameterInfo.getFiltrationScript());
    }

    @Test
    public void testCreateFromAddAction()
    {
        List<DtObject> defaultValue = asList(new SimpleDtObject("ou$1", "OU1"), new SimpleDtObject("ou$2", "OU2"));

        when(handlerUtils.transferDefaultValue(defaultValue)).thenReturn(asList("ou$1", "ou$2"));

        String desc = "This is a disciption of attribute";

        //@formatter:off
        SaveFormParameterAction action = SaveFormParameterAction.create()
                .setCode("paramCode")
                .setTitle("paramTitle")
                .setTypeCode(ObjectAttributeType.CODE)
                .setTypeProperties(getTypeProperties())
                .setEditable(true)
                .setRequired(true)
                .setViewPresentation(createPresentation(Presentations.BO_REFERENCE))
                .setEditPresentation(createPresentation(Presentations.BO_SELECT))
                .setDefaultValue(defaultValue)
                .setDescription(desc);
        //@formatter:on

        FormParameterInfo parameterInfo = factory.create(action);
        FormParameter parameter = parameterInfo.getParameter();

        assertEquals("paramCode", parameter.getCode());
        assertEquals("paramTitle", parameter.getTitle());

        IProperties title = parameter.getTitleAsProps();
        assertEquals(1, title.propertyNames().size());
        assertEquals("paramTitle", title.getProperty("en"));

        AttributeType attrType = parameter.getType();
        assertEquals(ObjectAttributeType.CODE, attrType.getCode());
        assertEquals(parameter, attrType.getAttribute());

        ru.naumen.metainfo.shared.elements.ObjectAttributeType objectType = attrType.cast();
        Assert.assertNotNull(objectType);
        assertEquals(OU.FQN, objectType.getRelatedMetaClass());
        assertEquals(Sets.newHashSet(OU_CASE1, OU_CASE2), objectType.getPermittedTypes());

        assertTrue(parameter.isEditable());
        assertTrue(parameter.isRequired());

        assertEquals(Presentations.BO_REFERENCE, parameter.getViewPresentation().getCode());
        assertEquals(Presentations.BO_SELECT, parameter.getEditPresentation().getCode());

        assertEquals(asList("ou$1", "ou$2"), parameter.getDefaultValue());

        assertEquals(desc, parameter.getDescription());

        Assert.assertNull(parameterInfo.getFiltrationScript());
    }

    @Test
    public void testCreateWithDefaultValueScript()
    {
        ScriptDto script = new ScriptDto("script-code");

        //@formatter:off
        SaveFormParameterAction action = SaveFormParameterAction.create()
                .setCode("paramCode")
                .setTitle("paramTitle")
                .setTypeCode(StringAttributeType.CODE)
                .setViewPresentation(createPresentation(Presentations.STRING_VIEW))
                .setEditPresentation(createPresentation(Presentations.STRING_EDIT))
                .setDefaultByScript(true)
                .setScriptForDefault(script);
        //@formatter:on

        FormParameterInfo parameterInfo = factory.create(action);

        assertEquals(script, parameterInfo.getDefaultValueScript());
    }

    private static IProperties createPresentation(String code)
    {
        IProperties properties = new MapProperties();
        properties.setProperty(Presentations.ATTR_CODE, code);
        return properties;
    }

    private static IProperties getTypeProperties()
    {
        IProperties properties = new MapProperties();
        properties.setProperty(METACLASS_FQN, "ou");
        properties.setProperty(PERMITTED_TYPES, Sets.newHashSet(OU_CASE1, OU_CASE2));
        return properties;
    }
}
