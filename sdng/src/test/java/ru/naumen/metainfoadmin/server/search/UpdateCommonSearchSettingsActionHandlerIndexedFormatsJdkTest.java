package ru.naumen.metainfoadmin.server.search;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.*;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import jakarta.transaction.RollbackException;
import jakarta.transaction.Status;
import jakarta.transaction.Synchronization;
import jakarta.transaction.SystemException;
import jakarta.transaction.Transaction;
import jakarta.transaction.TransactionManager;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.admin.server.permission.AdminPermissionCheckService;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.settings.SettingsStorage;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.core.shared.settings.Settings;
import ru.naumen.fts.server.lucene.docbuilders.parse.FileContentParserProvider;
import ru.naumen.metainfo.server.spi.MetainfoModification;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.CommonSearchSetting;
import ru.naumen.metainfo.shared.search.CommonSearchSettings;
import ru.naumen.metainfo.shared.search.IndexedFileTypesConstants.Codes;
import ru.naumen.metainfo.shared.search.IndexedFileTypesConstants.Codes.SpecialCodes;
import ru.naumen.metainfo.shared.search.UpdateCommonSearchSettingAction;
import ru.naumen.sec.server.admin.log.CommonSearchSettingsLogService;
import ru.naumen.sec.server.autorize.AdminChecker;

@RunWith(MockitoJUnitRunner.class)
public class UpdateCommonSearchSettingsActionHandlerIndexedFormatsJdkTest
{
    @Mock
    private MessageFacade messageFacade;

    @Mock
    private SettingsStorage settingsStorage;

    @Mock
    private MetainfoModification metainfoModification;

    @Mock
    private CommonSearchSettingsLogService commonSearchSettingsLogService;

    @Mock
    private TransactionManager transactionManager;

    @Mock
    private FileContentParserProvider fileContentParserProvider;

    @Mock
    private Transaction transaction;

    @Mock
    private AdminChecker adminChecker;

    @Mock
    private AdminPermissionCheckService adminPermissionCheckService;

    private UpdateCommonSearchSettingsActionHandler handler;

    @Before
    public void setUp() throws Exception
    {

        final Settings settings = new Settings();
        settings.setCommonSearchSettings(new CommonSearchSettings());
        when(settingsStorage.getSettings()).thenReturn(settings);

        when(transactionManager.getTransaction()).thenReturn(transaction);

        final UpdateIndexedFormatsHandler updateIndexedFormatsHandler = new UpdateIndexedFormatsHandler(messageFacade,
                settingsStorage, metainfoModification,
                commonSearchSettingsLogService, transactionManager, fileContentParserProvider);
        final Map<String, UpdateCommonSearchSettingHandler> handlers = Map.of(
                CommonSearchSettings.INDEXED_FORMATS_CODE, updateIndexedFormatsHandler);
        handler = new UpdateCommonSearchSettingsActionHandler(handlers, messageFacade,
                adminChecker, adminPermissionCheckService);
    }

    @Test
    public void testUpdateIndexedFormats() throws DispatchException, RollbackException, SystemException
    {
        final SimpleDtObject simpleDtObject = new SimpleDtObject();
        simpleDtObject.setProperty(FakeMetaClassesConstants.CODE, CommonSearchSettings.INDEXED_FORMATS_CODE);
        simpleDtObject.setProperty(CommonSearchSetting.VALUE, List.of(Codes.MS_OFFICE));
        final UpdateCommonSearchSettingAction action = new UpdateCommonSearchSettingAction();
        action.setValue(simpleDtObject);
        handler.executeInTransaction(action, null);

        final ArgumentCaptor<Settings> settingsArgumentCaptor = ArgumentCaptor.forClass(Settings.class);
        final ArgumentCaptor<CommonSearchSettings> commonSettingsCaptor = ArgumentCaptor.forClass(
                CommonSearchSettings.class);
        final ArgumentCaptor<Synchronization> syncCaptor = ArgumentCaptor.forClass(Synchronization.class);
        verify(settingsStorage).saveSettings(settingsArgumentCaptor.capture());
        verify(commonSearchSettingsLogService).logCommonSearchSettingsChanged(commonSettingsCaptor.capture(),
                commonSettingsCaptor.capture());
        verify(transaction).registerSynchronization(syncCaptor.capture());

        final CommonSearchSettings persistedSettings = settingsArgumentCaptor.getValue().getCommonSearchSettings();
        assertEquals(List.of(Codes.MS_OFFICE), persistedSettings.getIndexedFormats());

        final List<CommonSearchSettings> loggedSettings = commonSettingsCaptor.getAllValues();
        final CommonSearchSettings newSettings = loggedSettings.get(0);
        final CommonSearchSettings oldSettings = loggedSettings.get(1);

        assertEquals(List.of(Codes.MS_OFFICE), newSettings.getIndexedFormats());
        assertTrue(oldSettings.getIndexedFormats().isEmpty());

        final Synchronization sync = syncCaptor.getValue();
        sync.afterCompletion(Status.STATUS_COMMITTED);

        verify(fileContentParserProvider).reconfigure(eq(List.of(Codes.MS_OFFICE)));
    }

    @Test
    public void testEmptyIndexFormats() throws DispatchException, RollbackException, SystemException
    {
        final SimpleDtObject simpleDtObject = new SimpleDtObject();
        simpleDtObject.setProperty(FakeMetaClassesConstants.CODE, CommonSearchSettings.INDEXED_FORMATS_CODE);
        simpleDtObject.setProperty(CommonSearchSetting.VALUE, List.of());
        final UpdateCommonSearchSettingAction action = new UpdateCommonSearchSettingAction();
        action.setValue(simpleDtObject);
        handler.executeInTransaction(action, null);

        final ArgumentCaptor<Settings> settingsArgumentCaptor = ArgumentCaptor.forClass(Settings.class);
        final ArgumentCaptor<CommonSearchSettings> commonSettingsCaptor = ArgumentCaptor.forClass(
                CommonSearchSettings.class);
        final ArgumentCaptor<Synchronization> syncCaptor = ArgumentCaptor.forClass(Synchronization.class);
        verify(settingsStorage).saveSettings(settingsArgumentCaptor.capture());
        verify(commonSearchSettingsLogService).logCommonSearchSettingsChanged(commonSettingsCaptor.capture(),
                commonSettingsCaptor.capture());
        verify(transaction).registerSynchronization(syncCaptor.capture());

        final CommonSearchSettings persistedSettings = settingsArgumentCaptor.getValue().getCommonSearchSettings();
        assertEquals(List.of(SpecialCodes.USER_DEFINED_EMPTY), persistedSettings.getIndexedFormats());

        final List<CommonSearchSettings> loggedSettings = commonSettingsCaptor.getAllValues();
        final CommonSearchSettings newSettings = loggedSettings.get(0);
        final CommonSearchSettings oldSettings = loggedSettings.get(1);

        assertEquals(List.of(SpecialCodes.USER_DEFINED_EMPTY), newSettings.getIndexedFormats());
        assertTrue(oldSettings.getIndexedFormats().isEmpty());

        final Synchronization sync = syncCaptor.getValue();
        sync.afterCompletion(Status.STATUS_COMMITTED);

        verify(fileContentParserProvider).reconfigure(eq(Collections.emptyList()));
    }

    @Test
    public void testTransactionRollback() throws DispatchException, RollbackException, SystemException
    {
        final SimpleDtObject simpleDtObject = new SimpleDtObject();
        simpleDtObject.setProperty(FakeMetaClassesConstants.CODE, CommonSearchSettings.INDEXED_FORMATS_CODE);
        simpleDtObject.setProperty(CommonSearchSetting.VALUE, List.of());
        final UpdateCommonSearchSettingAction action = new UpdateCommonSearchSettingAction();
        action.setValue(simpleDtObject);
        handler.executeInTransaction(action, null);

        final ArgumentCaptor<Synchronization> syncCaptor = ArgumentCaptor.forClass(Synchronization.class);
        verify(transaction).registerSynchronization(syncCaptor.capture());

        final Synchronization sync = syncCaptor.getValue();
        sync.afterCompletion(Status.STATUS_ROLLEDBACK);

        verify(fileContentParserProvider, times(0)).reconfigure(anyList());
    }

    @Test(expected = FxException.class)
    public void testExceptionOnNull() throws DispatchException
    {
        final SimpleDtObject simpleDtObject = new SimpleDtObject();
        simpleDtObject.setProperty(FakeMetaClassesConstants.CODE, CommonSearchSettings.INDEXED_FORMATS_CODE);
        final UpdateCommonSearchSettingAction action = new UpdateCommonSearchSettingAction();
        action.setValue(simpleDtObject);
        handler.executeInTransaction(action, null);
    }
}
