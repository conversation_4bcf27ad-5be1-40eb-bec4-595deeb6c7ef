package ru.naumen.websocket.server.config;

import static org.junit.Assert.assertEquals;

import java.net.InetSocketAddress;
import java.net.URI;
import java.util.Deque;
import java.util.LinkedList;

import org.junit.Test;
import org.mockito.Mockito;

import ru.naumen.sec.server.jwt.mobile.utils.JwtTokenExtractor;
import ru.naumen.websocket.server.config.WebSocketConfig.StompBrokerAddressSupplier;

/**
 * Тестирование {@link WebSocketConfig}
 * <AUTHOR>
 * @since 09.11.2021
 */
public class WebSocketConfigJdkTest
{
    /**
     * Тестирование корректности {@link StompBrokerAddressSupplier}
     */
    @Test
    public void testStompBrokerAddressSupplier()
    {
        StompBrokerAddressSupplier supplier = new WebSocketConfig(
                Mockito.mock(WebSocketSessionRegistryImpl.class),
                Mockito.mock(JwtTokenExtractor.class),
                new WebsocketProperties(null,
                        "(tcp://*******:61613,tcp://*******:61614)",
                        false,
                        20000,
                        60000,
                        20000,
                        20000)
        ).new StompBrokerAddressSupplier();

        Deque<String> expected = new LinkedList<>();
        expected.add("/*******:61613");
        expected.add("/*******:61614");
        expected.add("/*******:61613");

        for (int i = 0; i < 3; i++)
        {
            InetSocketAddress inetSocketAddress = (InetSocketAddress)supplier.get();
            assertEquals(expected.poll(), inetSocketAddress.toString());
        }
    }

    /**
     * Тестирование корректности обработки строки с параметрами подключения и создания URI из них
     */
    @Test
    public void testGetExternalStompBrokerURIs()
    {
        WebSocketConfig webSocketConfig = new WebSocketConfig(
                Mockito.mock(WebSocketSessionRegistryImpl.class),
                Mockito.mock(JwtTokenExtractor.class),
                new WebsocketProperties(
                        null,
                        "(tcp://*******:61613,tcp://*******:61614)",
                        false,
                        20000,
                        60000,
                        20000,
                        20000)
        );

        Deque<String> expected = new LinkedList<>();
        expected.add("tcp://*******:61613");
        expected.add("tcp://*******:61614");

        for (URI uri : webSocketConfig.getExternalStompBrokerURIs())
        {
            assertEquals(expected.poll(), uri.toString());
        }
    }
}
