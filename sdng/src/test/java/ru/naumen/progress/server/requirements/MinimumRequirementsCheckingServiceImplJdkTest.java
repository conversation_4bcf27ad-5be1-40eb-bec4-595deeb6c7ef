package ru.naumen.progress.server.requirements;

import static org.mockito.Mockito.*;

import org.junit.Before;
import org.junit.Test;

import ru.naumen.core.server.checks.CheckResult;

public class MinimumRequirementsCheckingServiceImplJdkTest
{
    @Before
    public void setUp()
    {
        System.clearProperty("skipEnvCheck");
    }

    @Test
    public void testThatItReallyChecks()
    {
        MinimumRequirementsCheckingServiceImpl service = mock(MinimumRequirementsCheckingServiceImpl.class);
        when(service.isInCiEnvironment()).thenReturn(false);
        when(service.checkThatMinimumRequirementsAreSatisfied()).thenReturn(new CheckResult("TEST"));
        doCallRealMethod().when(service).throwIfMinimumRequirementsAreNotSatisfied();

        service.throwIfMinimumRequirementsAreNotSatisfied();

        verify(service, times(1)).throwIfMinimumRequirementsAreNotSatisfied();
        verify(service, times(1)).isInCiEnvironment();
        verify(service, times(1)).checkThatMinimumRequirementsAreSatisfied();
        verifyNoMoreInteractions(service);
    }

    @Test
    public void testThatNoCheckIsPerformedWhenPropertyPassed()
    {
        System.setProperty("skipEnvCheck", "");
        MinimumRequirementsCheckingServiceImpl service = mock(MinimumRequirementsCheckingServiceImpl.class);
        when(service.isInCiEnvironment()).thenReturn(false);
        doCallRealMethod().when(service).throwIfMinimumRequirementsAreNotSatisfied();

        service.throwIfMinimumRequirementsAreNotSatisfied();

        verify(service, only()).throwIfMinimumRequirementsAreNotSatisfied();
    }

    @Test
    public void testThatNoCheckIsPerformedWhenPropertyPassedWithValue()
    {
        System.setProperty("skipEnvCheck", "true");
        MinimumRequirementsCheckingServiceImpl service = mock(MinimumRequirementsCheckingServiceImpl.class);
        when(service.isInCiEnvironment()).thenReturn(false);
        doCallRealMethod().when(service).throwIfMinimumRequirementsAreNotSatisfied();

        service.throwIfMinimumRequirementsAreNotSatisfied();

        verify(service, only()).throwIfMinimumRequirementsAreNotSatisfied();
    }

    @Test
    public void testThatCheckIsPerformedWhenPropertyPassedWithAnyOtherValueThanTrue()
    {
        System.setProperty("skipEnvCheck", "someInvalidValue");
        MinimumRequirementsCheckingServiceImpl service = mock(MinimumRequirementsCheckingServiceImpl.class);
        when(service.isInCiEnvironment()).thenReturn(false);
        when(service.checkThatMinimumRequirementsAreSatisfied()).thenReturn(new CheckResult("TEST"));
        doCallRealMethod().when(service).throwIfMinimumRequirementsAreNotSatisfied();

        service.throwIfMinimumRequirementsAreNotSatisfied();

        verify(service, times(1)).throwIfMinimumRequirementsAreNotSatisfied();
        verify(service, times(1)).isInCiEnvironment();
        verify(service, times(1)).checkThatMinimumRequirementsAreSatisfied();
        verifyNoMoreInteractions(service);
    }
}
