<?xml version="1.0" encoding="UTF-8"?>
<!--$Id$ -->
<module>
	<!-- Отключаем эмуляцию стека выполнения -->
    <!--<set-property name="compiler.stackMode" value="strip" /> -->
    <!--<set-property name="compiler.emulatedStack" value="false" /> -->
    
    <!-- Класс прекомпиляции модуля, подменяем для отключения оптимизации в модуле operator -->
    <define-configuration-property name="x.compiler.class" is-multi-valued="false"/>    
    
    <define-linker name="xsiframe" class="com.google.gwt.core.linker.NauCrossSiteIframeLinker" />
</module>
