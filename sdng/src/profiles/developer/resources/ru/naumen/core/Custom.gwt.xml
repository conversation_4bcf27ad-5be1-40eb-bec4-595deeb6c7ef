<?xml version="1.0" encoding="UTF-8"?>
<!--$Id$ -->
<module>
	<!-- Делаем одну пермутацию для целей тестирования, так быстрее -->
	<collapse-all-properties />
    <!-- Запрещаем обфускацию имен css классов для удобства отладки -->
	<set-configuration-property name="CssResource.obfuscationPrefix" value="empty" />
	<set-configuration-property name="CssResource.style" value="pretty" />
	
	<!-- Необходимо для работы DevMode в режиме отладки -->
	<set-configuration-property name="installCode" value="true" />
	<set-property name="ClientBundle.enableInlining" value="true" />

</module>