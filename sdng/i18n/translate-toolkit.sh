#!/bin/sh
#Тестируем properties -> po -> properties
#Автор: Лику<PERSON>ин Александр
#Дата: 29.05.2013

rm -f -r test

mkdir -p test/properties
cp -r ../src/main/resources/i18n/* test/properties

mkdir -p test/pot
prop2po -P test/properties test/pot

mkdir -p test/po
prop2po -t test/properties/messages_ru.properties test/properties/messages_ru.properties test/po/messages_ru.po
prop2po -t test/properties/messages_ru.properties test/properties/messages_en.properties test/po/messages_en.po

#pofilter test/po/messages_ru.po test/po/messages_ru.po.log
#pofilter -t isfuzzy -t untranslated test/po/messages_ru.po test/po/messages_ru.po.log
pofilter test/po/messages_ru.po test/po/messages_ru.po.log --excludefilter=simplecaps --excludefilter=startcaps --excludefilter=endpunc --excludefilter=puncspacing --excludefilter=startpunc --excludefilter=unchanged
pofilter test/po/messages_en.po test/po/messages_en.po.log --excludefilter=simplecaps --excludefilter=startcaps --excludefilter=endpunc --excludefilter=puncspacing --excludefilter=startpunc

#mkdir -p test/properties2
#po2prop test/po/messages_ru.po test/properties2/messages_ru.properties  -t pot/messages_ru.pot
#po2prop test/po/messages_en.po test/properties2/messages_en.properties  -t pot/messages_en.pot