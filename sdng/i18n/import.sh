#!/bin/sh
#Экспорт строковых констант в файлы для перввода
#Ожидаемые данные: файлы в каталоге ../src/main/translation/en
#Результат: файлы properties рядом с java файлами
#Автор: Ли<PERSON><PERSON><PERSON><PERSON>н Александр
#Дата: 07.05.2013
#Особенности:
# Если при импорте были ошибки, то они отображаются в  конце лога import.log после строки "javadoc: error"
#  Стандартная ошибка - не поддерживается inner class для локализации

#Удаляем лишние файлы
rm -f ../src/main/translation/en/ru.naumen.core.client.widgets.SimplePager.DisplayMessages_en.properties

#ant -f i18n.xml -Dmode=import -DtargetLocale=en
ant -f i18n.xml -Dmode=import -DtargetLocale=ru