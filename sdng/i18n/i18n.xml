<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- Необходимо подключить работу с maven через ant  http://maven.apache.org/ant-tasks/installation.html -->
<project default="javadoc" xmlns:artifact="antlib:org.apache.maven.artifact.ant">

    <path id="maven-ant-tasks.classpath"
          path="/usr/share/java/maven-ant-tasks.jar" />
    <typedef resource="org/apache/maven/artifact/ant/antlib.xml"
             uri="antlib:org.apache.maven.artifact.ant"
             classpathref="maven-ant-tasks.classpath" /> 

	<!-- Режим экспорт или импорт export | import-->
	<property name="mode" value="export" />
	<!-- Целевая локаль en, ru, ch-->
	<property name="targetLocale" value="en" />

	<artifact:pom id="sdngpom" file="../pom.xml" />
	<artifact:dependencies filesetId="sdngdeps" pomRefId="sdngpom" />
	<!--Используем  https://github.com/nshestakov/gwt-translation-helper, выложен на mvn.naumen.ru с версией 0.0.1-->
	<artifact:dependencies>
		<dependency groupId="com.crypticsquid" artifactId="gwt-translation-helper" version="0.0.2.nsd-SNAPSHOT" />
	</artifact:dependencies>

	<target name="javadoc">
		<javadoc access="public" additionalparam="-targetLocale ${targetLocale} -mode ${mode} -verbose" packagenames="com.google.*,ru.naumen.*" sourcepath="../src/main/java;../src/main/resources">
			<classpath>
				<fileset refid="sdngdeps" />
			</classpath>
			<doclet name="com.crypticsquid.javadoc.TranslationFileGenerator" path="${com.crypticsquid:gwt-translation-helper:jar}" />
		</javadoc>
	</target>
</project>
